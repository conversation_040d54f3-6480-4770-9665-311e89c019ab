package cn.gwssi.isearch.client.http;

import cn.gwssi.isearch.client.action.Action;
import cn.gwssi.isearch.client.exception.CouldNotConnectException;
import cn.gwssi.isearch.client.http.apache.HttpDeleteWithEntity;
import cn.gwssi.isearch.client.http.apache.HttpGetWithEntity;
import com.google.gson.Gson;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.auth.Credentials;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public class ISearchHttpClient extends AbstractISearchClient {

    private final static Logger log = LoggerFactory.getLogger(ISearchHttpClient.class);

    protected ContentType requestContentType = ContentType.APPLICATION_JSON.withCharset("utf-8");

    private CloseableHttpClient httpClient;
    private CloseableHttpAsyncClient asyncClient;

    private HttpClientContext httpClientContextTemplate;

    private Credentials credentials;

    /**
     * @throws IOException in case of a problem or the connection was aborted during request,
     *                     or in case of a problem while reading the response stream
     * @throws CouldNotConnectException if an {@link HttpHostConnectException} is encountered
     */
    @Override
    public <T> Pair<CloseableHttpResponse, HttpUriRequest> execute(Action<T> clientRequest) throws IOException {
        return execute(clientRequest, null, 0);
    }

    // FIXED 2023-03-22 同时返回 request 对象，用来给上一层使用
    // 请求错误有时候会导致 close_wait 问题，需要 abort 连接来规避这个问题
    public <T> Pair<CloseableHttpResponse, HttpUriRequest> execute(Action<T> clientRequest, RequestConfig requestConfig, int retryCount) throws IOException {
        // 最大重试次数
        int maxRetryCount = this.getServerPoolSize();

        // FIXED 2022-10-13 使用xpack的Authorization认证，不能在后面拼接un、pw
//        // 设置 x-pack 的账号和密码
//        if (null != credentials) {
//            clientRequest.setCredentials(credentials);
//        }

        HttpUriRequest request = prepareRequest(clientRequest, requestConfig);
//        CloseableHttpResponse response = null;
        try {
            return Pair.of(executeRequest(request), request);
        } catch (HttpHostConnectException ex) {
            if (++retryCount < maxRetryCount) {
                return this.execute(clientRequest, requestConfig, retryCount);
            } else {
                throw new CouldNotConnectException(ex.getHost().toURI(), ex);
            }
        }
//        finally {
//            if (response != null) {
//                try {
//                    response.close();
//                } catch (IOException ex) {
//                    log.error("Exception occurred while closing response stream.", ex);
//                }
//            }
//        }
    }

    @Override
    public <T> void executeAsync(final Action<T> clientRequest, final ISearchResultHandler<? super T> resultHandler) {
        executeAsync(clientRequest, resultHandler, null);
    }

    public <T> void executeAsync(final Action<T> clientRequest, final ISearchResultHandler<? super T> resultHandler, final RequestConfig requestConfig) {
        synchronized (this) {
            if (!asyncClient.isRunning()) {
                asyncClient.start();
            }
        }

        HttpUriRequest request = prepareRequest(clientRequest, requestConfig);
        executeAsyncRequest(clientRequest, resultHandler, request);
    }

    @Override
    public void shutdownClient() {
        try {
            close();
        } catch (IOException e) {
            log.error("Exception occurred while shutting down the sync client.", e);
        }
    }

    @Override
    public void close() throws IOException {
        super.close();
        asyncClient.close();
        httpClient.close();
    }

    protected <T> HttpUriRequest prepareRequest(final Action<T> clientRequest, final RequestConfig requestConfig) {
        String restUrl = getRequestURL(getNextServer(), clientRequest.getRestPath());
        HttpUriRequest request = null;
        if (clientRequest.getParas() != null){
            request =  constructHttpMethod(clientRequest.getRestMethodName(),
                    restUrl,clientRequest.getParas(), requestConfig);
        }else {
            request =  constructHttpMethod(clientRequest.getRestMethodName(),
                    restUrl,clientRequest.getSerializerParam(gson), requestConfig);
        }
        log.debug("Request method={} url={}", clientRequest.getRestMethodName(), restUrl);

        // add headers added to action
        for (Entry<String, Object> header : clientRequest.getHeaders().entrySet()) {
            request.addHeader(header.getKey(), header.getValue().toString());
        }

        return request;
    }

    protected CloseableHttpResponse executeRequest(HttpUriRequest request) throws IOException {
        if (httpClientContextTemplate != null) {
            return httpClient.execute(request, createContextInstance());
        }

        return httpClient.execute(request);
    }

    protected <T> Future<HttpResponse> executeAsyncRequest(Action<T> clientRequest,
                                                           ISearchResultHandler<? super T> resultHandler, HttpUriRequest request) {
        if (httpClientContextTemplate != null) {
            return asyncClient.execute(request, createContextInstance(), new DefaultCallback<T>(clientRequest, request, resultHandler));
        }

        return asyncClient.execute(request, new DefaultCallback<T>(clientRequest, request, resultHandler));
    }

    protected HttpClientContext createContextInstance() {
        HttpClientContext context = HttpClientContext.create();
        context.setCredentialsProvider(httpClientContextTemplate.getCredentialsProvider());
        context.setAuthCache(httpClientContextTemplate.getAuthCache());

        return context;
    }

    protected HttpUriRequest constructHttpMethod(String methodName, String url, String payload, RequestConfig requestConfig){
        HttpUriRequest httpUriRequest = getRequest(methodName,url);
        if (httpUriRequest instanceof HttpRequestBase && requestConfig != null) {
            ((HttpRequestBase) httpUriRequest).setConfig(requestConfig);
        }


        if (httpUriRequest != null && httpUriRequest instanceof HttpEntityEnclosingRequest && payload != null) {
            EntityBuilder entityBuilder = EntityBuilder.create()
                    .setText(payload)
                    .setContentType(requestContentType);

            if (isRequestCompressionEnabled()) {
                entityBuilder.gzipCompress();
            }

            ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entityBuilder.build());
        }
        return httpUriRequest;
    }

    protected HttpUriRequest constructHttpMethod(String methodName, String url, List<NameValuePair> pairs, RequestConfig requestConfig) {
        HttpUriRequest httpUriRequest = getRequest(methodName,url);
        if (httpUriRequest instanceof HttpRequestBase && requestConfig != null) {
            ((HttpRequestBase) httpUriRequest).setConfig(requestConfig);
        }

        if (httpUriRequest != null && httpUriRequest instanceof HttpEntityEnclosingRequest && pairs != null) {
            EntityBuilder entityBuilder = EntityBuilder.create()
                    .setParameters(pairs)
                    .setContentType(requestContentType);

            if (isRequestCompressionEnabled()) {
                entityBuilder.gzipCompress();
            }

            ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entityBuilder.build());
        }
        return httpUriRequest;
    }


    private <T> T deserializeResponse(HttpResponse response, final HttpRequest httpRequest, Action<T> clientRequest) throws IOException {
        return (T) response;

    }

    private HttpUriRequest getRequest(String methodName, String url){
        HttpUriRequest httpUriRequest = null;
        if (methodName.equalsIgnoreCase("POST")) {
//            URIBuilder uriBuilder = new URIBuilder(url);
            httpUriRequest = new HttpPost(url);
            log.debug("POST method created based on client request");
        } else if (methodName.equalsIgnoreCase("PUT")) {
            httpUriRequest = new HttpPut(url);
            log.debug("PUT method created based on client request");
        } else if (methodName.equalsIgnoreCase("DELETE")) {
            httpUriRequest = new HttpDeleteWithEntity(url);
            log.debug("DELETE method created based on client request");
        } else if (methodName.equalsIgnoreCase("GET")) {
            httpUriRequest = new HttpGetWithEntity(url);
            log.debug("GET method created based on client request");
        } else if (methodName.equalsIgnoreCase("HEAD")) {
            httpUriRequest = new HttpHead(url);
            log.debug("HEAD method created based on client request");
        }
        return httpUriRequest;
    }

    public CloseableHttpClient getHttpClient() {
        return httpClient;
    }

    public void setHttpClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    public CloseableHttpAsyncClient getAsyncClient() {
        return asyncClient;
    }

    public void setAsyncClient(CloseableHttpAsyncClient asyncClient) {
        this.asyncClient = asyncClient;
    }

    public Gson getGson() {
        return gson;
    }

    public void setGson(Gson gson) {
        this.gson = gson;
    }

    public HttpClientContext getHttpClientContextTemplate() {
        return httpClientContextTemplate;
    }

    public void setHttpClientContextTemplate(HttpClientContext httpClientContext) {
        this.httpClientContextTemplate = httpClientContext;
    }

    public Credentials getCredentials() {
        return this.credentials;
    }

    public void setCredentials(Credentials credentials) {
        this.credentials = credentials;
    }

    protected class DefaultCallback<T> implements FutureCallback<HttpResponse> {
        private final Action<T> clientRequest;
        private final HttpRequest request;
        private final ISearchResultHandler<? super T> resultHandler;
        public DefaultCallback(Action<T> clientRequest, final HttpRequest request, ISearchResultHandler<? super T> resultHandler) {
            this.clientRequest = clientRequest;
            this.request = request;
            this.resultHandler = resultHandler;
        }

        @Override
        public void completed(final HttpResponse response) {
            T jestResult = null;
            try {
                jestResult = deserializeResponse(response, request, clientRequest);
            } catch (Exception e) {
                failed(e);
            } catch (Throwable t) {
                failed(new Exception("Problem during request processing", t));
            }
            if (jestResult != null) resultHandler.completed(jestResult);
        }

        @Override
        public void failed(final Exception ex) {
            log.error("Exception occurred during async execution.", ex);
            if (ex instanceof HttpHostConnectException) {
                String host = ((HttpHostConnectException) ex).getHost().toURI();
                resultHandler.failed(new CouldNotConnectException(host, ex));
                return;
            }
            resultHandler.failed(ex);
        }

        @Override
        public void cancelled() {
            log.warn("Async execution was cancelled; this is not expected to occur under normal operation.");
        }
    }

}
