package cn.gwssi.isearch.client.action;

import com.google.gson.Gson;
import org.apache.http.NameValuePair;
import org.apache.http.auth.Credentials;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface Action<T> {

    /**
     * POST DELETE PUT GET HEAD
     * @return
     */
    String getRestMethodName();

    /**
     * Rest 请求URL
     * @return
     */
    String getRestPath();

    String getData(Gson gson);

    /**
     * 参数序列化后字符串
     * @return
     */
    String getSerializerParam(Gson gson);

    String getPathToResult();

    @Deprecated
    List<NameValuePair> getParas();

    Map<String, Object> getHeaders();

    void setCredentials(Credentials credentials);

}
