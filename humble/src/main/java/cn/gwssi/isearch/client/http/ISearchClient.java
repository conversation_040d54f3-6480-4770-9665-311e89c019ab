package cn.gwssi.isearch.client.http;


import cn.gwssi.isearch.client.action.Action;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpRequest;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;

import java.io.Closeable;
import java.io.IOException;
import java.util.Set;


/**
 * <AUTHOR>
 */
public interface ISearchClient extends Closeable {

    <T> Pair<CloseableHttpResponse, HttpUriRequest> execute(Action<T> clientRequest) throws IOException;

    <T> void executeAsync(Action<T> clientRequest, ISearchResultHandler<? super T> ISearchResultHandler);

    /**
     * @deprecated Use {@link #close()} instead.
     */
    @Deprecated
    void shutdownClient();

    void setServers(Set<String> servers);
}
