package cn.gwssi.isearch;

import cn.gwssi.isearch.client.ISearchClientFactory;
import cn.gwssi.isearch.client.config.HttpClientConfig;
import cn.gwssi.isearch.client.http.ISearchClient;
import cn.gwssi.isearch.param.RestMethod;
import org.apache.http.client.methods.CloseableHttpResponse;

import java.io.IOException;

/**
 * Created by duq on 2019/3/27.
 */
public class IsearchClientFactoryTest
{

    public static void main(String[] args) throws IOException {
        post();
    }

    public static void post() throws IOException {
        HttpClientConfig httpClientConfig = new HttpClientConfig.Builder(
                "http://192.168.2.178:9200").connTimeout(150).readTimeout(300).build();
        ISearchClientFactory factory = new ISearchClientFactory();
        factory.setHttpClientConfig(httpClientConfig);
        ISearchClient ISearchClient = factory.getObject();
        RequestAction clientRequest = new RequestAction.Builder<>()
                .setRestMethodName(RestMethod.POST.getValue())
                .setRestPath("/isearch/patent/all")
                .build();
//        CloseableHttpResponse closeableHttpResponse = null;
//            closeableHttpResponse = ISearchClient.execute(clientRequest);
//        int status = closeableHttpResponse.getStatusLine().getStatusCode();
//        System.out.println(status);
    }

}
