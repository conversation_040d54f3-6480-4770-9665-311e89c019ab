package cn.gwssi.isearch;

import cn.gwssi.isearch.client.action.Action;
import cn.gwssi.isearch.param.Parameters;
import com.google.gson.Gson;
import org.apache.http.NameValuePair;
import org.apache.http.auth.Credentials;
import org.apache.http.message.BasicNameValuePair;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Created by duq on 2019/3/27.
 */
public class RequestAction<T> implements Action<T> {

    private final ConcurrentMap<String, Object> headerMap = new ConcurrentHashMap<String, Object>();
    private Map<String, String> params = new LinkedHashMap<>();
    private String restMethodName;
    private String restPath;
    private RequestAction(Builder builder){
        this.headerMap.putAll(builder.headers);
        this.params.putAll(builder.parameters);
        this.restMethodName = builder.restMethodName;
        this.restPath = builder.restPath;
    }

    @Override
    public String getRestMethodName() {
        return restMethodName;
    }

    @Override
    public String getRestPath() {
        return null;
    }

    @Override
    public String getData(Gson gson) {
        return null;
    }

    @Override
    public String getSerializerParam(Gson gson) {
        return null;
    }

    @Override
    public List<NameValuePair> getParas() {
        List<NameValuePair> pairs = new ArrayList<>();
        for (Map.Entry<String, String> entry : params.entrySet()){
            pairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        return pairs;
    }

    @Override
    public void setCredentials(Credentials credentials) {}

    @Override
    public String getPathToResult() {
        return null;
    }

    @Override
    public Map<String, Object> getHeaders() {
        return headerMap;
    }

    protected static class Builder<T extends Action, K> {
        protected Map<String, String> parameters = new LinkedHashMap<>();
        protected Map<String, Object> headers = new LinkedHashMap<String, Object>();
        protected Set<String> cleanApiParameters = new LinkedHashSet<String>();
        protected String restMethodName;
        protected String restPath;

        public Builder toggleApiParameter(String key, boolean enable) {
            if (enable) {
                addCleanApiParameter(key);
            } else {
                removeCleanApiParameter(key);
            }

            return this;
        }

        public K removeCleanApiParameter(String key) {
            cleanApiParameters.remove(key);
            return (K) this;
        }

        public Builder addCleanApiParameter(String key) {
            cleanApiParameters.add(key);
            return this;
        }

        public Builder setParameter(String key, String value) {
            parameters.put(key, value);
            return this;
        }

        public Builder setParameter(Map<String, String> parameters) {
            for (Map.Entry<String, String> entry : parameters.entrySet()) {
                this.parameters.put(entry.getKey(), entry.getValue());
            }
            return this;
        }

        public Builder setHeader(String key, Object value) {
            headers.put(key, value);
            return  this;
        }

        public Builder setHeader(Map<String, Object> headers) {
            this.headers.putAll(headers);
            return this;
        }

        public Builder setRestMethodName(String restMethodName){
            this.restMethodName = restMethodName;
            return this;
        }

        public Builder setRestPath(String restPath){
            this.restPath = restPath;
            return this;
        }

//        public K refresh(boolean refresh) {
//            return setParameter(Parameters.REFRESH, refresh);
//        }

        /**
         * All REST APIs accept the case parameter.
         * When set to camelCase, all field names in the result will be returned
         * in camel casing, otherwise, underscore casing will be used. Note,
         * this does not apply to the source document indexed.
         */
        public Builder resultCasing(String caseParam) {
            setParameter(Parameters.RESULT_CASING, caseParam);
            return this;
        }

        public RequestAction build(){
            return new RequestAction(this);
        };
    }
}
