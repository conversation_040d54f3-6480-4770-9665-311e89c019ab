Usage
------------
创建一个HttpClient实例：
``` java
 HttpClientConfig httpClientConfig = new HttpClientConfig.Builder(
                 "http://192.168.2.178:9200")
                 .connTimeout(10)//连接时间，设置太短会报  connect timed out
                 .readTimeout(300)
                 .defaultCredentials("gwssi","gwssi123")//用户密码，可以不增加这个参数
                 .build();
         ISearchClientFactory factory = new ISearchClientFactory();// 或 new ISearchClientFactory(httpClientConfig);
         factory.setHttpClientConfig(httpClientConfig);
         ISearchClient iSearchClient = factory.getObject();
         CloseableHttpResponse closeableHttpResponse = iSearchClient.execute(clientRequest);
         String response = EntityUtils.toString(closeableHttpResponse.getEntity(), Charset.forName("utf-8"));
         int status = closeableHttpResponse.getStatusLine().getStatusCode();
```
>`ISearchClientFactory`提供了两个构造方法，一个带`HttpClientConfig`参数，一个不带参数，`HttpClientConfig`中的`connTimeout`为连接时间，`readTimeout`为读取时间。使用方法在Maven上增加如下依赖
```xml
<dependency>
    <groupId>cn.gwssi.isearch</groupId>
    <artifactId>humble</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```
 