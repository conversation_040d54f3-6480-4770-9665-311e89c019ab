<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.gwssi.isearch</groupId>
        <artifactId>isearch-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>IClient</artifactId>
    <version>${revision}</version>
    
    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>
    
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>humble</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-antlr-builder</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-expr-builder</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-fieldmapping-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>plugins-common-search</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>entity</artifactId>
        </dependency>
    
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>org.json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
    
</project>
