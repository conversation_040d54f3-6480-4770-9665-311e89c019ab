## Usage
#### 创建Client实例
``` java
String url = "http://114.116.54.141:8101";
HttpClientConfig clientConfig = new HttpClientConfig.Builder(url)
        .multiThreaded(true)
        .connTimeout(5 * 60 * 1000).readTimeout(5 * 60 * 1000)
        .defaultCredentials("wfl", "1123")
        .build();

httpClient = new Client(clientConfig, "/dao");
```
>`Client`构造函数由两个参数构成。第一个参数`HttpClientConfig`指定了客户端和ES通信所必须的http参数。例如:URL, 用户名密码， http连接超时时长等。第二个参数指定了本地xml的路径，主要用于检索表达式的解析和转换。注意，创建一个client实例即可，不需要为每次检索/统计请求创建一个client实例。

#### 检索（search）
##### 1. 一般检索
``` java
final String DB = "patent2";
final String name = "all";
String expre = "发明人=刘佳";
IPSearchResult ipSearchResult = httpClient.searchRequest().search(DB, name, expre, SubGson.class);
```
> 执行检索时，需要指定indexname, name,检索表达式以及类名。最后一个参数主要用于用户自定义返回结果。该类必须继承自`GsonObject`。如下所示:

``` java
//SubGson.java
public class SubGson extends GsonObject {
    private String pdt;
    private String ftko;
    private String pid;
}
```
> `SubGson`中指定了三个属性：pdt、ftko、pid。当用户自定义的这些属性和ES `_source`中定义的key一致时，结果中将返回对应的value。

##### 2. 指定field的检索
检索时也可以指定ES返回的结果集中包含的field，以及from和size属性。当用户不指定from 和size属性时。默认返回前10条数据。
``` java
String expre = "发明人=刘佳";
FieldList fieldList = new FieldList();
fieldList.add("pdt");
fieldList.add("ftko");
fieldList.add("lbno");
IPSearchResult ipSearchResult = httpClient.searchRequest().search(DB, name, expre, fieldList, SubGson.class, 0, 10);
```
##### 3. 指定排序字段的检索
检索时也可以同时指定ES返回的结果集中包含的field、排序字段以及from和size属性。
``` java
String expre = "";
FieldList fieldList = new FieldList();
fieldList.add("pdt");
fieldList.add("ftko");
fieldList.add("aymd");
SortFieldList sortFieldList = new SortFieldList();
sortFieldList.add(new SortField("aymd", SortField.IPSorting.ASC));
IPSearchResult ipSearchResult = httpClient.searchRequest().search(DB, name, expre, fieldList, sortFieldList, SubGson.class, 0, 20);
```
#### 聚合（Aggregation）
##### 1. 一维聚合
``` java
private String DB = "patent2"
AggregationDim aggregationDim = new AggregationDim("ano");
aggregationDim.setFilterValues("CN200880019700.8");
List<OneDimCountResult> results = httpClient.aggregationRequest().oneDimCount(DB, "all", "申请号=CN200880019700.8", aggregationDim);
```
参数从左向右依次为"indexName"、"typename"、"查询表达式"以及聚合对象。其中聚合对象`AggregationDim`是一个变参，用户可以输入多个并列的聚合对象做一维统计。如下所示:
``` java
AggregationDim aggregationDim = new AggregationDim("apo");
AggregationDim aggregationDim1 = new AggregationDim("ano");
aggregationDim1.setFilterValues("CN200880019700.8");
Map<String, List<OneDimCountResult>> results = httpClient.aggregationRequest().oneDimCount(DB, "all", "", aggregationDim, aggregationDim1);
```
##### 2. 二维聚合
二维聚合允许用户在一个维度的基础上，进一步做聚合统计。示例：
``` java
AggregationDim aggregationDim1 = new AggregationDim("ano");
aggregationDim1.setFilterValues("CN200880019700.8");
AggregationDim aggregationDim = new AggregationDim("apo");
List<TwoDimCountResult> twoDimCount = httpClient.aggregationRequest().twoDimCount(DB, "all", "", aggregationDim1, aggregationDim);
```
注意，二维统计时，后者需要在前者的基础上做聚合，所以参数的顺序很重要，最后传入的`AggregationDim`将在前一个`AggregationDim`的基础上进一步做聚合统计。如上所示，apo将在ano的基础上做聚合统计。
###### 3. 多维聚合
目前iclient暂时只支持最多三维聚合，超过三维将抛异常。示例：
``` java
AggregationDim aggregationDim1 = new AggregationDim("pdt");

AggregationDim aggregationDim2 = new AggregationDim("ano");
aggregationDim2.setFilterValues("CN200610129938.9;CN200810240624.5");

AggregationDim aggregationDim = new AggregationDim("apo");
aggregationDim.setFilterValues("浙江大学;东莞市瑞德丰生物科技有限公司;西北农林科技大学");
List<MultiDimCountResult> twoDimCount = httpClient.aggregationRequest().multiDimCount(DB, "all", "", aggregationDim1, aggregationDim2, aggregationDim);
```
**注意**，三维统计时，后者将在前者的基础上做聚合，所以参数的顺序很重要，后传入的`AggregationDim`将在前一个`AggregationDim`的基础上进一步做聚合统计。如上所示，apo将在ano基础上，ano将在pdt的基础上做聚合。

#### 个数统计（Count）
``` java
String expre = "发明人=刘佳";
final String DB = "patent2";
double count = client.searchRequest().count(DB, "all", expre);
```
参数顺序依次为indexname, typename以及查询语句。
