package cn.gwssi.data.service.core.search.aggregation;

import com.google.gson.JsonObject;

import java.util.Objects;

/**
 * Place holder class used to represent the root aggregation
 * returned to the user for processing.
 *
 * <AUTHOR>
 */
public class RootAggregation extends MetricAggregation {

    public RootAggregation(String name, JsonObject root) {
        super(name, root);
    }


    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), "root");
    }
}
