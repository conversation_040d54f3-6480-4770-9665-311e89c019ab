package cn.gwssi.data.service.base;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import com.google.gson.Gson;

/**
 * <AUTHOR> keser
 */
public class BaseRequest extends GenericResultAbstractAction {

    private String method;
    private String path;
    private String data;

    public BaseRequest(Builder builder) {
        super(builder);
        this.method = builder.method;
        this.path = builder.path;
        this.data = builder.data;
    }

    @Override
    public String getRestMethodName() {
        return this.method;
    }

    @Override
    public String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return this.path;
    }

    @Override
    public String getData(Gson gson) {
        return this.data;
    }

    public static class Builder extends AbstractAction.Builder<BaseRequest, Builder> {

        private String method;
        private String path;
        private String data;

        public Builder setMethod(String method) {
            this.method = method;
            return this;
        }

        public Builder setPath(String path) {
            this.path = path;
            return this;
        }

        public Builder setData(String data) {
            this.data = data;
            return this;
        }

        @Override
        public BaseRequest build() {
            return new BaseRequest(this);
        }
    }
}
