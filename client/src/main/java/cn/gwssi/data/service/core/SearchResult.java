package cn.gwssi.data.service.core;

import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.data.service.client.ISearchResult;
import cn.gwssi.data.service.cloning.CloneUtils;
import cn.gwssi.data.service.core.search.aggregation.MetricAggregation;
import cn.gwssi.data.service.core.search.aggregation.RootAggregation;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.*;

/**
 * <AUTHOR> keser
 */
public class SearchResult extends ISearchResult {

    public static final String EXPLANATION_KEY = "_explanation";
    public static final String HIGHLIGHT_KEY = "highlight";
    public static final String SORT_KEY = "sort";
    public static final String COLLAPSE_FIELD_KEY = "fields";
    public static final String[] PATH_TO_TOOK = "took".split("/");
    public static final String[] PATH_TO_SCROLL = "_scroll_id".split("/");
    public static final String[] PATH_TO_TOTAL = "hits/total".split("/");
    public static final String[] PATH_TO_TOTAL_NATIVE = "hits/total/value".split("/");
    public static final String[] PATH_TO_MAX_SCORE = "hits/max_score".split("/");
    public static final String[] PATH_TO_INNER_RESULT = "hits/hits/inner_hits".split("/");

    public SearchResult(SearchResult searchResult) {
        super(searchResult);
    }

    public SearchResult(Gson gson) {
        super(gson);
    }

    @Override
    @Deprecated
    public <T> T getSourceAsObject(Class<T> clazz) {
        return super.getSourceAsObject(clazz);
    }

    @Override
    @Deprecated
    public <T> List<T> getSourceAsObjectList(Class<T> type) {
        return super.getSourceAsObjectList(type);
    }

    public <T> Hit<T, Void> getFirstHit(Class<T> sourceType) {
        return getFirstHit(sourceType, Void.class);
    }

    public <T, K> Hit<T, K> getFirstHit(Class<T> sourceType, Class<K> explanationType) {
        Hit<T, K> hit = null;

        List<Hit<T, K>> hits = getHits(sourceType, explanationType, true);
        if (!hits.isEmpty()) {
            hit = hits.get(0);
        }

        return hit;
    }

    public <T> List<Hit<T, Void>> getHits(Class<T> sourceType) {
        return getHits(sourceType, true);
    }

    public <T extends GsonObject> List<Hit<T, Void>> getInnerHits(Class<T> sourceType) {
        return getInnerHits(sourceType, Void.class, false, true);
    }

    public <T extends GsonObject> Map<String, List<Hit<T, Void>>> getInnerHitsAsMap(Class<T> sourceType) {
        return getInnerHitsAsMap(sourceType, Void.class, false, true);
    }

    public <T> List<Hit<T, Void>> getHits(Class<T> sourceType, boolean addEsMetadataFields) {
        return getHits(sourceType, Void.class, addEsMetadataFields);
    }

    public <T, K> List<Hit<T, K>> getHits(Class<T> sourceType, Class<K> explanationType) {
        return getHits(sourceType, explanationType, false, true);
    }

    public <T, K> List<Hit<T, K>> getHits(Class<T> sourceType, Class<K> explanationType, boolean addEsMetadataFields) {
        return getHits(sourceType, explanationType, false, addEsMetadataFields);
    }

    protected <T, K> List<Hit<T, K>> getHits(Class<T> sourceType, Class<K> explanationType, boolean returnSingle, boolean addEsMetadataFields) {
        return getHits(jsonObject, sourceType, explanationType, returnSingle, addEsMetadataFields);
    }

    protected <T, K> List<Hit<T, K>> getHits(JsonObject sourceObject, Class<T> sourceType, Class<K> explanationType, boolean returnSingle, boolean addEsMetadataFields) {
        List<Hit<T, K>> sourceList = new ArrayList<Hit<T, K>>();

        if (sourceObject != null) {
            String[] keys = getKeys();
            if (keys != null) { // keys would never be null in a standard search scenario (i.e.: unless search class is overwritten)
                String sourceKey = keys[keys.length - 1];
                JsonElement obj = sourceObject.get(keys[0]);
                for (int i = 1; i < keys.length - 1; i++) {
                    obj = ((JsonObject) obj).get(keys[i]);
                }

                if (obj.isJsonObject()) {
                    sourceList.add(extractHit(sourceType, explanationType, obj, sourceKey, addEsMetadataFields));
                } else if (obj.isJsonArray()) {
                    for (JsonElement hitElement : obj.getAsJsonArray()) {
                        sourceList.add(extractHit(sourceType, explanationType, hitElement, sourceKey, addEsMetadataFields));
                        if (returnSingle) break;
                    }
                }
            }
        }

        return sourceList;
    }

    protected <T extends GsonObject, K> List<Hit<T, K>> getInnerHits(Class<T> sourceType, Class<K> explanationType, boolean returnSingle, boolean addEsMetadataFields) {
        List<Hit<T, K>> sourceList = new ArrayList<Hit<T, K>>();

        if (jsonObject != null) {
            // keys would never be null in a standard search scenario (i.e.: unless search class is overwritten)
            String sourceKey = PATH_TO_INNER_RESULT[2];
            JsonElement obj = jsonObject.getAsJsonObject(PATH_TO_INNER_RESULT[0]).get(PATH_TO_INNER_RESULT[1]);

            if (obj.isJsonObject()) {
                Hit<T, K> hit = this.extractInnerHit(sourceType, explanationType, obj, sourceKey, addEsMetadataFields);
                if (null != hit) {
                    sourceList.add(hit);
                }
            } else if (obj.isJsonArray()) {
                for (JsonElement hitElement : obj.getAsJsonArray()) {
                    Hit<T, K> hit = this.extractInnerHit(sourceType, explanationType, hitElement, sourceKey, addEsMetadataFields);
                    if (null != hit) {
                        sourceList.add(hit);
                    }
                    if (returnSingle) break;
                }
            }
        }

        return sourceList;
    }

    protected <T extends GsonObject, K> Map<String, List<Hit<T, K>>> getInnerHitsAsMap(Class<T> sourceType, Class<K> explanationType, boolean returnSingle, boolean addEsMetadataFields) {
        Map<String, List<Hit<T, K>>> map = new HashMap<>();

        if (jsonObject != null) {
            // keys would never be null in a standard search scenario (i.e.: unless search class is overwritten)
            String sourceKey = PATH_TO_INNER_RESULT[2];
            JsonElement obj = jsonObject.getAsJsonObject(PATH_TO_INNER_RESULT[0]).get(PATH_TO_INNER_RESULT[1]);

            if (obj.isJsonObject()) {
                String field = this.extractValue(obj);
                List<Hit<T, K>> hits = this.extractInnerHits(sourceType, explanationType, obj, sourceKey, addEsMetadataFields);
                if (null != hits) {
                    map.put(field, hits);
                }
            } else if (obj.isJsonArray()) {
                for (JsonElement hitElement : obj.getAsJsonArray()) {
                    String field = this.extractValue(hitElement);
                    List<Hit<T, K>> hits = this.extractInnerHits(sourceType, explanationType, hitElement, sourceKey, addEsMetadataFields);
                    if (null != hits) {
                        map.put(field, hits);
                    }
                    if (returnSingle) break;
                }
            }
        }

        return map;
    }

    protected <T extends GsonObject, K> List<Hit<T, K>> extractInnerHits(Class<T> sourceType, Class<K> explanationType, JsonElement hitElement, String sourceKey, boolean addEsMetadataFields) {
        if (hitElement.isJsonObject()) {
            JsonObject source = hitElement.getAsJsonObject().getAsJsonObject(sourceKey);
            Set<Map.Entry<String, JsonElement>> entrySet = source.entrySet();
            if (entrySet.size() > 0) {
                // inner_hits 可以有多个，这里只取第一个，其余的忽略掉
                JsonObject hitsObj = entrySet.iterator().next().getValue().getAsJsonObject();
                return this.getHits(hitsObj, sourceType, explanationType, false, addEsMetadataFields);
            }
        }

        return null;
    }


    protected <T extends GsonObject, K> Hit<T, K> extractInnerHit(Class<T> sourceType, Class<K> explanationType, JsonElement hitElement, String sourceKey, boolean addEsMetadataFields) {
        if (hitElement.isJsonObject()) {
            JsonObject source = hitElement.getAsJsonObject().getAsJsonObject(sourceKey);
            Set<Map.Entry<String, JsonElement>> entrySet = source.entrySet();
            if (entrySet.size() > 0) {
                // inner_hits 可以有多个，这里只取第一个，其余的忽略掉
                JsonObject hitsObj = entrySet.iterator().next().getValue().getAsJsonObject();
                List<Hit<T, K>> hits = this.getHits(hitsObj, sourceType, explanationType, false, addEsMetadataFields);

                // inner_hits 下的 hits，返回第一个对象，其余的作为 children 放到第一个对象中
                Hit<T, K> tk = null;
                int size = hits.size();
                if (size > 0) {
                    tk = hits.get(0);

                    if (size > 1) {
                        List<T> others = new ArrayList<>();
                        for (int i = 1; i < size; i++) {
                            others.add(hits.get(i).source);
                        }
                        tk.source.setChildren(others);
                    }
                }
                return tk;
            }
        }

        return null;
    }

    protected <T, K> Hit<T, K> extractHit(Class<T> sourceType, Class<K> explanationType, JsonElement hitElement, String sourceKey, boolean addEsMetadataFields) {
        Hit<T, K> hit = null;

        if (hitElement.isJsonObject()) {
            JsonObject hitObject = hitElement.getAsJsonObject();
            JsonObject source = hitObject.getAsJsonObject(sourceKey);


            String index = hitObject.get("_index").getAsString();
            String type = hitObject.get("_type").getAsString();

            String id = hitObject.get("_id").getAsString();

            Double score = null;
            if (hitObject.has("_score") && !hitObject.get("_score").isJsonNull()) {
                score = hitObject.get("_score").getAsDouble();
            }

            String parent = null;
            String routing = null;

            if (hitObject.has("_parent") && !hitObject.get("_parent").isJsonNull()) {
                parent = hitObject.get("_parent").getAsString();
            }

            if (hitObject.has("_routing") && !hitObject.get("_routing").isJsonNull()) {
                routing = hitObject.get("_routing").getAsString();
            }

            JsonElement explanation = hitObject.get(EXPLANATION_KEY);
            Map<String, List<String>> highlight = extractHighlight(hitObject.getAsJsonObject(HIGHLIGHT_KEY));
            List<String> sort = extractSort(hitObject.getAsJsonArray(SORT_KEY));

            List<String> matchedQueries = new ArrayList<>();
            if (hitObject.has("matched_queries") && !hitObject.get("matched_queries").isJsonNull()) {
                JsonArray rawMatchedQueries = hitObject.get("matched_queries").getAsJsonArray();

                for (int i = 0; i < rawMatchedQueries.size(); i++) {
                    JsonElement jsonElement = rawMatchedQueries.get(i);
                    matchedQueries.add(jsonElement.getAsString());
                }
//                rawMatchedQueries.forEach(matchedQuery -> {
//                    matchedQueries.add(matchedQuery.getAsString());
//                });
            }

            if (addEsMetadataFields) {
                JsonObject clonedSource = null;
                for (MetaField metaField : META_FIELDS) {
                    JsonElement metaElement = hitObject.get(metaField.esFieldName);
                    if (metaElement != null) {
                        if (clonedSource == null) {
                            if (source == null) {
                                clonedSource = new JsonObject();
                            } else {
                                clonedSource = (JsonObject) CloneUtils.deepClone(source);
                            }
                        }
                        clonedSource.add(metaField.internalFieldName, metaElement);
                    }
                }
                if (clonedSource != null) {
                    source = clonedSource;
                }
            }

            hit = new Hit<T, K>(
                    sourceType,
                    source,
                    explanationType,
                    explanation,
                    highlight,
                    sort,
                    index,
                    type,
                    id,
                    score,
                    parent,
                    routing,
                    matchedQueries
            );

        }

        return hit;
    }

    // 抽取合并字段的值，原始数据为：
    // "fields": {
    //   "anm": ["CN201010052951.5"]
    // }
    private String extractValue(JsonElement element) {
        JsonObject obj = element.getAsJsonObject().getAsJsonObject(COLLAPSE_FIELD_KEY);
        JsonElement value = obj.get(obj.keySet().iterator().next());
        if (value instanceof JsonObject) {
            return value.getAsString();
        } else if (value instanceof JsonArray) {
            return value.getAsJsonArray().get(0).getAsString();
        }

        return "";
    }

    protected List<String> extractSort(JsonArray sort) {
        if (sort == null) {
            return null;
        }

        List<String> retval = new ArrayList<String>(sort.size());
        for (JsonElement sortValue : sort) {
            retval.add(sortValue.isJsonNull() ? "" : sortValue.getAsString());
        }
        return retval;
    }

    protected Map<String, List<String>> extractHighlight(JsonObject highlight) {
        Map<String, List<String>> retval = null;

        if (highlight != null) {
            Set<Map.Entry<String, JsonElement>> highlightSet = highlight.entrySet();
            retval = new HashMap<String, List<String>>(highlightSet.size());

            for (Map.Entry<String, JsonElement> entry : highlightSet) {
                List<String> fragments = new ArrayList<String>();
                for (JsonElement element : entry.getValue().getAsJsonArray()) {
                    fragments.add(element.getAsString());
                }
                retval.put(entry.getKey(), fragments);
            }
        }

        return retval;
    }

    public Long getTook() {
        Long took = null;
        JsonElement obj = getPath(PATH_TO_TOOK);
        if (obj != null) took = obj.getAsLong();
        return took;
    }

    public String getScrollId() {
        String scroll = null;
        JsonElement obj = getPath(PATH_TO_SCROLL);
        if (obj != null) scroll = obj.getAsString();
        return scroll;
    }

    public Long getTotal() {
        Long total = null;
        JsonElement obj = getPath(PATH_TO_TOTAL);
        if (obj != null) total = obj.getAsLong();
        return total;
    }

    public long getTotal(boolean isNative) {
        Long total = null;
        JsonElement obj = getPath(PATH_TO_TOTAL_NATIVE);
        if (obj != null) total = obj.getAsLong();
        return total;
    }

    public Float getMaxScore() {
        Float maxScore = null;
        JsonElement obj = getPath(PATH_TO_MAX_SCORE);
        if (obj != null && !obj.isJsonNull()) maxScore = obj.getAsFloat();
        return maxScore;
    }

    protected JsonElement getPath(String[] path) {
        JsonElement retval = null;
        if (jsonObject != null) {
            JsonElement obj = jsonObject;
            for (String component : path) {
                if (obj == null) break;
                obj = ((JsonObject) obj).get(component);
            }
            retval = obj;
        }
        return retval;
    }

    public MetricAggregation getAggregations() {
        final String rootAggrgationName = "aggs";
        if (jsonObject == null) return new RootAggregation(rootAggrgationName, new JsonObject());
        if (jsonObject.has("aggregations"))
            return new RootAggregation(rootAggrgationName, jsonObject.getAsJsonObject("aggregations"));
        if (jsonObject.has("aggs")) return new RootAggregation(rootAggrgationName, jsonObject.getAsJsonObject("aggs"));

        return new RootAggregation(rootAggrgationName, new JsonObject());
    }

    /**
     * Immutable class representing a search hit.
     *
     * @param <T> type of source
     * @param <K> type of explanation
     * <AUTHOR> keser
     */
    public class Hit<T, K> {

        public final T source;
        public final K explanation;
        public final Map<String, List<String>> highlight;
        public final List<String> sort;
        public final String index;
        public final String type;
        public final String id;
        public final Double score;
        public final String parent;
        public final String routing;
        public final List<String> matchedQueries;

        public Hit(Class<T> sourceType, JsonElement source, Class<K> explanationType, JsonElement explanation) {
            this(sourceType, source, explanationType, explanation, null, null);
        }

        public Hit(Class<T> sourceType, JsonElement source, Class<K> explanationType, JsonElement explanation,
                   Map<String, List<String>> highlight, List<String> sort) {
            this(sourceType, source, explanationType, explanation, highlight, sort, null, null, null, null, null, null, null);
        }

        public Hit(Class<T> sourceType, JsonElement source, Class<K> explanationType, JsonElement explanation,
                   Map<String, List<String>> highlight, List<String> sort, String index, String type, String id,
                   Double score, String parent, String routing, List<String> matchedQueries) {
            if (source == null) {
                this.source = null;
            } else {
                this.source = createSourceObject(source, sourceType);
            }
            if (explanation == null) {
                this.explanation = null;
            } else {
                this.explanation = createSourceObject(explanation, explanationType);
            }
            this.highlight = highlight;
            this.sort = sort;

            this.index = index;
            this.type = type;
            this.id = id;
            this.score = score;
            this.parent = parent;
            this.routing = routing;
            this.matchedQueries = matchedQueries;
        }

        public Hit(Class<T> sourceType, JsonElement source, Class<K> explanationType, JsonElement explanation,
                   Map<String, List<String>> highlight, List<String> sort, String index, String type, String id, Double score) {
            this(sourceType, source, explanationType, explanation, highlight, sort, index, type, id, score, null, null, null);
        }

        public Hit(T source) {
            this(source, null, null, null);
        }

        public Hit(T source, K explanation) {
            this(source, explanation, null, null);
        }

        public Hit(T source, K explanation, Map<String, List<String>> highlight, List<String> sort) {
            this(source, explanation, highlight, sort, null, null, null, null, null);
        }

        public Hit(T source, K explanation, Map<String, List<String>> highlight, List<String> sort, String index, String type, String id, Double score, List<String> matchedQueries) {
            this.source = source;
            this.explanation = explanation;
            this.highlight = highlight;
            this.sort = sort;
            this.index = index;
            this.type = type;
            this.id = id;
            this.score = score;
            this.parent = null;
            this.routing = null;
            this.matchedQueries = matchedQueries;
        }

        @Override
        public int hashCode() {
            return Objects.hash(
                    source,
                    explanation,
                    highlight,
                    sort,
                    index,
                    type,
                    id);
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == null) {
                return false;
            }
            if (obj == this) {
                return true;
            }
            if (obj.getClass() != getClass()) {
                return false;
            }

            Hit rhs = (Hit) obj;
            return Objects.equals(source, rhs.source)
                    && Objects.equals(explanation, rhs.explanation)
                    && Objects.equals(highlight, rhs.highlight)
                    && Objects.equals(sort, rhs.sort)
                    && Objects.equals(index, rhs.index)
                    && Objects.equals(type, rhs.type)
                    && Objects.equals(id, rhs.id);
        }
    }

}
