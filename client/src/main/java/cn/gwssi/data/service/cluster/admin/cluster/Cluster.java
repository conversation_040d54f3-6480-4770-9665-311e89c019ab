package cn.gwssi.data.service.cluster.admin.cluster;


import java.io.IOException;
import java.util.Map;

public interface Cluster {

    /**
     * 获取集群状态
     * @return
     * @throws Exception
     */
    String clusterState() throws Exception;

    /**
     * 获取集群状态
     * @param indices
     * @return
     * @throws Exception
     */
    String clusterState(String... indices) throws Exception;

    /**
     * 获取集群状态
     * @param builder
     * @return
     * @throws Exception
     */
    String clusterState(ClusterStateParamsBuilder builder) throws Exception;

    /**
     * 获得集群状态
     * @return
     * @throws Exception
     */
    String clusterStats()throws Exception;

    /**
     * 获得集群状态
     * @param builder
     * @return
     * @throws Exception
     */
    String clusterStats(ClusterStatsParamsBuilder builder)throws Exception;

    /**
     * 获取集群节点信息
     * @param builder
     * @return
     * @throws Exception
     */
    String nodesInfo(NodesInfoParamsBuilder builder) throws Exception;

    /**
     * 获取集群节点信息
     * @return
     * @throws Exception
     */
    String nodesInfo() throws Exception;

    /**
     * 获得指定索引的集群健康状态
     * @param index
     * @return
     */
    String clusterHealth(String index) throws IOException;

    /**
     * 获得集群健康状态
     * @param builder
     * @return
     * @throws IOException
     */
    String clusterHealth(ClusterHealthParamsBuilder builder) throws IOException;

    /**
     * 获得索集群的健康状态
     * @return
     */
    String clusterHealth() throws IOException;

    /**
     * 节点状态信息
     * @return
     * @throws IOException
     */
    String nodesStats() throws IOException;

    /**
     * 节点状态信息
     * @param builder
     * @return
     * @throws IOException
     */
    String nodesStats(NodesStatsParamBuilder builder) throws IOException;

    /**
     * 更新集群设置
     * @param persistents
     * @param transients
     * @return
     * @throws IOException
     */
    String updateSettings(Map<String, String> persistents, Map<String, String> transients) throws IOException;
    String updatePersistentSettings(String key, String value) throws IOException;
    String updateTransientSettings(String key, String value) throws IOException;

}
