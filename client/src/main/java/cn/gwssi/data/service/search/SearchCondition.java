package cn.gwssi.data.service.search;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.condition.filter.util.IPTypeUtil;
import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.util.FeatureClient;
import cn.gwssi.data.util.SearchUtil;
import cn.gwssi.data.util.Utility;
import cn.gwssi.meta.IPType;
import cn.gwssi.syntax.condition.Condition;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.QueryCondition;
import cn.gwssi.xparser.IPDaoFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class SearchCondition {

    private ISearchClientProxy httpClient;

    public SearchCondition(ISearchClientProxy httpClient) {
        this.httpClient = httpClient;
    }

    public Condition generateSearchCondition(QueryCondition condition) throws IPException {
        int windowSize = condition.isVectorSort() ? 0 : -1;
        return this.generateSearchCondition(condition.getQuery(), condition.getDefaultField(),
                condition.getFieldList(), condition.getSortFields(), condition.getHighLightCondition(), condition.getCollapseCondition(),
                condition.getScroll(), condition.getScrollSliceId(), condition.getScrollSliceMax(),
                condition.getGroups(), windowSize, condition.getFrom(), condition.getSize(), condition.getIndexName(), condition.isUnique(), condition.getOthers());
    }

    public Condition generateSearchCondition(String queryString, String defaultField,
                                             FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition,
                                             String scroll, int scrollSliceId, int scrollSliceMax,
                                             Map<String, List<String>> groups, int windowSize, int from, int size, String indexName, boolean isUnique, Map<String, Object> others) throws IPException {
        IPType ipType = IPDaoFactory.getInstance().getIPType(indexName);
        SearchUtil searchUtil = new SearchUtil(httpClient, indexName, isUnique);
        IPCondition statementCon = searchUtil.getStatementCondition(queryString, defaultField, indexName, groups, others, false);

        FieldList filterFields = handleFieldList(fieldList, ipType);
        SortFieldList sortFields = handleSortFieldList(sortFieldList, ipType);

        Condition condition = new Condition();
        condition.setFrom(from);
        condition.setSize(size);
        condition.setFieldList(filterFields);
        condition.setSortFields(sortFields);
        condition.setIpCondition(statementCon);
        condition.setHighLightCondition(highLightCondition);
        condition.setCollapseCondition(collapseCondition);
        condition.setScroll(scroll);
        condition.setScrollSliceId(scrollSliceId);
        condition.setScrollSliceMax(scrollSliceMax);

        // 向量检索，有排序条件时，查询所有记录，并返回排序字段的值，用排序字段的值手动排序和分页
        boolean hasVector = searchUtil.hasVector();
        condition.setHasVector(hasVector);
        if (hasVector) {

            // 分页查询，计算window_size，以 500 为一个幅度，取值 500 1000 1500 ... 5000
            if (windowSize > -1) {
                windowSize = (int) Math.ceil((from + size) / FeatureClient.getShardsCount() / FeatureClient.getRatio());
                windowSize = (windowSize + 1) * FeatureClient.getRatio();
                if (windowSize > 5000) {
                    windowSize = 5000;
                }
                condition.setPictureWindowSize(windowSize);
            } else {
                // 查询所有记录
                condition.setFrom(0);
                condition.setSize(IPConstants.VECTOR_MAX_ROW);
            }

            String field = null;
            if (null != sortFieldList && sortFieldList.size() > 0) {
                for (SortField sortField : sortFieldList) {
                    if (!"_score".equals(sortField.getFieldName())) {
                        field = sortField.getFieldName();
                        break;
                    }
                }
            }

            // 查询排序字段的值
            if (null != field) {
                filterFields.add(field);
            }
        }

        // distinct 查询，把去重字段的值查询出来，下一次查询要用到
        if (null != collapseCondition) {
            filterFields.add(collapseCondition.getField());
        }

        return condition;
    }

    private FieldList handleFieldList(FieldList fieldList, IPType ipType) throws IPException {
        FieldList result = new FieldList();
        for (String fieldName : Utility.emptyIfNull(fieldList)) {
            result.add(IPTypeUtil.getSourceColumnName(ipType, fieldName));
        }
        return result;
    }

    private SortFieldList handleSortFieldList(SortFieldList sortFieldList, IPType ipType) throws IPException {
        SortFieldList result = new SortFieldList();
        for (SortField sortField : Utility.emptyIfNull(sortFieldList)) {
            String fullColumnName = IPTypeUtil.getFullColumnName(ipType, sortField.getFieldName().toLowerCase());
            SortField temp = new SortField(fullColumnName.toLowerCase(), sortField.getOrder());
            if (StringUtils.isNotBlank(sortField.getCustomOrder())) {
                temp = new SortField(fullColumnName.toLowerCase(), sortField.getCustomOrder());
            }
            temp.setMissing(sortField.getMissing());
            result.add(temp);
        }
        return result;
    }
}
