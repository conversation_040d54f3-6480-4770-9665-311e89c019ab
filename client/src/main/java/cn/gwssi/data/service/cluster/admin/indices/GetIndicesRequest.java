package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.AbstractMultiTypeActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class GetIndicesRequest extends GenericResultAbstractAction {

    protected GetIndicesRequest(Builder builder) {
        super(builder);
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.ROOT + "/" + super.buildURI(elasticsearchVersion);
    }

    public static class Builder extends AbstractMultiTypeActionBuilder<GetIndicesRequest, Builder> {

        @Override
        public GetIndicesRequest build() {
            return new GetIndicesRequest(this);
        }
    }

}
