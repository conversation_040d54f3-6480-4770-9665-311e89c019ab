package cn.gwssi.data.service.action;


import cn.gwssi.data.service.client.ISearchResult;

/**
 * Represents an Action that <b>can <i>(but NOT necessarily does)</i></b> operate on a targeted single document on Elasticsearch.
 *
 * <AUTHOR> keser
 */
public interface DocumentTargetedAction<T extends ISearchResult> extends Action<T> {

    String getIndex();

    String getType();

    String getId();
}
