package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.client.ISearchResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Collection;

public class IndicesImpl implements Indices {

    private final static Logger logger = LoggerFactory.getLogger(IndicesImpl.class);

    private ISearchClientProxy httpClient;

    public IndicesImpl(ISearchClientProxy httpClient) {
        this.httpClient = httpClient;
    }

    @Override
    public String createIndex(String indexName, String source) throws IOException {
        CreateIndexRequest.Builder builder = new CreateIndexRequest.Builder(indexName).payload(source);

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String createIndex(String indexName, String settings, String mappings, String alias) throws IOException {
        CreateIndexRequest.Builder builder = new CreateIndexRequest.Builder(indexName);
        if (StringUtils.isNotEmpty(settings)) {
            builder.settings(settings);
        }
        if (StringUtils.isNotEmpty(mappings)) {
            builder.mappings(mappings);
        }
        if (StringUtils.isNotEmpty(alias)) {
            builder.aliases(alias);
        }

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String indexIndices(Collection<String> indexNames) throws IOException {
        GetIndicesRequest.Builder builder = new GetIndicesRequest.Builder();
        if (null != indexNames && indexNames.size() > 0) {
            builder.addIndices(indexNames);
        }

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String indexStats(Collection<String> indexNames, IndicesStatsRequest.Metric... metrics) throws IOException {
        IndicesStatsRequest.Builder builder = new IndicesStatsRequest.Builder().addMetrics(metrics);
        if (null != indexNames && indexNames.size() > 0) {
            builder.addIndices(indexNames);
        }

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public boolean indexExist(String indexName) throws IOException {
        IndicesExistsRequest.Builder builder = new IndicesExistsRequest.Builder(indexName);

        ISearchResult result = httpClient.execute(builder.build());
        JSONObject json = JSON.parseObject(result.getJsonString());
        return json.containsKey("exists") && json.getBoolean("exists");
    }

    @Override
    public String getIndexMapping(String indexName) throws IOException {
        GetMappingRequest.Builder builder = new GetMappingRequest.Builder();
        builder.addIndex(indexName);

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String updateIndexSettings(String indexName, String config) throws IOException {
        UpdateSettingsRequest.Builder builder = new UpdateSettingsRequest.Builder(config).addIndex(indexName);

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String updateIndexMappings(String indexName, String typename, String config) throws IOException {
        UpdateMappingRequest.Builder builder = new UpdateMappingRequest.Builder(indexName, typename, config);

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String deleteIndex(String indexName) throws IOException {
        DeleteIndexRequest.Builder builder = new DeleteIndexRequest.Builder(indexName);

        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

}
