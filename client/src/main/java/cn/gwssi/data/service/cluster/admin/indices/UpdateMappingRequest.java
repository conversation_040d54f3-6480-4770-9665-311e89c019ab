package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class UpdateMappingRequest extends GenericResultAbstractAction {

    protected UpdateMappingRequest(Builder builder) {
        super(builder);

        this.indexName = builder.index;
        this.typeName = builder.type;
        this.payload = builder.source;
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return super.buildURI(elasticsearchVersion) + HttpPath.INDEX_MAPPINGS;
    }

    @Override
    public String getRestMethodName() {
        return "PUT";
    }

    public static class Builder extends GenericResultAbstractAction.Builder<UpdateMappingRequest, Builder> {
        private String index;
        private String type;
        private Object source;

        public Builder(String index, String type, Object source) {
            this.index = index;
            this.type = type;
            this.source = source;
        }

        @Override
        public UpdateMappingRequest build() {
            return new UpdateMappingRequest(this);
        }
    }

}
