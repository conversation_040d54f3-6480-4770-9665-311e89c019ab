package cn.gwssi.data.service.cluster.admin.indices;

import java.io.IOException;
import java.util.Collection;

public interface Indices {

    /**
     * 创建索引
     * @param indexName
     * @param source
     * @return
     * @throws IOException
     */
    public String createIndex(String indexName, String source) throws IOException;

    /**
     * 创建索引
     * @param indexName
     * @param settings
     * @param mappings
     * @param alias
     * @return
     * @throws IOException
     */
    public String createIndex(String indexName, String settings, String mappings, String alias) throws IOException;

    /**
     * 获取索引信息，包含 settings、mappings、alias、warmers
     * @param indexNames
     * @return
     * @throws IOException
     */
    public String indexIndices(Collection<String> indexNames) throws IOException;

    /**
     * 索引信息统计
     * @param indexNames
     * @param metrics
     * @return
     * @throws IOException
     */
    public String indexStats(Collection<String> indexNames, IndicesStatsRequest.Metric... metrics) throws IOException;

    /**
     * 索引是否存在
     * @param indexName
     * @return
     * @throws IOException
     */
    public boolean indexExist(String indexName) throws IOException;

    /**
     * 获取索引mappings
     * @param indexName
     * @return
     * @throws IOException
     */
    public String getIndexMapping(String indexName) throws IOException;

    /**
     * 修改索引settings
     * @param indexName 索引名称
     * @param config config
     * @return string
     * @throws IOException IOException
     */
    public String updateIndexSettings(String indexName, String config) throws IOException;

    /**
     * 修改索引mapping
     * @param indexName 索引名称
     * @param typename 类型名称
     * @param config config
     * @return string
     * @throws IOException IOException
     */
    public String updateIndexMappings(String indexName, String typename, String config) throws IOException;

    /**
     * 删除索引
     * @param indexName 索引名称
     * @return string
     * @throws IOException IOException
     */
    public String deleteIndex(String indexName) throws IOException;

}
