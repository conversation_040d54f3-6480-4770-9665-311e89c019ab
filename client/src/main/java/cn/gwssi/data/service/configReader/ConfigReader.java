package cn.gwssi.data.service.configReader;

import cn.gwssi.data.service.search.Search;
import cn.gwssi.xparser.IPConfigReader;
import cn.gwssi.xparser.IPDaoFactory;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 17:00
 */
public class ConfigReader {

    private static ConfigReader _instance = null;

    private static final String INDEX_NAME = ".config";
    private static final String INDEX_TYPE = "config";
    private static final int PAGE_SIZE = 1000;

    public static ConfigReader getInstance() {
        if (_instance == null) {
            synchronized(IPDaoFactory.class){
                if(_instance == null){
                    _instance = new ConfigReader();
                }
            }
        }

        return _instance;
    }

    public void readConfig(String filePath, boolean isEnv) throws Exception {
        new IPConfigReader(filePath, isEnv);
    }

    public void readConfig(Search search) throws Exception {
        String query = "{\"query\": {\"match_all\": {}},\"_source\":{\"include\":[\"indexName\",\"typeName\",\"confXml\"]}}";
        String result = search.searchByJson(INDEX_NAME, INDEX_TYPE, query, null ,null, null, 0, PAGE_SIZE);

        JSONArray hits = (JSONArray)JSONPath.read(result, "$.hits.hits");
        if (null != hits && hits.size() > 0) {
            for (int i = 0; i < hits.size(); i++) {

                JSONObject hit = hits.getJSONObject(i);
                if (null != hit && hit.containsKey("_source")) {
                    JSONObject source = hit.getJSONObject("_source");
                    String typeName = source.getString("typeName");
                    String indexName = source.getString("indexName");
                    String xml = source.getString("confXml");
                    new IPConfigReader(indexName, typeName, xml);
                }
            }
        }
    }
}
