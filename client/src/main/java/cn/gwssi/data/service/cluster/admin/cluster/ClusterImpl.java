package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.client.ISearchResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ClusterImpl implements Cluster {

    private final static Logger logger = LoggerFactory.getLogger(ClusterImpl.class);

    private ISearchClientProxy httpClient;

    public ClusterImpl(ISearchClientProxy httpClient){
        this.httpClient = httpClient;
    }

    @Override
    public String clusterState() throws Exception {
        ClusterStateRequest request = new ClusterStateRequest.Builder().build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterState(String... indices) throws Exception {
        Set<String> set = new HashSet<>();
        if (null ==indices || indices.length ==0){
            set.addAll(Arrays.asList(indices));
        }
        ClusterStateRequest request = new ClusterStateRequest.Builder().indices(set).build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterState(ClusterStateParamsBuilder builder) throws Exception {
        ClusterStateRequest.Builder clusterStateBuilder = new ClusterStateRequest.Builder();
        Set<String> indices = builder.getIndices();
        Set<String> metrics = builder.getMetrics();
        if (null != indices && indices.size() > 0){
            clusterStateBuilder.indices(indices);
        }
        if (metrics != null && metrics.size() > 0){
            clusterStateBuilder.addMetric(metrics);
        }
        ISearchResult result = httpClient.execute(clusterStateBuilder.build());
        return result.getJsonString();
    }

    @Override
    public String clusterStats() throws Exception {
        ClusterStatsRequest request = new ClusterStatsRequest.Builder().build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterStats(ClusterStatsParamsBuilder builder) throws Exception {
        ClusterStatsRequest.Builder requestBuilder = new ClusterStatsRequest.Builder();
        requestBuilder.addNodeId(builder.getNodeIds());
        ISearchResult result = httpClient.execute(requestBuilder.build());
        return result.getJsonString();
    }

    @Override
    public String nodesInfo(NodesInfoParamsBuilder builder) throws Exception {
        NodesInfoRequest.Builder requestBuilder = new NodesInfoRequest.Builder();
        requestBuilder.addMetrics(builder.getMetricMap());
        requestBuilder.addNodeIds(builder.getNodeIds());
        ISearchResult result= httpClient.execute(requestBuilder.build());
        return result.getJsonString();
    }

    @Override
    public String nodesInfo() throws Exception {
        NodesInfoRequest request = new NodesInfoRequest.Builder().build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterHealth(String index) throws IOException {
        if (null != index && !"".equals(index)){
            return clusterHealth();
        }
        HealthRequest request = new HealthRequest.Builder().addIndex(index).build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterHealth(ClusterHealthParamsBuilder builder) throws IOException {
        HealthRequest request = new HealthRequest.Builder().level(builder.getLevel())
                .waitForStatus(builder.getWait_for_status())
                .local(builder.getLocal())
                .timeout(builder.getTimeout_in_seconds())
                .build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String clusterHealth() throws IOException {
        HealthRequest request = new HealthRequest.Builder().build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String nodesStats() throws IOException {
        NodesStatsRequest request = new NodesStatsRequest.Builder().build();
        ISearchResult result = httpClient.execute(request);
        return result.getJsonString();
    }

    @Override
    public String nodesStats(NodesStatsParamBuilder builder) throws IOException {
        NodesStatsRequest.Builder requestBuilder = new NodesStatsRequest.Builder();
        requestBuilder.addNodeIds(builder.getNodeIds());
        requestBuilder.addMetricMap(builder.getMetricMap());
        ISearchResult result = httpClient.execute(requestBuilder.build());
        return result.getJsonString();
    }

    @Override
    public String updateSettings(Map<String, String> persistents, Map<String, String> transients) throws IOException {
        UpdateSettingsRequest.Builder builder = new UpdateSettingsRequest.Builder(null)
                .addPersistents(persistents).addTransients(transients);
        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String updatePersistentSettings(String key, String value) throws IOException {
        UpdateSettingsRequest.Builder builder = new UpdateSettingsRequest.Builder(null).addPersistent(key, value);
        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }

    @Override
    public String updateTransientSettings(String key, String value) throws IOException {
        UpdateSettingsRequest.Builder builder = new UpdateSettingsRequest.Builder(null).addTransient(key, value);
        ISearchResult result = httpClient.execute(builder.build());
        return result.getJsonString();
    }
}
