package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

import java.util.HashSet;
import java.util.Set;

public class ClusterStatsRequest extends GenericResultAbstractAction {

    private Set<String> nodeIdSet;

    public ClusterStatsRequest(Builder builder){
        super(builder);
        nodeIdSet = builder.nodeIdSet;
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.CLUSTER_STATS  + appendRestPath();
    }

    private String appendRestPath(){
        String restUrl = "";
        StringBuffer stringBuffer = new StringBuffer();
        if (nodeIdSet != null && nodeIdSet.size() > 0){
            int i = 0;
            for (String nodeId: nodeIdSet){
                i++;
                if (i< nodeIdSet.size()){
                    stringBuffer.append(nodeId).append(",");
                }else {
                    stringBuffer.append(nodeId);
                }
            }
            restUrl = stringBuffer.toString();
        }
        if (!restUrl.equals("")){
            restUrl = "/nodes/"+ restUrl;
        }
        return restUrl;
    }

    public static class Builder extends AbstractAction.Builder<ClusterStatsRequest, Builder> {

        private Set<String> nodeIdSet = new HashSet<>();

        public void addNodeId(Set<String> nodeIdSet){
            this.nodeIdSet.addAll(nodeIdSet);
        }

        @Override
        public ClusterStatsRequest build() {
            return new ClusterStatsRequest(this);
        }
    }
}
