package cn.gwssi.data.service.action;

import cn.gwssi.data.service.core.DocumentResult;
import com.google.gson.Gson;
import org.apache.http.NameValuePair;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class SingleResultAbstractDocumentTargetedAction extends AbstractDocumentTargetedAction<DocumentResult> {
    public SingleResultAbstractDocumentTargetedAction(Builder builder) {
        super(builder);
    }

    @Override
    public DocumentResult createNewElasticSearchResult(String responseBody, int statusCode, String reasonPhrase, Gson gson) {
        return createNewElasticSearchResult(new DocumentResult(gson), responseBody, statusCode, reasonPhrase, gson);
    }
//
//    @Override
//    public String getSerializerParam() {
//        return super.getSerializerParam();
//    }

//    @Override
//    public List<NameValuePair> getParas() {
//        return null;
//    }
}
