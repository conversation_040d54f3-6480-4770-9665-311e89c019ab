package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.AbstractMultiTypeActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class GetMappingRequest extends GenericResultAbstractAction {

    protected GetMappingRequest(Builder builder) {
        super(builder);
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return super.buildURI(elasticsearchVersion) + HttpPath.INDEX_MAPPINGS;
    }

    public static class Builder extends AbstractMultiTypeActionBuilder<GetMappingRequest, Builder> {

        @Override
        public GetMappingRequest build() {
            return new GetMappingRequest(this);
        }
    }

}
