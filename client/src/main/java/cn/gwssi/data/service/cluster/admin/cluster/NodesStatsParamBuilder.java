package cn.gwssi.data.service.cluster.admin.cluster;

import java.util.*;

public class NodesStatsParamBuilder {

    private Map<String, Metric> metricMap;

    private Set<String> nodeIds;

    public NodesStatsParamBuilder(Builder builder){
        this.metricMap = builder.metricMap;
        this.nodeIds = builder.nodeIds;
    }

    public Map<String, Metric> getMetricMap(){
        return this.metricMap;
    }

    public Set<String> getNodeIds(){
        return this.nodeIds;
    }

    public enum Metric{
        INDICES("indices"),
        FS("fs"),
        HTTP("http"),
        JVM("jvm"),
        OS("os"),
        PROCESS("process"),
        THREAD_POOL("thread_pool"),
        TRANSPORT("transport"),
        BREAKER("breaker");

        private String key;

        Metric(String key){
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    }
    public static class Builder{

        private Map<String, Metric> metricMap = new LinkedHashMap<>();

        private Set<String> nodeIds = new LinkedHashSet<>();

        public void setNodeIds(String... nodeId){
            if (null != nodeId && nodeId.length > 0)
                this.nodeIds.addAll(new HashSet<>(Arrays.asList(nodeId)));
        }

        /**
         * Indices stats about size, document count, indexing and deletion times, search times, field cache size , merges and flushes
         */
        public Builder withIndices(boolean hasIndices) {
            metricMap.put(Metric.INDICES.getKey(),Metric.INDICES);
            return this;
        }

        /**
         * File system information, data path, free disk space, read/write stats
         */
        public Builder withFs(boolean hasFs) {
            metricMap.put(Metric.FS.getKey(),Metric.FS);
            return this;
        }

        /**
         * HTTP connection information
         */
        public Builder withHttp(boolean hasHttp) {
            metricMap.put(Metric.HTTP.getKey(),Metric.HTTP);
            return this;
        }

        /**
         * JVM stats, memory pool information, garbage collection, buffer pools
         */
        public Builder withJvm(boolean hasJvm) {
            metricMap.put(Metric.JVM.getKey(),Metric.JVM);
            return this;
        }


        /**
         * Operating system stats, load average, cpu, mem, swap
         */
        public Builder withOs(boolean hasOs) {
            metricMap.put(Metric.OS.getKey(),Metric.OS);
            return this;
        }

        /**
         * Process statistics, memory consumption, cpu usage, open file descriptors
         */
        public Builder withProcess(boolean hasProcess) {
            metricMap.put(Metric.PROCESS.getKey(),Metric.PROCESS);
            return this;
        }

        /**
         * Statistics about each thread pool, including current size, queue and rejected tasks
         */
        public Builder withThreadPool(boolean hasThreadPoll) {
            metricMap.put(Metric.THREAD_POOL.getKey(),Metric.THREAD_POOL);
            return this;
        }

        /**
         * Transport statistics about sent and received bytes in cluster communication
         */
        public Builder withTransport(boolean hasTrasport) {
            metricMap.put(Metric.TRANSPORT.getKey(),Metric.TRANSPORT);
            return this;
        }

        public NodesStatsParamBuilder build(){
            return new NodesStatsParamBuilder(this);
        }

    }
}
