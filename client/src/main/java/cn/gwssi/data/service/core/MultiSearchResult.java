package cn.gwssi.data.service.core;

import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> keser
 */
public class MultiSearchResult extends ISearchResult {
    private static final String RESPONSES_KEY = "responses";
    private static final String ERROR_KEY = "error";

    public MultiSearchResult(MultiSearchResult source) {
        super(source);
    }

    public MultiSearchResult(Gson gson) {
        super(gson);
    }

    public List<MultiSearchResponse> getResponses() {
        List<MultiSearchResponse> multiSearchResponses = new ArrayList<MultiSearchResponse>();

        if(jsonObject != null && jsonObject.has(RESPONSES_KEY)) {
            JsonArray responsesArray = jsonObject.getAsJsonArray(RESPONSES_KEY);
            for(JsonElement responseElement : responsesArray) {
                multiSearchResponses.add(new MultiSearchResponse(responseElement.getAsJsonObject()));
            }
        }

        return multiSearchResponses;
    }

    public class MultiSearchResponse {

        public final boolean isError;
        public final String errorMessage;
        public final JsonElement error;
        public final SearchResult searchResult;

        public MultiSearchResponse(JsonObject jsonObject) {
            final JsonElement error = jsonObject.get(ERROR_KEY);
            if(error != null) {
                this.isError = true;
                this.error = error;
                if (error.isJsonPrimitive()) {
                    this.errorMessage = error.getAsString();
                } else if (error.isJsonObject()){
                    this.errorMessage = error.getAsJsonObject().get("reason").getAsString();
                } else {
                    this.errorMessage = error.toString();
                }
                this.searchResult = null;
            } else {
                this.isError = false;
                this.errorMessage = null;
                this.error = JsonNull.INSTANCE;

                this.searchResult = new SearchResult(gson);
                this.searchResult.setSucceeded(true);
                this.searchResult.setResponseCode(responseCode);
                this.searchResult.setJsonObject(jsonObject);
                this.searchResult.setJsonString(jsonObject.toString());
                this.searchResult.setPathToResult("hits/hits/_source");
            }
        }
    }
}
