package cn.gwssi.data.service.core;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.AbstractMultiTypeActionBuilder;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.Gson;

import java.io.UnsupportedEncodingException;

public class Analysis extends AbstractAction<ISearchResult> {

    private String query;

    public Analysis(String query) {
        this.query = query;
        super.addUrlParameter();
    }

    public Analysis(Builder builder) {
        super(builder);
        super.addUrlParameter();
        this.query = builder.query;
    }

    @Override
    public String getRestMethodName() {
        return "POST";
    }

    @Override
    public String getRestPath() {
        String url = HttpPath.ANALYSIS ;
        try {
            url = HttpPath.ANALYSIS + super.buildQueryString();
        } catch (UnsupportedEncodingException e) {
            log.error("Error occurred while adding parameters to uri.", e);
        }
        return url;
    }

    @Override
    public String getSerializerParam(Gson gson) {
        return query;
    }
    @Override
    public ISearchResult createNewElasticSearchResult(String responseBody, int statusCode, String reasonPhrase, Gson gson) {
        return createNewElasticSearchResult(new SearchResult(gson), responseBody, statusCode, reasonPhrase, gson);
    }

    public static class Builder extends AbstractMultiTypeActionBuilder<Analysis, Analysis.Builder> {
        protected String query;

        public Builder(String query) {
            this.query = query;
        }

        @Override
        public Analysis build() {
            return new Analysis(this);
        }
    }

}
