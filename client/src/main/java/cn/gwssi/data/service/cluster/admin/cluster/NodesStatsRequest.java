package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.cluster.UrlUtils;

import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

public class NodesStatsRequest extends GenericResultAbstractAction {

    private final static String STATS = "/stats";

    private Set<String> nodeIds;

    public NodesStatsRequest(Builder builder){
        super(builder);
        this.nodeIds = builder.nodeIds;
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        String nodesStatsUrl = HttpPath.NODES_STATS + UrlUtils.appendRestPath(nodeIds)+STATS;
        return nodesStatsUrl + super.buildURI(elasticsearchVersion);
    }

    public static class Builder extends AbstractAction.Builder<NodesStatsRequest, Builder> {

        private Set<String> nodeIds = new LinkedHashSet<>();

        public void addNodeIds(Set<String> nodeIds){
            this.nodeIds = nodeIds;
        }

        public void addMetricMap(Map<String, NodesStatsParamBuilder.Metric> metricMap){
            addMetrics(metricMap);
        }

        private void addMetrics(Map<String, NodesStatsParamBuilder.Metric> metrics){
            for (Map.Entry<String, NodesStatsParamBuilder.Metric> entry : metrics.entrySet()){
                String key = entry.getKey();
                switch (key){
                    case "indices" :
                        withIndices(entry.getValue());
                        break;
                    case "os":
                        withOs(entry.getValue());
                        break;
                    case "process":
                        withProcess(entry.getValue());
                        break;
                    case "jvm":
                        withJvm(entry.getValue());
                        break;
                    case "thread_pool":
                        withThreadPool(entry.getValue());
                        break;
                    case "transport":
                        withTransport(entry.getValue());
                        break;
                    case "http":
                        withHttp(entry.getValue());
                        break;
                    case "fs":
                        withFs(entry.getValue());
                        break;
                }
            }

        }

        /**
         * Indices stats about size, document count, indexing and deletion times, search times, field cache size , merges and flushes
         */
        public Builder withIndices(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.INDICES.getKey());
        }

        /**
         * File system information, data path, free disk space, read/write stats
         */
        public Builder withFs(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.FS.getKey());
        }

        /**
         * HTTP connection information
         */
        public Builder withHttp(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.HTTP.getKey());
        }

        /**
         * JVM stats, memory pool information, garbage collection, buffer pools
         */
        public Builder withJvm(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.JVM.getKey());
        }

        /**
         * Operating system stats, load average, cpu, mem, swap
         */
        public Builder withOs(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.OS.getKey());
        }

        /**
         * Process statistics, memory consumption, cpu usage, open file descriptors
         */
        public Builder withProcess(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.PROCESS.getKey());
        }

        /**
         * Statistics about each thread pool, including current size, queue and rejected tasks
         */
        public Builder withThreadPool(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.THREAD_POOL.getKey());
        }

        /**
         * Transport statistics about sent and received bytes in cluster communication
         */
        public Builder withTransport(NodesStatsParamBuilder.Metric metric) {
            return addCleanApiParameter(metric.TRANSPORT.getKey());
        }

        @Override
        public NodesStatsRequest build() {
            return new NodesStatsRequest(this);
        }
    }
}
