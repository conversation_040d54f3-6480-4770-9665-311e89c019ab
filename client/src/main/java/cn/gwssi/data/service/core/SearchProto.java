package cn.gwssi.data.service.core;

import cn.gwssi.data.service.action.AbstractMultiTypeActionBuilder;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.core.search.sort.Sort;
import cn.gwssi.data.service.params.Parameters;
import cn.gwssi.data.service.params.SearchType;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class SearchProto extends Search {

    private String query;
    private List<Sort> sortList;
    private int from;
    private int size;
    private final String templateSuffix;

    protected SearchProto(Builder builder) {
        this(builder, "");
    }

    protected SearchProto(TemplateBuilder templatedBuilder) {
        this(templatedBuilder, "/template");
    }

    private SearchProto(Builder builder, String templateSuffix) {
        super(builder);
        super.addUrlParameter();
        this.query = builder.query;
        this.sortList = builder.sortList;
        this.includePatternList = builder.includePatternList;
        this.excludePatternList = builder.excludePatternList;
        this.from = builder.from;
        this.size = builder.size;
        this.templateSuffix = templateSuffix;
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return super.superBuildURI(elasticsearchVersion) + HttpPath.ROOT + templateSuffix;
    }

    @Override
    public String getData(Gson gson) {
        String data;
        if (sortList.isEmpty() && includePatternList.isEmpty() && excludePatternList.isEmpty() && from < 0 && size < 0) {
            data = query;
        } else {
            JsonObject queryObject = gson.fromJson(query, JsonObject.class);

            if (queryObject == null) {
                queryObject = new JsonObject();
            }

            if (!sortList.isEmpty()) {
                JsonArray sortArray = normalizeSortClause(queryObject);

                for (Sort sort : sortList) {
                    sortArray.add(sort.toJsonObject());
                }
            }

            if (!includePatternList.isEmpty() || !excludePatternList.isEmpty()) {
                JsonObject sourceObject = normalizeSourceClause(queryObject);

                addPatternListToSource(sourceObject, "includes", includePatternList);
                addPatternListToSource(sourceObject, "excludes", excludePatternList);
            }

            if (from >= 0) {
                queryObject.addProperty("from", from);
            }

            if (size >= 0) {
                queryObject.addProperty("size", size);
            }

            data = gson.toJson(queryObject);
        }

        return data;
    }

    public static class Builder extends AbstractMultiTypeActionBuilder<SearchProto, SearchProto.Builder> {
        protected String query;
        protected int from;
        protected int size;
        protected List<Sort> sortList = new LinkedList<Sort>();
        protected List<String> includePatternList = new ArrayList<String>();
        protected List<String> excludePatternList = new ArrayList<String>();

        public Builder(String query) {
            this.query = query;
        }

        public Builder setSearchType(SearchType searchType) {
            return setParameter(Parameters.SEARCH_TYPE, searchType);
        }

        public Builder enableTrackScores() {
            this.setParameter(Parameters.TRACK_SCORES, true);
            return this;
        }

        public Builder addSort(Sort sort) {
            sortList.add(sort);
            return this;
        }

        public Builder addSourceExcludePattern(String excludePattern) {
            excludePatternList.add(excludePattern);
            return this;
        }

        public Builder addSourceIncludePattern(String includePattern) {
            includePatternList.add(includePattern);
            return this;
        }

        public Builder addSort(Collection<Sort> sorts) {
            sortList.addAll(sorts);
            return this;
        }

        public Builder setFrom(int from) {
            this.from = from;
            return this;
        }

        public Builder setSize(int size) {
            this.size = size;
            return this;
        }

        @Override
        public SearchProto build() {
            return new SearchProto(this);
        }
    }

    public static class TemplateBuilder extends SearchProto.Builder {
        public TemplateBuilder(String templatedQuery) {
            super(templatedQuery);
        }

        @Override
        public SearchProto build() {
            return new SearchProto(this);
        }
    }

}
