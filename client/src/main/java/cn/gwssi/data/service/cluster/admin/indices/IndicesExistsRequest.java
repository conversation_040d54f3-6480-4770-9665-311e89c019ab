package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.AbstractMultiIndexActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

import java.util.Collection;

/**
 * <AUTHOR> Sonmez
 */
public class IndicesExistsRequest extends GenericResultAbstractAction {

    protected IndicesExistsRequest(Builder builder) {
        super(builder);
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return super.buildURI(elasticsearchVersion) + HttpPath.INDEX_EXISTS;
    }

    public static class Builder extends AbstractMultiIndexActionBuilder<IndicesExistsRequest, Builder> {

        public Builder(String index){
            addIndex(index);
        }

        public Builder(Collection<? extends String> indices){
            addIndices(indices);
        }

        @Override
        public IndicesExistsRequest build() {
            return new IndicesExistsRequest(this);
        }
    }

}
