package cn.gwssi.data.service.search;

import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.common.common.pojo.IPSearchResult;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.syntax.condition.QueryCondition;
import cn.gwssi.syntax.condition.parser.ParserError;
import com.google.common.collect.Multimap;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface Search {

    /*------------------------------------- single Document search --------------------------------------------*/
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Class<T> gsonClazz, int from, int size) throws IPException;

    /*-------------------------------------multiple Indices search--------------------------------------------*/
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Class<T> gsonClazz, int from, int size) throws IPException;;

    /*-------------------------------------special search--------------------------------------------*/

    public <T extends GsonObject> IPSearchResult<T> searchVector(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchVector(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;;

    public <T extends GsonObject> IPSearchResult<T> searchWithHL(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchWithHL(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchDistinct(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, CollapseCondition collapseCondition, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchDistinct(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, CollapseCondition collapseCondition, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> Map<String, List<T>> searchDistinctChildren(QueryCondition<T> queryCondition, Collection<String> values, Map<String, Object> others) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchWithScroll(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, String scroll, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchWithScroll(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, String scroll, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchScroll(String scrollId, String scroll, Class<T> gsonClazz) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchGroup(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> searchGroup(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException;

    public Map<String, Collection<Integer>> searchImage(String indexName, String typeName, Set<String> ids, Set<String> imagePaths, int size) throws IPException;

    public String searchByJson(String indexName, String typeName, String query, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, int from, int size) throws IPException;

    public <T extends GsonObject> IPSearchResult<T> search(QueryCondition<T> condition) throws IPException;

    /*-------------------------------------count--------------------------------------------*/

    public Long count(String indexName, String typeName, String queryString) throws IPException;

    public Long count(Collection<String> indexNames, Collection<String> typeNames, String queryString) throws IPException;

    /*-------------------------------------isearch analysis--------------------------------------------*/

    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Map<String, List<String>> groups, Map<String, Object> others, int from, int size) throws IPException;

    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Map<String, Object> others, int from, int size) throws IPException;

    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, int from, int size) throws IPException;

    public String analysis(QueryCondition condition) throws IPException;

    public ParserError checkExpress(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others) throws IPException;

    public Set<String> extractValues(String query) throws IPException;

    public Multimap<String, String> extractValues(String query, String defaultField) throws IPException;

    public Multimap<String, String> extractValues(String query, String defaultField, Set<String> includes, Set<String> excludes) throws IPException;

}
