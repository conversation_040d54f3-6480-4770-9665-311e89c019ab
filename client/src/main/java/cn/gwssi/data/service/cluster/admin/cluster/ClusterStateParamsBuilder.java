package cn.gwssi.data.service.cluster.admin.cluster;

import java.util.LinkedHashSet;
import java.util.Set;

public class ClusterStateParamsBuilder {

    private Set<String> metrics;

    private Set<String> indices;

    public ClusterStateParamsBuilder(Builder builder){
        metrics = builder.metrics;
        indices = builder.indices;
    }

    public Set<String> getMetrics(){
        return metrics;
    }

    public Set<String> getIndices(){
        return indices;
    }

    public enum Status {
        RED("red"), YELLOW("yellow"), GREEN("green");

        private final String key;

        Status(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    }

    public static enum Metrics{
        VERSION("version"),

        MASTER_NODE("master_node"),

        NODES("nodes"),

        ROUTING_TABLE("routing_table"),

        METADATA("metadata"),

        BLOCKS("blocks");

        private final String key;

        Metrics(String key){
            this.key = key;
        }

        public String getKey(){
            return key;
        }
    }

    public static class Builder{
        private Set<String> metrics = new LinkedHashSet<>();

        private Set<String> indices = new LinkedHashSet<>();


        public Builder withVersion(boolean version) {
            if (version)
                metrics.add(Metrics.VERSION.getKey());
            return this;
        }

        /**
         * Shows the elected master_node part of the response.
         */
        public Builder withMasterNode(boolean masterNode) {
            if (masterNode)
                metrics.add(Metrics.MASTER_NODE.getKey());
            return this;
        }

        /**
         * Shows the nodes part of the response
         */
        public Builder withNodes(boolean nodes) {
            if (nodes)
                metrics.add(Metrics.NODES.getKey());
            return this;
        }

        /**
         * Shows the routing_table part of the response.
         */
        public Builder withRoutingTable(boolean routingTable) {
            if (routingTable)
                metrics.add(Metrics.ROUTING_TABLE.getKey());
            return this;
        }

        /**
         * Shows the metadata part of the response.
         */
        public Builder withMetadata(boolean metadata) {
            if (metadata)
                metrics.add(Metrics.METADATA.getKey());
            return this;
        }

        /**
         * Shows the blocks part of the response
         */
        public Builder withBlocks(boolean blocks) {
            if (blocks)
                metrics.add(Metrics.BLOCKS.getKey());
            return this;
        }

        public Builder addIndices(String... indices){
            if (null != indices && indices.length > 0){
                for (String index : indices){
                    this.indices.add(index);
                }
            }
            return this;
        }

        public ClusterStateParamsBuilder build(){
            return new ClusterStateParamsBuilder(this);
        }

    }


}
