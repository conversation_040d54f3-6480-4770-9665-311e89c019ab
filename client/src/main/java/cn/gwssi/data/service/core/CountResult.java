package cn.gwssi.data.service.core;

import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.Gson;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CountResult extends ISearchResult {

    public CountResult(CountResult countResult) {
        super(countResult);
    }

    public CountResult(Gson gson) {
        super(gson);
    }

    @Override
    @Deprecated
    public <T> T getSourceAsObject(Class<T> clazz) {
        return super.getSourceAsObject(clazz);
    }

    @Override
    @Deprecated
    public <T> List<T> getSourceAsObjectList(Class<T> type) {
        return super.getSourceAsObjectList(type);
    }

    public Long getCount() {
        Long count = null;

        if (isSucceeded) {
            count = getSourceAsObject(Long.class);
        }

        return count;
    }

}
