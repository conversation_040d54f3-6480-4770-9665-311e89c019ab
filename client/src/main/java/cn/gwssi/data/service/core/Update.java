package cn.gwssi.data.service.core;

import cn.gwssi.data.service.action.BulkableAction;
import cn.gwssi.data.service.action.SingleResultAbstractDocumentTargetedAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.params.Parameters;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class Update extends SingleResultAbstractDocumentTargetedAction implements BulkableAction<DocumentResult> {

    protected Update(Builder builder) {
        super(builder);
        this.payload = builder.payload;
    }

    @Override
    public String getBulkMethodName() {
        return "update";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.UPDATE + "/" + super.buildURI(elasticsearchVersion);
    }

    @Override
    public String getRestMethodName() {
        return "POST";
    }

    @Override
    public String getPathToResult() {
        return "ok";
    }

    public static class Builder extends SingleResultAbstractDocumentTargetedAction.Builder<Update, Builder> {
        private final Object payload;

        public Builder(Object payload) {
            this.payload = payload;
        }

        public Update build() {
            return new Update(this);
        }
    }

    public static class VersionBuilder extends Builder {
        public VersionBuilder(Object payload, Long version) {
            super(payload);
            this.setParameter(Parameters.VERSION, version);
        }
    }
}
