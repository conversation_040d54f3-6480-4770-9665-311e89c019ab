package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class DeleteIndexRequest extends GenericResultAbstractAction {

    protected DeleteIndexRequest(Builder builder) {
        super(builder);
        indexName = builder.index;
        typeName = builder.type;
    }

    @Override
    public String getRestMethodName() {
        return "DELETE";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.ROOT + "/" + super.buildURI(elasticsearchVersion);
    }

    public static class Builder extends GenericResultAbstractAction.Builder<DeleteIndexRequest, Builder> {
        private String index;
        private String type;

        public Builder(String index) {
            this.index = index;
        }

        public Builder type(String type) {
            this.type = type;
            return this;
        }

        @Override
        public DeleteIndexRequest build() {
            return new DeleteIndexRequest(this);
        }
    }

}
