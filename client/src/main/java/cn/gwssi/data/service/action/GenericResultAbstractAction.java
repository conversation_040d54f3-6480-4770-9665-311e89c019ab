package cn.gwssi.data.service.action;

import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.Gson;

/**
 * <AUTHOR> keser
 */
public abstract class GenericResultAbstractAction extends AbstractAction<ISearchResult> {

    public GenericResultAbstractAction() {
    }

    public GenericResultAbstractAction(Builder builder) {
        super(builder);
    }

    @Override
    public ISearchResult createNewElasticSearchResult(String responseBody, int statusCode, String reasonPhrase, Gson gson) {
        return createNewElasticSearchResult(new ISearchResult(gson), responseBody, statusCode, reasonPhrase, gson);
    }
}
