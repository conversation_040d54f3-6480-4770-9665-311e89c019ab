package cn.gwssi.data.service.cluster.admin.cluster;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

public class ClusterStatsParamsBuilder {

    @Getter
    private Set<String> nodeIds;

    public ClusterStatsParamsBuilder(Builder builder){
        this.nodeIds = builder.nodeIds;
    }

    public static class Builder{

        private Set<String> nodeIds = new LinkedHashSet<>();

        public Builder nodeIds(String... nodeId){
            if (null != nodeId && nodeId.length > 0){
                nodeIds.addAll(new HashSet<>(Arrays.asList(nodeId)));
            }
            return this;
        }

        public ClusterStatsParamsBuilder build(){
            return new ClusterStatsParamsBuilder(this);
        }
    }
}
