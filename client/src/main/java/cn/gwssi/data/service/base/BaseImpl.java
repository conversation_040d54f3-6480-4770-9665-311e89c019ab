package cn.gwssi.data.service.base;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.client.ISearchClientProxy;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 17:03
 */
public class BaseImpl implements Base {

    private ISearchClientProxy httpClient;

    public BaseImpl(ISearchClientProxy httpClient) {
        this.httpClient = httpClient;
    }

    @Override
    public String sendRequest(String method, String path, String data, Map<String, Object> params) throws IOException {
        BaseRequest.Builder builder = new BaseRequest.Builder().setMethod(method).setPath(path);

        if (null != params && params.size() > 0) {
            builder.setParameter(params);
        } else if (null != data) {
            builder.setData(data);
        }

        return httpClient.execute(builder.build()).getJsonString();
    }
}
