package cn.gwssi.data.service.document;

import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.common.common.pojo.UpdateResult;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.Args;
import cn.gwssi.common.util.StringUtil;
import cn.gwssi.data.common.Constants;
import cn.gwssi.data.common.ErrorCodes;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.client.ISearchResult;
import cn.gwssi.data.service.core.*;
import cn.gwssi.data.util.Utility;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

public class DocumentImpl implements Document {
    private final static Logger logger = LoggerFactory.getLogger(DocumentImpl.class);
    private ISearchClientProxy humbleClient;
    private final static int BATCH_SIZE_MAX = 500;
    private final static String BATCH_FAILED_ITEMS = "batchFailedItems";
    private final static String BATCH_ALL_ITEM_IDS = "batchAllIds";

    public DocumentImpl(ISearchClientProxy humbleClient) {
        this.humbleClient = humbleClient;
    }

    @Override
    public String index(String indexName, String typeName, String jsonString) throws IPException {
        return this.index(indexName, typeName, null, jsonString);
    }

    @Override
    public String index(String indexName, String typeName, String id, String jsonString) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);

        Index.Builder builder = new Index.Builder(jsonString).index(indexName).type(typeName);
        if (!StringUtil.isNullOrEmpty(id)) {
            builder.id(id);
        }

        Index indexAction = builder.build();
        DocumentResult result;
        try {
            result = humbleClient.execute(indexAction);
        } catch (IOException e) {
            throw ErrorCodes.ES_INDEX_ERROR0.exception(e);
        }

        if (!result.isSucceeded()) {
            throw ErrorCodes.ES_INDEX_ERROR1.exception(result.getErrorMessage());
        }

        return result.toString();
    }

    @Override
    public String index(String indexName, String typeName, GsonObject object) throws IPException {
        String jsonString = Constants.GSON.toJson(object);
        String id = object.getID();
        return this.index(indexName, typeName, id, jsonString);
    }

    @Override
    public List<String> index(String indexName, String typeName, Map<String, String> datas) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);

        Set<String> failedItems = new HashSet<>();
        List<String> allItemIds = new ArrayList<>();

        final Set<String> batch = datas.keySet();
        if (batch.size() > 0) {
            int batch_num = 0;
            Bulk.Builder bulkBuilder = new Bulk.Builder();

            for (String id : batch) {
                Index.Builder builder = new Index.Builder(datas.get(id)).index(indexName).type(typeName).id(id);
                bulkBuilder.addAction(builder.build());

                if (++batch_num == BATCH_SIZE_MAX) {
                    bulkActionExecute(failedItems, allItemIds, bulkBuilder);

                    batch_num = 0;
                    bulkBuilder = new Bulk.Builder();
                }
            }

            bulkActionExecute(failedItems, allItemIds, bulkBuilder);
        }

        if (!failedItems.isEmpty()) {
            throw ErrorCodes.ES_INDEX_ERROR1.exception(failedItems.toString());
        }

        return allItemIds;
    }

    @Override
    public List<String> index(String indexName, String typeName, List<? extends GsonObject> objects) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);

        Set<String> failedItems = new HashSet<>();
        List<String> allItemIds = new ArrayList<>();

        int size = objects.size();
        int batch_size = (int)Math.ceil((double)size / BATCH_SIZE_MAX);

        for (int i = 0; i < batch_size; i++) {
            int start = i * BATCH_SIZE_MAX;
            int end = Math.min(start + BATCH_SIZE_MAX, size);

            Bulk.Builder bulkBuilder = new Bulk.Builder();
            for (int j = start; j < end; j++) {
                GsonObject gsonObject = objects.get(j);
                String _id = gsonObject.getID();
                String jsonString = Constants.GSON.toJson(gsonObject);
                Index.Builder builder = new Index.Builder(jsonString).index(indexName).type(typeName);
                if (!StringUtil.isNullOrEmpty(_id)) {
                    builder.id(_id);
                }

                bulkBuilder.addAction(builder.build());
            }

            bulkActionExecute(failedItems, allItemIds, bulkBuilder);
        }

        if (!failedItems.isEmpty()) {
            throw ErrorCodes.ES_INDEX_ERROR1.exception(failedItems.toString());
        }

        return allItemIds;
    }

    @Override
    public List<String> indexBulk(String indexName, String typeName, List<String> datas) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);

        Set<String> failedItems = new HashSet<>();
        List<String> allItemIds = new ArrayList<>();

        final List<List<String>> batch = Lists.partition(datas, BATCH_SIZE_MAX);
        for (List<String> list : batch) {
//            Bulk.Builder bulkBuilder = addCredential(new Bulk.Builder());
            Bulk.Builder bulkBuilder = new Bulk.Builder();
            for (int i = 0; i < list.size(); i++) {
                String source = list.get(i);
                Index.Builder builder = new Index.Builder(source).index(indexName).type(typeName);

                bulkBuilder.addAction(builder.build());
            }

            bulkActionExecute(failedItems, allItemIds, bulkBuilder);
        }

        if (!failedItems.isEmpty()) {
            throw ErrorCodes.ES_INDEX_ERROR1.exception(failedItems.toString());
        }

        return allItemIds;
    }

    @Override
    public Map<String, List<String>> bulkActionExecute(Bulk.Builder bulkBuilder) throws IPException {
        Bulk bulk = bulkBuilder.build();
        BulkResult buckResult;
        try {
            buckResult = humbleClient.execute(bulk);
        } catch (Exception e) {
            logger.error("====es bulk index error====", e);
            throw ErrorCodes.ES_DOC_INDEX_ERROR.exception(e.getMessage());
        }

        return classifyBulkIndexResults(buckResult);
    }

    // 执行批量 action
    @Override
    public void bulkActionExecute(Set<String> failedItems, List<String> allItemIds, Bulk.Builder bulkBuilder) throws IPException {
        Map<String, List<String>> classifyResult = this.bulkActionExecute(bulkBuilder);
        failedItems.addAll(classifyResult.get(BATCH_FAILED_ITEMS));
        allItemIds.addAll(classifyResult.get(BATCH_ALL_ITEM_IDS));
    }

    private Map<String, List<String>> classifyBulkIndexResults(BulkResult result) {
        List<String> failedItems = new ArrayList<>();
        List<String> allItemIds = new ArrayList<>();

        for (BulkResult.BulkResultItem item : Utility.emptyIfNull(result.getFailedItems())) {
            StringBuilder buf = new StringBuilder();
            buf.append("id = [" + item.id + "]type = [" + item.errorType + "] reson= [" + item.errorReason
                    + "] error = [" + item.error + "]");
            failedItems.add(buf.toString());
        }
        for (BulkResult.BulkResultItem item : Utility.emptyIfNull(result.getItems())) {
            allItemIds.add(item.id);
        }
        Map<String, List<String>> results = new HashMap<>();
        results.put(BATCH_FAILED_ITEMS, failedItems);
        results.put(BATCH_ALL_ITEM_IDS, allItemIds);
        return results;
    }

    @Override
    public String update(String indexName, String typeName, String _id, String jsonString) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);
        Args.notEmpty(_id, Constants.ES_INDEX_ID);

        try {
//            Update updateAction = addCredential(new Update.Builder(jsonString).index(indexName).type(typeName).id(_id)).build();
            Update updateAction = new Update.Builder(jsonString).index(indexName).type(typeName).id(_id).build();

//            logger.debug(updateAction.toString());
            DocumentResult result = humbleClient.execute(updateAction);
//            logger.debug(result.toString());

            if (!result.isSucceeded()) {
                throw ErrorCodes.ES_UPDATE_ERROR1.exception(result.getErrorMessage());
            }

            return result.toString();
        } catch (IOException e) {
            throw ErrorCodes.ES_UPDATE_ERROR0.exception(e);
        }
    }

    @Override
    public String update(String indexName, String typeName, GsonObject object) throws IPException {
        String jsonString = Constants.GSON.toJson(object);
        String _id = object.getID();
        return this.update(indexName, typeName, _id, jsonString);
    }

    @Override
    public UpdateResult update(String indexName, String typeName, List<GsonObject> objects) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);

        UpdateResult result = new UpdateResult();
        final List<List<GsonObject>> batch = Lists.partition(objects, BATCH_SIZE_MAX);
//        Bulk.Builder bulkBuilder = addCredential(new Bulk.Builder());
        Bulk.Builder bulkBuilder = new Bulk.Builder();
        for (List<GsonObject> list : batch) {
            for (int index = 0; index < list.size(); index++) {
                GsonObject gsonObject = objects.get(index);
                String _id = gsonObject.getID();

                String jsonString = Constants.GSON.toJson(gsonObject);
                StringBuilder buf = new StringBuilder();
                buf.append("{\"doc\" : ").append(jsonString).append("}");

//                Update.Builder builder = addCredential(new Update.Builder(buf.toString()).index(indexName).type(typeName));
                Update.Builder builder = new Update.Builder(buf.toString()).index(indexName).type(typeName);
                //TODO 测试发现，若一个ID为空时，ES返回失败；可能需要做异常处理。
                if (!StringUtil.isNullOrEmpty(_id)) {
                    builder.id(_id);
                }
                Update update = builder.build();
                bulkBuilder.addAction(update);
            }
            Bulk bulk = bulkBuilder.build();
            BulkResult buckResult;
            try {
                buckResult = humbleClient.execute(bulk);
            } catch (Exception e) {
                logger.error("====es bulk update error====", e);
                throw ErrorCodes.ES_UPDATE_ERROR0.exception(e);
            }
            Map<String, List<String>> classifyResult = classifyBulkIndexResults(buckResult);

            result.setFailList(classifyResult.get(BATCH_FAILED_ITEMS));
            result.setSuccessList(classifyResult.get(BATCH_ALL_ITEM_IDS));
        }
        return result;
    }

    @Override
    public UpdateResult update(String indexName, String typeName, String queryString, GsonObject object) throws IPException {


        //        logger.info("queryString: " + (null == queryString ? "null" : queryString));


//
//        Search search = new SearchImpl();
//        double _count = search.count(indexName, typeName, queryString);
//        if (_count > IPConstants.BATCH_UPDATE_MAX_ROW) {
//            throw ErrorCodes.ES_BATCH_UPDATE_TOO_MANY.exception(queryString, _count + "", IPConstants.BATCH_UPDATE_MAX_ROW + "");
//        }
//
//        String _idName = "_id";
//        List<ValueStringObject> _idList = search.getValue(indexName, typeName, queryString, _idName, ValueStringObject.class, 0, IPConstants.BATCH_UPDATE_MAX_ROW);
//
//        if (_idList != null && !_idList.isEmpty()) {
//            ArrayList<GsonObject> list = new ArrayList<GsonObject>(_idList.size());
//            for (ValueStringObject _id : _idList) {
//                GsonObject newObject = (GsonObject) object.clone();
//                newObject.setID(_id.get_id());
//                list.add(newObject);
//            }
//
//            return this.update(indexName, typeName, list);
//        } else {
//            logger.info("the result sets is invalid. _idList: ", _idList == null ? "null" : _idList.size());
//            return null;
//        }
        return null;
    }

    @Override
    public List<UpdateResult> update(List<String> indexNames, List<String> typeNames, String queryString, GsonObject object) throws IPException {
        List<UpdateResult> resultList = new ArrayList<>();
//
//        if (null == indexNames || typeNames == null || indexNames.size() < 1 || indexNames.size() != typeNames.size()) {
//            throw ErrorCodes.ES_UPDATE_ERROR1.exception("indexNames或typeNames参数不正确");
//        }
//        for (int i = 0; i < indexNames.size(); i++) {
//            UpdateResult updateResult = update(indexNames.get(i), typeNames.get(i), queryString, object);
//            resultList.add(updateResult);
//        }
        return resultList;
    }

    @Override
    public Map<String, String> getVectors(String indexNames, String indexTypes, String id) {
        //TODO
        return null;
    }

    @Override
    public Map<String, String> getVectors(String indexNames, String indexTypes, Map<String, String> fields) throws IOException {
        //TODO
        return null;
    }

    @Override
    public Map<String, String> getVectors(String indexNames, String indexTypes, Map<String, String> fields, String keyWordStr) throws IOException {
        //TODO
        return null;
    }

    @Override
    public List<String> tokenize(String indexNames, String indexTypes, Map<String, String> fields) throws IOException {
        //TODO
        return null;
    }

    @Override
    public String delete(String indexName, String typeName, String _id) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);
        Args.notEmpty(_id, Constants.ES_INDEX_ID);

//        Delete.Builder builder = addCredential(new Delete.Builder(_id));
//        StringBuilder buf = new StringBuilder("index ");
//        buf.append("index:[").append(indexName).append("];");
//        buf.append("type:[").append(typeName).append("];");
//        logger.debug(buf.toString());

        Delete.Builder builder = new Delete.Builder(_id).index(indexName).type(typeName);
        Delete deleteAction = builder.build();
        DocumentResult result;
        try {
            result = humbleClient.execute(deleteAction);
        } catch (IOException ex) {
            throw ErrorCodes.ES_DOC_DELETE_ERROR.exception(ex.getMessage());
        }
        if (!result.isSucceeded()) {
            throw ErrorCodes.ES_DOC_DELETE_ERROR.exception("failed to delete document Id" + _id);
        }
        return result.toString();
    }

    @Override
    public List<String> delete(String indexName, String typeName, List<String> ids) throws IPException {
        Set<String> failedItems = new HashSet<>();
        List<String> allItemIds = new ArrayList<>();

        final List<List<String>> batch = Lists.partition(ids, BATCH_SIZE_MAX);
        for (List<String> list : batch) {
//            Bulk.Builder bulkBuilder = addCredential(new Bulk.Builder());
            Bulk.Builder bulkBuilder = new Bulk.Builder();
            for (int i = 0; i < list.size(); i++) {
                String id = ids.get(i);
                Delete.Builder builder = new Delete.Builder(id).index(indexName).type(typeName);

                bulkBuilder.addAction(builder.build());
            }

            bulkActionExecute(failedItems, allItemIds, bulkBuilder);
        }

        if (!failedItems.isEmpty()) {
            throw ErrorCodes.ES_INDEX_ERROR1.exception(failedItems.toString());
        }

        return allItemIds;
    }

    @Override
    public <T extends GsonObject>MultiDocsResult<T> multiGet(String indexName, String typeName, Set<String> ids) throws IPException {
        Args.notEmpty(indexName, Constants.ES_INDEX_NAME);
        Args.notEmpty(typeName, Constants.ES_TYPE_NAME);
        ISearchResult result = null;
        MultiDocsResult<T> multiDocsResult = null;
        MultiGet multiGetRequest = new MultiGet.Builder.ById(indexName,typeName).addId(ids).build();
        try {
            result =  humbleClient.execute(multiGetRequest);
            multiDocsResult = new MultiDocsResult(result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return multiDocsResult;
    }
}
