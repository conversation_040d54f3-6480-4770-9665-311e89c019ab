package cn.gwssi.data.service.document;

import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.common.common.pojo.UpdateResult;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.data.service.core.Bulk;
import cn.gwssi.data.service.core.MultiDocsResult;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface Document {

    /*-------------------------------------------document-------------------------------------------*/

    /**
     *
     * @param indexName
     * @param typeName
     * @param jsonString
     * @return
     * @throws IPException
     */
    public String index(String indexName, String typeName, String jsonString) throws IPException;

    /**
     * 根据传入的索引名称、类型、以及json字符串索引一篇文档
     * @param indexName: 索引名称
     * @param typeName： 类型名称
     * @return id :自定义的索引ID
     * @param jsonString:json字符串
     * @throws IPException
     */
    public String index(String indexName, String typeName, String id, String jsonString) throws IPException;

    /**
     * 根据传入的索引名称、类型、以及json对象索引一篇文档
     * @param indexName：索引名称
     * @param typeName：类型名称
     * @param object
     * @return
     * @throws IPException
     */
    public String index(String indexName, String typeName, GsonObject object) throws IPException;

    /**
     * 批量索引多个文档。
     * @param indexName
     * @param typeName
     * @param datas
     * @return
     * @throws IPException
     */
    public List<String> index(String indexName, String typeName, Map<String, String> datas) throws IPException;

    /**
     * 批量索引多个文档。
     * @param indexName
     * @param typeName
     * @param objects
     * @return
     * @throws IPException
     */
    public List<String> index(String indexName, String typeName, List<? extends GsonObject> objects) throws IPException;

    /**
     * 批量索引多个文档。
     * @param indexName
     * @param typeName
     * @param objects
     * @return
     * @throws IPException
     */
    public List<String> indexBulk(String indexName, String typeName, List<String> objects) throws IPException;

    /**
     * 批量执行
     * @param bulkBuilder
     * @return
     * @throws IPException
     */
    public Map<String, List<String>> bulkActionExecute(Bulk.Builder bulkBuilder) throws IPException;

    /**
     * 批量执行
     * @param bulkBuilder
     * @return
     * @throws IPException
     */
    public void bulkActionExecute(Set<String> failedItems, List<String> allItemIds, Bulk.Builder bulkBuilder) throws IPException;

    /*-------------------------------------------update-------------------------------------------*/

    /**
     * 更新一条索引
     * @param indexName
     * @param typeName
     * @param _id
     * @param jsonString
     * @return
     * @throws IPException
     */
    public String update(String indexName, String typeName, String _id, String jsonString) throws IPException;

    /**
     * 更新一条索引
     * @param indexName
     * @param typeName
     * @param object
     * @return
     * @throws IPException
     */
    public String update(String indexName, String typeName, GsonObject object) throws IPException;

    /**
     * 更新一些documents
     * @param indexName
     * @param typeName
     * @param objects
     * @return
     */
    public UpdateResult update(String indexName, String typeName, List<GsonObject> objects) throws IPException;

    /**
     * 根据查询条件，更新对应的documents
     * @param indexName
     * @param typeName
     * @param queryString
     * @param object
     * @return
     * @throws IPException
     */
    public UpdateResult update(String indexName, String typeName, String queryString, GsonObject object) throws IPException;

    /**
     * 接受list结构的indexNames和typeNames，将query在每个index中进行查询更新（原始需求：应对按语种分库问题）。
     * @param indexNames
     * @param typeNames
     * @param queryString
     * @param object
     * @return
     * @throws IPException
     */
    public List<UpdateResult> update(List<String> indexNames, List<String> typeNames, String queryString, GsonObject object)
            throws IPException;

    /*-------------------------------------------Term Vectors-------------------------------------------*/

    /**
     * 获取词条向量
     * @param indexNames
     * @param indexTypes
     * @param id
     * @return
     */
    public Map<String, String> getVectors(String indexNames, String indexTypes, String id);

    /**
     * 获取词条向量
     * @param indexNames
     * @param indexTypes
     * @param fields
     * @return
     * @throws IOException
     */
    public Map<String, String> getVectors(String indexNames, String indexTypes, Map<String, String> fields) throws IOException;

    /**
     * 获取词条向量
     * @param indexNames
     * @param indexTypes
     * @param fields
     * @param keyWordStr
     * @return
     * @throws IOException
     */
    public Map<String, String> getVectors(String indexNames, String indexTypes, Map<String, String> fields, String keyWordStr) throws IOException;

    /**
     * 获取分词结果。
     * @param indexNames
     * @param indexTypes
     * @param fields
     * @return
     * @throws IOException
     */
    public List<String> tokenize(String indexNames, String indexTypes, Map<String, String> fields) throws IOException;

    /*-------------------------------------------Document deletion-------------------------------------------*/

    /**
     * 删除一条document。
     * @param indexName
     * @param typeName
     * @param id
     * @return
     * @throws Exception
     */
    public String delete(String indexName, String typeName, String id) throws IPException;

    /**
     * 批量删除document。
     * @param indexName
     * @param typeName
     * @param ids
     * @return
     * @throws Exception
     */
    public List<String> delete(String indexName, String typeName, List<String> ids) throws IPException;

    /**
     * 根据id获得文档信息
     * @param indexName
     * @param typeName
     * @param ids
     * @return
     * @throws IPException
     */
    <T extends GsonObject>MultiDocsResult<T> multiGet(String indexName, String typeName, Set<String> ids) throws IPException;
}
