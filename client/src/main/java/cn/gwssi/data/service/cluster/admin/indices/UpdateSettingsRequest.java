package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.AbstractMultiIndexActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * Change specific index level settings in real time.
 *
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class UpdateSettingsRequest extends GenericResultAbstractAction {

    protected UpdateSettingsRequest(Builder builder) {
        super(builder);
        this.payload = builder.source;
    }

    @Override
    public String getRestMethodName() {
        return "PUT";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return super.buildURI(elasticsearchVersion) + HttpPath.INDEX_UPDATE_SETTINGS;
    }

    public static class Builder extends AbstractMultiIndexActionBuilder<UpdateSettingsRequest, Builder> {
        private final Object source;

        /**
         * Please see the <a href="http://www.elasticsearch.org/guide/en/elasticsearch/reference/current/indices-update-settings.html#indices-update-settings">related page on Elasticsearch guide</a>
         * for the list of settings that can be changed using this action/API.
         *
         * @param source body of request that includes updated settings
         */
        public Builder(Object source) {
            this.source = source;
        }

        @Override
        public UpdateSettingsRequest build() {
            return new UpdateSettingsRequest(this);
        }
    }

}
