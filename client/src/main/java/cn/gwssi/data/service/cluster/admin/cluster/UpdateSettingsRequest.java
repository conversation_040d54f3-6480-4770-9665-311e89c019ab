package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Allows to update cluster wide specific settings. Settings updated can either be persistent (applied cross restarts)
 * or transient (will not survive a full cluster restart). The cluster responds with the settings updated.
 * <br/>
 * <br/>
 * There is a specific list of settings that can be updated, please see
 * <a href="http://www.elastic.co/guide/en/elasticsearch/reference/current/cluster-update-settings.html#cluster-settings">Elasticsearch docs</a>
 * for more information.
 *
 * <AUTHOR> keser
 */
public class UpdateSettingsRequest extends GenericResultAbstractAction {

    private Map<String, String> persistents;
    private Map<String, String> transients;

    protected UpdateSettingsRequest(Builder builder) {
        super(builder);
        this.payload = builder.source;
        this.persistents = builder.persistents;
        this.transients = builder.transients;
    }

    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.CLUSTER_UPDATE_SETTINGS;
    }

    @Override
    public String getRestMethodName() {
        return "PUT";
    }

    @Override
    public String getData(Gson gson) {
        if (this.payload != null) {

            if (payload instanceof String) {
                return (String) payload;
            } else {
                return gson.toJson(payload);
            }
        } else {
            JsonObject jsonObject = new JsonObject();

            if (this.transients.size() > 0) {
                jsonObject.add("transient", gson.toJsonTree(this.transients));
            }
            if (this.persistents.size() > 0) {
                jsonObject.add("persistent", gson.toJsonTree(this.persistents));
            }
            if (jsonObject.size() == 0) {
                return null;
            }

            return jsonObject.toString();
        }
    }

    public static class Builder extends AbstractAction.Builder<UpdateSettingsRequest, Builder> {

        private Map<String, String> persistents = new LinkedHashMap<>();
        private Map<String, String> transients = new LinkedHashMap<>();
        private Object source;

        /**
         * There is a specific list of settings that can be updated, please see
         * <a href="http://www.elastic.co/guide/en/elasticsearch/reference/current/cluster-update-settings.html#cluster-settings">Elasticsearch docs</a>
         * for more information.
         */
        public Builder(Object source) {
            this.source = source;
        }

        public Builder addPersistent(String key, String value) {
            this.persistents.put(key, value);
            return this;
        }

        public Builder addPersistents(Map<String, String> persistents) {
            if (null != persistents && persistents.size() > 0) {
                this.persistents.putAll(persistents);
            }
            return this;
        }

        public Builder addTransient(String key, String value) {
            this.transients.put(key, value);
            return this;
        }

        public Builder addTransients(Map<String, String> transients) {
            if (null != transients && transients.size() > 0) {
                this.transients.putAll(transients);
            }
            return this;
        }

        @Override
        public UpdateSettingsRequest build() {
            return new UpdateSettingsRequest(this);
        }
    }

}
