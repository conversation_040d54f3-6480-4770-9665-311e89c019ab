package cn.gwssi.data.service.client;

import cn.gwssi.SerializerFactory;
import cn.gwssi.data.service.aggregation.Aggregation;
import cn.gwssi.data.service.aggregation.AggregationImpl;
import cn.gwssi.data.service.base.Base;
import cn.gwssi.data.service.base.BaseImpl;
import cn.gwssi.data.service.cluster.admin.cluster.Cluster;
import cn.gwssi.data.service.cluster.admin.cluster.ClusterImpl;
import cn.gwssi.data.service.cluster.admin.indices.Indices;
import cn.gwssi.data.service.cluster.admin.indices.IndicesImpl;
import cn.gwssi.data.service.configReader.ConfigReader;
import cn.gwssi.data.service.document.Document;
import cn.gwssi.data.service.document.DocumentImpl;
import cn.gwssi.data.service.search.Search;
import cn.gwssi.data.service.search.SearchImpl;
import cn.gwssi.isearch.client.ISearchClientFactory;
import cn.gwssi.isearch.client.config.HttpClientConfig;
import cn.gwssi.serializers.Serializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Client {
    final static Logger logger = LoggerFactory.getLogger(Client.class);
    private HttpClientConfig clientConfig;
    private ISearchClientProxy humbleClient;
    private String xmlFolder;
    private boolean isEnv = false;

    public Client(HttpClientConfig config) {
        this.clientConfig = config;
        initClientParams();
    }
    public Client(HttpClientConfig config, String xmlFolder) {
        this(config);
        this.xmlFolder = xmlFolder;
    }
    public Client(HttpClientConfig config, String xmlFolder, boolean isEnv) {
        this(config, xmlFolder);
        this.isEnv = isEnv;
    }

    public ISearchClientProxy getISearchClientProxy(){
        return humbleClient;
    }

    public Base baseRequest() {
        return new BaseImpl(this.humbleClient);
    }

    public Cluster clusterRequest() {
        return new ClusterImpl(this.humbleClient);
    }

    public Indices indexRequest() {
        return new IndicesImpl(this.humbleClient);
    }

    public Document DocumentRequest() {
        return new DocumentImpl(this.humbleClient);
    }

    public Search searchRequest() {
        loadingIPConfig();
        return new SearchImpl(this.humbleClient);
    }

    public Aggregation aggregationRequest() {
        loadingIPConfig();
        return new AggregationImpl(this.humbleClient);
    }

    private void initClientParams() {
        initHumbleClient(clientConfig);
    }

    private void initHumbleClient(HttpClientConfig clientConfig) {
        ISearchClientFactory searchClientFactory = new ISearchClientFactory();
        searchClientFactory.setHttpClientConfig(clientConfig);
        humbleClient = new ISearchClientProxy(searchClientFactory.getObject(), getSerializer());
    }

    private Serializer getSerializer() {
        Serializer serializer = SerializerFactory.builder().setThreadSafe(true).setSoftReferences(true)
                .setMaximunCapacity(10).build();
        return serializer;
    }

    private void loadingIPConfig() {
        try {
            if (StringUtils.isNotEmpty(xmlFolder)) {
                ConfigReader.getInstance().readConfig(xmlFolder, isEnv);
            } else {
                ConfigReader.getInstance().readConfig(new SearchImpl(this.humbleClient));
            }
        } catch (Exception ex) {
            logger.error("config xml loading failed", ex);
            throw new RuntimeException("load config xml error", ex);
        }
    }
}
