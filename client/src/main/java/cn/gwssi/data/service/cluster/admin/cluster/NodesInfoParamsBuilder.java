package cn.gwssi.data.service.cluster.admin.cluster;

import java.util.*;

public class NodesInfoParamsBuilder {

    private Map<String,Metric> metricMap;

    private Set<String> nodeIds;

    public NodesInfoParamsBuilder(Builder builder){
        this.metricMap = builder.metricMap;
        this.nodeIds = builder.nodeIds;
    }

    public Map<String, Metric> getMetricMap(){
        return this.metricMap;
    }

    public Set<String> getNodeIds(){
        return this.nodeIds;
    }

    public enum Metric{
        SETTINGS("settings"),
        OS("os"),
        PROCESS("process"),
        JVM("jvm"),
        THREAD_POOL("thread_pool"),
        TRANSPORT("transport"),
        HTTP("http"),
        PLUGINS("plugins"),
        INGEST("ingest"),
        INDICES("indices");

        private String key;

        Metric(String key){
            this.key = key;
        }

        public String getKey(){
            return this.key;
        }

    }

    public static class Builder{

        private Map<String, Metric> metricMap = new HashMap<>();

        private Set<String> nodeIds = new HashSet<>();

        public void setNodeIds(String... nodeId){
            if (null != nodeId && nodeId.length > 0)
                this.nodeIds.addAll(new HashSet<>(Arrays.asList(nodeId)));
        }

        public Builder withSettings(boolean hasSettings) {
            if (hasSettings)
                metricMap.put(Metric.SETTINGS.getKey(), Metric.SETTINGS);
            return this;
        }

        public Builder withOs(boolean hasOs) {
            if (hasOs)
                metricMap.put(Metric.OS.getKey(), Metric.OS);
            return this;
        }

        public Builder withProcess(boolean hasProcess) {
            if (hasProcess)
                metricMap.put(Metric.PROCESS.getKey(), Metric.PROCESS);
            return this;
        }

        public Builder withJvm(boolean hasJvm) {
            if (hasJvm)
                metricMap.put(Metric.JVM.getKey(), Metric.JVM);
            return this;
        }

        public Builder withThreadPool(boolean hasThreadPool) {
            if (hasThreadPool)
                metricMap.put(Metric.THREAD_POOL.getKey(), Metric.THREAD_POOL);
            return this;
        }

        public Builder withTransport(boolean hasTransport) {
            if (hasTransport)
                metricMap.put(Metric.TRANSPORT.getKey(), Metric.TRANSPORT);
            return this;
        }

        public Builder withHttp(boolean hasHttp) {
            if (hasHttp)
                metricMap.put(Metric.HTTP.getKey(), Metric.HTTP);
            return this;
        }

        public Builder withPlugins(boolean hasPlugins) {
            if (hasPlugins)
                metricMap.put(Metric.PLUGINS.getKey(), Metric.PLUGINS);
            return this;
        }

        public Builder withIngest(boolean hasIngest){
            if (hasIngest)
                metricMap.put(Metric.INGEST.getKey(), Metric.INGEST);
            return this;
        }

        public NodesInfoParamsBuilder build(){
            return new NodesInfoParamsBuilder(this);
        }
    }
}
