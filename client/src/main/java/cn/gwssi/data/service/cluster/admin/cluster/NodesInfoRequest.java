package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static cn.gwssi.data.service.cluster.UrlUtils.appendRestPath;

public class NodesInfoRequest extends GenericResultAbstractAction {

    private Set<String> nodeIds;

    public NodesInfoRequest(Builder builder){
        super(builder);
        this.nodeIds = builder.nodeIds;
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.NODES_INFO  + appendRestPath(nodeIds);
    }

    public static class Builder extends AbstractAction.Builder<NodesInfoRequest, Builder>{

        private Set<String> nodeIds = new HashSet<>();

        public void addMetrics(Map<String, NodesInfoParamsBuilder.Metric> metrics){
             for (Map.Entry<String, NodesInfoParamsBuilder.Metric> entry : metrics.entrySet()){
                 String key = entry.getKey();
                 switch (key){
                     case "settings" :
                         withSettings(entry.getValue());
                         break;
                     case "os":
                         withOs(entry.getValue());
                         break;
                     case "process":
                         withProcess(entry.getValue());
                         break;
                     case "jvm":
                         withJvm(entry.getValue());
                         break;
                     case "thread_pool":
                         withThreadPool(entry.getValue());
                         break;
                     case "transport":
                         withTransport(entry.getValue());
                         break;
                     case "http":
                         withHttp(entry.getValue());
                         break;
                     case "plugins":
                         withPlugins(entry.getValue());
                         break;
                     case "ingest":
                         withIngest(entry.getValue());
                         break;

                 }
             }

        }

        public void addNodeIds(Set<String> nodeIds){
            this.nodeIds = nodeIds;
        }

        public Builder withSettings(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.SETTINGS.getKey());
        }

        public Builder withOs(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.OS.getKey());
        }

        public Builder withProcess(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.PROCESS.getKey());
        }

        public Builder withJvm(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.JVM.getKey());
        }

        public Builder withThreadPool(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.THREAD_POOL.getKey());
        }

        public Builder withTransport(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.TRANSPORT.getKey());
        }

        public Builder withHttp(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.HTTP.getKey());
        }

        public Builder withPlugins(NodesInfoParamsBuilder.Metric metric) {
            return addCleanApiParameter(metric.PLUGINS.getKey());
        }

        public Builder withIngest(NodesInfoParamsBuilder.Metric metric){
            return addCleanApiParameter(metric.INGEST.getKey());
        }
        @Override
        public NodesInfoRequest build() {
            return new NodesInfoRequest(this);
        }
    }
}
