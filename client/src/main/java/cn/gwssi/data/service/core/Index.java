package cn.gwssi.data.service.core;

import cn.gwssi.data.service.action.BulkableAction;
import cn.gwssi.data.service.action.SingleResultAbstractDocumentTargetedAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.params.Parameters;
import org.apache.http.NameValuePair;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class Index extends SingleResultAbstractDocumentTargetedAction implements BulkableAction<DocumentResult> {

    protected Index(Builder builder) {
        super(builder);
        this.payload = builder.source;
    }

    @Override
    public String getPathToResult() {
        return "ok";
    }

//    @Override
//    public List<NameValuePair> getParas() {
//        return null;
//    }

    @Override
    public String getRestMethodName() {
        return "PUT";
    }
//
//    @Override
//    public String getSerializerParam() {
//        return super.getSerializerParam();
//    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.INDEX + "/" + super.buildURI(elasticsearchVersion);
    }

    @Override
    public String getBulkMethodName() {
        Collection<Object> opType = getParameter(Parameters.OP_TYPE);
        if (opType != null) {
            if (opType.size() > 1) {
                throw new IllegalArgumentException("Expecting a single value for OP_TYPE parameter, you provided: " + opType.size());
            }
            return (opType.size() == 1 && ((opType.iterator().next()).toString().equalsIgnoreCase("create"))) ? "create" : "index";
        } else {
            return "index";
        }
    }

    public static class Builder extends SingleResultAbstractDocumentTargetedAction.Builder<Index, Builder> {
        private final Object source;

        public Builder(Object source) {
            this.source = source;
            this.id(getIdFromSource(source)); // set the default for id if it exists in source
        }

        public Index build() {
            return new Index(this);
        }
    }
}
