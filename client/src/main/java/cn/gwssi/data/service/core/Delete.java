package cn.gwssi.data.service.core;

import cn.gwssi.data.service.action.BulkableAction;
import cn.gwssi.data.service.action.SingleResultAbstractDocumentTargetedAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class Delete extends SingleResultAbstractDocumentTargetedAction implements BulkableAction<DocumentResult> {

    protected Delete(Builder builder) {
        super(builder);
    }

    @Override
    public String getRestMethodName() {
        return "DELETE";
    }

    @Override
    public String getPathToResult() {
        return "ok";
    }

    @Override
    public String getBulkMethodName() {
        return "delete";
    }

    public static class Builder extends SingleResultAbstractDocumentTargetedAction.Builder<Delete, Builder> {

        public Builder(String id) {
            this.id(id);
        }

        public Delete build() {
            return new Delete(this);
        }

    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.DELETE + "/" + super.buildURI(elasticsearchVersion);
    }
}
