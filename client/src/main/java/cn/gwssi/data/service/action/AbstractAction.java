package cn.gwssi.data.service.action;

import cn.gwssi.data.common.Constants;
import cn.gwssi.data.service.annotations.JestId;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.ISearchResult;
import cn.gwssi.data.service.params.Parameters;
import cn.gwssi.data.util.FeatureClient;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import com.google.gson.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.auth.Credentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.security.Principal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public abstract class AbstractAction<T extends ISearchResult> implements Action<T> {

    public static String CHARSET = "utf-8";

    protected final static Logger log = LoggerFactory.getLogger(AbstractAction.class);
    protected String indexName;
    protected String typeName;
    protected String nodes;
    protected Object payload;

    private final ConcurrentMap<String, Object> headerMap = new ConcurrentHashMap<String, Object>();
    private final Multimap<String, Object> parameterMap = LinkedHashMultimap.create();
    private final Set<String> cleanApiParameters = new LinkedHashSet<String>();

    public AbstractAction() {
//        // FIXED 2024-05-10 统一在这里设置，否则要在 Search SearchScroll Analyze 等实现里加
//        if (StringUtils.isNotBlank(FeatureClient.SEARCH_PREFERENCE)) {
//            parameterMap.put("preference", FeatureClient.SEARCH_PREFERENCE);
//        }
//        if (StringUtils.isNotBlank(FeatureClient.SEARCH_TYPE)) {
//            parameterMap.put("search_type", FeatureClient.SEARCH_TYPE);
//        }
    }

    @Override
    public String getSerializerParam(Gson gson) {
        return this.getData(gson);
    }

    @Override
    public List<NameValuePair> getParas() {
        return null;
    }

    @SuppressWarnings("unchecked")
    public AbstractAction(Builder builder) {
        parameterMap.putAll(builder.parameters);
        headerMap.putAll(builder.headers);
        cleanApiParameters.addAll(builder.cleanApiParameters);

//        // FIXED 2024-05-10 统一在这里设置，否则要在 Search SearchScroll Analyze 等实现里加
//        if (StringUtils.isNotBlank(FeatureClient.SEARCH_PREFERENCE)) {
//            parameterMap.put("preference", FeatureClient.SEARCH_PREFERENCE);
//        }
//        if (StringUtils.isNotBlank(FeatureClient.SEARCH_TYPE)) {
//            parameterMap.put("search_type", FeatureClient.SEARCH_TYPE);
//        }

        if (builder instanceof AbstractMultiIndexActionBuilder) {
            indexName = ((AbstractMultiIndexActionBuilder) builder).getJoinedIndices();
            if (builder instanceof AbstractMultiTypeActionBuilder) {
                indexName = ((AbstractMultiTypeActionBuilder) builder).getJoinedIndices();
                typeName = ((AbstractMultiTypeActionBuilder) builder).getJoinedTypes();
            }
        } else if (builder instanceof AbstractMultiINodeActionBuilder) {
            nodes = ((AbstractMultiINodeActionBuilder) builder).getJoinedNodes();
        }
    }

    // FIXED 2024-06-14 要和插件保持一致，目前只有 Search 和 Analyze 需要这些参数，只在这几个的实现里调用
    protected void addUrlParameter() {
        if (StringUtils.isNotBlank(FeatureClient.SEARCH_PREFERENCE)) {
            parameterMap.put("preference", FeatureClient.SEARCH_PREFERENCE);
        }
        if (StringUtils.isNotBlank(FeatureClient.SEARCH_TYPE)) {
            parameterMap.put("search_type", FeatureClient.SEARCH_TYPE);
        }
    }

    protected T createNewElasticSearchResult(T result, String responseBody, int statusCode, String reasonPhrase, Gson gson) {
        JsonObject jsonMap = parseResponseBody(responseBody);
        int errorCode = parseStatus(responseBody);
        if (statusCode == 200 && errorCode!= 200){
            statusCode = errorCode;
            if (!jsonMap.get("msg").isJsonNull()) {
                reasonPhrase = jsonMap.get("msg").getAsString();
            }
        }
        result.setResponseCode(statusCode);
        result.setJsonString(responseBody);
        result.setJsonObject(jsonMap);
        result.setPathToResult(getPathToResult());
        if (isHttpSuccessful(statusCode)) {
            result.setSucceeded(true);
            log.debug("Request and operation succeeded");
        } else {
            result.setSucceeded(false);
            // provide the generic HTTP status code error, if one hasn't already come in via the JSON response...
            // eg.
            //  IndicesExist will return 404 (with no content at all) for a missing index, but:
            //  Update will return 404 (with an error message for DocumentMissingException)
            if (result.getErrorMessage() == null) {
                result.setErrorMessage(statusCode + " " + (reasonPhrase == null ? "null" : reasonPhrase));
            }
            log.debug("Response is failed. errorMessage is " + result.getErrorMessage());
        }
        return result;
    }

    protected boolean isHttpSuccessful(int httpCode) {
        return (httpCode / 100) == 2;
    }

    protected JsonObject parseResponseBody(String responseBody) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return new JsonObject();
        }

        JsonElement parsed = new JsonParser().parse(responseBody);
        if (parsed.isJsonObject()) {
            return parsed.getAsJsonObject();
        } else {
            throw new JsonSyntaxException("Response did not contain a JSON Object");
        }
    }

    private int parseStatus(String responseBody){
        JsonObject jsonObject = parseResponseBody(responseBody);
        int statusCode = 200;
        if (jsonObject.has("status")){
            String status = jsonObject.get("status").getAsString();
            if (status.equals("error")){
                statusCode = 400;
            }
        }
        return statusCode;
    }

    public static String getIdFromSource(Object source) {
        if (source == null) return null;
        Field[] fields = source.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(JestId.class)) {
                try {
                    field.setAccessible(true);
                    Object name = field.get(source);
                    return name == null ? null : name.toString();
                } catch (IllegalAccessException e) {
                    log.error("Unhandled exception occurred while getting annotated id from source", e);
                }
            }
        }
        return null;
    }

    public Collection<Object> getParameter(String parameter) {
        return parameterMap.get(parameter);
    }

    public Object getHeader(String header) {
        return headerMap.get(header);
    }

    @Override
    public Map<String, Object> getHeaders() {
        return headerMap;
    }

    @Override
    public String getRestPath() {
        String finalUri = buildURI(ElasticsearchVersion.UNKNOWN);
        if (!parameterMap.isEmpty() || !cleanApiParameters.isEmpty()) {
            try {
                finalUri += buildQueryString();
            } catch (UnsupportedEncodingException e) {
                // unless CHARSET is overridden with a wrong value in a subclass,
                // this exception won't be thrown.
                log.error("Error occurred while adding parameters to uri.", e);
            }
        }
        return finalUri;
    }

    @Override
    public String getData(Gson gson) {
        if (payload == null) {
            return null;
        } else if (payload instanceof String) {
            return (String) payload;
        } else {
            return gson.toJson(payload);
        }
    }

    @Override
    public List<NameValuePair> getParams() {
        return null;
    }

    @Override
    public void setCredentials(Credentials credentials) {
        Principal principal = credentials.getUserPrincipal();
        if (null != principal) {
            String username = principal.getName();

            if (StringUtils.isNotBlank(username)) {
                parameterMap.put(Constants.USER_NAME_KEY, username);
                parameterMap.put(Constants.PASSWORD_KEY, credentials.getPassword());
            }
        }
    }

    @Override
    public String getPathToResult() {
        return null;
    }

    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        StringBuilder sb = new StringBuilder();

        try {
            if (StringUtils.isNotBlank(indexName)) {
                sb.append(URLEncoder.encode(indexName, CHARSET));

                String commandExtension = getURLCommandExtension(elasticsearchVersion);

                if (StringUtils.isNotBlank(commandExtension)) {
                    sb.append("/").append(URLEncoder.encode(commandExtension, CHARSET));
                }

                if (StringUtils.isNotBlank(typeName)) {
                    sb.append("/").append(URLEncoder.encode(typeName, CHARSET));
                }
            }
        } catch (UnsupportedEncodingException e) {
            // unless CHARSET is overridden with a wrong value in a subclass,
            // this exception won't be thrown.
            log.error("Error occurred while adding index/type to uri", e);
        }

        return sb.toString();
    }

    protected String getURLCommandExtension(ElasticsearchVersion elasticsearchVersion) {
        return null;
    }

    protected String buildQueryString() throws UnsupportedEncodingException {
        StringBuilder queryString = new StringBuilder();

        if (!cleanApiParameters.isEmpty()) {
            queryString.append("/").append(Joiner.on(',').join(cleanApiParameters));
        }

        queryString.append("?");
        for (Map.Entry<String, Object> entry : parameterMap.entries()) {
            queryString.append(URLEncoder.encode(entry.getKey(), CHARSET))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue().toString(), CHARSET))
                    .append("&");
        }
        // if there are any params  ->  deletes the final ampersand
        // if no params             ->  deletes the question mark
        queryString.deleteCharAt(queryString.length() - 1);

        return queryString.toString();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("uri", getRestPath())
                .add("method", getRestMethodName())
                .toString();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getRestPath(), getRestMethodName(), getHeaders(), payload);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (obj.getClass() != getClass()) {
            return false;
        }

        AbstractAction rhs = (AbstractAction) obj;
        return Objects.equals(getRestPath(), rhs.getRestPath())
                && Objects.equals(getRestMethodName(), rhs.getRestMethodName())
                && Objects.equals(getHeaders(), rhs.getHeaders())
                && Objects.equals(payload, rhs.payload);
    }

    public abstract String getRestMethodName();

    @SuppressWarnings("unchecked")
    protected static abstract class Builder<T extends Action, K> {
        protected Multimap<String, Object> parameters = LinkedHashMultimap.<String, Object>create();
        protected Map<String, Object> headers = new LinkedHashMap<String, Object>();
        protected Set<String> cleanApiParameters = new LinkedHashSet<String>();

        public K toggleApiParameter(String key, boolean enable) {
            if (enable) {
                addCleanApiParameter(key);
            } else {
                removeCleanApiParameter(key);
            }

            return (K) this;
        }

        public K removeCleanApiParameter(String key) {
            cleanApiParameters.remove(key);
            return (K) this;
        }

        public K addCleanApiParameter(String key) {
            cleanApiParameters.add(key);
            return (K) this;
        }

        public K setParameter(String key, Object value) {
            parameters.put(key, value);
            return (K) this;
        }

        @Deprecated
        public K setParameter(Map<String, Object> parameters) {
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                this.parameters.put(entry.getKey(), entry.getValue());
            }
            return (K) this;
        }

        public K setHeader(String key, Object value) {
            headers.put(key, value);
            return (K) this;
        }

        public K setHeader(Map<String, Object> headers) {
            this.headers.putAll(headers);
            return (K) this;
        }

        public K refresh(boolean refresh) {
            return setParameter(Parameters.REFRESH, refresh);
        }

        /**
         * All REST APIs accept the case parameter.
         * When set to camelCase, all field names in the result will be returned
         * in camel casing, otherwise, underscore casing will be used. Note,
         * this does not apply to the source document indexed.
         */
        public K resultCasing(String caseParam) {
            setParameter(Parameters.RESULT_CASING, caseParam);
            return (K) this;
        }

        abstract public T build();
    }
}
