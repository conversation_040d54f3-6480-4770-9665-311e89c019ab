package cn.gwssi.data.service.core.search.aggregation;

import com.google.gson.JsonObject;

import java.util.Objects;

import static cn.gwssi.data.service.core.search.aggregation.AggregationField.DOC_COUNT;

/**
 * <AUTHOR>
 */
public class ReverseAggregation extends Bucket {

    public static final String TYPE = "reverse";

    public ReverseAggregation(String name, JsonObject filterAggregation) {
        super(name, filterAggregation, filterAggregation.get(String.valueOf(DOC_COUNT)).getAsLong());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), TYPE);
    }

}
