package cn.gwssi.data.service.cluster.admin.cluster;

import lombok.Getter;

@Getter
public class ClusterHealthParamsBuilder {
    private String indexName;
    private Boolean pretty;
    private ClusterHealthParamsBuilder.Level level;
    private ClusterHealthParamsBuilder.Status wait_for_status;
    private Integer wait_for_relocating_shards;
    private Integer wait_for_active_shards;
    private Integer timeout_in_seconds;
    private Boolean local;
    //成对出现
    private Integer wait_for_nodes;
    private ClusterHealthParamsBuilder.Compare compareSign;

    private ClusterHealthParamsBuilder(Builder builder) {
        this.indexName = builder.indexName;
        this.pretty = builder.pretty;
        this.level = builder.level;
        this.wait_for_status = builder.wait_for_status;
        this.wait_for_relocating_shards = builder.wait_for_relocating_shards;
        this.wait_for_active_shards = builder.wait_for_active_shards;
        this.timeout_in_seconds = builder.timeout_in_seconds;
        this.local = builder.local;
        //成对出现
        this.wait_for_nodes = builder.wait_for_nodes;
        this.compareSign = builder.compareSign;
    }

    public static class Builder {
        private String indexName;
        private Boolean pretty;
        private ClusterHealthParamsBuilder.Level level;
        private ClusterHealthParamsBuilder.Status wait_for_status;
        private Integer wait_for_relocating_shards;
        private Integer wait_for_active_shards;
        private Integer wait_for_nodes;
        private ClusterHealthParamsBuilder.Compare compareSign = ClusterHealthParamsBuilder.Compare.INVALID;
        private Integer timeout_in_seconds;
        private Boolean local;

        public Builder() {
        }

        public Builder indexName(String indexName) {
            this.indexName = indexName;
            return this;
        }

        public Builder pretty(Boolean pretty) {
            this.pretty = pretty;
            return this;
        }

        public Builder Level(ClusterHealthParamsBuilder.Level level) {
            this.level = level;
            return this;
        }

        public Builder wait_for_status(ClusterHealthParamsBuilder.Status status) {
            this.wait_for_status = status;
            return this;
        }

        public Builder wait_for_relocating_shards(int relocatingShards) {
            this.wait_for_relocating_shards = relocatingShards;
            return this;
        }

        public Builder wait_for_active_shards(int wait_for_active_shards) {
            this.wait_for_active_shards = wait_for_active_shards;
            return this;
        }

        public Builder wait_for_nodes(int number, ClusterHealthParamsBuilder.Compare compareSign) {
            this.wait_for_nodes = number;
            this.compareSign = compareSign;
            return this;
        }

        public Builder timeout_in_seconds(int number) {
            this.timeout_in_seconds = number;
            return this;
        }

        public Builder local(Boolean local) {
            this.local = local;
            return this;
        }

        public ClusterHealthParamsBuilder build() {
            return new ClusterHealthParamsBuilder(this);
        }
    }

    public static enum Level {
        CLUSTER("cluster"),
        INDICES("indices"),
        SHARDS("shards");

        private String value;

        Level(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

//        public static ClusterHealthRequest.Level levelConvertFun(Level level) {
//            return levelConvert.get(level);
//        }

        public String toString() {
            return this.getValue();
        }
    }

    public static enum Status {
        GREEN("green"),
        YELLOW("yellow"),
        RED("red");

        private String value;

        Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public String toString() {
            return this.getValue();
        }

    }

    public static enum Compare {
        GE("ge"),  //表示 >=
        LE("le"), //表示 <=
        GT("gt"),  //表示 >
        LT("lt"),  //表示 <
        INVALID("invalid");

        private String value;
        Compare(String value) {
            this.value = value;
        }

        String getValue() {
            return value;
        }
    }

}
