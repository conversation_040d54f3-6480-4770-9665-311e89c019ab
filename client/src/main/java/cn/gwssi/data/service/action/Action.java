package cn.gwssi.data.service.action;

import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.Gson;
import org.apache.http.NameValuePair;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface Action<T extends ISearchResult> extends cn.gwssi.isearch.client.action.Action {

    List<NameValuePair> getParams();

    T createNewElasticSearchResult(String responseBody, int statusCode, String reasonPhrase, Gson gson);
}
