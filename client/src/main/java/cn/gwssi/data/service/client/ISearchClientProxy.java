package cn.gwssi.data.service.client;

import cn.gwssi.data.service.action.Action;
import cn.gwssi.isearch.client.http.ISearchClient;
import cn.gwssi.serializers.Serializer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.Header;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class ISearchClientProxy {

    private final static Logger log = LoggerFactory.getLogger(ISearchClientProxy.class);

    public static final String ELASTIC_SEARCH_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ";

    private ISearchClient searchClient;
    private Serializer serializer;
    protected Gson gson = new GsonBuilder().setDateFormat(ELASTIC_SEARCH_DATE_FORMAT).create();

    public ISearchClientProxy(ISearchClient searchClient, Serializer serializer) {
        this.searchClient = searchClient;
        this.serializer = serializer;
    }

    public Serializer getSerializer() {
        return serializer;
    }

    public <T extends ISearchResult> T execute(Action<T> clientRequest) throws IOException {
        Pair<CloseableHttpResponse, HttpUriRequest> response = searchClient.execute(clientRequest);

        return deserializeResponse(response, "", clientRequest);
    }

    // FIXED 2023-03-22 请求错误有时候会导致 close_wait 问题，需要 abort 连接来规避这个问题
    private <T extends ISearchResult> T deserializeResponse(Pair<CloseableHttpResponse, HttpUriRequest> pair, final String httpRequest, Action<T> clientRequest) throws IOException {
        CloseableHttpResponse response = pair.getLeft();
        HttpUriRequest request = pair.getRight();

        StatusLine statusLine = response.getStatusLine();
        String responseBody = null;

        try {
            if (null != response.getEntity()) {
                responseBody = EntityUtils.toString(response.getEntity());
            }

            return clientRequest.createNewElasticSearchResult(
                    responseBody,
                    statusLine.getStatusCode(),
                    statusLine.getReasonPhrase(),
                    gson
            );
        } catch (com.google.gson.JsonSyntaxException e) {
            log.error("检索错误，错误信息：：：：：：：：" + responseBody);

            for (Header header : response.getHeaders("Content-Type")) {
                final String mimeType = header.getValue();
                if (!mimeType.startsWith("application/json")) {
                    // probably a proxy that responded in text/html
//                    final String message = "Request " + httpRequest.toString() + " yielded " + mimeType
//                            + ", should be json: " + statusLine.toString();
                    final String message = "Request mimeType : " + mimeType + " , Response Message: " + responseBody;
                    throw new IOException(message, e);
                }
            }
            throw e;
        } finally {
            if (response != null) {
                if (null != response.getStatusLine() && response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                    request.abort();
                }

                try {
                    response.close();

                    if (null != response.getEntity() && null != response.getEntity().getContent()) {
                        response.getEntity().getContent().close();
                    }
                } catch (IOException ex) {
                    log.error("Exception occurred while closing response stream.", ex);
                }
            }
        }
    }

}
