package cn.gwssi.data.service.search;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.*;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.collapse.InnerHitCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.NumberUtil;
import cn.gwssi.data.common.ErrorCodes;
import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.client.ISearchResult;
import cn.gwssi.data.service.core.*;
import cn.gwssi.data.service.core.search.sort.Sort;
import cn.gwssi.data.util.CollapseUtil;
import cn.gwssi.data.util.SearchErrorHandler;
import cn.gwssi.data.util.SearchLogUtil;
import cn.gwssi.data.util.SearchUtil;
import cn.gwssi.syntax.condition.Condition;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.QueryCondition;
import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.converter.ExpressionConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Multimap;
import com.google.gson.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public class SearchImpl implements Search {

    protected final static Logger log = LoggerFactory.getLogger(SearchImpl.class);

    private ISearchClientProxy httpClient;
    private SearchCondition searchCondition;

    public SearchImpl(ISearchClientProxy httpClient) {
        this.httpClient = httpClient;
        this.searchCondition = new SearchCondition(httpClient);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, Class<T> gsonClazz) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, new FieldList(), new SortFieldList(), gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, fieldList, new SortFieldList(), gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, fieldList, new SortFieldList(), gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, new FieldList(), sortFieldList, gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, new FieldList(), sortFieldList, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException {
        return this.search(indexName, typeName, queryString, defaultField, fieldList, sortFieldList, gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.search(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, Class<T> gsonClazz) throws IPException {
        return this.search(indexNames, typeNames, queryString, defaultField, new FieldList(), new SortFieldList(), gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz) throws IPException {
        return this.search(indexNames, typeNames, queryString, defaultField, fieldList, new SortFieldList(), gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz) throws IPException {
        return this.search(indexNames, typeNames, queryString, defaultField, new FieldList(), sortFieldList, gsonClazz,
                IPConstants.DEFAULT_START_ROW, IPConstants.DEFAULT_PAGE_ROW);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Class<T> gsonClazz, int from, int size) throws IPException {
        return this.search(indexNames, indexTypes, queryString, defaultField, new FieldList(), new SortFieldList(), gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, FieldList fieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        return this.search(indexNames, indexTypes, queryString, defaultField, fieldList, new SortFieldList(), gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        return this.search(indexNames, indexTypes, queryString, defaultField, new FieldList(), sortFieldList, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult searchResult = this.searchCommon(indexNames, indexTypes, queryString, defaultField, fieldList, sortFieldList, null, null, null, null, -1, from, size, false, null);
        IPSearchResult<T> result = this.handleSearchResult(searchResult, gsonClazz, false);
        result.setHasVector(searchResult.hasVector());
        return result;
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.search(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, highLightCondition, collapseCondition, scroll, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, highLightCondition, collapseCondition, scroll, null, -1, from, size, false, null);
        return this.handleSearchResult(result, gsonClazz, null != scroll);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchVector(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.searchVector(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, others, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchVector(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, null, null, null, null, 0, from, size, false, others);
        result.setHasVector(false);
        return this.handleSearchResult(result, gsonClazz, false);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchWithHL(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.searchWithHL(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, highLightCondition, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchWithHL(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, highLightCondition, null, null, null, -1, from, size, false, null);
        return this.handleSearchResult(result, gsonClazz, false);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchDistinct(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, CollapseCondition collapseCondition, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.searchDistinct(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, collapseCondition, others, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchDistinct(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, CollapseCondition collapseCondition, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, null, collapseCondition, null, null, -1, from, size, false, null);
        return this.handleSearchDistinctResult(indexNames, queryString, defaultField, null, collapseCondition, result, others, gsonClazz, size);
    }

    @Override
    public <T extends GsonObject> Map<String, List<T>> searchDistinctChildren(QueryCondition<T> queryCondition, Collection<String> values, Map<String, Object> others) throws IPException {
        Map<String, List<T>> result = new HashMap<>();

        String indexName = queryCondition.getIndexName();
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        CollapseCondition collapseCondition = queryCondition.getCollapseCondition();
        String field = collapseCondition.getField();

        // inner_hits检索
        SearchResult innerResult = this.searchDistinctChildren(indexNames, queryCondition.getQuery(), queryCondition.getDefaultField(), queryCondition.getGroups(), collapseCondition, values, others);
        if (innerResult.getTotal() <= 0) {
            return null;
        }

        // inner_hits 的结果处理：1、去掉本身 2、提取source
        Map<String, List<SearchResult.Hit<T, Void>>> hitsMap = innerResult.getInnerHitsAsMap(queryCondition.getGsonClass());
        for (Map.Entry<String, List<SearchResult.Hit<T, Void>>> entry : hitsMap.entrySet()) {
            String key = entry.getKey().trim();
            List<SearchResult.Hit<T, Void>> hits = entry.getValue();

//            for (SearchResult.Hit<T, Void> hit : hits) {
//                JSONObject recordObj = JSON.parseObject(JSON.toJSONString(hit.source));
//                final String value = recordObj.getString(field);
//                if (!key.equalsIgnoreCase(value.trim())) {
//                    children.add(hit.source);
//                }
//            }

            // FIXED 2024-04-02 field 字段的值都是一样的，不能用来过滤
            List<T> children = new ArrayList<>();
            if (hits.size() > 1) {
                children = hits.stream().skip(1).map(hit -> hit.source).collect(Collectors.toList());
            }

            result.put(key, children);
        }

        return result;
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchWithScroll(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, String scroll, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.searchWithScroll(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, scroll, others, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchWithScroll(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, String scroll, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, null, null, scroll, null, -1, from, size, false, others);
        return this.handleSearchResult(result, gsonClazz, true);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchScroll(String scrollId, String scroll, Class<T> gsonClazz) throws IPException {
        SearchScroll.Builder builder = new SearchScroll.Builder(scrollId, scroll);
        SearchResult result;
        try {
            result = httpClient.execute(builder.build());

            return this.handleSearchResult(result, gsonClazz, true);
        } catch (Exception e) {
            log.error("searchScroll异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchGroup(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);
        return this.searchGroup(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, groups, others, gsonClazz, from, size);
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> searchGroup(Collection<String> indexNames, Collection<String> typeNames, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size) throws IPException {
        SearchResult result = this.searchCommon(indexNames, typeNames, queryString, defaultField, fieldList, sortFieldList, null, null, null, groups, -1, from, size, false, others);
        return this.handleSearchResult(result, gsonClazz, false);
    }

    @Override
    public Map<String, Collection<Integer>> searchImage(String indexName, String typeName, Set<String> ids, Set<String> imagePaths, int size) throws IPException {
        long start = System.currentTimeMillis();
        Condition condition = new SearchUtil(httpClient, indexName, false).getImageInnerCondition(ids, imagePaths, size);
        long end = System.currentTimeMillis();

        log.info("检索耗时--图像 inner 检索--{}", end - start);

        SearchResult searchResult = this.searchCommon(condition, indexName, null);
        if (!searchResult.isSucceeded()) {
            SearchErrorHandler.handleSearchError(searchResult);
        }

        // 解析结果
        Map<String, Collection<Integer>> result = new HashMap<>();
        JSONArray hits = JSON.parseObject(searchResult.getJsonString()).getJSONObject("hits").getJSONArray("hits");
        for (int i = 0, length = hits.size(); i < length; i++) {

            JSONObject hit = hits.getJSONObject(i);
            String id = hit.getString("_id");
            Collection<Integer> values = (Collection<Integer>)JSONPath.read(hit.toJSONString(), "$.inner_hits.image.hits.hits[*]._nested.offset");
            result.put(id, values);
        }

        return result;
    }

    @Override
    public String searchByJson(String indexName, String typeName, String jsonQuery, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, int from, int size) throws IPException {
        checkJsonQuery(jsonQuery);
        SearchProto.Builder builder = new SearchProto.Builder(jsonQuery)
                .addIndex(indexName).addType(typeName)
                .setFrom(from).setSize(size);
        new SearchParameter().addParameter(builder, others);

        if (null != fieldList && fieldList.size() > 0) {
            for (String field : fieldList) {
                builder.addSourceIncludePattern(field);
            }
        }

        if (null != sortFieldList && sortFieldList.size() > 0) {
            List<Sort> sorts = sortConverter(sortFieldList);
            builder.addSort(sorts);
        }

        SearchResult result;
        try {
            result = httpClient.execute(builder.build());
        } catch (Exception e) {
            log.error("searchByJson异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }

        return result.getJsonString();
    }

    @Override
    public <T extends GsonObject> IPSearchResult<T> search(QueryCondition<T> queryCondition) throws IPException {
        // 不能用 sliceMax 参数来判断，否则每次递归都会触发 slice
//        // 滚动检索，并发
//        int sliceMax = queryCondition.getScrollSliceMax();
//        if (StringUtils.isNotBlank(queryCondition.getScroll()) && sliceMax > 0) {
//            this.searchScrollParallel(queryCondition, sliceMax);
//        }

        // 普通检索
        long start = System.currentTimeMillis();
        Condition condition = searchCondition.generateSearchCondition(queryCondition);
        long end = System.currentTimeMillis();

        log.info("检索条件--{}", SearchLogUtil.conditionToString(queryCondition.getQuery(), queryCondition.getFrom(), queryCondition.getSize(), queryCondition.isUnique(), queryCondition.getScroll(), queryCondition.getScrollSliceId(), queryCondition.getGroups(), queryCondition.getCollapseCondition(), queryCondition.getAggregationDims()));
        log.info("检索耗时--表达式转化--{}", end - start);
        
        SearchResult searchResult = this.searchCommon(condition, queryCondition.getIndexName(), queryCondition.getOthers());
        IPSearchResult<T> result = this.handleSearchScrollResult(searchResult, queryCondition);

        // 滚动检索，把结果消费掉
        if (StringUtils.isNotBlank(queryCondition.getScroll()) && null != queryCondition.getScrollConsumer()) {
            this.consumeScroll(result, queryCondition);
        }

        return result;
    }

    @Override
    public Long count(String indexName, String indexType, String queryString) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(indexType);

        return this.count(indexNames, typeNames, queryString);
    }

    @Override
    public Long count(Collection<String> indexNames, Collection<String> indexTypes, String queryString) throws IPException {
        checkIndexTypeParam(indexNames, indexTypes);
        String typeName = indexTypes.iterator().next();
        String indexName = indexNames.iterator().next();

        Condition condition = new Condition();
        IPCondition statementCon = new SearchUtil(httpClient, indexName, false).getStatementCondition(queryString, "", indexName, null, null, false);
        condition.setIpCondition(statementCon);

        //序列化
        String byteString = httpClient.getSerializer().writeObjectToString(condition);

        Count.Builder builder = new Count.Builder().query(byteString);
        builder.addType(typeName);
        builder.addIndex(indexName);
        Count count = builder.build();

        CountResult result;
        try {
            result = httpClient.execute(count);
        } catch (Exception e) {
            log.error("count异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }

        if (!result.isSucceeded()) {
            SearchErrorHandler.handleSearchError(result);
        }
        return result.getCount();
    }

    @Override
    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Map<String, List<String>> groups, Map<String, Object> others, int from, int size) throws IPException {
        Condition condition = searchCondition.generateSearchCondition(queryString, defaultField, fieldList, sortFieldList, highLightCondition, collapseCondition, scroll, -1, -1, groups, -1, from, size, indexName, false, others);
        return this.analysis(condition, others);
    }

    @Override
    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll, Map<String, Object> others, int from, int size) throws IPException {
        return this.analysis(indexName, typeName, queryString, defaultField, fieldList, sortFieldList, highLightCondition, collapseCondition, scroll, others, from, size);
    }

    @Override
    public String analysis(String indexName, String typeName, String queryString, String defaultField, FieldList fieldList, SortFieldList sortFieldList, Map<String, Object> others, int from, int size) throws IPException {
        return this.analysis(indexName, typeName, queryString, defaultField, fieldList, sortFieldList, null, null, null, others, from, size);
    }

    @Override
    public String analysis(QueryCondition queryCondition) throws IPException {
        Condition condition = searchCondition.generateSearchCondition(queryCondition);
        return this.analysis(condition, queryCondition.getOthers());
    }

    @Override
    public ParserError checkExpress(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others) throws IPException {
        ParserError error = ExpressionConverter.getInstance().doCheck(queryString, defaultField);
        if (null != error) {
            error.setHasError(true);
            return error;
        }

        boolean hasVector = false;
        try {
            SearchUtil searchUtil = new SearchUtil(httpClient, indexName, false);
            searchUtil.getStatementCondition(queryString, defaultField, indexName, null, others, true);
            hasVector = searchUtil.hasVector();
        } catch (IPException e) {
            String errMessage = e.getMessage();

            // 位置信息重组
            Object param = e.getParamObject();
            if (param instanceof ConditionOffset) {
                ConditionOffset offset = (ConditionOffset) param;
                error = new ParserError(offset, errMessage, "");
                error.setErrCode(e.getErrCode());
            }

            if (null == error) {
                error = new ParserError(errMessage);
            }
        }

        if (null == error) {
            error = new ParserError();
            error.setWholeMsg("");
            error.setErrCode("");
        } else {
            error.setHasError(true);
        }

        error.setHasVector(hasVector);
        return error;
    }

    @Override
    public Set<String> extractValues(String query) throws IPException {
        return ExpressionConverter.getInstance().doExtract(query);
    }

    @Override
    public Multimap<String, String> extractValues(String query, String defaultField) throws IPException {
        return ExpressionConverter.getInstance().doExtract(query, defaultField);
    }

    @Override
    public Multimap<String, String> extractValues(String query, String defaultField, Set<String> includes, Set<String> excludes) throws IPException {
        Multimap<String, String> values = this.extractValues(query, defaultField);
        Set<String> keys = values.keySet();
        if (null != includes) {
            keys.retainAll(includes);
        }
        if (null != excludes) {
            keys.removeAll(excludes);
        }

        return values;
    }

    private void checkJsonQuery(String jsonQuery) throws IPException {
        JsonObject queryObject = null;
        try {
            queryObject = GSON.fromJson(jsonQuery, JsonObject.class);
        } catch (Exception e) {
            throw ErrorCodes.ES_QUERY_JSON_SYNAX_ERROR.exception("json格式不正确");
        }

        if (null != queryObject && !queryObject.has("query")) {
            throw ErrorCodes.ES_QUERY_JSON_SYNAX_ERROR.exception("必须包含 query 节点");
        }
    }

    private void checkIndexTypeParam(Collection<String> indexNames, Collection<String> indexTypes) throws IPException {
        if (indexNames == null || indexNames.isEmpty()) {
            throw ErrorCodes.ES_QUERY_INDEX_NAME_NULL.exception();
        }

        if (indexTypes == null || indexTypes.isEmpty()) {
            throw ErrorCodes.ES_QUERY_TYPE_NAME_NULL.exception();
        }
    }

    private static final Gson GSON = new GsonBuilder().addSerializationExclusionStrategy(new ExclusionStrategy() {
        @Override
        public boolean shouldSkipField(FieldAttributes f) {
            // 这里作判断，决定要不要排除该字段,return true为排除
            // 按字段名排除
            return "_id".compareToIgnoreCase(f.getName()) == 0 || "_index".compareToIgnoreCase(f.getName()) == 0
                    || "_type".compareToIgnoreCase(f.getName()) == 0 || "score".compareToIgnoreCase(f.getName()) == 0
                    || "_sort".compareToIgnoreCase(f.getName()) == 0;
        }

        @Override
        public boolean shouldSkipClass(Class<?> clazz) {
            return false;
        }
    }).create();

    private SearchResult searchCommon(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField,
                                      FieldList fieldList, SortFieldList sortFieldList, HighLightCondition highLightCondition, CollapseCondition collapseCondition, String scroll,
                                      Map<String, List<String>> groups, int windowSize, int from, int size, boolean isUnique, Map<String, Object> others) throws IPException {
        checkIndexTypeParam(indexNames, indexTypes);
        String indexName = indexNames.iterator().next();

        long start = System.currentTimeMillis();
        Condition condition = searchCondition.generateSearchCondition(queryString, defaultField, fieldList, sortFieldList, highLightCondition, collapseCondition, scroll, -1, -1, groups, windowSize, from, size, indexName, isUnique, others);
        long end = System.currentTimeMillis();

        log.info("检索条件--{}", SearchLogUtil.conditionToString(queryString, from, size, isUnique, scroll, -1, groups, collapseCondition, null));
        log.info("检索耗时--表达式转化--{}", end - start);

        return this.searchCommon(condition, indexName, others);
    }

    private SearchResult searchCommon(Condition condition, String indexName, Map<String, Object> others) throws IPException {
        //序列化
        String byteString = httpClient.getSerializer().writeObjectToString(condition);

        cn.gwssi.data.service.core.Search.Builder builder = new cn.gwssi.data.service.core.Search.Builder(byteString);
        builder.addType(indexName);
        builder.addIndex(indexName);
        new SearchParameter().addParameter(builder, others);
        cn.gwssi.data.service.core.Search search = builder.build();

        try {
            long start = System.currentTimeMillis();
            SearchResult result = httpClient.execute(search);
            long end = System.currentTimeMillis();
            log.info("检索耗时--http响应--{}", end - start);

            result.setHasVector(condition.hasVector());
            return result;
        } catch (Exception e) {
            log.error("search异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }
    }

    // 滚动检索，并发
    private <T extends GsonObject> void searchScrollParallel(QueryCondition<T> queryCondition, int sliceMax) throws IPException {
        queryCondition.setScrollSliceMax(sliceMax);

        // 创建线程池
        final CountDownLatch count = new CountDownLatch(sliceMax);
        final ExecutorService executorService = Executors.newFixedThreadPool(sliceMax);

        for (int index = 0; index < sliceMax; index++) {
            final int number = index;

            Callable<Exception> callable = () -> {
                // 设置 slice 参数
                queryCondition.setScrollSliceId(number);

                this.search(queryCondition);
                count.countDown();
                return null;
            };

            executorService.submit(callable);
        }

        // 等待执行完毕，关闭线程池
        try {
            count.await();
        } catch (InterruptedException e) {
            throw ErrorCodes.ES_QUERY_ERROR4.exception();
        }

        executorService.shutdown();
    }

    // 滚动查询
    private <T extends GsonObject> void consumeScroll(IPSearchResult<T> result, QueryCondition<T> queryCondition) throws IPException {
        // 消费第一次查询结果
        if (result.getTotal() <= 0 || !queryCondition.getScrollConsumer().consume(result)) {
            return;
        }

        // 重新查询并消费
        String scrollId = result.getScrollId();
        while (true) {
            result = this.searchScroll(scrollId, queryCondition.getScroll(), queryCondition.getGsonClass());
            if (result.getRecords().size() <= 0 || !queryCondition.getScrollConsumer().consume(result)) {
                break;
            }
        }
    }

    // 滚动检索结果处理
    private <T extends GsonObject> IPSearchResult<T> handleSearchScrollResult(SearchResult result, QueryCondition<T> queryCondition) throws IPException {
        if (!result.isSucceeded()) {
            SearchErrorHandler.handleSearchError(result);
        }

        // 语义排序强制设置为 false，结果中不做处理
        if (queryCondition.isVectorSort()) {
            result.setHasVector(false);
        }
        // 结果处理
        boolean hasScroll = StringUtils.isNotBlank(queryCondition.getScroll());
        IPSearchResult<T> searchResult = this.handleSearchResult(result, queryCondition.getGsonClass(), hasScroll);
        String scrollId = searchResult.getScrollId();

        // 存在合并时做二次查询，这时候会把 scroll 丢掉，需要重新补上
        if (null != queryCondition.getCollapseCondition()) {
            CollapseCondition collapseCondition = queryCondition.getCollapseCondition().clone();
            searchResult = this.handleSearchDistinctResult(Collections.singleton(queryCondition.getIndexName()), queryCondition.getQuery(), queryCondition.getDefaultField(), queryCondition.getGroups(), collapseCondition, result, queryCondition.getOthers(), queryCondition.getGsonClass(), queryCondition.getSize());
            searchResult.setScrollId(scrollId);
        }

        searchResult.setHasVector(result.hasVector());
        return searchResult;
    }

    private <T extends GsonObject> IPSearchResult<T> handleSearchResult(SearchResult result, Class<T> gsonClazz, boolean hasScroll) throws IPException {
        if (!result.isSucceeded()) {
            SearchErrorHandler.handleSearchError(result);
        }

        long took = result.getTook();
        log.info("检索耗时--es took--{}", took);

        long total = result.getTotal();
        if (result.hasVector()) {
            total = Math.min(total, IPConstants.VECTOR_MAX_ROW);
        }

        List<SearchResult.Hit<T, Void>> hitsList = result.getHits(gsonClazz);
        List<T> list = null;

        // 语义检索，重新计算分数
        if (hitsList.size() > 0 && result.hasVector()) {
            double score_max = hitsList.get(0).score;

            // 重新计算分数。规则为 x-min/max-min
            if (score_max >= 1) {
                double score_min = hitsList.get(hitsList.size() - 1).score;
                double score_diff = score_max - score_min;

                list = hitsList.stream().map(hit -> {
                    double score = NumberUtil.calScore(hit.score, score_min, score_diff);
                    T source = hit.source;
                    source.setScore(score);
                    source.setRouting(hit.routing);
                    return source;
                }).collect(Collectors.toList());
            }
        }

        if (null == list) {
            list = hitsList.stream().map(hit -> {
                T source = hit.source;
                if (null == source) {
                    log.error("search解析结果异常，具体错误请查看相关日志：：：：：：：：Unhandled exception occurred while converting source to the object：：：：：：：：");
                    throw new RuntimeException(ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG).getMessage());
                }
                source.setScore(hit.score);
                source.setRouting(hit.routing);
                return source;
            }).collect(Collectors.toList());
        }

        IPSearchResult<T> ipResult = new IPSearchResult<>();
        ipResult.setTotal(total);
        ipResult.setTook(took);
        ipResult.setRecords(list);

        if (hasScroll) {
            String scrollId = result.getScrollId();
            ipResult.setScrollId(scrollId);
        }

        return ipResult;
    }

    private <T extends GsonObject> IPSearchResult<T> handleSearchDistinctResult(Collection<String> indexNames, String query, String defaultField, Map<String, List<String>> groups, CollapseCondition condition, SearchResult searchResult, Map<String, Object> others, Class<T> gsonClazz, int size) throws IPException {
        if (!searchResult.isSucceeded()) {
            SearchErrorHandler.handleSearchError(searchResult);
        }

        IPSearchResult<T> result = this.handleSearchResult(searchResult, gsonClazz, false);
        // FIXED 2023-12-07 语义检索后的合并，传参 size 是 2000。inner_hits 需要的内存太大，这时候不再去取 inner_hits，走另一个接口分页取
        if (result.getTotal() <= 0 || size >= IPConstants.VECTOR_MAX_ROW) {
            List<T> records = CollapseUtil.getInstance().distinctWithoutES(result.getRecords(), condition);
            result.setRecords(records);
            result.setTotal(records.size());
            return result;
        }

        // FIXED 2024-08-29 size 设置 -1 时，不再去取 inner_hits
        if (condition.getSize() < 0) {
            return result;
        }

        // 判断合并字段是否有值
        String field = condition.getField();
        Collection<String> values = (Collection<String>) JSONPath.read(searchResult.getJsonString(), "$.hits.hits[*]._source." + field);
        values = values.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (values.size() <= 0) {
            return result;
        }

        // 重新查询
        SearchResult innerResult = this.searchDistinctChildren(indexNames, query, defaultField, groups, condition, values, others);
        if (innerResult.getTotal() <= 0) {
            return result;
        }

        // inner_hits 的结果作为 children 放到 hits 中
        Map<String, List<SearchResult.Hit<T, Void>>> hitsMap = innerResult.getInnerHitsAsMap(gsonClazz);
        result.getRecords().forEach(record -> {
            JSONObject recordObj = JSON.parseObject(JSON.toJSONString(record));
            final String id = recordObj.getString("pid");
            final String routing = recordObj.getString("routing");
            final String value = recordObj.getString(field);

            List<SearchResult.Hit<T, Void>> hits = hitsMap.get(value);
            if (null != hits) {
                hits = hitsMap.get(value);
                if (null != hits) {
                    record.setChildren(hits.stream().filter(hit -> routing.equals(hit.routing) && !hit.id.equals(id)).map(hit -> hit.source).collect(Collectors.toList()));
                }
            }
        });

        return result;
    }

    // 取合并后的 inner_hits
    private SearchResult searchDistinctChildren(Collection<String> indexNames, String query, String defaultField, Map<String, List<String>> groups, CollapseCondition condition, Collection<String> values, Map<String, Object> others) throws IPException {
        String field = condition.getField();

        // 用 innerHits 的条件来区分是第一次查询还是第二次查询
        FieldList fieldList = new FieldList();
        fieldList.add("pid");
        fieldList.add(field);

        InnerHitCondition inner = new InnerHitCondition(field);
        inner.setFieldList(fieldList);
        inner.setSortFieldList(condition.getSorts());
        inner.setFrom(0);
        inner.setSize(condition.getSize());
        condition.addInnerHits(inner);

        // 表达式类似 anm in (1,2,3)。把原来的表达式拼进去，组内也需要过滤
//        JSONArray values = JSONPath.read(searchResult.getJsonString(), "$.hits.hits[*]._source." + field, List.class);
        query = field + " in (" + StringUtils.join(values, ",") + ") AND (" + query + ")";

        // 重新查询
        SearchResult innerResult = this.searchCommon(indexNames, indexNames, query, defaultField, new FieldList(new String[]{}), null, null, condition, null, groups, -1, 0, values.size(), false, others);
        if (!innerResult.isSucceeded()) {
            SearchErrorHandler.handleSearchError(innerResult);
        }

        return innerResult;
    }

//    private <T extends GsonObject> List<SearchResult.Hit<T, Void>> handleSearchDistinctResult(SearchResult result, Class<T> gsonClazz) {
//        List<SearchResult.Hit<T, Void>> list = new ArrayList<>();
//        List<TermsAggregation.Entry> buckets = result.getAggregations().getTermsAggregation("hits").getBuckets();
//        if (null != buckets && buckets.size() > 0) {
//            for (TermsAggregation.Entry entry : buckets) {
//
//                // 只返回第一个对象，其余的作为 children 放到第一个对象中
//                List<SearchResult.Hit<T, Void>> hits = entry.getTopHitsAggregation("hit").getHits(gsonClazz);
//                if (null != hits) {
//                    SearchResult.Hit<T, Void> hit = hits.get(0);
//                    if (hits.size() > 1) {
//
//                        List<T> others = hits.stream().skip(1).map(other -> other.source).collect(Collectors.toList());
//                        hit.source.setChildren(others);
//                    }
//
//                    list.add(hit);
//                }
//            }
//        }
//
//        return list;
//    }
//
//    private <T extends GsonObject> IPSearchResult<T> handleSearchVectorResult(SearchResult result, SortFieldList sortFieldList, Class<T> gsonClazz, int from, int size) throws IPException {
//        if (!result.isSucceeded()) {
//            throw ErrorCodes.ES_QUERY_ERROR1.exception(result.getErrorMessage());
//        }
//
//        long total = result.getTotal();
//        total = Math.min(IPConstants.VECTOR_MAX_ROW, total);
//
//        // 取排序字段
//        String field = null;
//        boolean isAsc = false;
//        if (null != sortFieldList && sortFieldList.size() > 0) {
//            for (SortField sortField : sortFieldList) {
//                if (!"_score".equals(sortField.getFieldName())) {
//                    field = sortField.getFieldName();
//                    isAsc = SortField.IPSorting.ASC.equals(sortField.getOrder());
//                    break;
//                }
//            }
//        }
//
//        // 排序，分页，设置score，转化为对象
//        final String fieldFinal = field;
//        boolean isAscFinal = isAsc;
//        List<SearchResult.Hit<T, Void>> hitsList = result.getHits(gsonClazz);
//        List<T> list = hitsList.stream()
//                .sorted((data1, data2) -> this.compareObject(data1.source, data2.source, fieldFinal, isAscFinal))
//                .skip(from).limit(size)
//                .map(hit -> {
//                    T source = hit.source;
//                    source.setScore(hit.score);
//                    return source;
//                }).collect(Collectors.toList());
//
//        long took = result.getTook();
//
//        IPSearchResult<T> ipResult = new IPSearchResult<>();
//        ipResult.setTotal(total);
//        ipResult.setTook(took);
//        ipResult.setRecords(list);
//
//        return ipResult;
//    }
//
//    // 处理特定类型的错误
//    private void handleSearchError(SearchResult result) throws IPException {
//        Collection errorTypes = (Collection) JSONPath.read(result.getErrorMessage(), "..caused_by.type");
//        if (errorTypes.size() > 0) {
//            if (errorTypes.contains(IPConstants.ERROR_MAX_CLAUSES)) {
//                throw ErrorCodes.ES_QUERY_ERROR2.exception();
//            } else if (errorTypes.contains(IPConstants.ERROR_MAX_BUCKETS)) {
//                throw ErrorCodes.ES_QUERY_ERROR3.exception();
//            }
//        }
//
//        // 解析异常，向下解析
//        String errorReason = result.getErrorMessage();
//        if (!result.isSucceeded()) {
//            JsonObject jsonObject = result.getJsonObject();
//
//            if (jsonObject.has("msg")) {
//                errorReason = jsonObject.get("msg").getAsString();
//            }
//
//            if (jsonObject.has("error")) {
//                JsonObject error = jsonObject.getAsJsonObject("error");
//                errorReason = error.toString();
//
//                if (error.has("reason")) {
//                    errorReason = error.get("reason").getAsString();
//                }
//
//                if (error.has("root_cause")) {
//                    JsonElement rootCause = error.get("root_cause");
//                    JsonObject root = null;
//                    if (rootCause instanceof JsonObject) {
//                        root = (JsonObject) rootCause;
//                    } else if (rootCause instanceof JsonArray) {
//                        JsonArray roots = (JsonArray) rootCause;
//                        root = roots.get(roots.size() - 1).getAsJsonObject();
//                    }
//
//                    if (null != root && root.has("reason")) {
//                        errorReason = root.get("reason").getAsString();
//                    }
//                }
//            }
//        }
//
//        throw ErrorCodes.ES_QUERY_ERROR1.exception(errorReason);
//    }

    private String analysis(Condition condition, Map<String, Object> others) throws IPException {
        String byteString = httpClient.getSerializer().writeObjectToString(condition);

        ISearchResult result;
        try {
            Analysis.Builder builder = new Analysis.Builder(byteString);
            new SearchParameter().addParameter(builder, others);
            result = httpClient.execute(builder.build());
        } catch (IOException e) {
            log.error("analysis异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }
        return result.getJsonString();
    }

    private List<Sort> sortConverter(List<SortField> sortFields) {
        List<Sort> sorts = new ArrayList<>();
        if (null != sortFields && sortFields.size() > 0) {
            for (SortField sortField : sortFields) {
                sorts.add(sortConverter(sortField));
            }
        }

        return sorts;
    }

    private Sort sortConverter(SortField sortField) {
        Sort.Sorting order = Sort.Sorting.DESC;
        if (sortField.getOrder() == SortField.IPSorting.ASC) {
            order = Sort.Sorting.ASC;
        }

        return new Sort(sortField.getFieldName().toLowerCase(), order);
    }

//    private int compareObject(Object obj1, Object obj2, final String field, final boolean isAsc) {
//        JSONObject object1 = JSON.parseObject(JSON.toJSONString(obj1));
//        JSONObject object2 = JSON.parseObject(JSON.toJSONString(obj2));
//        String value1 = (null == object1.get(field)) ? null : object1.getString(field);
//        String value2 = (null == object2.get(field)) ? null : object2.getString(field);
//
//        if (StringUtils.isBlank(value1) && StringUtils.isBlank(value2)) {
//            return 0;
//        } else if (StringUtils.isBlank(value1)) {
//            return 1;
//        } else if (StringUtils.isBlank(value2)) {
//            return -1;
//        } else {
//            if (NumberUtils.isCreatable(value1) && NumberUtils.isCreatable(value2)) {
//                return isAsc ?
//                        (int) (Double.parseDouble(value1) - Double.parseDouble(value2)) :
//                        (int) (Double.parseDouble(value2) - Double.parseDouble(value1));
//            } else {
//                return isAsc ?
//                        value1.compareTo(value2) :
//                        value2.compareTo(value1);
//            }
//        }
//    }

}
