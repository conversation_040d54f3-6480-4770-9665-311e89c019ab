package cn.gwssi.data.service.client;

import cn.gwssi.data.service.core.*;

import java.util.LinkedHashMap;
import java.util.Map;

public class Credential {
    String username;
    String password;

    public Count.Builder addCredential(Count.Builder builder)
    {
        if (credentialAvailable())
            return builder.setHeader(makeCredential());

        return builder;
    }

    public Bulk.Builder addCredential(Bulk.Builder builder)
    {
        if(credentialAvailable())
            return builder.setHeader(makeCredential());

        return builder;
    }

    public Index.Builder addCredential(Index.Builder builder)
    {
        if(credentialAvailable())
            return builder.setHeader(makeCredential());

        return builder;
    }

    public Search.Builder addCredential(Search.Builder builder)
    {
        if(credentialAvailable())
            return builder.setHeader(makeCredential());

        return builder;
    }

    public Update.Builder addCredential(Update.Builder builder)
    {
        if(credentialAvailable())
            return builder.setHeader(makeCredential());

        return builder;
    }

    private boolean credentialAvailable()
    {
        if (this.username != null && this.password != null)
        {
            return true;
        }

        return false;
    }

    private Map<String, Object> makeCredential()
    {
        Map<String, Object> map = new LinkedHashMap<>();

        map.put("un", this.username);

        map.put("pw", this.password);

        return map;
    }
}
