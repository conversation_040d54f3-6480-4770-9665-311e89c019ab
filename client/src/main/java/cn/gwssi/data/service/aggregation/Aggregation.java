package cn.gwssi.data.service.aggregation;


import cn.gwssi.common.common.pojo.*;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.syntax.condition.QueryCondition;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface Aggregation {

    @Deprecated
    public List<OneDimCountResult> oneDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim) throws IPException;

    @Deprecated
    public Map<String, List<OneDimCountResult>> oneDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    @Deprecated
    public List<OneDimCountResult> oneDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim) throws IPException;

    @Deprecated
    public List<OneDimCountResult> oneDimCount(QueryCondition condition) throws IPException;

    @Deprecated
    public List<TwoDimCountResult> twoDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim1, AggregationDim aggregationDim2) throws IPException;

    @Deprecated
    public List<TwoDimCountResult> twoDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim1, AggregationDim aggregationDim2) throws IPException;

    @Deprecated
    public List<TwoDimCountResult> twoDimCount(QueryCondition condition) throws IPException;

    @Deprecated
    public List<MultiDimCountResult> multiDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    @Deprecated
    public List<MultiDimCountResult> multiDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    @Deprecated
    public List<MultiDimCountResult> multiDimCount(QueryCondition condition) throws IPException;

    /*********************************any dim count***********************************/

    public List<DimCountResult> dimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCount(String indexName, String typeName, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCountWithGroup(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCountWithGroup(String indexName, String typeName, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException;

    public List<DimCountResult> dimCount(QueryCondition condition) throws IPException;

    /*********************************search and aggregation***********************************/

    public <T extends GsonObject> OneDimSearchResult<T> oneDimCountWithSearch(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, Class<T> gsonClazz, int from, int size, AggregationDim aggregationDim) throws IPException;

    public <T extends GsonObject> OneDimSearchResult<T> oneDimCountWithSearch(String indexName, String typeName, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size, AggregationDim aggregationDim) throws IPException;

}
