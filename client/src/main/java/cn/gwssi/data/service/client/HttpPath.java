package cn.gwssi.data.service.client;

public interface HttpPath
{
    /**
     * 项目根路径
     */
    String ROOT = "/_isearch";

    /**
     * 索引文档入口
     */
    String INDEX = ROOT + "/_indx";

    /**
     * 更新入口
     */
    String UPDATE = ROOT + "/_updt";

    /**
     * 删除入口
     */
    String DELETE = ROOT + "/_del";

    /**
     * 原生es检索入口
     */
    String SEARCH = ROOT + "/_srch";

    /**
     * 表达式检索入口
     */
    String QUERY = ROOT + "/_query";

    /**
     * 批量操作入口
     */
    String BULK = ROOT + "/_bk";

    /**
     * count入口
     */
    String COUNT = ROOT + "/_count";

    /**
     * analysis入口
     */
    String ANALYSIS = ROOT + "/_analysis";

    /**
     * scroll 入口
     */
    String SCROLL = ROOT + "/scroll";

    /**
     * 集群健康接口
     */
    String CLUSTER_HEALTH = ROOT + "/cluster/health";

    /**
     * 集群状态
     */
    String CLUSTER_STATE = ROOT + "/cluster/state";

    /**
     * 集群状态
     */
    String CLUSTER_STATS = ROOT + "/cluster/stats";

    /**
     * 集群节点信息
     */
    String NODES_INFO = ROOT + "/nodes";

    /**
     * 集群更新settings
     */
    String CLUSTER_UPDATE_SETTINGS = ROOT + "/cluster/settings";

    /**
     * 集群状态
     */
    String NODES_STATS = ROOT +"/nodes";

    /**
     * 索引mappings
     */
    String INDEX_MAPPINGS = ROOT + "/mappings";

    /**
     * 索引统计
     */
    String INDEX_STATS = "/stats";

    /**
     * 索引是否存在
     */
    String INDEX_EXISTS = ROOT + "/exists";

    /**
     * 索引更新settings
     */
    String INDEX_UPDATE_SETTINGS = ROOT + "/settings";

}
