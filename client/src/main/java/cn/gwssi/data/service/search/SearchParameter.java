package cn.gwssi.data.service.search;

import cn.gwssi.data.service.action.AbstractMultiIndexActionBuilder;
import cn.gwssi.data.service.params.Parameters;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class SearchParameter {

    private final static String[] params = new String[]{Parameters.MAX_CONCURRENT_SHARD_REQUESTS, Parameters.TRACK_SCORES};

    // others 的参数转化为 parameter，追加到请求的 url 后
    public void addParameter(AbstractMultiIndexActionBuilder builder, Map<String, Object> others) {
        if (null != others) {
            for (String param : params) {
                if (null != others.get(param)) {
                    String value = others.get(param).toString();
                    if (StringUtils.isNotBlank(value)) {
                        builder.setParameter(param, value);
                    }
                }
            }
        }
    }

}
