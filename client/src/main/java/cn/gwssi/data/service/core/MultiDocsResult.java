package cn.gwssi.data.service.core;

import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.data.service.client.ISearchResult;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.List;

public class MultiDocsResult<T extends GsonObject> extends ISearchResult {

    public MultiDocsResult(ISearchResult source) {
        super(source);
    }

    public MultiDocsResult(Gson gson) {
        super(gson);
    }

    public long getTotal(){
        JsonArray jsonElements = this.getJsonObject().getAsJsonArray("docs");
        long total = 0;
        for (JsonElement jsonElement : jsonElements){
            boolean found = jsonElement.getAsJsonObject().get("found").getAsBoolean();
            if(found){
                total++;
            }
        }
        return total;
    }


    public List<T> getMultiDocsResult(Class<T> tClass){
        List<T> sourceObject = new ArrayList<>();
        JsonArray jsonArray = this.getJsonObject().getAsJsonArray("docs");
        T object = null;
        for (JsonElement jsonElement : jsonArray){
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            if (jsonObject.has("_source")){
                jsonObject.getAsJsonObject("_source").addProperty("index",jsonObject.get("_index").getAsString());
                jsonObject.getAsJsonObject("_source").addProperty("type",jsonObject.get("_type").getAsString());
                jsonObject.getAsJsonObject("_source").addProperty("id",jsonObject.get("_id").getAsString());
                object = createSourceObject(jsonObject.getAsJsonObject("_source"),tClass);
                if (null != object){
                    sourceObject.add(object);
                }
            }
        }
         return sourceObject;
    }

}
