package cn.gwssi.data.service.cluster.admin.indices;

import cn.gwssi.data.service.action.AbstractMultiIndexActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import com.google.common.base.Joiner;

/**
 * <AUTHOR>
 * <AUTHOR> keser
 */
public class IndicesStatsRequest extends GenericResultAbstractAction {

    protected IndicesStatsRequest(Builder builder) {
        super(builder);
//        indexName = builder.getJoinedIndices();
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        String superUri = super.buildURI(elasticsearchVersion);
        return HttpPath.ROOT + (superUri.length() == 0 ? "" : "/") + superUri + HttpPath.INDEX_STATS;
    }

    public enum Metric {
        CLEAR("clear"),
        DOCS("docs"),
        STORE("store"),
        INDEXING("indexing"),
        GET("get"),
        WARMER("warmer"),
        MERGE("merge"),
        FLUSH("flush"),
        REFRESH("refresh"),
        SEARCH("search"),
        COMPLETION("completion"),
        FIELDDATA("fielddata"),
        REQUEST_CACHE("request_cache"),
        SUGGEST("suggest"),
        TRANSLOG("translog");

        private String value;

        Metric(String value) {
            this.value = value;
        }

        public String toString() {
            return this.value;
        }
    }

    public static class Builder extends AbstractMultiIndexActionBuilder<IndicesStatsRequest, Builder> {

        public Builder addMetrics(Metric... metrics) {
            if (null != metrics && metrics.length > 0) {
                for (Metric metric : metrics) {

                    // 除 clear 之外，都调用 toggleApiParameter 方法
                    // TODO indexing 和 search 可能需要其它参数，用到时再加
                    if (metric == Metric.CLEAR) {
                        this.clear(true);
                    } else {
                        this.addCleanApiParameter(metric.toString());
                    }
                }
            }

            return this;
        }

        public Builder clear(boolean clear) {
            return setParameter("clear", clear);
        }

        public Builder docs(boolean docs) {
            return toggleApiParameter("docs", docs);
        }

        public Builder store(boolean store) {
            return toggleApiParameter("store", store);
        }

        public Builder indexing(boolean indexing) {
            return toggleApiParameter("indexing", indexing);
        }

        public Builder indexing(boolean indexing, String... types) {
            toggleApiParameter("indexing", indexing);
            setParameter("types", Joiner.on(",").join(types));
            return this;
        }

        public Builder get(boolean get) {
            return toggleApiParameter("get", get);
        }

        public Builder warmer(boolean warmer) {
            return toggleApiParameter("warmer", warmer);
        }

        public Builder merge(boolean merge) {
            return toggleApiParameter("merge", merge);
        }

        public Builder flush(boolean flush) {
            return toggleApiParameter("flush", flush);
        }

        public Builder refresh(boolean refresh) {
            return toggleApiParameter("refresh", refresh);
        }

        public Builder search(boolean search) {
            return toggleApiParameter("search", search);
        }

        public Builder search(boolean search, String... groups) {
            toggleApiParameter("search", search);
            setParameter("groups", Joiner.on(",").join(groups));
            return this;
        }

        public Builder completion(boolean completion) {
            return toggleApiParameter("completion", completion);
        }

        public Builder fielddata(boolean fielddata) {
            return toggleApiParameter("fielddata", fielddata);
        }

        public Builder requestCache(boolean requestCache) {
            return toggleApiParameter("request_cache", requestCache);
        }

        public Builder suggest(boolean suggest) {
            return toggleApiParameter("suggest", suggest);
        }

        public Builder translog(boolean translog) {
            return toggleApiParameter("translog", translog);
        }

        // TODO add "search with groups" parameter

        @Override
        public IndicesStatsRequest build() {
            return new IndicesStatsRequest(this);
        }
    }
}