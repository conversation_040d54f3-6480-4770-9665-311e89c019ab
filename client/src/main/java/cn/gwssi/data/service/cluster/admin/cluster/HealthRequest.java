package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractMultiIndexActionBuilder;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import com.google.common.base.Preconditions;

public class HealthRequest extends GenericResultAbstractAction {

    protected HealthRequest(Builder builder) {
        super(builder);
    }

    @Override
    public String getPathToResult() {
        return null;
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        return HttpPath.CLUSTER_HEALTH + "/" + super.buildURI(elasticsearchVersion);
    }

    public static class Builder extends AbstractMultiIndexActionBuilder<HealthRequest, Builder> {
        public Builder waitForNoRelocatingShards() {
            return waitForNoRelocatingShards(true);
        }

        public Builder waitForNoRelocatingShards(boolean wait) {
            return setParameter("wait_for_no_relocating_shards", wait);
        }

        public Builder waitForStatus(ClusterHealthParamsBuilder.Status status) {
            return waitForStatus(status.getValue());
        }

        private Builder waitForStatus(String status) {
            return setParameter("wait_for_status", status);
        }

        public Builder level(ClusterHealthParamsBuilder.Level level) {
            return level(level.getValue());
        }

        private Builder level(String level) {
            return setParameter("level", level);
        }

        public Builder local(boolean local) {
            return setParameter("local", local);
        }

        public Builder local() {
            return local(true);
        }

        public Builder timeout(int seconds) {
            Preconditions.checkArgument(seconds >= 0, "seconds must not be negative");
            return setParameter("timeout", seconds + "s");
        }

        @Override
        public HealthRequest build() {
            return new HealthRequest(this);
        }
    }

}
