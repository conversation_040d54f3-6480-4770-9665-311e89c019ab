package cn.gwssi.data.service.aggregation;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.*;
import cn.gwssi.common.common.pojo.aggregation.AggeragationCardinalityDim;
import cn.gwssi.common.common.pojo.aggregation.AggrOrder;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationFiltersDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.condition.filter.util.IPTypeUtil;
import cn.gwssi.data.common.ErrorCodes;
import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.core.SearchResult;
import cn.gwssi.data.service.core.search.aggregation.*;
import cn.gwssi.data.service.search.SearchParameter;
import cn.gwssi.data.util.FeatureClient;
import cn.gwssi.data.util.SearchErrorHandler;
import cn.gwssi.data.util.SearchLogUtil;
import cn.gwssi.data.util.SearchUtil;
import cn.gwssi.meta.IPColumn;
import cn.gwssi.meta.IPField;
import cn.gwssi.meta.IPMultiField;
import cn.gwssi.meta.IPType;
import cn.gwssi.syntax.condition.Condition;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.QueryCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.meta.IPColumnType;
import cn.gwssi.xparser.IPDaoFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class AggregationImpl implements Aggregation {

    private static final ThreadLocal<Map<String, Integer>> threadLocalMap = ThreadLocal.withInitial(() -> new ConcurrentHashMap<>());
    private final static Logger logger = LoggerFactory.getLogger(AggregationImpl.class);
    private ISearchClientProxy httpClient;

    public AggregationImpl(ISearchClientProxy httpClient) {
        this.httpClient = httpClient;
    }

    @Deprecated
    @Override
    public List<OneDimCountResult> oneDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);

        return this.oneDimCount(indexNames, typeNames, queryString, defaultField, others, aggregationDim);
    }

    @Deprecated
    @Override
    public Map<String, List<OneDimCountResult>> oneDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);

        for (AggregationDim agg : aggregationDims) {
            this.checkAndHandleDim(indexNames, agg);
        }

        SearchResult searchResult = this.searchResult(indexNames, typeNames, queryString, defaultField, null, null, false, others, 0, 0, aggregationDims);
        long total = searchResult.getTotal();
        MetricAggregation metricAggr = searchResult.getAggregations();

        Map<String, List<OneDimCountResult>> result = new HashMap<>();
        if (metricAggr != null) {
            for (AggregationDim dim : aggregationDims) {
                BucketAggregation aggregation = getBucketAggregation(dim, metricAggr);
                List<DimCountResult> aggResults = this.extractResult(aggregation, dim, total);
                List<OneDimCountResult> aggResultFormat = aggResults.stream().map(this::toOne).collect(Collectors.toList());
                result.put(dim.getFieldName(), aggResultFormat);
            }
        }

        return result;
    }

    @Deprecated
    @Override
    public List<TwoDimCountResult> twoDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim1, AggregationDim aggregationDim2) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> indexTypes = new ArrayList<>();
        indexTypes.add(typeName);

        return this.twoDimCount(indexNames, indexTypes, queryString, defaultField, others, aggregationDim1, aggregationDim2);
    }


    @Deprecated
    @Override
    public List<MultiDimCountResult> multiDimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> indexTypes = new ArrayList<>();
        indexTypes.add(typeName);

        return this.multiDimCount(indexNames, indexTypes, queryString, defaultField, others, aggregationDims);
    }

    @Override
    public <T extends GsonObject> OneDimSearchResult<T> oneDimCountWithSearch(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, Class<T> gsonClazz, int from, int size, AggregationDim aggregationDim) throws IPException {
        return this.oneDimCountWithSearch(indexName, typeName, queryString, defaultField, others, gsonClazz, from, size, aggregationDim);
    }

    @Override
    public <T extends GsonObject> OneDimSearchResult<T> oneDimCountWithSearch(String indexName, String typeName, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, Class<T> gsonClazz, int from, int size, AggregationDim aggregationDim) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);
        ArrayList<String> typeNames = new ArrayList<>();
        typeNames.add(typeName);

        this.checkAndHandleDim(indexNames, aggregationDim);

        SearchResult searchResult = this.searchResult(indexNames, typeNames, queryString, defaultField, null, groups, false, others, from, size, aggregationDim);
        long total = searchResult.getTotal();

        List<OneDimCountResult> aggResult = new ArrayList<>();
        MetricAggregation metricAggr = searchResult.getAggregations();
        if (metricAggr != null) {
            BucketAggregation aggregation = getBucketAggregation(aggregationDim, metricAggr);
            List<DimCountResult> aggResults = this.extractResult(aggregation, aggregationDim, total);
            aggResult = aggResults.stream().map(this::toOne).collect(Collectors.toList());
        }

        IPSearchResult<T> searchResult_ = this.handleSearchResult(searchResult, gsonClazz);
        OneDimSearchResult<T> result = new OneDimSearchResult();
        result.setSearchResult(searchResult_);
        result.setAggResult(aggResult);
        return result;
    }

    @Deprecated
    @Override
    public List<OneDimCountResult> oneDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim aggregationDim) throws IPException {
        List<DimCountResult> results = this.dimCount(indexNames, indexTypes, queryString, defaultField, others, aggregationDim);
        return results.stream().map(this::toOne).collect(Collectors.toList());
    }

    @Deprecated
    @Override
    public List<OneDimCountResult> oneDimCount(QueryCondition condition) throws IPException {
        List<DimCountResult> results = this.dimCount(condition);
        return results.stream().map(this::toOne).collect(Collectors.toList());
    }

    @Deprecated
    @Override
    public List<TwoDimCountResult> twoDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim parentAggDim, AggregationDim childAggDim) throws IPException {
        List<DimCountResult> results = this.dimCount(indexNames, indexTypes, queryString, defaultField, others, parentAggDim, childAggDim);
        return this.toTwo(results);
    }

    @Deprecated
    @Override
    public List<TwoDimCountResult> twoDimCount(QueryCondition condition) throws IPException {
        List<DimCountResult> results = this.dimCount(condition);
        return this.toTwo(results);
    }

    @Deprecated
    @Override
    public List<MultiDimCountResult> multiDimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        List<DimCountResult> results = this.dimCount(indexNames, indexTypes, queryString, defaultField, others, aggregationDims);
        List<MultiDimCountResult> resultList = new ArrayList<>();
        this.toMulti(results, resultList, "001", "");
        return resultList;
    }

    @Deprecated
    @Override
    public List<MultiDimCountResult> multiDimCount(QueryCondition condition) throws IPException {
        List<DimCountResult> results = this.dimCount(condition);
        List<MultiDimCountResult> resultList = new ArrayList<>();
        this.toMulti(results, resultList, "001", "");
        return resultList;
    }

    private OneDimCountResult toOne(DimCountResult result) {
        OneDimCountResult one = new OneDimCountResult();
        one.setName(result.getName());
        one.setCount(result.getCount());
        one.setRatio(result.getRatio());
        one.setHasChildren(false);
        return one;
    }

    private List<TwoDimCountResult> toTwo(List<DimCountResult> results) {
        List<TwoDimCountResult> dimCountResults = new ArrayList<>();
        for (DimCountResult result : results) {
            List<DimCountResult> children = result.getChildren();
            if (null != children && children.size() > 0) {
                for (DimCountResult child : children) {

                    TwoDimCountResult dimCountResult = new TwoDimCountResult();
                    dimCountResult.setGroupName(result.getName());
                    dimCountResult.setName(child.getName());
                    dimCountResult.setCount(child.getCount());
                    dimCountResult.setRatio(child.getRatio());
                    dimCountResult.setHasChildren(false);
                    dimCountResults.add(dimCountResult);
                }
            }
        }

        return dimCountResults;
    }

    private void toMulti(List<DimCountResult> results, List<MultiDimCountResult> resultList, String pid, String pname) {
        for (int i = 0; i < results.size(); i++) {
            DimCountResult result = results.get(i);
            String id = pid + StringUtils.rightPad(i + "", 3, '0');
            String name = result.getName();

            MultiDimCountResult dimCountResult = new MultiDimCountResult();
            dimCountResult.setPid(pid);
            dimCountResult.setPName(pname);
            dimCountResult.setId(id);
            dimCountResult.setKey(name);
            dimCountResult.setCount(result.getCount());
            resultList.add(dimCountResult);

            // 递归解析子节点
            List<DimCountResult> children = result.getChildren();
            if (null != children && children.size() > 0) {
                this.toMulti(children, resultList, id, name);
            }
        }
    }

    @Override
    public List<DimCountResult> dimCount(String indexName, String typeName, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        return this.dimCount(indexName, typeName, queryString, defaultField, null, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        return this.dimCount(indexNames, indexTypes, queryString, defaultField, null, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCount(String indexName, String typeName, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> indexTypes = new ArrayList<>();
        indexTypes.add(typeName);

        return this.dimCount(indexNames, indexTypes, queryString, defaultField, collapseCondition, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCount(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        return this.dimCountCommon(indexNames, indexTypes, queryString, defaultField, collapseCondition, null, false, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCountWithGroup(String indexName, String typeName, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        ArrayList<String> indexNames = new ArrayList<>();
        indexNames.add(indexName);

        ArrayList<String> indexTypes = new ArrayList<>();
        indexTypes.add(typeName);

        return this.dimCountWithGroup(indexNames, indexTypes, queryString, defaultField, groups, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCountWithGroup(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, Map<String, List<String>> groups, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        return this.dimCountCommon(indexNames, indexTypes, queryString, defaultField, null, groups, false, others, aggregationDims);
    }

    @Override
    public List<DimCountResult> dimCount(QueryCondition condition) throws IPException {
        Collection<String> indexNames = Collections.singleton(condition.getIndexName());
        return this.dimCountCommon(indexNames, indexNames, condition.getQuery(), condition.getDefaultField(), condition.getCollapseCondition(), condition.getGroups(), condition.isUnique(), condition.getOthers(), condition.getAggregationDims());
    }

    // 包含所有参数的
    private List<DimCountResult> dimCountCommon(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, List<String>> groups, boolean isUnique, Map<String, Object> others, AggregationDim... aggregationDims) throws IPException {
        String indexName = indexNames.iterator().next();

        for (int i = aggregationDims.length - 1; i > 0; i--) {
            aggregationDims[i - 1].addAggregationDim(aggregationDims[i]);
        }

        // FIXED 2025-01-02 兼容 cardinality 去重聚合
        AggregationDim dim = aggregationDims[0];
        // FIXED 2025-01-07 设置 shard_size
        this.checkAndHandleDim(indexNames, dim);

        SearchResult searchResult = this.searchResult(indexNames, indexTypes, queryString, defaultField, collapseCondition, groups, isUnique, others, 0, 0, dim);
        long total = searchResult.getTotal();

        BucketAggregation aggregation = getBucketAggregation(aggregationDims[0], searchResult.getAggregations());
        return this.extractResult(aggregation, aggregationDims[0], total);
    }

    // filters 聚合和 terms 聚合取结果时要用不同的方法，暂时没有时间优化
    private List<DimCountResult> extractResult(BucketAggregation bucketAggregation, AggregationDim aggregationDim, long total) {
        if (bucketAggregation instanceof FiltersAggregation) {
            return this.extractResult((FiltersAggregation) bucketAggregation, aggregationDim, total);
        } else {
            return this.extractResult((TermsAggregation) bucketAggregation, aggregationDim, total);
        }
    }

    // 结果重组为树形
    private List<DimCountResult> extractResult(FiltersAggregation filtersAggregation, AggregationDim aggregationDim, long total) {
        List<DimCountResult> results = new ArrayList<>();

        if (null != filtersAggregation) {
            List<Bucket> buckets = filtersAggregation.getBuckets();
            if (null != buckets && buckets.size() > 0) {
                List<String> filterValues = aggregationDim.getFilterValue();
                for (Bucket entry : buckets) {

                    String name = entry.getName();
                    if (null == filterValues || filterValues.contains(name)) {
                        DimCountResult result = new DimCountResult();

                        // FIXED 2023-03-22 nested 的 count 要从 reverse 里取
                        long count = entry.getCount();
                        if (count <= 0) continue;

                        // 递归处理结果
                        List<AggregationDim> childrenDims = aggregationDim.getAggregationDims();
                        if (null != childrenDims && childrenDims.size() > 0) {
                            for (AggregationDim childDim : childrenDims) {

                                // FIXED 2025-01-02 子聚合使用 cardinality 的直接替换数量，不作为 children
                                if (childDim instanceof AggeragationCardinalityDim) {
                                    this.setCountOfMetric(entry, childDim, isNestField(aggregationDim), result);
                                    continue;
                                }

                                // 递归处理。理论上 terms 聚合只允许有一个，所以这里用 set 处理而不是 add。允许多个的时候再修改。
                                BucketAggregation childAggregation = this.getBucketAggregation(aggregationDim, childDim, entry);
                                List<DimCountResult> childResults = this.extractResult(childAggregation, childDim, count);
                                if (childResults.size() > 0) {
                                    result.setChildren(childResults);
                                }
                            }
                        }

                        // count 有可能会被 distinct count 覆盖，要在这里计算和赋值
                        result.setName(name);
                        result.setCount(count);
                        results.add(result);
                    }
                }

            }

            // FIXED 2025-02-10 nested字段的结果排序使用的是 doc_count，应该用 for_doc_num 下的 doc_count 排序
            if (isNestField(aggregationDim)) {
                // FIXED 2025-04-22 对于嵌套字段，size 放大过了，这里再截取一下
                int size = threadLocalMap.get().get(aggregationDim.getFieldName());
                if (AggrOrder.ORDER_COUNT_DESC == aggregationDim.getAggrOrder()) {
                    results = results.stream().sorted(Comparator.comparingLong(DimCountResult::getCount).reversed()).limit(size).collect(Collectors.toList());
                } else if (AggrOrder.ORDER_COUNT_ASC == aggregationDim.getAggrOrder()) {
                    results = results.stream().sorted(Comparator.comparingLong(DimCountResult::getCount)).limit(size).collect(Collectors.toList());
                }
            }

            // other暂时先不计算
            // 计算百分比
            results.forEach(result -> result.setRatio(total));
        }

        return results;
    }

    // 结果重组为树形
    private List<DimCountResult> extractResult(TermsAggregation termsAggregation, AggregationDim aggregationDim, long total) {
        List<DimCountResult> results = new ArrayList<>();

        if (null != termsAggregation) {
            List<TermsAggregation.Entry> buckets = termsAggregation.getBuckets();
            if (null != buckets && buckets.size() > 0) {
                List<String> filterValues = aggregationDim.getFilterValue();
                for (TermsAggregation.Entry entry : buckets) {

                    String name = this.getKeyOfTermAggregation(entry);
                    if (null == filterValues || filterValues.contains(name)) {
                        DimCountResult result = new DimCountResult();

                        // FIXED 2023-03-22 nested 的 count 要从 reverse 里取
                        long count = this.getCountOfAggregation(entry);
                        if (count <= 0) continue;

                        // 递归处理结果
                        List<AggregationDim> childrenDims = aggregationDim.getAggregationDims();
                        if (null != childrenDims && childrenDims.size() > 0) {
                            for (AggregationDim childDim : childrenDims) {

                                // FIXED 2025-01-02 子聚合使用 cardinality 的直接替换数量，不作为 children
                                if (childDim instanceof AggeragationCardinalityDim) {
                                    this.setCountOfMetric(entry, childDim, isNestField(aggregationDim), result);
                                    continue;
                                }

                                // 递归处理。理论上 terms 聚合只允许有一个，所以这里用 set 处理而不是 add。允许多个的时候再修改。
                                BucketAggregation childAggregation = this.getBucketAggregation(aggregationDim, childDim, entry);
                                List<DimCountResult> childResults = this.extractResult(childAggregation, childDim, count);
                                if (childResults.size() > 0) {
                                    result.setChildren(childResults);
                                }
                            }
                        }

                        // count 有可能会被 distinct count 覆盖，要在这里计算和赋值
                        result.setName(name);
                        result.setCount(count);
                        results.add(result);
                    }
                }
            }

            // FIXED 2025-02-10 nested字段的结果排序使用的是 doc_count，应该用 for_doc_num 下的 doc_count 排序
            if (isNestField(aggregationDim)) {
                // FIXED 2025-04-22 对于嵌套字段，size 放大过了，这里再截取一下
                int size = threadLocalMap.get().get(aggregationDim.getFieldName());
                if (AggrOrder.ORDER_COUNT_DESC == aggregationDim.getAggrOrder()) {
                    results = results.stream().sorted(Comparator.comparingLong(DimCountResult::getCount).reversed()).limit(size).collect(Collectors.toList());
                } else if (AggrOrder.ORDER_COUNT_ASC == aggregationDim.getAggrOrder()) {
                    results = results.stream().sorted(Comparator.comparingLong(DimCountResult::getCount)).limit(size).collect(Collectors.toList());
                }
            }

            // 添加 sum_other_doc_count
            long otherCount = termsAggregation.getSumOtherCount();
            if (otherCount > 0) {
                DimCountResult otherResult = new DimCountResult().setCountOther(otherCount);
                results.add(otherResult);
            }

            // FIXED 2023-12-14 sum_other_doc_count 在 nested 情况下这个值不对，用 total 计算百分比可能会导致ratio之和大于1
            // 计算百分比
            results.forEach(result -> result.setRatio(total));
        }

        return results;
    }


    // FIXED 2025-01-07 设置 shard_size
    private void checkAndHandleDim(Collection<String> indexNames, AggregationDim dim) throws IPException {
        String indexName = indexNames.iterator().next();
        checkAggregationDim(indexNames, dim);
        handleRawAggregationDim(dim, indexName);
        handleSize(dim);
    }

    private void checkAggregationDim(Collection<String> indexNames, AggregationDim aggregationDim) throws IPException {
        checkIndexTypeParam(indexNames, indexNames);
        String indexName = indexNames.iterator().next();
        checkAggregationDim(indexName, aggregationDim);
    }

    /**
     * 检查输入参数
     *
     * @param indexNames
     * @param indexTypes
     * @throws IPException
     */
    private void checkIndexTypeParam(Collection<String> indexNames, Collection<String> indexTypes) throws IPException {
        if (indexNames == null || indexNames.isEmpty()) {
            throw ErrorCodes.ES_QUERY_INDEX_NAME_NULL.exception();
        }

        if (indexTypes == null || indexTypes.isEmpty()) {
            throw ErrorCodes.ES_QUERY_INDEX_NAME_NULL.exception();
        }
    }

    /**
     * 是否可以作为统计字段
     *
     * @param indexName
     * @param aggregationDim
     * @throws IPException
     */
    private void checkAggregationDim(String indexName, AggregationDim aggregationDim) throws IPException {
        String fieldName = aggregationDim.getFieldName();
        IPType ipType = IPDaoFactory.getInstance().getIPType(indexName);
        IPField ipField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName));

        // 判断虚拟字段
        if (null != ipType.getVirtualField(fieldName)) {
            throw ErrorCodes.ES_AGGREGATION_DIM_ERROR0.exception(fieldName, "虚拟字段");
        }

        IPColumn ipColumn;
        if (ipField.isMultiField()) {
            IPField parentIPField = (IPField) ipField.getIpFieldContainer();
            ipColumn = parentIPField.getIpColumn();
        } else {
            ipColumn = ipField.getIpColumn();
        }
        IPColumnType ipColumnType = ipColumn.getColumnType();

        // 判断 tiny_text 是否有 raw 字段
        boolean hasRaw = false;
        if (ipColumnType.equals(IPColumnType.COLUMN_TYPE_TINY_TEXT)) {
            IPField subField = ipField.getIPMultiField(ipField);
            if (subField instanceof IPMultiField) {
                IPMultiField subMultiField = (IPMultiField) subField;
                String subName = subMultiField.getMultiColumnName();
                if (StringUtils.isNotEmpty(subName) && subName.equalsIgnoreCase(IPConditionConstants.MULTI_FIELD_COLUMNNAME)) {
                    hasRaw = true;
                }
            }

            if (!hasRaw) {
                throw ErrorCodes.ES_AGGREGATION_DIM_ERROR3.exception(fieldName, "tiny_text");
            } else {
                return;
            }
        }

        // 判断其它类型
        if (!(ipColumnType.equals(IPColumnType.COLUMN_TYPE_LONG)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_DOUBLE)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_FLOAT)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_INTEGER)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_BYTE)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_SHORT)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_DATE)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_ITEM_NO)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_BOOLEAN)
                || ipColumnType.equals(IPColumnType.COLUMN_TYPE_RAW_DATE))) {
            throw ErrorCodes.ES_AGGREGATION_DIM_ERROR0.exception(fieldName, ipColumnType.getColumnValue());
        }

        // 递归检查
        List<AggregationDim> children = aggregationDim.getAggregationDims();
        if (CollectionUtils.isNotEmpty(children)) {
            for (AggregationDim child : children) {
                this.checkAggregationDim(indexName, child);
            }
        }
    }

    /**
     * @param rawAggregationDim
     * @param indexName
     * @return
     */
    private void handleRawAggregationDim(AggregationDim rawAggregationDim, String indexName) throws IPException {
        IPType ipType = IPDaoFactory.getInstance().getIPType(indexName);
        String fieldName = rawAggregationDim.getFieldName();
        String fullColumnName = IPTypeUtil.getFullColumnName(ipType, fieldName);

        String[] strArray = fullColumnName.trim().split("\\.");
        //普通字段
        if (strArray.length == 0) {
            return;
        } else if (strArray.length == 2) {
            //多值字段
            if ("raw".equals(strArray[1])) {
                rawAggregationDim.setFieldName(fullColumnName);
            } else {  //一级嵌套字段
                rawAggregationDim.setNestedFieldName(strArray[0]);
                this.resetSize(rawAggregationDim);
            }
        } else if (strArray.length == 3) {
            //多值嵌套字段
            if ("raw".equals(strArray[2])) {
                rawAggregationDim.setFieldName(strArray[1] + "." + strArray[2]);
                rawAggregationDim.setNestedFieldName(strArray[0]);
                this.resetSize(rawAggregationDim);
            } else {
                logger.warn("no supporting more than one nested fields temporarily");
            }
        }

        // 递归处理
        List<AggregationDim> children = rawAggregationDim.getAggregationDims();
        if (CollectionUtils.isNotEmpty(children)) {
            for (AggregationDim child : children) {
                this.handleRawAggregationDim(child, indexName);
            }
        }
    }

    // FIXED 2025-04-22 对于嵌套字段，存储原始 size，同时 size 放大
    private void resetSize(AggregationDim dim) {
        threadLocalMap.get().put(dim.getFieldName(), dim.getSize());
        int size = Math.min(dim.getSize() * 5, 1000);
        dim.setSize(size);
    }

    // FIXED 2025-01-07 设置 shard_size
    private void handleSize(AggregationDim dim) {
        if (dim.getShardSize() <= 0) {
            dim.setShardSize(FeatureClient.getAggShardSize());
        }

        // 递归设置
        List<AggregationDim> children = dim.getAggregationDims();
        if (CollectionUtils.isNotEmpty(children)) {
            for (AggregationDim child : children) {
                this.handleSize(child);
            }
        }
    }

    private SearchResult search(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, List<String>> groups, boolean isUnique, Map<String, Object> others, int from, int size, AggregationDim... aggregationDim) throws IPException {
        String typeName = indexTypes.iterator().next();
        String indexName = indexNames.iterator().next();

        SearchUtil searchUtil = new SearchUtil(httpClient, indexName, isUnique);

        long start = System.currentTimeMillis();
        IPCondition statementCon = searchUtil.getStatementCondition(queryString, defaultField, indexName, groups, others, false);
        long end = System.currentTimeMillis();

        logger.info("聚合条件--{}", SearchLogUtil.conditionToString(queryString, from, size, isUnique, null, -1, groups, collapseCondition, aggregationDim));
        logger.info("聚合耗时--表达式转化--{}", end - start);

        Condition condition = new Condition();
        condition.setIpCondition(statementCon);
        condition.setAggregationDims(Arrays.asList(aggregationDim));
        condition.setCollapseCondition(collapseCondition);
        condition.setFrom(from);
        condition.setSize(size);

        return this.doSearch(condition, indexName, typeName, others);
    }

    private SearchResult doSearch(Condition condition, String indexName, String typeName, Map<String, Object> others) throws IPException {
        String byteString = httpClient.getSerializer().writeObjectToString(condition);

        cn.gwssi.data.service.core.Search.Builder builder = new cn.gwssi.data.service.core.Search.Builder(byteString);
        builder.addType(typeName);
        builder.addIndex(indexName);
        new SearchParameter().addParameter(builder, others);
        cn.gwssi.data.service.core.Search search = builder.build();

        try {
            long start = System.currentTimeMillis();
            SearchResult result = httpClient.execute(search);
            long end = System.currentTimeMillis();
            logger.info("聚合耗时--http响应--{}", end - start);

            return result;
        } catch (Exception e) {
            logger.error("aggregation异常：：：：：：：：", e);
            throw ErrorCodes.ES_QUERY_ERROR1.exception(SearchErrorHandler.ERROR_MSG);
        }
    }

    private SearchResult searchResult(Collection<String> indexNames, Collection<String> indexTypes, String queryString, String defaultField, CollapseCondition collapseCondition, Map<String, List<String>> groups, boolean isUnique, Map<String, Object> others, int from, int size, AggregationDim... aggregationDim) throws IPException {
        SearchResult result = this.search(indexNames, indexTypes, queryString, defaultField, collapseCondition, groups, isUnique, others, from, size, aggregationDim);
        if (!result.isSucceeded()) {
            SearchErrorHandler.handleSearchError(result);
            return null;
        } else {
            long took = result.getTook();
            logger.info("聚合耗时--es took--{}", took);

            return result;
        }
    }

    private BucketAggregation getBucketAggregation(AggregationDim parentAggDim, AggregationDim childAggdim, Bucket entry) {
        BucketAggregation dim2TermAggr;
        if (isNestField(parentAggDim)) {
            dim2TermAggr = getBucketAggregation(childAggdim, entry.getAggregation("reverse_for_" + childAggdim.getFieldName(), RootAggregation.class));
        } else {
            dim2TermAggr = getBucketAggregation(childAggdim, entry);
        }
        return dim2TermAggr;
    }

    private BucketAggregation getBucketAggregation(AggregationDim aggDim, MetricAggregation metricAgg) {
        // filters 聚合，返回的结果中没有 nested
        if (aggDim instanceof AggregationFiltersDim) {
            return metricAgg.getFiltersAggregation(aggDim.getFieldName());
        } else if (isNestField(aggDim)) {
            return metricAgg.getAggregation(aggDim.getNestedFieldName(), RootAggregation.class).getTermsAggregation(aggDim.getFieldName());
        } else {
            return metricAgg.getTermsAggregation(aggDim.getFieldName());
        }
    }

    private <T extends GsonObject> IPSearchResult<T> handleSearchResult(SearchResult result, Class<T> gsonClazz) throws IPException {
        if (!result.isSucceeded()) {
            throw ErrorCodes.ES_QUERY_ERROR1.exception(result.getErrorMessage());
        }

        long total = result.getTotal();
        long took = result.getTook();
        List<T> list = result.getHits(gsonClazz).stream().map(hit -> {
            T source = hit.source;
            source.setScore(hit.score);
            return source;
        }).collect(Collectors.toList());

        IPSearchResult<T> ipResult = new IPSearchResult<>();
        ipResult.setTotal(total);
        ipResult.setTook(took);
        ipResult.setRecords(list);
        return ipResult;
    }

    // Filters 聚合不能使用 nested
    private boolean isNestField(AggregationDim aggregationDim) {
        return !(aggregationDim instanceof AggregationFiltersDim) && StringUtils.isNotBlank(aggregationDim.getNestedFieldName());
    }

    private String getKeyOfTermAggregation(TermsAggregation.Entry entry) {
        String key = entry.getKeyAsString();
        return StringUtils.isBlank(key) ? entry.getKey() : key;
    }

    // 先从 reverse 中取
    private long getCountOfAggregation(Bucket entry) {
        ReverseAggregation reverse = entry.getReverseAggregation("for_doc_num");
        return (null == reverse) ? entry.getCount() : entry.getReverseAggregation("for_doc_num").getCount();
    }

    // metric 聚合取 count
    private void setCountOfMetric(Bucket entry, AggregationDim dim, boolean isReverse, DimCountResult result) {
        MetricAggregation metric = entry;
        if (isReverse) {
            metric = metric.getAggregation("reverse_for_" + dim.getFieldName(), RootAggregation.class);
        }
        if (isNestField(dim)) {
            metric = metric.getAggregation(dim.getNestedFieldName(), RootAggregation.class);
        }

        if (dim instanceof AggeragationCardinalityDim) {
            long count = this.getCountOfCardinality(metric, entry);
            result.setUnique(count);
        }
    }

    private long getCountOfCardinality(MetricAggregation metric, Bucket bucket) {
        CardinalityAggregation cardinality = metric.getCardinalityAggregation(IPConstants.CARDINALITY_AGG_NAME);
        return null == cardinality ? bucket.getCount() : cardinality.getCardinality();
    }

}
