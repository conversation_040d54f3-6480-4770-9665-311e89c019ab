package cn.gwssi.data.service.cluster.admin.cluster;

import cn.gwssi.data.service.action.AbstractAction;
import cn.gwssi.data.service.action.GenericResultAbstractAction;
import cn.gwssi.data.service.client.ElasticsearchVersion;
import cn.gwssi.data.service.client.HttpPath;
import cn.gwssi.data.service.cluster.UrlUtils;

import java.util.LinkedHashSet;
import java.util.Set;

public class ClusterStateRequest extends GenericResultAbstractAction {

    private Set<String> indices;

    private Set<String> metrics;

    protected ClusterStateRequest(Builder builder){
        super(builder);
        indices = builder.indices;
        metrics = builder.metrics;
    }

    @Override
    protected String buildURI(ElasticsearchVersion elasticsearchVersion) {
        String url = HttpPath.CLUSTER_STATE;
        url = url + UrlUtils.appendRestPath(metrics)+ UrlUtils.appendRestPath(indices);
        return url;
    }

    @Override
    public String getRestMethodName() {
        return "GET";
    }

    public static class Builder extends AbstractAction.Builder<ClusterStateRequest, Builder> {

        private Set<String> indices = new LinkedHashSet<>();

        private Set<String> metrics = new LinkedHashSet<>();

        public void addMetric(Set<String> metrics){
            this.metrics = metrics;
        }

        /**
         * When not filtering metadata, a comma separated list of indices to include in the response.
         */
        public Builder indices(Set<String> indices) {
            this.indices.addAll(indices);
            return this;
        }

        /**
         * For debugging purposes, you can retrieve the cluster state local to a particular node.
         */
        public Builder local() {
            return addCleanApiParameter("local");
        }

        @Override
        public ClusterStateRequest build() {
            return new ClusterStateRequest(this);
        }
    }
}
