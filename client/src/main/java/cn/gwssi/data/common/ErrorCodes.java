package cn.gwssi.data.common;

import cn.gwssi.common.exception.code.*;

public class ErrorCodes {

    /**
     * 根据表达式批量更新数据时， 每次更新的最大记录数
     */
    public static final ErrorCode3 ES_BATCH_UPDATE_TOO_MANY = new ErrorCode3("upd001", "表达式[{0}]批量更新数据时，需要更新的记录数{1}, 超过最大限制数量{2}");


    public static final ErrorCode1 ES_INDEX_CREATE_ERROR = new ErrorCode1("E100002", "ES创建索引错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_GET_INDICES_ERROR = new ErrorCode1("E100003", "ES获取索引indices错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_GET_STATS_ERROR = new ErrorCode1("E100004", "ES获取索引stats错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_GET_EXIST_ERROR = new ErrorCode1("E100005", "ES获取索引exist错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_UPDATE_SETTINGS_ERROR = new ErrorCode1("E100006", "ES修改索引settings错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_UPDATE_MAPPING_ERROR = new ErrorCode1("E100007", "ES修改索引mapping错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_DELETE_ERROR = new ErrorCode1("E100008", "ES删除索引错误，错误信息：{0}");
    public static final ErrorCode1 ES_DOC_INDEX_ERROR = new ErrorCode1("E100010", "ES执行index错误，错误信息：{0}");
    public static final ErrorCode1 ES_INDEX_GET_MAPPING_ERROR = new ErrorCode1("E100011", "ES获取索引mappings错误，错误信息：{0}");


    /**
     * 索引数据错误
     */
    public static final ErrorCode0 ES_INDEX_ERROR0 = new ErrorCode0("idx000", "索引数据错误");
    public static final ErrorCode1 ES_INDEX_ERROR1 = new ErrorCode1("idx001", "索引数据错误，错误信息{0}");

    /**
     * 更新数据错误
     */
    public static final ErrorCode0 ES_UPDATE_ERROR0 = new ErrorCode0("upd000", "更新数据错误");
    public static final ErrorCode1 ES_UPDATE_ERROR1 = new ErrorCode1("upd001", "更新数据错误，错误信息{0}");


    public static ErrorCode1 ES_DOC_DELETE_ERROR = new ErrorCode1("E100012", "ES执行delete错误，错误信息：{0}");

    /**
     * json语句格式错误
     */
    public static final ErrorCode1 ES_QUERY_JSON_SYNAX_ERROR = new ErrorCode1("qry005", "检索语句错误，{0}");

    /**
     * 检索数据时索引名称为空
     */
    public static final ErrorCode0 ES_QUERY_INDEX_NAME_NULL = new ErrorCode0("qry011", "检索数据时错误，索引名为空");

    /**
     * 检索数据时类型名称为空
     */
    public static final ErrorCode0 ES_QUERY_TYPE_NAME_NULL = new ErrorCode0("qry012", "检索数据时错误，类型名称为空");


    /**
     * 检索数据错误
     */
    public static final ErrorCode1 ES_QUERY_ERROR1 = new ErrorCode1("qry001", "检索数据时错误，错误信息：{0}");
    public static final ErrorCode0 ES_QUERY_ERROR2 = new ErrorCode0("qry002", "检索条件的数量超过最大限制，请简化表达式后再检索");
    public static final ErrorCode0 ES_QUERY_ERROR3 = new ErrorCode0("qry003", "聚合结果的数量超过最大限制，请修改检索或聚合条件");
    public static final ErrorCode0 ES_QUERY_ERROR30 = new ErrorCode0("qry004", "您的检索式包含了过于宽泛的通配符，请尝试缩小通配范围以获取更精确的搜索结果");
    public static final ErrorCode0 ES_QUERY_ERROR4 = new ErrorCode0("qry013", "并发滚动检索线程等待时异常");

    /**
     * 检索结果转json异常
     */
    public static final ErrorCode0 ES_SEARCHRESULT_TO_JSON_ERROR = new ErrorCode0("result001", "检索结果转json异常");

    public static final ErrorCode2 ES_AGGREGATION_DIM_ERROR0 = new ErrorCode2("agg000", "字段{0}类型为{1}，不能做统计分析");
    public static final ErrorCode1 ES_AGGREGATION_DIM_ERROR1 = new ErrorCode1("agg001", "多维统计分析时，维度参数个数为{0}，小于三个，请使用一维或者二维统计分析接口");
    public static final ErrorCode2 ES_AGGREGATION_DIM_ERROR2 = new ErrorCode2("agg002", "统计数据时，维度参数设置错误， FilterValue[{0}]大于size[{1}]");
    public static final ErrorCode2 ES_AGGREGATION_DIM_ERROR3 = new ErrorCode2("agg003", "字段{0}类型为{1}，没有raw字段，不能做统计分析");

    /**
     * 抽取特征值错误
     */
    public static final ErrorCode0 ES_EXTRACT_FEATURE = new ErrorCode0("feature001", "提取特征值异常！");
    public static final ErrorCode0 ES_EXTRACT_FEATURE_SEMANTIC = new ErrorCode0("feature002", "提取特征值异常！");
    public static final ErrorCode0 ES_EXTRACT_BASE64 = new ErrorCode0("feature003", "图片转BASE64异常！");
    public static final ErrorCode0 ES_EXTRACT_FEATURE_TOO_SHORT = new ErrorCode0("feature009", "您输入的内容不支持语义检索，请重新输入内容进行检索。");

    /**
     * 查询号码异常
     */
    public static final ErrorCode0 ES_QUERY_ANO_ERROR = new ErrorCode0("feature003", "查询申请号或公开(公告)号异常");
    public static final ErrorCode0 ES_QUERY_ANO_NO_EXIST = new ErrorCode0("feature004", "申请号或公开(公告)号不存在");
    public static final ErrorCode0 ES_QUERY_ANO_NO_EXIST_TACD = new ErrorCode0("feature005", "申请号或公开(公告)号没有查询到全文");
    public static final ErrorCode0 ES_QUERY_ANO_NO_EXIST_TACD_AD = new ErrorCode0("feature006", "申请号或公开(公告)号没有查询到全文和申请日/公开日");

    /**
     * 申请人组异常
     */
    public static final ErrorCode1 ES_QUERY_GROUPS_VALUES_NULL = new ErrorCode1("feature007", "申请人组 {0} 中没有申请人");
    public static final ErrorCode0 ES_QUERY_GROUPS_NULL = new ErrorCode0("feature008", "检索数据时错误，申请人组为空");

    /**
     * 丢失字段或无法转化
     */
    public static final ErrorCode0 ES_QUERY_CONVERT_ERROR = new ErrorCode0("qry020", "子表达式丢失字段或转化错误");
}
