package cn.gwssi.data.common;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class Constants {

    public static final String USER_NAME_KEY = "un";
    public static final String PASSWORD_KEY = "pw";
    public static final String ES_INDEX_NAME = "索引名称";
    public static final String ES_TYPE_NAME = "类型名称";
    public static final String ES_INDEX_ID = "主键";
    public static final String ES_NODE_ID = "节点ID";

    /**
    * GSON 对象
	 */
    public static final Gson GSON = new GsonBuilder().addSerializationExclusionStrategy(new ExclusionStrategy() {
        @Override
        public boolean shouldSkipField(FieldAttributes f) {
            // 这里作判断，决定要不要排除该字段,return true为排除
            // 按字段名排除
            return "id".compareToIgnoreCase(f.getName()) == 0 || "document".compareToIgnoreCase(f.getName()) == 0
                    || "type".compareToIgnoreCase(f.getName()) == 0 || "score".compareToIgnoreCase(f.getName()) == 0;
        }

        @Override
        public boolean shouldSkipClass(Class<?> clazz) {
            return false;
        }
    }).create();
}
