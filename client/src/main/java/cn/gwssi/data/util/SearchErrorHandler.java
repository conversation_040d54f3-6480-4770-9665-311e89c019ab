package cn.gwssi.data.util;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.data.common.ErrorCodes;
import cn.gwssi.data.service.client.ISearchResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;

public class SearchErrorHandler {

    public static final String ERROR_MSG = "检索异常！";
    protected final static Logger log = LoggerFactory.getLogger(SearchErrorHandler.class);

    // 异常处理
    public static void handleSearchError(ISearchResult result) throws IPException {
        String errorReason = result.getErrorMessage();
        log.error("检索错误，错误信息：：：：：：：：" + errorReason);

        // 判断是否为 json 格式
        try {
            JSON.parse(errorReason);
        } catch (Exception e) {
            if (errorReason.startsWith("400 转化为queryBuilder时出错：")) {
                errorReason = ERROR_MSG;
            }
            throw ErrorCodes.ES_QUERY_ERROR1.exception(errorReason);
        }

        // 处理特定类型的错误
        Collection errorTypes = (Collection) JSONPath.read(result.getErrorMessage(), "..caused_by.type");
        if (errorTypes.size() > 0) {
            if (errorTypes.contains(IPConstants.ERROR_MAX_CLAUSES)) {
                throw ErrorCodes.ES_QUERY_ERROR2.exception();
            } else if (errorTypes.contains(IPConstants.ERROR_MAX_BUCKETS)) {
                throw ErrorCodes.ES_QUERY_ERROR3.exception();
            } else if (errorTypes.contains(IPConstants.ERROR_EMPTY_STACK)) {
                throw ErrorCodes.ES_QUERY_ERROR30.exception();
            }
        }

        // 解析异常，向下解析
        if (!result.isSucceeded()) {
            JsonObject jsonObject = result.getJsonObject();

            if (jsonObject.has("msg")) {
                errorReason = jsonObject.get("msg").getAsString();
            }

            if (jsonObject.has("error")) {
                JsonObject error = jsonObject.getAsJsonObject("error");
                errorReason = error.toString();

                if (error.has("reason")) {
                    errorReason = error.get("reason").getAsString();
                }

                if (error.has("root_cause")) {
                    JsonElement rootCause = error.get("root_cause");
                    JsonObject root = null;
                    if (rootCause instanceof JsonObject) {
                        root = (JsonObject) rootCause;
                    } else if (rootCause instanceof JsonArray) {
                        JsonArray roots = (JsonArray) rootCause;
                        root = roots.get(roots.size() - 1).getAsJsonObject();
                    }

                    if (null != root && root.has("reason")) {
                        errorReason = root.get("reason").getAsString();
                    }
                }
            }
        }

        // 处理特定类型的错误
        if (errorReason.contains("exceeds maxClauseCount")) {
            throw ErrorCodes.ES_QUERY_ERROR30.exception(errorReason);
        }

        throw ErrorCodes.ES_QUERY_ERROR1.exception(errorReason);
    }

}
