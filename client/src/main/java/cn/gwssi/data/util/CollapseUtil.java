package cn.gwssi.data.util;

import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.comparators.ComparatorChain;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

public class CollapseUtil<T extends GsonObject> {

    private static CollapseUtil instance;

    private CollapseUtil() {}

    public static CollapseUtil getInstance() {
        if (null == instance) {
            instance = new CollapseUtil();
        }
        return instance;
    }

    public List<T> distinctWithoutES(List<T> list, CollapseCondition collapse) {
        // 用“合并字段”分组
        final String field = collapse.getField();
        Map<String, List<T>> groups = list.stream()
                .filter(patent -> StringUtils.isNotBlank(getPatentValue(patent, field)))
                .collect(Collectors.groupingBy(patent -> getPatentRoutingAndField(patent, field)));

        // 生成排序规则
        Comparator<T> comparator = generateComparator(collapse.getSorts());

        Map<String, List<T>> relations = new HashMap<>();
        Set<String> childIds = new HashSet<>();
        for (List<T> patents : groups.values()) {
            if (patents.size() <= 1) {
                continue;
            }

            // 组内排序
            patents.sort(comparator);

            // 记录被合并的children
            List<T> children = patents.subList(1, patents.size());
            relations.put(getPatentId(patents.get(0)), children);

            Set<String> childrenIds = children.stream().map(this::getPatentId).collect(Collectors.toSet());
            childIds.addAll(childrenIds);
        }

        return list.stream()
                .filter(patent -> !childIds.contains(getPatentId(patent)))
                .peek(patent -> patent.setChildren(relations.get(getPatentId(patent)))).collect(Collectors.toList());
    }

    private Comparator<T> generateComparator(SortFieldList sorts) {
        ComparatorChain comparator = new ComparatorChain();
        for (SortField sort : sorts) {
            String field = sort.getFieldName();
            String customOrder = sort.getCustomOrder();

            boolean isReverse = false;
            Comparator<T> singleComparator;
            if (StringUtils.isNotBlank(customOrder)) {
                singleComparator = Comparator.comparingInt(patent -> getPatentValueOfInteger(patent, field, customOrder));
            } else {
                String order = sort.getOrder().name();
                isReverse = "desc".equalsIgnoreCase(order);
                singleComparator = Comparator.comparing(patent -> getPatentValueOfString(patent, field, order));
            }

            comparator.addComparator(singleComparator, isReverse);
        }

        return comparator;
    }

    private String getPatentValue(T patent, String field) {
        try {
            return BeanUtils.getSimpleProperty(patent, field);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            // TODO 记录日志等等
            return null;
        }
    }

    private String getPatentValueOfString(T patent, String field, String order) {
        String value = this.getPatentValue(patent, field);
        if (null != order && StringUtils.isBlank(value)) {
            return "desc".equalsIgnoreCase(order) ? "" : "\uFFFF";
        } else {
            return value;
        }
    }

    private int getPatentValueOfInteger(T patent, String field, String customOrder) {
        String value = this.getPatentValue(patent, field);
        return StringUtils.isBlank(value) ? Integer.MIN_VALUE : customOrder.indexOf(value);
    }

    private String getPatentId(T patent) {
        return this.getPatentValue(patent, "pid");
    }

    private String getPatentRoutingAndField(T patent, String field) {
        return this.getPatentValue(patent, "routing") + "-" + this.getPatentValue(patent, field);
    }

}
