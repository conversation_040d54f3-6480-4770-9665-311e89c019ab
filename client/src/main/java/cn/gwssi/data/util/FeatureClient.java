package cn.gwssi.data.util;

import cn.gwssi.data.service.client.Client;
import cn.gwssi.isearch.client.config.HttpClientConfig;
import org.apache.http.HttpHost;
import org.apache.http.conn.routing.HttpRoute;

import java.util.concurrent.TimeUnit;

public class FeatureClient {

    private static final int MAX_CONNECTION = 4;
    private static final int MAX_CONNECTION_PER_ROUTE = 2;
    public static String IMAGE_FEATURE_URL = "http://*************:20079";
    public static String SEMANTIC_FEATURE_URL = "http://*************:20079";
    public static final String IMAGE_FEATURE_PATH = "/for_img/feature_img";
    public static final String SEMANTIC_FEATURE_PATH = "/for_text/feature_text";
    public static int shardsCount = 150;
    public static int ratio = 50;
    public static String SEARCH_PREFERENCE = "primary_first";
    public static String SEARCH_TYPE = "";
    public static int aggShardSize = 1510;

    private static Client imageClient = null;
    private static Client semanticClient = null;

    private FeatureClient(){}

    public static Client getImageClient() {
        if (null == imageClient) {
            imageClient = createClient(IMAGE_FEATURE_URL);
        }

        return imageClient;
    }

    public static Client getSemanticClient() {
        if (null == semanticClient) {
            semanticClient = createClient(SEMANTIC_FEATURE_URL);
        }

        return semanticClient;
    }

    private static Client createClient(String url) {
        HttpRoute route = new HttpRoute(HttpHost.create(url));
        HttpClientConfig clientConfig = new HttpClientConfig.Builder(url)
                .multiThreaded(true).maxTotalConnection(MAX_CONNECTION)
                .defaultMaxTotalConnectionPerRoute(MAX_CONNECTION_PER_ROUTE)
                .maxTotalConnectionPerRoute(route, MAX_CONNECTION_PER_ROUTE)
                .connTimeout(30 * 1000).readTimeout(2 * 60 * 1000)
                .keepAliveTimeout(60, TimeUnit.SECONDS)
                .maxConnectionIdleTime(10, TimeUnit.SECONDS)
                .build();

        return new Client(clientConfig);
    }

    public static void setImageFeatureUrl(String url) {
        IMAGE_FEATURE_URL = url;
    }

    public static void setSemanticFeatureUrl(String url) {
        SEMANTIC_FEATURE_URL = url;
    }

    public static int getShardsCount() {
        return shardsCount;
    }

    public static void setShardsCount(int shardsCount) {
        FeatureClient.shardsCount = shardsCount;
    }

    public static int getRatio() {
        return ratio;
    }

    public static void setRatio(int ratio) {
        FeatureClient.ratio = ratio;
    }

    public static String getSearchPreference() {
        return SEARCH_PREFERENCE;
    }

    public static void setSearchPreference(String searchPreference) {
        SEARCH_PREFERENCE = searchPreference;
    }

    public static String getSearchType() {
        return SEARCH_TYPE;
    }

    public static void setSearchType(String searchType) {
        SEARCH_TYPE = searchType;
    }

    public static int getAggShardSize() {
        return aggShardSize;
    }

    public static void setAggShardSize(int aggShardSize) {
        FeatureClient.aggShardSize = aggShardSize;
    }
}
