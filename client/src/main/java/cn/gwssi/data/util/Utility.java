package cn.gwssi.data.util;

import java.util.Collection;
import java.util.Collections;


public class Utility {
//    /**
//     * 从 Response 中解析错误原因
//     * @param actionResponse actionResponse
//     * @return reason
//     */
//    public static String getErrorReason(ActionResponse actionResponse) {
//        DataNode errorNode = actionResponse.data().sub("error");
//        if (null == errorNode) {
//            return actionResponse.getStringValue();
//        }
//
//        DataNode reasonNode = errorNode.sub("reason");
//        if (null == reasonNode) {
//            return errorNode.asText();
//        }
//
//        return reasonNode.asText();
//    }

//    public static <T> Iterable<T> emptyIfNull(Iterable<T> iterable) {
//        return iterable == null ? Collections.<T>emptyList(): iterable;
//    }
    public static <T> Collection<T> emptyIfNull(Collection<T> input) {
        return input == null ? Collections.<T>emptyList() : input;
    }

    public static <T> boolean  isNullOrEmpty(Collection<T> input) {
        return input == null || input.isEmpty();
    }

    public static <T> boolean isNotNullAndNotEmpty(Collection<T> input) {
        return input != null && !input.isEmpty();
    }
}
