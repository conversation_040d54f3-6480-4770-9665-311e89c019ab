package cn.gwssi.data.util;

import cn.gwssi.data.service.search.IVectorProcessor;

public class ProcessorRegistry {

    private static IVectorProcessor vectorProcessor;

    public static void register(IVectorProcessor vectorProcessor) {
        ProcessorRegistry.vectorProcessor = vectorProcessor;
    }

    public static IVectorProcessor getVectorProcessor() {
        if (vectorProcessor == null) {
            throw new RuntimeException("未实现 IVectorProcessor ");
        }
        return vectorProcessor;
    }

}
