package cn.gwssi.data.util;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.exception.code.ErrorCode0;
import cn.gwssi.condition.FieldMappingFilterFactory;
import cn.gwssi.data.common.ErrorCodes;
import cn.gwssi.data.service.client.Client;
import cn.gwssi.data.service.client.ISearchClientProxy;
import cn.gwssi.data.service.core.Search;
import cn.gwssi.data.service.core.SearchResult;
import cn.gwssi.data.service.search.IVectorProcessor;
import cn.gwssi.data.service.search.SearchCondition;
import cn.gwssi.isearch.plugins.common.search.filter.*;
import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.InItem;
import cn.gwssi.meta.IPType;
import cn.gwssi.syntax.condition.*;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;
import cn.gwssi.syntax.parser.converter.ExpressionConverter;
import cn.gwssi.syntax.parser.converter.util.Constant;
import cn.gwssi.xparser.IPDaoFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class SearchUtil {

    protected final static Logger log = LoggerFactory.getLogger(SearchUtil.class);

    private IVectorProcessor vectorProcessor = ProcessorRegistry.getVectorProcessor();

    private final ISearchClientProxy httpClient;
    private final String indexName;
    private final boolean isUnique;
    private final static String pnc = "CN";
    private boolean hasVector = false;

    public SearchUtil(ISearchClientProxy httpClient, String indexName, boolean isUnique) {
        this.httpClient = httpClient;
        this.indexName = indexName;
        this.isUnique = isUnique;
    }

    // 图像条件
    public Condition getImageInnerCondition(Set<String> ids, Set<String> imagePaths, int size) throws IPException {
        // ids 条件
        AbstractItemConditionExt idsCondition = new InItem("ids", ids.toArray(new String[ids.size()]));

        // 抽取特征值
        List<float[]> features = new ArrayList<>();
        for (String path : imagePaths) {
            float[] floats = this.extractFeature(path, true);
            features.add(floats);
        }

        // 图像条件
        PictureCondition picture = new PictureCondition();
        picture.setImageColumnName(Constant.Field.P.getName());
        picture.setImageColumnFea(features);

        Condition condition = new Condition();
        condition.setIpCondition(idsCondition);
        condition.setPictureCondition(Collections.singletonList(picture));
        condition.setSize(size);

        return condition;
    }

    // 转化为 StatementItemCondition，处理一些特殊字段
    public IPCondition getStatementCondition(String queryString, String defaultField, String indexName, Map<String, List<String>> groups, Map<String, Object> others, boolean isForCheck) throws IPException {
        // 转化为 ScannerItemCondition
        IPCondition ipCondition = ExpressionConverter.getInstance().doFilter(queryString, defaultField);

        if (!isForCheck) {
            // 处理申请人组。在这里处理虚拟字段，下面处理嵌套字段
            String[] pacgElements = Optional.ofNullable(others).map(v -> (String[])v.get("pacg_elements")).orElse(IPConstants.FIELD_PACG_ELEMENTS);
            this.handleGroup(ipCondition, groups, pacgElements);
        }

        // 转化为 StatementItemCondition
        IPType ipType = IPDaoFactory.getInstance().getIPType(indexName);
        ipCondition = FieldMappingFilterFactory.getInstance().doFilter(ipType, ipCondition);

        // 做一些处理
//        if (!isForCheck) {
        this.handleCondition(ipCondition, others, isForCheck);
//        }

        // 转化为 abstractItemCondition
        this.convert2AbstractItemCondition(ipCondition);
        return ipCondition;
    }

    /**
     * 处理申请人组，pacg 转为多个字段 or 连接
     */
    private void handleGroup(IPCondition ipCondition, Map<String, List<String>> groups, String[] pacgElements) throws IPException {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0, length = subConditions.size(); i < length; i++) {
            IPCondition subCondition = subConditions.get(i);
            if (!subCondition.isLeafCondition()) {

                // 递归查找
                this.handleGroup(subCondition, groups, pacgElements);
            } else {
                ScannerItemCondition itemCondition = (ScannerItemCondition) subCondition;
                String value = itemCondition.getItemValue().getValue();

                if (IPConstants.FIELD_PACG.equalsIgnoreCase(itemCondition.getFieldName())
                        || IPConstants.FIELD_PACG_CN.equalsIgnoreCase(itemCondition.getFieldName())) {
                    // 检查申请人组下是否有申请人
                    if (null == groups) {
                        throw ErrorCodes.ES_QUERY_GROUPS_NULL.exception();
                    }
                    List<String> groupValues = groups.get(value);
                    if (null == groupValues || groupValues.size() <= 0) {
                        throw ErrorCodes.ES_QUERY_GROUPS_VALUES_NULL.exception(value);
                    }

                    // 转化为多个条件
                    IPCondition newCondition = this.addGroupCondition(groupValues, pacgElements);
                    subConditions.set(i, newCondition);
                }
            }
        }
    }

    /**
     * 递归查找，做一些处理:
     * 1、图像，抽取特征值
     * 2、语义，抽取特征值，或根据公开（公告）号查询全文，然后抽取特征值
     * 3、RAD，转换为“申请日早于”，且“语义等于”。如果申请日不存在，则去掉该条件
     * 4、RPD，转换为“公开（公告）日早于”，且“语义等于”。如果申请日不存在，则去掉该条件
     * 5、PACG，字段转化为申请人中/英/日/原，值转化为具体的申请人
     */
    private void handleCondition(IPCondition ipCondition, Map<String, Object> others, boolean isForCheck) throws IPException {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0, length = subConditions.size(); i < length; i++) {
            IPCondition subCondition = subConditions.get(i);
            if (!subCondition.isLeafCondition()) {

                // 递归查找
                this.handleCondition(subCondition, others, isForCheck);
            } else {
                StatementItemCondition itemCondition = (StatementItemCondition) subCondition;
                String field = itemCondition.getFieldName();
                Value value = itemCondition.getValue();
                String itemValue = value.getValue();

                // 四种特殊字段
                if (field.equalsIgnoreCase(Constant.Field.P.name()) || field.equalsIgnoreCase(Constant.Field.R.name())
                        || field.equalsIgnoreCase(Constant.Field.RAD.name()) || field.equalsIgnoreCase(Constant.Field.RPD.name())) {
                    // 设置一个标识，向量检索要用到
                    this.hasVector = true;

                    // 只检查表达式，不做处理
                    if (isForCheck && field.equalsIgnoreCase(Constant.Field.P.name())) return;

                    if (field.equalsIgnoreCase(Constant.Field.P.name())) {
                        // 值必须为base64
//                        if (!itemValue.matches(IPConditionConstants.IMAGE_START)) {
//                            throw IPConditionErrCodes.EXPRESSION_IMAGE_BASE64_ERROR.exception();
//                        }

                        field = Constant.Field.P.getName();
                        value.setVector(this.extractFeature(itemValue, true));
                    } else if (field.equalsIgnoreCase(Constant.Field.R.name())) {
                        IPCondition newCondition = this.addSemantic(itemValue, others);
                        subConditions.set(i, newCondition);
                        continue;

                        // FIXED 2023-04-21 可能为多个号码和语义，使用最下面的一大堆代码做处理
//                        String pnc = "CN";
//
//                        // 值为号码类型，提取全文
//                        // 申请号/公开号不存在时，抛出异常
//                        // 全文不存在时，抛出异常
//                        if (itemValue.matches(IPConditionConstants.AN_PN_START)) {
//                            JSONObject relateValues = this.extractDateAndR(itemValue, null).getJSONObject(0);
//                            if (relateValues.containsKey(IPConstants.RELATE_RETURN_FIELD)) {
//                                itemValue = relateValues.getString(IPConstants.RELATE_RETURN_FIELD);
//                                pnc = relateValues.getString(IPConstants.RELATE_RETURN_FIELD2);
//                            } else {
//                                throw ErrorCodes.ES_QUERY_ANO_NO_EXIST_TACD.exception();
//                            }
//                        } else {
//                            // 根据语言判断国别
//                            pnc = LanguageUtil.detect(itemValue);
//                        }
//
//                        // 特征值提取异常时，转为布尔查询
//                        float[] feature = this.extractFeature(itemValue, false, pnc);
//                        if (null != feature) {
//                            field = Constant.Field.getField(pnc);
//                            value.setVector(feature);
//                        } else {
//                            // 转化为 tacd 布尔查询
//                            field = IPConstants.RELATE_RETURN_FIELD;
//                            value.setValue(itemValue);
//                            itemCondition.setIpColumnType(IPColumnType.COLUMN_TYPE_LONG_TEXT);
//                            itemCondition.setIpAnalyzerType(IPAnalyzerType.ANALYZER_SMART);
//                        }
                    } else {
                        field = field.equalsIgnoreCase(Constant.Field.RAD.name()) ? Constant.Field.RAD.getName() : Constant.Field.RPD.getName();
                        JSONObject relateValues = this.extractDateAndR(itemValue).getJSONObject(0);

                        // 一个条件改为两个条件
                        IPCondition newCondition = this.addSemanticCondition(field, relateValues);
                        if (null != newCondition) {
                            subConditions.set(i, newCondition);
                            continue;
                        } else {
                            throw ErrorCodes.ES_QUERY_ANO_NO_EXIST_TACD_AD.exception();
                        }
                    }

                    itemCondition.setFieldName(field);
                    itemCondition.setValue(value);
                }
            }
        }
    }

    /**
     * 处理 RAD RPD
     * 转化为两个条件，ad = 20991231 and r = 语义值
     */
    private IPCondition addSemanticCondition(String field, JSONObject relateValues) throws IPException {
        String date = this.extractDate(field, relateValues);
        String tacd = this.getTextOfAnPn(relateValues, false, "");
//        String tacd = relateValues.getString(IPConstants.RELATE_RETURN_FIELD);
//        String pnc = relateValues.getString(IPConstants.RELATE_RETURN_FIELD2);

        // FIXED 2024-07-16 申请日、优先权日都有可能为空，这时候取公开日
        if (StringUtils.isBlank(date)) {
            date = relateValues.getString(Constant.Field.RPD.getName());
        }

        // 转化为 ad = 20991231
        StatementItemCondition dateCondition = null;
        if (StringUtils.isNotBlank(date)) {

            Value dateValue = new Value(date);
            dateCondition = new StatementItemCondition();
            dateCondition.setFieldName(field);
            dateCondition.setValue(dateValue);
            dateCondition.setOperator(ItemOperator.ITEM_OPERATOR_LT);
            dateCondition.setIpAnalyzerType(IPAnalyzerType.ANALYZER_TYPE_NONE);
            dateCondition.setIpColumnType(IPColumnType.COLUMN_TYPE_DATE);
        }

        // 转化为 r = 语义值 或 tacd = 语义值
        StatementItemCondition semanticCondition = this.convertSemantic(tacd);

        // 转化为两个子表达式
        if (null != dateCondition && null != semanticCondition) {
            IPCondition condition = new IPCondition();
            condition.addSubCondition(dateCondition);
            condition.addSubCondition(semanticCondition);
            condition.addConnector(ItemConnector.ITEM_CONNECTOR_AND);
            return condition;
        }

        return null == dateCondition ? semanticCondition : dateCondition;
    }

    // 处理申请人组，转化为多个条件
    private IPCondition addGroupCondition(List<String> values, String[] pacgElements) {
        Value value = new Value(values.toArray(new String[values.size()]));
        value.setIn(true);

        IPCondition condition = new IPCondition();
        for (String ele : pacgElements) {
            ScannerItemCondition subCondition = new ScannerItemCondition();
            subCondition.setFieldName(ele);
            subCondition.setOperator(ItemOperator.ITEM_OPERATOR_IN);
            subCondition.setItemValue(value);
            condition.addSubCondition(subCondition);
        }

        for (int i = 0; i < pacgElements.length - 1; i++) {
            condition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
        }

        return condition;
    }

    // 转化为 AbstractItemCondition，树型结构
    private void convert2AbstractItemCondition(IPCondition ipCondition) throws IPException {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0, length = subConditions.size(); i < length; i++) {
            IPCondition subCondition = subConditions.get(i);
            if (!subCondition.isLeafCondition()) {
                this.convert2AbstractItemCondition(subCondition);
            } else {
                StatementItemCondition statementItemCondition = (StatementItemCondition) subCondition;
                AbstractItemConditionExt itemConditionExt = this.convert2AbstractItemCondition(statementItemCondition);
                subConditions.set(i, itemConditionExt);
            }
        }
    }

    // 转化为 AbstractItemCondition，叶子节点
    private AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition condition) throws IPException {
        String fieldName = condition.getFieldName();

        if (StringUtils.isNotBlank(fieldName)) {
            IPColumnType columnType = condition.getIpColumnType();
            ItemConverter converter = this.getConverter(columnType);
            AbstractItemConditionExt itemConditionExt = converter.convert2AbstractItemCondition(condition);

            if (null != itemConditionExt) {
                itemConditionExt.setNestedField(condition.isNestedField());
                itemConditionExt.setNestedName(condition.getNestedFieldName());
                itemConditionExt.setBoost(condition.getValue().getBoost());
                // 两个条件的 isReverse 作比较。单个取反，就是取反。理论上不会有双重取反的情况
                // 例子1：mbi = not a 其中 not 是操作符，condition.isReverse() = true
                // 例子2：mbi != a 其中 != 是操作符，itemConditionExt.isReverse() = true
                // 例子3：not a!=b 前面的 not 是连接符，后面的 != 是操作符，连接符和操作符不会同时处理，所以不用考虑
                itemConditionExt.setReverse(condition.isReverse() ^ itemConditionExt.isReverse());
                return itemConditionExt;
            }
        }

        throw ErrorCodes.ES_QUERY_CONVERT_ERROR.exception();
    }

    private ItemConverter getConverter(IPColumnType columnType) {
        switch (columnType) {
            case COLUMN_TYPE_LONG:
            case COLUMN_TYPE_DOUBLE:
            case COLUMN_TYPE_FLOAT:
            case COLUMN_TYPE_BYTE:
            case COLUMN_TYPE_INTEGER:
            case COLUMN_TYPE_SHORT:
                return new FilterNumber();
            case COLUMN_TYPE_ITEM_NO:
                return new FilterItemNo();
            case COLUMN_TYPE_TINY_TEXT:
                return new FilterTinyText();
            case COLUMN_TYPE_LONG_TEXT:
                return new FilterLongText();
            case COLUMN_TYPE_DATE:
            case COLUMN_TYPE_RAW_DATE:
                return new FilterRawDate();
            case COLUMN_TYPE_GEO_POINT:
                return new FilterGeoPoint();
            case COLUMN_TYPE_IMG:
                return new FilterImage();
            case COLUMN_TYPE_SEMANTIC:
                return new FilterSemantic();
        }

        return null;
    }

    // 提取特征值
    private float[] extractFeature(String value, boolean isImage) throws IPException {
        Client client = null;
        String path = null;
        ErrorCode0 error = null;
        Map<String, Object> param = new HashMap<>();

        if (isImage) {
            client = FeatureClient.getImageClient();
            path = FeatureClient.IMAGE_FEATURE_PATH;
            error = ErrorCodes.ES_EXTRACT_FEATURE;

            String imageCodes = "";
            try {
                byte[] bytes = FileUtils.readFileToByteArray(new File(value.trim()));
                imageCodes = Base64.getEncoder().encodeToString(bytes).trim();
//                imageCodes = value.substring(value.indexOf(";base64,") + 8);
            } catch (IOException e) {
                e.printStackTrace();
                throw ErrorCodes.ES_EXTRACT_BASE64.exception(e);
            }

            param.put("img", imageCodes);
        } else {
            // TODO 这里预留一个处理，如果应用传过来的是json格式的特征值，直接转类型，不再调用接口抽取

            client = FeatureClient.getSemanticClient();
            path = FeatureClient.SEMANTIC_FEATURE_PATH;
            error = ErrorCodes.ES_EXTRACT_FEATURE_SEMANTIC;
            param.put("text", value);
            param.put("lang", pnc);
        }

        String result;
        try {
            result = client.baseRequest().sendRequest(HttpPost.METHOD_NAME, path, JSON.toJSONString(param), null);
        } catch (IOException e) {
            throw error.exception(e);
        }

        // TODO 判断具体错误类型
        JSONObject resultObj = JSON.parseObject(result);
        if (resultObj.containsKey("code") && !"200".equals(resultObj.getString("code"))) {
            throw ErrorCodes.ES_EXTRACT_FEATURE_TOO_SHORT.exception();
        } else {
            JSONArray jsonArray = resultObj.getJSONArray("feature");
            if (null == jsonArray || jsonArray.isEmpty()) {
                return null;
            } else {
                return this.convertJsonArray(jsonArray);
            }
        }
    }

    // 根据号码查询日期和全文
    private JSONArray extractDateAndR(String value) throws IPException {
        String query = IPConstants.RELATE_FIELD + " in (" + value + ")";
        FieldList fields = new FieldList();
//        fields.add(IPConstants.RELATE_RETURN_FIELD);
//        fields.add(IPConstants.RELATE_RETURN_FIELD2);
        fields.add(IPConstants.RELATE_RETURN_FIELD3);
        fields.add(Constant.Field.RAD.getName());
        fields.add(Constant.Field.RPD.getName());
        fields.addAll(Arrays.asList(IPConstants.RELATE_RETURN_FIELDS));

        // an/pn 可能会查出来重复数据，多取几条
        int size = value.split(",").length * 5;

        SortFieldList sorts = new SortFieldList();
        sorts.add(new SortField(Constant.Field.RPD.getName(), SortField.IPSorting.DESC));

        Condition condition = new SearchCondition(httpClient).generateSearchCondition(query, "", fields, sorts, null, null, null, -1, -1, null, -1, 0, size, indexName, false, null);
        String byteString = httpClient.getSerializer().writeObjectToString(condition);

        Search.Builder builder = new Search.Builder(byteString);
        builder.addType(indexName);
        builder.addIndex(indexName);

        SearchResult result;
        try {
            result = httpClient.execute(builder.build());
        } catch (IOException e) {
            throw ErrorCodes.ES_QUERY_ANO_ERROR.exception(e);
        }

        if (!result.isSucceeded()) {
            throw ErrorCodes.ES_QUERY_ANO_ERROR.exception();
        }

        // 判断条数
        long total = result.getTotal();
        if (total <= 0) {
            throw ErrorCodes.ES_QUERY_ANO_NO_EXIST.exception();
        }

        // 解析结果
        JSONArray resp = (JSONArray) JSONPath.read(result.getJsonString(), "$.hits.hits._source");
        if (null == resp || resp.isEmpty()) {
            throw ErrorCodes.ES_QUERY_ANO_NO_EXIST.exception();
        }

        return this.filterResult(resp);
    }

    // 查出来会有重复数据，只保留第一次出现的
    private JSONArray filterResult(JSONArray resp) {
        JSONArray result = new JSONArray();

        Set<String> values = new HashSet<>();
        for (int i = 0; i < resp.size(); i++) {
            JSONObject object = resp.getJSONObject(i);
            String ipphan = object.getString("ipphan");
            if (!values.contains(ipphan)) {
                result.add(object);
                values.add(ipphan);
            }
        }

        return result;
    }

    // jsonArray 转 float 数组
    private float[] convertJsonArray(JSONArray array) {
        int size = array.size();
        float[] feature = new float[size];

        for (int i = 0; i < size; i++) {
            feature[i] = array.getBigDecimal(i).floatValue();
        }

        return feature;
    }

    // 提取日期。如果是“新颖性检索”，取较早的日期
    private String extractDate(String field, JSONObject relateValues) {
        String date = relateValues.getString(field);
        String prde = relateValues.getString(IPConstants.RELATE_RETURN_FIELD3);

        return this.isUnique ? this.minDate(date, prde) : date;
    }

    // 取较早的日期
    private String minDate(String date1, String date2) {
        if (StringUtils.isBlank(date1)) {
            return date2;
        }
        if (StringUtils.isBlank(date2)) {
            return date1;
        }

        return date1.compareTo(date2) > 0 ? date2 : date1;
    }

    public boolean hasVector() {
        return hasVector;
    }

//    /*************************************
//     * 处理 R，逻辑太多，不单独拆分一个类了
//     * FIXED 2024-11-08 不区分号码还是文本，直接根据参数判断
//     ************************************/
//    private IPCondition addSemantic(String value, Map<String, Object> others) throws IPException {
//        boolean isAnPn = Optional.ofNullable(others).map(v -> v.get("isAnPn")).map(Object::toString).map(Boolean::parseBoolean).orElse(false);
//        boolean isIgnore = Optional.ofNullable(others).map(v -> v.get("isIgnore")).map(Object::toString).map(Boolean::parseBoolean).orElse(false);
//        String tfText = Optional.ofNullable(others).map(v -> v.get("tfText")).map(Object::toString).orElse("");
//
//        return isAnPn ? this.addSemanticOfAnPn(value, isIgnore, tfText) : this.convertSemantic(value + tfText);
//    }
//
//    // 号码类型，转为日期和语义
//    private IPCondition addSemanticOfAnPn(String value, boolean isIgnore, String tfText) throws IPException {
//        IPCondition textCondition = new IPCondition();
//        String minDate = "";
//        value = value.replaceAll("\\|", ",");
//
//        // 提取全文和最小日期
//        JSONArray relateValues = this.extractDateAndR(value);
//        for (int i = 0; i < relateValues.size(); i++) {
//            JSONObject relateValue = relateValues.getJSONObject(i);
//
//            // 计算最小日期
//            minDate = this.minDate(relateValue.getString(IPConstants.RELATE_RETURN_FIELD3), minDate);
//
//            // 语义条件
//            String text = getTextOfAnPn(relateValue, isIgnore, tfText);
//            textCondition.addSubCondition(this.convertSemantic(text));
//            if (i > 0) {
//                textCondition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
//            }
//        }
//
//        if (!this.isUnique || StringUtils.isBlank(minDate)) {
//            return textCondition;
//        }
//
//        // 日期条件。有多个号码时，取最早的日期，转化为 ad <= 20991231
//        Value dateValue = new Value(minDate);
//        StatementItemCondition dateCondition = new StatementItemCondition();
//        dateCondition.setFieldName(Constant.Field.RAD.getName());
//        dateCondition.setValue(dateValue);
//        dateCondition.setOperator(ItemOperator.ITEM_OPERATOR_LT);
//        dateCondition.setIpAnalyzerType(IPAnalyzerType.ANALYZER_TYPE_NONE);
//        dateCondition.setIpColumnType(IPColumnType.COLUMN_TYPE_DATE);
//
//        IPCondition condition = new IPCondition();
//        condition.addSubCondition(dateCondition);
//        condition.addSubCondition(textCondition);
//        condition.addConnector(ItemConnector.ITEM_CONNECTOR_AND);
//
//        return condition;
//    }

    // 根据专利号抽取全文
    private String getTextOfAnPn(JSONObject relateValue, boolean isIgnore, String tfText) {
        // FIXED 2025-05-21 不再从es中取全文
        return vectorProcessor.processR(relateValue, isIgnore) + tfText;

//        StringBuffer text = new StringBuffer();
//
//        for (String field : IPConstants.RELATE_RETURN_FIELDS) {
//            // 忽略背景技术
//            if (isIgnore && "tbcn".equals(field)) continue;
//
//            String value = relateValue.getString(field);
//            text.append(value);
//            if (StringUtils.isNotBlank(value) && !value.endsWith("\n")) {
//                text.append("\n");
//            }
//        }
//
//        text.append(tfText);
//        return text.toString();
    }

    /*************************************
     * 处理 R，逻辑太多，不单独拆分一个类了
     * FIXED 2024-12-12 号码和文本混检
     ************************************/
    // 转为多个查询语句。例如 【R = CN201921079664.4 | CN201921079664.5 | 文本描述】 转为 【ad <= 20991231 and r=(文本描述1 or 文本描述2 or 文本描述)】
    private IPCondition addSemantic(String value, Map<String, Object> others) throws IPException {
        String isAnPn = Optional.ofNullable(others).map(v -> v.get("isAnPn")).map(Object::toString).orElse("");
        boolean isIgnore = Optional.ofNullable(others).map(v -> v.get("isIgnore")).map(Object::toString).map(Boolean::parseBoolean).orElse(false);
        String tfText = Optional.ofNullable(others).map(v -> v.get("tfText")).map(Object::toString).orElse("");
        List<String> texts = this.extractText(value, isAnPn.split(","), isIgnore, tfText);

        // 有多个号码时，语义取 or
        IPCondition textCondition = new IPCondition();
        for (int i = 0; i < texts.size() - 1; i++) {
            textCondition.addSubCondition(this.convertSemantic(texts.get(i)));
            if (i > 0) {
                textCondition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
            }
        }

        // 有多个号码时，取最早的日期，转化为 ad <= 20991231
        String minDate = texts.get(texts.size() - 1);
        if (!this.isUnique || StringUtils.isBlank(minDate)) {
            return textCondition;
        }

        // 日期条件
        Value dateValue = new Value(minDate);
        StatementItemCondition dateCondition = new StatementItemCondition();
        dateCondition.setFieldName(Constant.Field.RAD.getName());
        dateCondition.setValue(dateValue);
        dateCondition.setOperator(ItemOperator.ITEM_OPERATOR_LT);
        dateCondition.setIpAnalyzerType(IPAnalyzerType.ANALYZER_TYPE_NONE);
        dateCondition.setIpColumnType(IPColumnType.COLUMN_TYPE_DATE);

        IPCondition condition = new IPCondition();
        condition.addSubCondition(dateCondition);
        condition.addSubCondition(textCondition);
        condition.addConnector(ItemConnector.ITEM_CONNECTOR_AND);

        return condition;
    }

    // 提取文本和最小日期，最小日期作为 list 的最后一个元素返回。号码类查询文本，文本直接使用
    private List<String> extractText(String value, String[] isAnPn, boolean isIgnore, String tfText) throws IPException {
        Multimap<String, String> values = ArrayListMultimap.create();
        this.identify(value, values, isAnPn, 0);

        // 文本直接使用
        List<String> texts = (List<String>) values.get("text");
        if (null == texts) {
            texts = new ArrayList<>();
        }

        // 通过 anpn 查询全文
        String minDate = "";
        List<String> anpns = (List<String>) values.get("anpn");
        if (null != anpns && anpns.size() > 0) {
            JSONArray relateValues = this.extractDateAndR(StringUtils.join(anpns, ","));

            // 提取全文和最小日期
            for (int i = 0; i < relateValues.size(); i++) {
                JSONObject relateValue = relateValues.getJSONObject(i);
                texts.add(this.getTextOfAnPn(relateValue, isIgnore, tfText));
                minDate = this.minDate(relateValue.getString(IPConstants.RELATE_RETURN_FIELD3), minDate);
            }
        }

        texts.add(minDate);
        return texts;
    }

    // 解析为两类，号码类、文本类
    private void identify(String value, Multimap<String, String> values, String[] isAnPn, int vIndex) {
        String anPnValue = "0";
        if (vIndex < isAnPn.length) {
            anPnValue = isAnPn[vIndex];
        }

        int index = value.indexOf("|");
        if (!"1".equals(anPnValue)) {
            values.put("text", copySelf(value));
        } else if (index < 0) {
            values.put("anpn", value);
        } else {
            values.put("anpn", value.substring(0, index).trim());
            this.identify(value.substring(index + 1).trim(), values, isAnPn, ++vIndex);
        }
    }

    // 文本类内容，拷贝到超过20字符
    private String copySelf(String self) {
        if (self.length() >= 20) return self;

        String value = self;
        int index = 1;
        while (index++ < 5) {
            value += self;
        }
//        while (value.length() < 20) {
//            value += self;
//        }
        return value;
    }

    // 转化为 r = 语义值 或 tacd = 语义值
    private StatementItemCondition convertSemantic(String value) throws IPException {
        StatementItemCondition semanticCondition = null;

        if (StringUtils.isNotBlank(value)) {
            String field = "";
            Value semanticValue = new Value();
            IPAnalyzerType analyzer = null;
            IPColumnType column = null;

            // 特征值提取异常时，转为布尔查询
            float[] feature = this.extractFeature(value, false);
            if (null != feature) {
                field = Constant.Field.getField(pnc);
                semanticValue.setVector(feature);
                analyzer = IPAnalyzerType.ANALYZER_SMART;
                column = IPColumnType.COLUMN_TYPE_SEMANTIC;
            } else {
                // 转化为 tacd 布尔查询
                field = IPConstants.RELATE_RETURN_FIELD;
                semanticValue.setValue(value);
                analyzer = IPAnalyzerType.ANALYZER_TYPE_NONE;
                column = IPColumnType.COLUMN_TYPE_LONG_TEXT;
            }

            semanticCondition = new StatementItemCondition();
            semanticCondition.setFieldName(field);
            semanticCondition.setValue(semanticValue);
            semanticCondition.setOperator(ItemOperator.ITEM_OPERATOR_EQ);
            semanticCondition.setIpAnalyzerType(analyzer);
            semanticCondition.setIpColumnType(column);
        }

        return semanticCondition;
    }
}
