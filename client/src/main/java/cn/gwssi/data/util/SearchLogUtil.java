package cn.gwssi.data.util;

import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;

import java.util.List;
import java.util.Map;

public class SearchLogUtil {

    public static String conditionToString(String query, int from, int size, boolean isUnique, String scroll, int scrollSliceId,
                                           Map<String, List<String>> groups, CollapseCondition collapseCondition, AggregationDim... aggregationDims) {
        String lineSeparator = System.getProperty("line.separator");

        StringBuffer stringBuffer = new StringBuffer(query).append(lineSeparator)
                .append("from：").append(from).append(lineSeparator)
                .append("size：").append(size).append(lineSeparator);
        if (isUnique) {
            stringBuffer.append("isUnique：").append(isUnique).append(lineSeparator);
        }
        if (null != scroll) {
            stringBuffer.append("scroll：").append(scroll).append(lineSeparator);
        }
        if (-1 != scrollSliceId) {
            stringBuffer.append("scrollSliceId：").append(scrollSliceId).append(lineSeparator);
        }
        if (null != groups) {
            stringBuffer.append("groups：").append(groups.size()).append("个").append(lineSeparator);
        }
        if (null != collapseCondition) {
            stringBuffer.append("collapseCondition：").append(collapseCondition.getField()).append(lineSeparator);
        }
        if (null != aggregationDims && aggregationDims.length > 0) {
            stringBuffer.append("aggregationDims：").append(dimsToString(aggregationDims)).append(lineSeparator);
        }

        return stringBuffer.toString();
    }

    // 聚合条件的 toString，输出样例：[dim1:[dim1c1:dim1c2:[dim1c1c1:dim1c1c2]]:dim2]
    private static String dimsToString(AggregationDim[] dims) {
        StringBuffer stringBuffer = new StringBuffer("[");
        for (AggregationDim dim : dims) {
            stringBuffer.append(dim.getFieldName()).append("：");

            List<AggregationDim> children = dim.getAggregationDims();
            if (null != children && children.size() > 0) {
                AggregationDim[] childs = dim.getAggregationDims().toArray(new AggregationDim[children.size()]);
                stringBuffer.append(dimsToString(childs));
            }
        }

        stringBuffer.append("]");
        return stringBuffer.toString();
    }
}
