package cn.gwssi.data.util;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class BeanUtil {
    protected static final Logger log = LoggerFactory.getLogger(BeanUtil.class.getName());

    /**
     *  VO对象转为 map
     *  仅适用于VO中字段全部为String和基本类型的情况
     *
     * @param obj
     * @param keepSuperFields 是否保留继承的属性
     * @param keepNull 是否保留null值。传参为false时，null值不添加到map
     * @return
     */
    public static Map<String, String> vo2Map(Object obj, boolean keepSuperFields, boolean keepNull) {
        Map<String, String> map = new HashMap<>();

        // 获取所有字段
        Field[] fieldsAll = null;
        Field[] fields = obj.getClass().getDeclaredFields();

        if (keepSuperFields) {
            Field[] fields2 = obj.getClass().getSuperclass().getDeclaredFields();
            fieldsAll = (Field[]) ArrayUtils.addAll(fields, fields2);
        } else {
            fieldsAll = fields;
        }

        // 反射取值
        for (Field field : fieldsAll) {
            String name = field.getName();
            if ("serialVersionUID".equals(name)) {
                continue;
            }

            field.setAccessible(true);
            Object value = null;
            try {
                value = field.get(obj);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                log.info("========VO转为map时，反射获取value错误========", e);
            }

            if (!keepNull && null == value) {
                continue;
            }

            map.put(name, format2String(field.getType(), value));
        }

        return map;
    }

    // 将String和基本类型，转化为String类型
    private static String format2String(Class valueClass, Object value) {
        String result = null;

        if (null != value) {
            if (valueClass.equals(String.class)) {
                result = value.toString();
            } else if (valueClass.equals(Long.class) || valueClass.equals(Long.TYPE) ||
                    valueClass.equals(Integer.class) || valueClass.equals(Integer.TYPE) ||
                    valueClass.equals(Double.class) || valueClass.equals(Double.TYPE) ||
                    valueClass.equals(Float.class) || valueClass.equals(Float.TYPE) ||
                    valueClass.equals(Short.class) || valueClass.equals(Float.TYPE) ||
                    valueClass.equals(Byte.class) || valueClass.equals(Byte.TYPE) ||
                    valueClass.equals(Boolean.class) || valueClass.equals(Boolean.TYPE) ||
                    valueClass.equals(Character.class) || valueClass.equals(Character.TYPE) ||
                    valueClass.equals(Float.class) || valueClass.equals(Float.TYPE)) {
                result = value + "";
            }
        }

        return result;
    }
}
