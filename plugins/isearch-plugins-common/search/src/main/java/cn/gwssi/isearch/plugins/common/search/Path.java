package cn.gwssi.isearch.plugins.common.search;

public interface Path
{
    /**
     * 项目根路径
     */
    String ACTION_ROOT = "iSearch_";

    /**
     * 项目根路径
     */
    String ROOT = "/_isearch";

    /**
     * 索引文档入口
     */
    String INDEX = ROOT + "/_indx";

    /**
     * 更新入口
     */
    String UPDATE = ROOT + "/_updt";

    /**
     * 删除入口
     */
    String DELETE = ROOT + "/_del";

    /**
     * 原生es检索入口
     */
    String SEARCH = ROOT + "/_srch";

    /**
     * 表达式检索入口
     */
    String QUERY = ROOT + "/_query";

    /**
     * 批量操作入口
     */
    String BULK = ROOT + "/_bk";

    /**
     * count入口
     */
    String COUNT = ROOT + "/_count";

    String ANALYSIS = ROOT + "/_analysis";
}
