package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

import java.util.List;

public class GteItem extends AbstractItemConditionExt<String> {

    private String format;

    public GteItem(String fieldName, String fieldValue) {
        super(fieldName, fieldValue);
    }

    public GteItem(String fieldName, String fieldValue, String format) {
        super(fieldName, fieldValue);
        this.format = format;
    }

    public GteItem(String fieldName, List<String> fieldValue) {
        super(fieldName, fieldValue);
    }

    public GteItem(String fieldName, String valueFreq, boolean isReverse) {
        super(fieldName, valueFreq, isReverse);
    }

    public String format() {
        return this.format;
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
