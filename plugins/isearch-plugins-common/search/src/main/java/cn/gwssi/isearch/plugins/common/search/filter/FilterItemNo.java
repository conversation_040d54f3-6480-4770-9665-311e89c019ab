package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.meta.IPAnalyzerType;

/**
 * 支持的分词器：none lowercase_analyzer
 * 支持的运算符：= != > >= < <=
 */
public class FilterItemNo extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) {
        String fieldName = statementItemCondition.getFieldName();
        String fullFieldName = statementItemCondition.getFullFieldName();
        ItemOperator operator = statementItemCondition.getOperator();
        IPAnalyzerType analyzer = statementItemCondition.getIpAnalyzerType();
        Value value = statementItemCondition.getValue();
        String fieldValue = value.getValue();

        // FIXED 2024-02-20 ipcms 等号码类型的字段，只有 keyword 形式，值强制转为大写
        if (analyzer == IPAnalyzerType.ANALYZER_TYPE_NGRAM) {
            fieldValue = fieldValue.toUpperCase();
        }

        switch (operator) {
            case ITEM_OPERATOR_GT:
                return new GtItem(fieldName, fieldValue);
            case ITEM_OPERATOR_GE:
                return new GteItem(fieldName, fieldValue);
            case ITEM_OPERATOR_LT:
                return new LtItem(fieldName, fieldValue);
            case ITEM_OPERATOR_LE:
                return new LteItem(fieldName, fieldValue);
            case ITEM_OPERATOR_NE:
                return handleEqOrNot(fieldName, fullFieldName, fieldValue, value.hasWildCard(), true);
            case ITEM_OPERATOR_EQ:
                return handleEqOrNot(fieldName, fullFieldName, fieldValue, value.hasWildCard(), false);
            case ITEM_OPERATOR_IN:
                return new InItem(fieldName, value.getMultiValues());
            default:
                return new EqItem(fieldName, fieldValue);
        }
    }

    // 处理 = !=
    private static AbstractItemConditionExt handleEqOrNot(String fieldName, String fullFieldName, String fieldValue, boolean hasWildCard, boolean isReverse) {
        if (hasWildCard) {
            return new WildcardItem(fieldName, fieldValue, isReverse, true);
        } else {
            return new EqItem(fieldName, fieldValue, isReverse);
        }
    }
}
