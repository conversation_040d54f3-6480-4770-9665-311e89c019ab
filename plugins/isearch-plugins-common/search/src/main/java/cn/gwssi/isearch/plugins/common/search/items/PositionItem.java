package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;

import java.util.List;

public class PositionItem extends AbstractItemConditionExt<String> {
    private List<ValueLocation> locations;
    private List<Value> values;

    public List<ValueLocation> locations() {
        return this.locations;
    }

    public PositionItem(String fieldName, List<Value> values, List<ValueLocation> locations) {
        super(fieldName);
        this.values = values;
        this.locations = locations;
    }

    public PositionItem(String fieldName, List<Value> values, List<ValueLocation> locations, boolean isReverser) {
        super(fieldName);
        this.values = values;
        this.locations = locations;
        this.setReverse(isReverser);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }

    public List<Value> getValues() {
        return values;
    }

    public void setValues(List<Value> values) {
        this.values = values;
    }
}
