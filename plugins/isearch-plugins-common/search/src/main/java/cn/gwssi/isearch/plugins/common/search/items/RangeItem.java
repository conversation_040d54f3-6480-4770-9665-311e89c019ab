package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

public class RangeItem extends AbstractItemConditionExt<String> {
    private String from;
    private String to;
    private String formatFrom;
    private String formatTo;

    public String from() {
        return this.from;
    }

    public String to() {
        return this.to;
    }

    public String formatFrom() {
        return this.formatFrom;
    }

    public String formatTo() {
        return this.formatTo;
    }

    public RangeItem(String fieldName, String from, String to) {
        this(fieldName, from, to, null, false);
    }

    public RangeItem(String fieldName, String from, String to, boolean isReverse) {
        this(fieldName, from, to, null, isReverse);
    }

    public RangeItem(String fieldName, String from, String to, String format) {
        this(fieldName, from, to, format, false);
    }

    public RangeItem(String fieldName, String from, String to, String formatFrom, boolean isReverse) {
        this(fieldName, from, to, formatFrom, formatFrom, isReverse);
    }

    public RangeItem(String fieldName, String from, String to, String formatFrom, String formatTo, boolean isReverse) {
        super(fieldName);
        this.from = from;
        this.to = to;
        this.formatFrom = formatFrom;
        this.formatTo = formatTo;
        this.setReverse(isReverse);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
