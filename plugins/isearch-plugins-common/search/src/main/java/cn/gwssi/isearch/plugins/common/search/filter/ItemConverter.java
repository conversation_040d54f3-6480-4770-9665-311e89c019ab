package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.offset.ConditionOffset;
import org.apache.commons.lang3.StringUtils;

public abstract class ItemConverter {

    public abstract AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException;

    /**
     * 提取 fieldName
     * 1、从 offset 中提取，表达式中最原始的值
     * 2、从 condition 中提取，可能经过了处理，加上了 nested 或 raw
     */
    String extractFieldName(StatementItemCondition statementItemCondition, IPConditionConstants.ConditionType conditionType) {
        String fieldName = statementItemCondition.getFieldName();

        ConditionOffset offset = statementItemCondition.getOffsetOf(conditionType);
        if (null != offset) {
            fieldName = offset.getChars();
        }

        // 去掉 raw，去掉嵌套字段
        if (StringUtils.isBlank(fieldName)) {
            fieldName = fieldName.replace(IPConstants.FULL_FIELD_NAME_ENDWITH, "");
            String nested = statementItemCondition.getNestedFieldName();
            if (StringUtils.isNotBlank(nested)) {
                fieldName = fieldName.replace(nested + ".", "");
            }
        }

        return fieldName;
    }
}
