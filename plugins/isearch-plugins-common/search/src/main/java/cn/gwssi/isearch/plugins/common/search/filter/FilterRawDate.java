package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueDate;
import cn.gwssi.syntax.condition.scannerValue.ValueRange;

public class FilterRawDate extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        ItemOperator operator = statementItemCondition.getOperator();
        String fieldName = statementItemCondition.getFieldName();
        Value value = statementItemCondition.getValue();

        // 转化为日期格式
        String frontValue = "";
        String endValue = "";
        if (value.isRange()) {
            ValueRange valueRange = value.getRange();
            frontValue = valueRange.getFrontValue();
            endValue = valueRange.getEndValue();
        } else {
            frontValue = value.getValue();
        }

        // 检查格式
        if (!ValueDate.isDateLegal(frontValue) || !ValueDate.isDateLegal(endValue)) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_DATE_FORMAT_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
        }

        // 转化为日期格式
        ValueDate frontValueDate = null;
        ValueDate endValueDate = null;
        if (value.isRange()) {
            frontValueDate = new ValueDate(frontValue);
            endValueDate = new ValueDate(endValue);
        } else {
            frontValueDate = new ValueDate(frontValue);
        }

        // 检查格式
        if (!ValueDate.isDateLegal(frontValueDate) || !ValueDate.isDateLegal(endValueDate)) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_DATE_FORMAT_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
        }

        // FIXED 2023-12-13 to要分成 >= 和 <= 两个，也不用检查精确度了
//        // 检查两个日期精确度是否匹配
//        if (null != endValueDate && !endValueDate.getType().equals(frontValueDate.getType())) {
//            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
//            throw IPConditionErrCodes.EXPRESSION_DATE_UNMATCH.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
//        }

        // 范围
        if (value.isRange()) {
            if (operator == ItemOperator.ITEM_OPERATOR_GT ||
                    operator == ItemOperator.ITEM_OPERATOR_LT ||
                    operator == ItemOperator.ITEM_OPERATOR_GE ||
                    operator == ItemOperator.ITEM_OPERATOR_LE) {
                throw IPConditionErrCodes.EXPRESSION_DATE_OPERATOR_ERROR.exception(fieldName, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
            } else {
                boolean isReverse = operator == ItemOperator.ITEM_OPERATOR_NE;
                return new RangeItem(fieldName, frontValueDate.getDateFormatted(), endValueDate.getDateFormatted(), frontValueDate.getFormat(), endValueDate.getFormat(), isReverse);
            }
        }

        // 单个值
        String fieldValue = frontValueDate.getDateFormatted();
        String format = frontValueDate.getFormat();

        switch (operator) {
            case ITEM_OPERATOR_EQ:
                return new RangeItem(fieldName, fieldValue, fieldValue, format);
            case ITEM_OPERATOR_NE:
                return new RangeItem(fieldName, fieldValue, fieldValue, format, true);
            case ITEM_OPERATOR_GT:
                return new GtItem(fieldName, fieldValue, format);
            case ITEM_OPERATOR_LT:
                return new LtItem(fieldName, fieldValue, format);
            case ITEM_OPERATOR_GE:
                return new GteItem(fieldName, fieldValue, format);
            case ITEM_OPERATOR_LE:
                return new LteItem(fieldName, fieldValue, format);
            default:
                return new EqItem(fieldName, fieldValue);
        }
    }
}
