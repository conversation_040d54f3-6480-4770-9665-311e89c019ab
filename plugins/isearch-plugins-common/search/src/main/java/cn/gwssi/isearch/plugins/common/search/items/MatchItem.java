package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

public class MatchItem extends AbstractItemConditionExt<String> {
    public MatchItem(String fieldName, String fieldValue) {
        super(fieldName, fieldValue);
    }

    public MatchItem(String fieldName, String valueFreq, boolean isReverse) {
        super(fieldName, valueFreq, isReverse);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
