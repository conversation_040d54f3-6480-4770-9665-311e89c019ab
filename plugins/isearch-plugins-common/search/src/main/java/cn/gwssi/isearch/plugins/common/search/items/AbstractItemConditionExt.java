package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.Item;
import cn.gwssi.syntax.condition.AbstractItemCondition;

import java.util.List;

public abstract class AbstractItemConditionExt<T> extends AbstractItemCondition<T> implements Item
{

    private float boost;

    public AbstractItemConditionExt(String fieldName) {
        super(fieldName);
    }

    public AbstractItemConditionExt(String fieldName, T fieldValue) {
        super(fieldName, fieldValue);
    }

    public AbstractItemConditionExt(String fieldName, T fieldValue, boolean isReverse) {
        super(fieldName, fieldValue, isReverse);
    }

    public AbstractItemConditionExt(String fieldName, List<T> fieldValue) {
        super(fieldName, fieldValue);
    }

    public float getBoost() {
        return boost;
    }

    public void setBoost(float boost) {
        this.boost = boost;
    }
}
