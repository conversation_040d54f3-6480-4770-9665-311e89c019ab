package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

import java.util.List;

public class LteItem extends AbstractItemConditionExt<String> {

    private String format;

    public LteItem(String fieldName, String fieldValue) {
        super(fieldName, fieldValue);
    }

    public LteItem(String fieldName, String fieldValue, String format) {
        super(fieldName, fieldValue);
        this.format = format;
    }

    public String format() {
        return this.format;
    }

    public LteItem(String fieldName, List<String> fieldValue) {
        super(fieldName, fieldValue);
    }

    public LteItem(String fieldName, String valueFreq, boolean isReverse) {
        super(fieldName, valueFreq, isReverse);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
