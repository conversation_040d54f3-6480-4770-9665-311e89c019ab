package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;

/**
 * 支持的分词器：所有
 * 支持的运算符：= !=
 */
public class FilterLongText extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        String fieldName = statementItemCondition.getFieldName();
        Value value = statementItemCondition.getValue();
        String fieldValue = value.getValue();
        boolean isReverse = statementItemCondition.getOperator() == ItemOperator.ITEM_OPERATOR_NE;

        // in，不支持。terms 检索只有 keyword 类型才能用
        if (value.isIn()) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_LONG_TEXT_INVALID3.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.COMPARATOR));
        }

        // 精确匹配、truecase
        // FIXED 2023-12-07 term 只能对 keyword 字段使用，对 text 字段检索结果是空的。这里只能使用 match_phrase
        if (value.hasQuotation() || value.isTrueCase()) {
            return new PhraseItem(fieldName, fieldValue, isReverse);
        }

        // freq
        if (value.isFreq()) {
            return new FreqItem(fieldName, value.getFreq(), isReverse);
        }

        // 通配符
        if (value.hasWildCard()) {
            // FIXED 2024-02-20 尽量使用 patent_phrase_wildcard，会自动分词，忽略大小写
            return new WildcardItem(fieldName, fieldValue, isReverse, false);
        }

        // 位置运算
        if (value.isLocation()) {
            return new PositionItem(fieldName, value.getValues(), value.getLocations(), isReverse);
        }

        // 其它
        return new PhraseItem(fieldName, fieldValue, isReverse);
    }
}
