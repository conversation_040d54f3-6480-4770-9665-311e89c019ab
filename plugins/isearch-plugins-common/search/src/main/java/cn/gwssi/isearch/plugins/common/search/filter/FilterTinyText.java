package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.meta.IPAnalyzerType;

/**
 * 支持的分词器：所有
 * 支持的运算符：= !=
 */
public class FilterTinyText extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        String fieldName = statementItemCondition.getFieldName();
        String fullFieldName = statementItemCondition.getFullFieldName();
//        String multiFieldName = statementItemCondition.getMultiFieldName();
        IPAnalyzerType analyzer = statementItemCondition.getIpAnalyzerType();
        Value value = statementItemCondition.getValue();
        String fieldValue = value.getValue();
        boolean isReverse = statementItemCondition.getOperator() == ItemOperator.ITEM_OPERATOR_NE;
        boolean hasRaw = fullFieldName.endsWith(IPConstants.FULL_FIELD_NAME_ENDWITH);

        // in，要用不分词的字段做term检索
        if (value.isIn()) {
            if (!hasRaw) {
                String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
                throw IPConditionErrCodes.EXPRESSION_LONG_TEXT_INVALID3.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.COMPARATOR));
            }

            return new InItem(fullFieldName, value.getMultiValues());
        }

        // 精确匹配、truecase，要用不分词的字段做term检索
        // FIXED 2023-12-07 term 只能对 keyword 字段使用，对 text 字段检索结果是空的。如果没有 raw 字段，使用 match_phrase
        if (value.hasQuotation() || value.isTrueCase()) {
            if (hasRaw) {
                return new EqItem(fullFieldName, fieldValue, isReverse);
            } else {
                return new PhraseItem(fieldName, fieldValue, isReverse);
            }
        }

        // freq
        if (value.isFreq()) {
            return new FreqItem(fieldName, value.getFreq(), isReverse);
        }

        // 通配符
        if (value.hasWildCard()) {
            // FIXED 2023-05-25 二元分词，强制使用 raw 字段做 patent_term_wildcard
            if (analyzer == IPAnalyzerType.ANALYZER_TYPE_NGRAM) {
                // FIXED 2024-02-20 值强制转为大写
                return new WildcardItem(fullFieldName, fieldValue.toUpperCase(), isReverse, true);
            }

            // FIXED 2024-02-20 尽量使用 patent_phrase_wildcard，会自动分词，忽略大小写
            return new WildcardItem(fieldName, fieldValue, isReverse, false);
        }

        // 位置运算
        if (value.isLocation()) {
            return new PositionItem(fieldName, value.getValues(), value.getLocations(), isReverse);
        }

        // 其它
        return new PhraseItem(fieldName, fieldValue, isReverse);
    }
}
