package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.VectorItem;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;

/**
 * 支持的分词器：无
 * 支持的运算符：=
 */
public class FilterSemantic extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        String fieldName = statementItemCondition.getFieldName();
        float[] fieldValue = statementItemCondition.getValue().getVector();
        ItemOperator operator = statementItemCondition.getOperator();

        // 操作符只能是 =
        if (!ItemOperator.ITEM_OPERATOR_EQ.equals(operator)) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_OPERATOR_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.COMPARATOR));
        }

        return new VectorItem(fieldName, fieldValue, false);
    }
}
