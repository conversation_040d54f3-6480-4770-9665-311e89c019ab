package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;
import cn.gwssi.syntax.condition.scannerValue.ValueFreq;

public class FreqItem extends AbstractItemConditionExt<ValueFreq> {
    private ValueFreq valueFreq;

    public FreqItem(String fieldName, ValueFreq valueFreq) {
        super(fieldName, valueFreq);
    }

    public FreqItem(String fieldName, ValueFreq valueFreq, boolean isReverse) {
        super(fieldName, valueFreq, isReverse);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
