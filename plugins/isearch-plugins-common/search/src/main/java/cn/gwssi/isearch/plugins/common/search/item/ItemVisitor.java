package cn.gwssi.isearch.plugins.common.search.item;

import cn.gwssi.isearch.plugins.common.search.items.*;

public interface ItemVisitor<T> {

    T visit(EqItem item);

    T visit(GteItem item);

    T visit(GtItem item);

    T visit(LteItem item);

    T visit(LtItem item);

    T visit(InItem item);

    T visit(FreqItem item);

    T visit(MatchItem item);

    T visit(PhraseItem item);

    T visit(PositionItem item);

    T visit(RangeItem item);

    T visit(WildcardItem item);

    T visit(GeoItem item);

    T visit(VectorItem item);
}
