package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

import java.util.List;

public class EqItem extends AbstractItemConditionExt<String> {
    public EqItem(String fieldName, String fieldValue) {
        super(fieldName, fieldValue);
    }

    public EqItem(String fieldName, String fieldValue, boolean isReverse) {
        super(fieldName, fieldValue, isReverse);
    }

    public EqItem(String fieldName, List<String> fieldValue) {
        super(fieldName, fieldValue);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
