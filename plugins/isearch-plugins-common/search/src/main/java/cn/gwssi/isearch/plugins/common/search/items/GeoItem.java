package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

import java.util.List;

public class GeoItem extends AbstractItemConditionExt<Double> {
    public GeoItem(String fieldName, List<Double> fieldValue) {
        super(fieldName, fieldValue);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
