package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

public class VectorItem extends AbstractItemConditionExt<String> {

    private float[] feature;
    private boolean isImage;
    private boolean isRescore = false;

    public VectorItem(String fieldName, float[] feature, boolean isImage) {
        super(fieldName);
        this.feature = feature;
        this.isImage = isImage;
    }

    public float[] getFeature() {
        return this.feature;
    }

    public boolean isImage() {
        return this.isImage;
    }

    public boolean isRescore() {
        return this.isRescore;
    }

    public void setRescore(boolean isRescore) {
        this.isRescore = isRescore;
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
