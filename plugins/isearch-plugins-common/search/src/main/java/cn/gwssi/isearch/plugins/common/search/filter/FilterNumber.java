package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueRange;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 支持的分词器：不需配分词器
 * 支持的运算符：= != > >= < <=
 */
public class FilterNumber extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        String fieldName = statementItemCondition.getFieldName();
        ItemOperator operator = statementItemCondition.getOperator();
        Value value = statementItemCondition.getValue();

        String frontValue = value.getValue();
        String endValue = String.valueOf(Integer.MIN_VALUE);
        if (value.isRange()) {
            ValueRange range = value.getRange();
            frontValue = range.getFrontValue();
            endValue = range.getEndValue();
        }

        // 检查格式
        if (!NumberUtils.isCreatable(frontValue) || !NumberUtils.isCreatable(endValue)) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_NUMBER_FORMAT_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
        }

        // 范围
        if (value.isRange()) {
            if (operator == ItemOperator.ITEM_OPERATOR_GT ||
                    operator == ItemOperator.ITEM_OPERATOR_LT ||
                    operator == ItemOperator.ITEM_OPERATOR_GE ||
                    operator == ItemOperator.ITEM_OPERATOR_LE) {
                String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
                throw IPConditionErrCodes.EXPRESSION_NUMBER_UNMATCH.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
            } else {
                boolean isReverse = operator == ItemOperator.ITEM_OPERATOR_NE;
                return new RangeItem(fieldName, frontValue, endValue, isReverse);
            }
        }

        // 单个值
        switch (operator) {
            case ITEM_OPERATOR_GT:
                return new GtItem(fieldName, frontValue);
            case ITEM_OPERATOR_GE:
                return new GteItem(fieldName, frontValue);
            case ITEM_OPERATOR_LT:
                return new LtItem(fieldName, frontValue);
            case ITEM_OPERATOR_LE:
                return new LteItem(fieldName, frontValue);
            case ITEM_OPERATOR_NE:
                return new EqItem(fieldName, frontValue, true);
            case ITEM_OPERATOR_EQ:
                return new EqItem(fieldName, frontValue);
            default:
                return new EqItem(fieldName, frontValue);
        }
    }
}
