package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

import java.util.Set;

public class InItem extends AbstractItemConditionExt<String> {

    private String[] inValues;

    public InItem(String fieldName, String[] inValues) {
        super(fieldName);
        this.inValues = inValues;
    }

    public String[] getInValues() {
        return inValues;
    }

    public void setInValues(String[] inValues) {
        this.inValues = inValues;
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
