package cn.gwssi.isearch.plugins.common.search.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.GeoItem;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 支持的分词器：无
 * 支持的运算符：=
 */
public class FilterGeoPoint extends ItemConverter {

    @Override
    public AbstractItemConditionExt convert2AbstractItemCondition(StatementItemCondition statementItemCondition) throws IPException {
        String fieldName = statementItemCondition.getFieldName();
        String fieldValue = statementItemCondition.getValue().getValue();
        ItemOperator operator = statementItemCondition.getOperator();

        // 操作符只能是 =
        if (!ItemOperator.ITEM_OPERATOR_EQ.equals(operator)) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_OPERATOR_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.COMPARATOR));
        }

        // 值必须是两个或三个数字
        List<Double> point = new ArrayList<>();
        String[] values = fieldValue.split(",");

        // 检查长度
        if (values.length < 2 || values.length > 3) {
            String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
            throw IPConditionErrCodes.EXPRESSION_GEO_NUMBER_LENGTH_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
        }

        // 检查格式
        for (String value : values) {
            if (!NumberUtils.isCreatable(value.trim())) {
                String field = this.extractFieldName(statementItemCondition, IPConditionConstants.ConditionType.FIELD);
                throw IPConditionErrCodes.EXPRESSION_GEO_NUMBER_ERROR.exception(field, statementItemCondition.getOffsetOf(IPConditionConstants.ConditionType.VALUE));
            }
            point.add(Double.parseDouble(value.trim()));
        }

        return new GeoItem(fieldName, point);
    }
}
