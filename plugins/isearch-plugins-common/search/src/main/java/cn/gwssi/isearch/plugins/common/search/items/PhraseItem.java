package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

public class PhraseItem extends AbstractItemConditionExt<String> {
    public PhraseItem(String fieldName, String fieldValue) {
        super(fieldName, fieldValue);
    }

    public PhraseItem(String fieldName, String fieldValue, boolean isReverse) {
        super(fieldName, fieldValue, isReverse);
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
