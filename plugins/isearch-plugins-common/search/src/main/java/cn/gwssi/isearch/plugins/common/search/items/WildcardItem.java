package cn.gwssi.isearch.plugins.common.search.items;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;

public class WildcardItem extends AbstractItemConditionExt<String> {

    /**
     * FXIED 2024-02-20 新的规则，参考 {@link ItemVisitorImpl} 的注释
     * FIXED 2023-12-06 通配符不仅仅使用中英文判断应该用哪一个，需要结合类型一起判断
     * item_no 使用 TermWildcardQueryBuilder
     * tiny_text 有 raw 的使用 TermWildcardQueryBuilder，否则根据中英文判断
     * long_text 根据中英文判断
     */
    private boolean useTerm;

    public boolean useTerm() {
        return this.useTerm;
    }

    public WildcardItem(String fieldName, String fieldValue) {
        this(fieldName, fieldValue, false, false);
    }

    public WildcardItem(String fieldName, String fieldValue, boolean isReverse) {
        this(fieldName, fieldValue, isReverse, false);
    }

    public WildcardItem(String fieldName, String fieldValue, boolean isReverse, boolean useTerm) {
        super(fieldName, fieldValue, isReverse);
        this.useTerm = useTerm;
    }

    @Override
    public <T> T accept(ItemVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
