<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.gwssi.isearch</groupId>
        <artifactId>plugins-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>plugins-common-image</artifactId>
    <version>${revision}</version>
    
    <dependencies>
        <!-- 这里引用的只是为了解决编译错误，实际使用的版本要在每个插件中控制 -->
        <dependency>
            <groupId>cn.gwssi.esplugins</groupId>
            <artifactId>vector-core</artifactId>
            <version>es670-5.1.3</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
