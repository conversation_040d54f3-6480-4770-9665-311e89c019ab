package cn.gwssi.isearch.plugins.converter;

import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.queries.KNNQueryBuilder;
import org.elasticsearch.index.queries.LSHQueryBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;

public abstract class VectorConverter {
    
    static String DEFAULT_IMAGE_FIELD = ".image_vec";

    static String DEFAULT_SEMANTIC_FIELD = ".semantic_vec";

    static int LIMIT_QUERY = -1;

    static int LIMIT_RESCORE = 0;

    public static float RESCORE_QUERY_WEIGHT = 0;

    static int DEFAULT_WINDOW_SIZE = 1000;

    static int MAX_WINDOW_SIZE = 10000;

    abstract String[] excludeFields();

    NestedQueryBuilder createNestedBuilder(String path, float[] content, boolean isPicture, boolean isRescore) {
        KNNQueryBuilder queryBuilder = isRescore ? new PQQueryBuilder() : new LSHQueryBuilder();
        queryBuilder.setFieldName(path + (isPicture ? DEFAULT_IMAGE_FIELD : DEFAULT_SEMANTIC_FIELD)).setQVector(content);

        return new NestedQueryBuilder(path, queryBuilder, ScoreMode.Max);
    }

//    NestedQueryBuilder createNestedRescoreBuilder(String path, float[] content, boolean isPicture) {
//        PQQueryBuilder pqQueryBuilder = new PQQueryBuilder()
//                .setFieldName(path + (isPicture ? DEFAULT_IMAGE_FIELD : DEFAULT_SEMANTIC_FIELD))
//                .setQVector(content);
//
//        return new NestedQueryBuilder(path, pqQueryBuilder, ScoreMode.Max);
//    }

    // 处理 windowSize
    public int getWindowSize(int windowSize) {
        if (windowSize <= 0) {
            return DEFAULT_WINDOW_SIZE;
        }
        if (windowSize > MAX_WINDOW_SIZE) {
            return MAX_WINDOW_SIZE;
        }

        return windowSize;
    }

}
