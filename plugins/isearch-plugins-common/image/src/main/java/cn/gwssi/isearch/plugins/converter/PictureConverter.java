package cn.gwssi.isearch.plugins.converter;

import cn.gwssi.syntax.condition.PictureCondition;
import org.elasticsearch.index.query.DisMaxQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 22:27
 */
public class PictureConverter extends VectorConverter {

//    // 转为查询语句
//    protected QueryBuilder createQueryBuilder(List<PictureCondition> pictureCondition) {
//        return (null == pictureCondition || pictureCondition.size() <= 0) ? null : this.createPictureBuilder(pictureCondition, false);
//    }
//
//    // 转为 rescore 语句。只对第一个字段做 rescore，如果对多个字段重排序，需要对每个字段设置不同的权重，效果差不多
//    public QueryBuilder createRescorerBuilder(List<PictureCondition> pictureCondition) {
//        return (null == pictureCondition || pictureCondition.size() <= 0) ? null : this.createPictureBuilder(pictureCondition.subList(0, 1), true);
//    }

    public QueryBuilder createQueryBuilder(List<PictureCondition> pictureCondition, boolean isRescore) {
        if (null == pictureCondition || pictureCondition.size() <= 0) {
            return null;
        }

        // 单图检索，不需要 dis_max
        if (isSinglePicture(pictureCondition)) {
            PictureCondition condition = pictureCondition.get(0);
            return super.createNestedBuilder(condition.getImageColumnName(), condition.getImageColumnFea().get(0), true, isRescore);
        }

        // 多图检索，包括多字段多图检索
        DisMaxQueryBuilder disMaxBuilder = new DisMaxQueryBuilder();
        for (PictureCondition condition : pictureCondition) {
            String path = condition.getImageColumnName();

            List<float[]> pictures = condition.getImageColumnFea();
            for (float[] picture : pictures) {
                NestedQueryBuilder nestedQuery = super.createNestedBuilder(path, picture, true, isRescore);
                disMaxBuilder.add(nestedQuery);
            }
        }

        return disMaxBuilder;
    }

    private boolean isSinglePicture(List<PictureCondition> pictureCondition) {
        return pictureCondition.size() == 1 && pictureCondition.get(0).getImageColumnFea().size() == 1;
    }

    // 需要排除的图片字段，否则返回数据过大
    @Override
    public String[] excludeFields() {
        return new String[]{ "*" + DEFAULT_IMAGE_FIELD };
    }
}
