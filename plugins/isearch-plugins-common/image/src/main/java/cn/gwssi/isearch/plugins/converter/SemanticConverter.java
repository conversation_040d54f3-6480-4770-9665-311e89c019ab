package cn.gwssi.isearch.plugins.converter;

import cn.gwssi.syntax.condition.SemanticCondition;
import org.elasticsearch.index.query.DisMaxQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;

public class SemanticConverter extends VectorConverter {

//    // 转为查询语句
//    protected QueryBuilder createQueryBuilder(SemanticCondition semanticCondition) {
//        return null == semanticCondition ? null : this.createSemanticBuilder(semanticCondition, false);
//    }
//
//    // 转为 rescore 语句
//    public QueryBuilder createRescorerBuilder(SemanticCondition semanticCondition) {
//        return null == semanticCondition ? null : this.createSemanticBuilder(semanticCondition, true);
//    }

    // 多个字段，用 dismax 运算
    public QueryBuilder createQueryBuilder(SemanticCondition semanticCondition, boolean isRescore) {
        if (null == semanticCondition || semanticCondition.getSemanticFields().size() <= 0) {
            return null;
        }

        DisMaxQueryBuilder disMaxBuilder = new DisMaxQueryBuilder();
        float[] features = semanticCondition.getSemanticFea();

        for (String field : semanticCondition.getSemanticFields()) {
            NestedQueryBuilder nestedQuery = super.createNestedBuilder(field, features, false, isRescore);
            disMaxBuilder.add(nestedQuery);
        }

        return disMaxBuilder;
    }

    // 需要排除的图片字段，否则返回数据过大
    @Override
    public String[] excludeFields() {
        return new String[]{ "*" + DEFAULT_SEMANTIC_FIELD };
    }

}
