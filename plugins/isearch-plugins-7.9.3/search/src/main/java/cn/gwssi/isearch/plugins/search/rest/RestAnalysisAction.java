package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.search.ExpressionService;
import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.rest.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestAnalysisAction extends RestQueryAction
{
//    public RestAnalysisAction(Settings settings, RestController controller, ExpressionService expressionService)
//    {
//        super(settings, controller, expressionService);
//    }

    public RestAnalysisAction(ExpressionService expressionService) {
        super(expressionService);
    }

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "query-analysis-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.ANALYSIS));

        return routes;
    }

    @Override
    protected BaseRestHandler.RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        try
        {
            SearchRequestBuilder requestBuilder = getSearchRequestBuilder(request, client);

            return channel -> channel.sendResponse(new BytesRestResponse(RestStatus.OK, requestBuilder.toString()));
        }
        catch (Exception e)
        {
            return channel -> channel.sendResponse(badResponse(e.getMessage()));
        }
    }
}
