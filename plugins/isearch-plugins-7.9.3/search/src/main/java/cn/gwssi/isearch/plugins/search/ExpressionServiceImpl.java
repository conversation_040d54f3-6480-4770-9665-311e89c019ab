package cn.gwssi.isearch.plugins.search;

import cn.gwssi.DeserializerFactory;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.collapse.InnerHitCondition;
import cn.gwssi.isearch.plugins.converter.ExpressionConverter;
import cn.gwssi.isearch.plugins.search.agg.BuilderFactory;
import cn.gwssi.isearch.plugins.search.collpase.CollapseService;
import cn.gwssi.isearch.plugins.search.collpase.PictureInnerService;
import cn.gwssi.isearch.plugins.search.settings.Value;
import cn.gwssi.syntax.condition.Condition;
import cn.gwssi.syntax.condition.PictureCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.rescore.QueryRescorerBuilder;
import org.elasticsearch.search.slice.SliceBuilder;
import org.elasticsearch.search.sort.ScoreSortBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortOrder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.elasticsearch.common.unit.TimeValue.parseTimeValue;

public class ExpressionServiceImpl implements ExpressionService {

    private ExpressionConverter converter;
    private final BuilderFactory aggBuilderFactory = new BuilderFactory();
    private final CollapseService collapseService = CollapseService.getInstance();
    private final PictureInnerService pictureService = PictureInnerService.getInstance();

//    private final static String SORT_SCRIPT = "def index = params.order.indexOf(doc[params.field].value);if(index<0) index=Integer.MAX_VALUE;return index;";
    private final static String SORT_SCRIPT = "return (null==doc[params.field].value || params.order.indexOf(doc[params.field].value)<0) ? Integer.MAX_VALUE : params.order.indexOf(doc[params.field].value)";

    @Override
    public void extractCondition(String condition, SearchRequestBuilder requestBuilder) throws Exception {
        Condition conditionVO = decode(condition, Condition.class);

        // 图像的 inner 检索
        List<PictureCondition> pictureCondition = conditionVO.getPictureCondition();
        if (CollectionUtils.isNotEmpty(pictureCondition)) {
            pictureService.addInnerPicture(requestBuilder, conditionVO.getIpCondition(), pictureCondition.get(0), conditionVO.getSize());
            return;
        }

        // 处理window_size。yml 有一个默认配置，如果有传参，用参数覆盖默认值
        int windowSize = conditionVO.getPictureWindowSize();
        if (windowSize <= 0 && Value.vector_window_size > 0) {
            windowSize = Value.vector_window_size;
        }
        if (windowSize <= 0) {
            windowSize = IPConditionConstants.DEFAULT_WINDOW_SIZE;
        }

        converter = new ExpressionConverter(conditionVO.getIpCondition(), windowSize);
        QueryBuilder query = converter.getQueryBuilder();

        // 去重
        boolean isCollapse = false;
        CollapseCondition collapse = conditionVO.getCollapseCondition();
        if (null != collapse) {
            List<InnerHitCondition> inner = collapse.getInnerHits();
            if (null == inner || inner.size() <= 0) {
                // 第一次查询，使用 dprm
                query = collapseService.addDistinct(collapse, query);
            } else {
                // 第二次查询，使用 inner_hits
                // query 使用 filter
                // collapse 和 rescore 不能同时使用
                isCollapse = true;
                query = QueryBuilders.boolQuery().filter(query);
                requestBuilder.setCollapse(collapseService.getCollapse(inner));
            }
        }

        // sort 和 rescore 不能同时使用
        // FIXED 2023-03-07 有 rescore 时，不使用 sort，bool 的 boost 设置为0
//        boolean hasNotSort = addSorts(conditionVO, requestBuilder);
        List<QueryRescorerBuilder> rescorerBuilders = converter.getRescorerBuilders();
        if (!isCollapse && rescorerBuilders.size() > 0) {
            addRescore(requestBuilder, rescorerBuilders);
//            query.boost(0);
        } else {
            addSorts(conditionVO, requestBuilder);
        }

//        addQuery(requestBuilder);
        requestBuilder.setQuery(query);

        addAggs(conditionVO, requestBuilder);

        addReturnedFields(conditionVO, requestBuilder);

        // sort 和 rescore 不能同时使用
//        if (hasNotSort) {
//            addRescore(requestBuilder);
//        }

//        addCollapse(conditionVO, requestBuilder);

        addScroll(conditionVO, requestBuilder);

        addSlice(conditionVO, requestBuilder);

        requestBuilder.setFrom(conditionVO.getFrom()).setSize(conditionVO.getSize());
    }

//    private void addQuery(SearchRequestBuilder requestBuilder) {
//        requestBuilder.setQuery(converter.getQueryBuilder());
//    }

    private boolean addSorts(Condition condition, SearchRequestBuilder requestBuilder) {
        SortFieldList sorts = condition.getSortFields();
        if (null != condition.getSortFields() && condition.getSortFields().size() > 0) {

            // FIXED 2022-12-06 如果只有 _score desc 一个排序条件，忽略掉
            if (sorts.size() == 1) {
                SortField sort = sorts.get(0);
                if (ScoreSortBuilder.NAME.equals(sort.getFieldName()) && SortField.IPSorting.DESC == sort.getOrder()) {
                    return true;
                }
            }

            for (SortField sortField : sorts) {
                ScriptSortBuilder customSort = this.customSort(sortField);
                if (null != customSort) {
                    requestBuilder.addSort(customSort);
                } else {
                    requestBuilder.addSort(sortField.getFieldName(), this.sortOrder(sortField.getOrder()));
                }
            }

            return false;
        }

        return true;
    }

    // FIXED 2024-12-12 增加自定义排序
    private ScriptSortBuilder customSort(SortField sortField) {
        String customOrder = sortField.getCustomOrder();
        if (StringUtils.isBlank(customOrder)) return null;

        Map<String, Object> params = new HashMap<>();
        params.put("order", customOrder);
        params.put("field", sortField.getFieldName());
        Script script = new Script(Script.DEFAULT_SCRIPT_TYPE, Script.DEFAULT_SCRIPT_LANG, SORT_SCRIPT, params);
        return new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.ASC);
    }

    private void addAggs(Condition condition, SearchRequestBuilder requestBuilder) {
        List<AggregationDim> dims = condition.getAggregationDims();
        if (dims != null && dims.size() > 0) {
            for (AggregationDim dim : dims) {
                requestBuilder.addAggregation(aggBuilderFactory.builder(dim).build());
            }
        }
    }

    private void addReturnedFields(Condition condition, SearchRequestBuilder requestBuilder) {
        String[] includes = this.list2Array(condition.getFieldList());
        requestBuilder.setFetchSource(includes, null);
    }

    private void addRescore(SearchRequestBuilder requestBuilder, List<QueryRescorerBuilder> builders) {
        if (builders.size() > 0) {
            for (QueryRescorerBuilder builder : builders) {
                requestBuilder.addRescorer(builder);
            }
        }
    }

    private void addScroll(Condition condition, SearchRequestBuilder requestBuilder) {
        String scroll = condition.getScroll();
        if (StringUtils.isNotBlank(scroll)) {
            requestBuilder.setScroll(new Scroll(parseTimeValue(scroll, null, "scroll")));
        }
    }

    private void addSlice(Condition condition, SearchRequestBuilder requestBuilder) {
        int id = condition.getScrollSliceId();
        int max = condition.getScrollSliceMax();
        if (id >= 0 && max > 0) {
            SliceBuilder sliceBuilder = new SliceBuilder(id, max);
            requestBuilder.slice(sliceBuilder);
        }
    }

    private <T> T decode(String msg, Class<T> clazz) {
        return DeserializerFactory.builder()
                .setThreadSafe(true)
                .setSoftReferences(false)
                .setMaximunCapacity(10)
                .build()
                .readFromByString(msg, clazz);
    }

//    /**
//     * FIXED 2022-11-30
//     * _score 排序要特殊处理一下
//     * sort 方法很奇怪，在 SearchSourceBuilder 中区分处理了，但是在 InnerHitBuilder 中并没有区分。所以这里还是要手动区分
//     */
//    private SortBuilder sortBuilder(SortField sortField) {
//        String name = sortField.getFieldName();
//        if (name.equals(ScoreSortBuilder.NAME)) {
//            return SortBuilders.scoreSort().order(sortOrder(sortField.getOrder()));
//        }
//
//        return SortBuilders.fieldSort(name).order(sortOrder(sortField.getOrder()));
//    }

    private SortOrder sortOrder(SortField.IPSorting ipSorting) {
        switch (ipSorting) {
            case ASC:
                return SortOrder.ASC;
            case DESC:
                return SortOrder.DESC;
            default:
                return SortOrder.ASC;
        }
    }

    private String[] list2Array(List<String> list) {
        return (null == list || list.size() <= 0) ? null : list.toArray(new String[list.size()]);
    }
}
