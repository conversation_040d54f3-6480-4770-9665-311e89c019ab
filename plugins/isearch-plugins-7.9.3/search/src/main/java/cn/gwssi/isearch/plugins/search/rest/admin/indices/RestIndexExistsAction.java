package cn.gwssi.isearch.plugins.search.rest.admin.indices;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.elasticsearch.rest.RestRequest.Method.GET;

public class RestIndexExistsAction extends BaseRestAction {

//    public RestIndexExistsAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestIndexExistsAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "index_exists_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, "/{index}"+Path.ROOT+"/exists"));
        routes.add(new RestHandler.Route(GET, "/{index}/{type}"+Path.ROOT+"/exists"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        final SearchRequest searchRequest = new SearchRequest();
        if (!Strings.isNullOrEmpty(request.param("index"))){
            searchRequest.indices(Strings.splitStringByCommaToArray(request.param("index")));
        }
        if (!Strings.isNullOrEmpty(request.param("type"))){
            searchRequest.types(Strings.splitStringByCommaToArray(request.param("type")));
        }
        searchRequest.routing(request.param("routing"));
        searchRequest.preference(request.param("preference"));
        searchRequest.indicesOptions(IndicesOptions.fromRequest(request, searchRequest.indicesOptions()));

        return channel -> client.search(searchRequest, new RestBuilderListener<SearchResponse>(channel) {
            @Override
            public RestResponse buildResponse(SearchResponse searchResponse, XContentBuilder builder) throws Exception {
                RestStatus status = searchResponse.status();
                builder.startObject();
                if (status.getStatus() == 200){
                    builder.field("exists", true);
                }
                builder.endObject();
                return new BytesRestResponse(status, builder);
            }
        });
    }


}
