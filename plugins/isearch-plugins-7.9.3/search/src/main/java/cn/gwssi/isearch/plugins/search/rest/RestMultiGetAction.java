package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.apache.logging.log4j.LogManager;
import org.elasticsearch.action.get.MultiGetRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.logging.DeprecationLogger;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestToXContentListener;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.elasticsearch.rest.RestRequest.Method.GET;
import static org.elasticsearch.rest.RestRequest.Method.POST;

public class RestMultiGetAction extends BaseRestAction {

    private static final DeprecationLogger deprecationLogger = new DeprecationLogger(
            LogManager.getLogger(org.elasticsearch.rest.action.document.RestMultiGetAction.class));
    public static final String TYPES_DEPRECATION_MESSAGE = "Specifying types in multi get requests is deprecated.";
    public static final String TYPES_DEPRECATION_MESSAGE_KEY = "[types removal]";

    private final boolean allowExplicitIndex;

//    public RestMultiGetAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//        this.allowExplicitIndex = MULTI_ALLOW_EXPLICIT_INDEX.get(settings);
//    }

    public RestMultiGetAction(Settings settings) {
        this.allowExplicitIndex = MULTI_ALLOW_EXPLICIT_INDEX.get(settings);
    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "document_mget_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, "/_mlget"));
        routes.add(new RestHandler.Route(POST, "/_mlget"));
        routes.add(new RestHandler.Route(GET, "/{index}/_mlget"));
        routes.add(new RestHandler.Route(POST, "/{index}/_mlget"));
        // Deprecated typed endpoints.
        routes.add(new RestHandler.Route(GET, "/{index}/{type}/_mlget"));
        routes.add(new RestHandler.Route(POST, "/{index}/{type}/_mlget"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        if (request.param("type") != null) {
            deprecationLogger.deprecatedAndMaybeLog("mget_with_types", TYPES_DEPRECATION_MESSAGE);
        }

        MultiGetRequest multiGetRequest = new MultiGetRequest();
        multiGetRequest.refresh(request.paramAsBoolean("refresh", multiGetRequest.refresh()));
        multiGetRequest.preference(request.param("preference"));
        multiGetRequest.realtime(request.paramAsBoolean("realtime", multiGetRequest.realtime()));
        if (request.param("fields") != null) {
            throw new IllegalArgumentException("The parameter [fields] is no longer supported, " +
                    "please use [stored_fields] to retrieve stored fields or _source filtering if the field is not stored");
        }
        String[] sFields = null;
        String sField = request.param("stored_fields");
        if (sField != null) {
            sFields = Strings.splitStringByCommaToArray(sField);
        }

        FetchSourceContext defaultFetchSource = FetchSourceContext.parseFromRestRequest(request);
        try (XContentParser parser = request.contentOrSourceParamParser()) {
            multiGetRequest.add(request.param("index"), request.param("type"), sFields, defaultFetchSource,
                    request.param("routing"), parser, allowExplicitIndex);
        }

        for (MultiGetRequest.Item item : multiGetRequest.getItems()) {
            if (item.type() != null) {
                deprecationLogger.deprecatedAndMaybeLog(TYPES_DEPRECATION_MESSAGE_KEY, TYPES_DEPRECATION_MESSAGE);
                break;
            }
        }

        return channel -> client.multiGet(multiGetRequest, new RestToXContentListener<>(channel));
    }
}
