package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestStatusToXContentListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.elasticsearch.rest.RestRequest.Method.DELETE;

public class RestClearScrollAction extends BaseRestAction {

//    public RestClearScrollAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestClearScrollAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "clear_scroll_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(DELETE, Path.ROOT + "/scroll"));
        routes.add(new RestHandler.Route(DELETE, Path.ROOT + "/scroll/{scroll_id}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String scrollIds = request.param("scroll_id");
        ClearScrollRequest clearRequest = new ClearScrollRequest();
        clearRequest.setScrollIds(Arrays.asList(Strings.splitStringByCommaToArray(scrollIds)));
        request.withContentOrSourceParamParserOrNull((xContentParser -> {
            if (xContentParser != null) {
                // NOTE: if rest request with xcontent body has request parameters, values parsed from request body have the precedence
                try {
                    clearRequest.fromXContent(xContentParser);
                } catch (IOException e) {
                    throw new IllegalArgumentException("Failed to parse request body", e);
                }
            }
        }));

        return channel -> client.clearScroll(clearRequest, new RestStatusToXContentListener<>(channel));
    }
}
