package cn.gwssi.isearch.plugins.search.rest.admin.cluster;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.elasticsearch.action.admin.cluster.stats.ClusterStatsRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestActions;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestClusterStatsAction extends BaseRestAction {

//    public RestClusterStatsAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestClusterStatsAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "cluster_stats_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.ROOT+"/cluster/stats"));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.ROOT+"/cluster/stats/nodes/{nodeId}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        ClusterStatsRequest clusterStatsRequest = new ClusterStatsRequest().nodesIds(request.paramAsStringArray("nodeId", null));
        clusterStatsRequest.timeout(request.param("timeout"));
        return channel -> client.admin().cluster().clusterStats(clusterStatsRequest,
                new RestActions.NodesResponseRestListener<>(channel));
    }

    @Override
    public boolean canTripCircuitBreaker() {
        return false;
    }
}
