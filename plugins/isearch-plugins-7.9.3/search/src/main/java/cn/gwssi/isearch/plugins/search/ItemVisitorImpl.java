package cn.gwssi.isearch.plugins.search;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.isearch.plugins.search.util.AnalyzerUtil;
import cn.gwssi.patsearch.query.AbstractRangeQueryBuilder;
import cn.gwssi.patsearch.query.freq.PhraseFreqQueryBuilder;
import cn.gwssi.patsearch.query.freq.TermFreqQueryBuilder;
import cn.gwssi.patsearch.query.freq.WildcardFreqQueryBuilder;
import cn.gwssi.patsearch.query.position.PositionNearQueryBuilder;
import cn.gwssi.patsearch.query.position.PositionPhraseQueryBuilder;
import cn.gwssi.patsearch.query.position.PositionTermQueryBuilder;
import cn.gwssi.patsearch.query.utils.range.*;
import cn.gwssi.patsearch.query.wildcard.PhraseWildcardQueryBuilder;
import cn.gwssi.patsearch.query.wildcard.TermWildcardQueryBuilder;
import cn.gwssi.syntax.condition.AbstractItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueFreq;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;
import cn.gwssi.syntax.condition.scannerValue.tool.ValueTool;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.queries.KNNQueryBuilder;
import org.elasticsearch.index.queries.LSHQueryBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.index.query.*;

import java.util.List;

public class ItemVisitorImpl implements ItemVisitor<QueryBuilder> {

    private ValueTool tool = ValueTool.getInstance();

    @Override
    public QueryBuilder visit(EqItem item) {
        List<String> fieldValues = item.getFieldValue();
        String fieldName = item.getFieldName();

        if (fieldValues.size() == 1) {
            String fieldValue = fieldValues.get(0);

            // 查询值为空的情况。实际上值为空的时候，解析就报错了，不会走到这里
            if (StringUtils.isBlank(fieldValue)) {
                return this.reverseIfNecessary(new ExistsQueryBuilder(fieldName), item);
            } else {
                return this.reverseIfNecessary(new TermQueryBuilder(fieldName, fieldValue), item);
            }
        } else {
            return this.reverseIfNecessary(new TermsQueryBuilder(fieldName, fieldValues), item);
        }
    }

    @Override
    public QueryBuilder visit(GteItem item) {
        RangeQueryBuilder query = QueryBuilders.rangeQuery(item.getFieldName()).gte(item.getFieldValue().get(0));
        this.formatIfNecessary(query, item.format());
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(GtItem item) {
        RangeQueryBuilder query = QueryBuilders.rangeQuery(item.getFieldName()).gt(item.getFieldValue().get(0));
        this.formatIfNecessary(query, item.format());
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(LteItem item) {
        RangeQueryBuilder query = QueryBuilders.rangeQuery(item.getFieldName()).lte(item.getFieldValue().get(0));
        this.formatIfNecessary(query, item.format());
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(LtItem item) {
        RangeQueryBuilder query = QueryBuilders.rangeQuery(item.getFieldName()).lt(item.getFieldValue().get(0));
        this.formatIfNecessary(query, item.format());
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(InItem item) {
        TermsQueryBuilder query = QueryBuilders.termsQuery(item.getFieldName(), item.getInValues());
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(FreqItem item) {
        ValueFreq valueFreq = item.getFieldValue().get(0);
        String fieldName = item.getFieldName();
        String fieldValue = valueFreq.getValue();
        SingleBound comparator = this.convertOperator(valueFreq.getOperator()).boundary(valueFreq.getCount());

        AbstractRangeQueryBuilder query;
        if (valueFreq.hasWildCard()) {
            query = new WildcardFreqQueryBuilder(fieldName, fieldValue);
        } else if (valueFreq.hasZh()) {
            query = new PhraseFreqQueryBuilder(fieldName, fieldValue);
        } else {
            query = new TermFreqQueryBuilder(fieldName, fieldValue);
        }

        query.range(comparator);
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(MatchItem item) {
        MatchQueryBuilder query = new MatchQueryBuilder(item.getFieldName(), item.getFieldValue().get(0));
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(PhraseItem item) {
        MatchPhraseQueryBuilder query = new MatchPhraseQueryBuilder(item.getFieldName(), item.getFieldValue().get(0));
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(PositionItem item) {
        String field = item.getFieldName();
        List<Value> values = item.getValues();
        List<ValueLocation> locations = item.locations();

        // 多个 span_near 嵌套
        SpanQueryBuilder query = this.buildSpanQuery(field, values.get(0));
        for (int i = 0, length = locations.size(); i < length; i++) {
            ValueLocation location = locations.get(i);

            // FIXED 2024-02-19 设置默认大于0
            Range range = this.convertOperator(location.getComparator(), location.getCount());
//            SingleBound comparator = this.convertOperator(location.getComparator()).boundary(location.getCount());

            SpanQueryBuilder subQuery = this.buildSpanQuery(field, values.get(i + 1));
            query = new PositionNearQueryBuilder(query, range)
                    .inOrder(location.isInOrder())
                    .addClause(subQuery);

            // 同句同段
            ValueLocation.SenSeg locationSenSeg = location.getSenSeg();
            if (null != locationSenSeg) {
                // FIXED 2024-02-19 逗号、句号、问号等同句判断符，分词后不再相同，改为 wildcard 形式。
                SpanQueryBuilder excludeQuery = null;
                if (ValueLocation.SenSeg.SEG == locationSenSeg) {
                    excludeQuery = new PositionTermQueryBuilder(field, IPConditionConstants.SEPARATOR_SEG);
                } else {
                    excludeQuery = new SpanMultiTermQueryBuilder(new TermWildcardQueryBuilder(field, IPConditionConstants.SEPARATOR_SEN));
                }
//                String excludes = this.getExcludes(locationSenSeg);
//                SpanQueryBuilder excludeQuery = new PositionTermQueryBuilder(field, excludes);
                query = new SpanNotQueryBuilder(query, excludeQuery);
            }
        }

        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(RangeItem item) {
        // FIXED 2024-03-29 代码恢复，使用 range
        String format = "";
        if (StringUtils.isNotBlank(item.formatFrom()) && StringUtils.isNotBlank(item.formatTo())) {
            format = item.formatFrom() + "||" + item.formatTo();
        }

        RangeQueryBuilder query = new RangeQueryBuilder(item.getFieldName()).from(item.from()).to(item.to());
        this.formatIfNecessary(query, format);
        return this.reverseIfNecessary(query, item);

//        // FIXED 2023-12-13 分成两种情况处理：单个值直接处理
//        if (item.from().equals(item.to())) {
//            RangeQueryBuilder query = new RangeQueryBuilder(item.getFieldName()).from(item.from()).to(item.to());
//            this.formatIfNecessary(query, item.formatFrom());
//            return this.reverseIfNecessary(query, item);
//        }
//
//        // FIXED 2023-12-13 多个值分成 >= 和 <= 用 must 连接
//        RangeQueryBuilder formRange = new RangeQueryBuilder(item.getFieldName()).gte(item.from());
//        this.formatIfNecessary(formRange, item.formatFrom());
//        QueryBuilder formQuery = this.reverseIfNecessary(formRange, item);
//
//        RangeQueryBuilder toRange = new RangeQueryBuilder(item.getFieldName()).lte(item.to());
//        this.formatIfNecessary(toRange, item.formatTo());
//        QueryBuilder toQuery = this.reverseIfNecessary(toRange, item);
//
//        return new BoolQueryBuilder().must(formQuery).must(toQuery);
    }

    @Override
    public QueryBuilder visit(WildcardItem item) {
        String field = item.getFieldName();
        String value = item.getFieldValue().get(0);

        /**
         * patent_phrase_wildcard 先分词再检索，patent_term_wildcard 作为一个整体检索
         *
         * text 类型字段：
         *   使用 patent_phrase_wildcard，自动分词，自动小写，最理想的用法
         *   使用 patent_term_wildcard，只能检一个汉字/单词，不能检短语，忽略大小写
         * keyword 类型字段：
         *   使用 patent_phrase_wildcard，检索不出
         *   使用 patent_term_wildcard，大小写敏感
         */
        // FIXED 2024-02-19 只有 keyword 类型的，使用 term。有 text 类型的，根据中英文使用 term 或 phrase
        QueryBuilder query = item.useTerm() ? new TermWildcardQueryBuilder(field, value) : buildWildcardQuery(field, value, false);
        return this.reverseIfNecessary(query, item);
    }

    @Override
    public QueryBuilder visit(GeoItem item) {
        String field = item.getFieldName();
        List<Double> value = item.getFieldValue();
        double distance = value.size() > 2 ? value.get(2) : IPConditionConstants.GEO_DISTANCE;

        GeoDistanceQueryBuilder geoBuilder = new GeoDistanceQueryBuilder(field).geoDistance(GeoDistance.PLANE)
                .distance(distance, DistanceUnit.KILOMETERS).point(value.get(0), value.get(1));
        return this.reverseIfNecessary(geoBuilder, item);
    }

    @Override
    public QueryBuilder visit(VectorItem item) {
        String field = item.getFieldName();
        float[] vector = item.getFeature();
        String field_vec = item.isImage() ? IPConditionConstants.DEFAULT_IMAGE_FIELD : IPConditionConstants.DEFAULT_SEMANTIC_FIELD;

        // 区分 gw_lsh 和 gw_pq
        KNNQueryBuilder builder = new LSHQueryBuilder();
        if (item.isRescore()) {
            builder = new PQQueryBuilder();
        }

        builder.setFieldName(field + field_vec).setQVector(vector);
        return new NestedQueryBuilder(field, builder, ScoreMode.Max);
    }

    // FIXED 2023-12-13 先 nested 再 must_not，把 nested 从 ExpressionConverter 中移出来
    private QueryBuilder reverseIfNecessary(QueryBuilder query, AbstractItemCondition item) {
        query = this.nestedIfNecessary(query, item);

        if (item.isReverse()) {
            return new BoolQueryBuilder().mustNot(query);
        }

        return query;
    }

    private QueryBuilder nestedIfNecessary(QueryBuilder query, AbstractItemCondition item) {
        if (item.isNestedField()) {
            query = new NestedQueryBuilder(item.getNestedName(), query, ScoreMode.None);
        }

        return query;
    }

    private void formatIfNecessary(RangeQueryBuilder query, String format) {
        if (StringUtils.isNotBlank(format)) {
            query.format(format);
        }
    }

    private Range convertOperator(ItemOperator operator, int count) {
        switch (operator) {
            case ITEM_OPERATOR_LT:
                return new DoubleBound().lt(count).gte(0);
            case ITEM_OPERATOR_LE:
                return new DoubleBound().lte(count).gte(0);
            case ITEM_OPERATOR_GT:
                return new GT().boundary(count);
            case ITEM_OPERATOR_GE:
                return new GTE().boundary(count);
            default:
                return new EQ().boundary(count);
        }
    }

    private SingleBound convertOperator(ItemOperator operator) {
        switch (operator) {
            case ITEM_OPERATOR_LT:
                return new LT();
            case ITEM_OPERATOR_LE:
                return new LTE();
            case ITEM_OPERATOR_GT:
                return new GT();
            case ITEM_OPERATOR_GE:
                return new GTE();
            default:
                return new EQ();
        }
    }

    private SpanQueryBuilder buildSpanQuery(String field, Value value) {
        String fieldValue = value.getValue();
        if (value.hasWildCard()) {
            return (SpanQueryBuilder) buildWildcardQuery(field, fieldValue, true);
        } else {
            // FIXED 2024-01-31 无论中英文都使用 patent_position_phrase，这样会使用分词器，英文自动转为小写
            return new PositionPhraseQueryBuilder(field, fieldValue);
//            return hasZh ? new PositionPhraseQueryBuilder(field, fieldValue) : new PositionTermQueryBuilder(field, fieldValue);
        }
    }

    /**
     * patent_term_wildcard 检索 text 类型时会忽略大小写，但是在检索 keyword 类型时，会有大小写问题
     * 1、中文，直接使用 patent_position_phrase
     * 2、中英文混合，通配符代表一个词，所以也使用 patent_position_phrase
     * 3、英文单词，使用 patent_term_wildcard。如果是位置检索，使用 span_multi 包裹
     * 4、英文短语，转为 span_near 语句，包裹多个子语句。有通配符的使用 patent_term_wildcard，没有通配符的使用 span_term
     */
    private QueryBuilder buildWildcardQuery(String field, String value, boolean isPosition) {
        boolean hasZh = tool.hasZh(value);
        boolean hasJPOrKR = tool.hasJPOrKR(value);

        if (hasZh || hasJPOrKR) {

            // 中文、中英文混合
            return new PhraseWildcardQueryBuilder(field, value);
        } else {
            // 用分词器分词
            List<String> words = AnalyzerUtil.analyzeText(value);
            if (words.size() == 0) {

                // 分词有错误
                return new PhraseWildcardQueryBuilder(field, value);
            } else if (words.size() == 1) {

                // 英文单词
                TermWildcardQueryBuilder builder = new TermWildcardQueryBuilder(field, value);
                return isPosition ? new SpanMultiTermQueryBuilder(builder) : builder;
            } else {

                // 英文短语，拆分为多个 span_multi 和 span_term。其中 span_multi 包裹 patent_term_wildcard
                SpanNearQueryBuilder builder = null;
                for (int i = 0, length = words.size(); i < length; i++) {
                    String word = words.get(i);
                    boolean hasWildcard = tool.hasWildCard(word);
                    SpanQueryBuilder spanBuilder = hasWildcard ? new SpanMultiTermQueryBuilder(new TermWildcardQueryBuilder(field, word)) : new SpanTermQueryBuilder(field, word);

                    if (i == 0) {
                        builder = new SpanNearQueryBuilder(spanBuilder, 0);
                    } else {
                        builder.addClause(spanBuilder);
                    }
                }

                return builder;
            }
        }
    }

    private String getExcludes(ValueLocation.SenSeg senSeg) {
        switch (senSeg) {
            case SEG:
                return IPConditionConstants.SEPARATOR_SEG;
            default:
                return IPConditionConstants.SEPARATOR_SEN;
        }
    }
}
