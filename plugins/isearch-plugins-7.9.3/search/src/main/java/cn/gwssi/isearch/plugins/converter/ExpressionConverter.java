package cn.gwssi.isearch.plugins.converter;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;
import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.VectorItem;
import cn.gwssi.isearch.plugins.search.ItemVisitorImpl;
import cn.gwssi.isearch.plugins.search.QueryBuilderHelper;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.rescore.QueryRescoreMode;
import org.elasticsearch.search.rescore.QueryRescorerBuilder;

import java.util.ArrayList;
import java.util.List;

public class ExpressionConverter {

    private final ItemVisitor<QueryBuilder> visitor = new ItemVisitorImpl();
    //    private final ItemVisitor<QueryRescorerBuilder> vectorVisitor = new VectorItemVisitorImpl();
    private final QueryBuilderHelper queryBuilderHelper = new QueryBuilderHelper();
    private final List<QueryBuilder> pictureBuilders = new ArrayList<>();
    private final List<QueryBuilder> semanticBuilders = new ArrayList<>();
    private final List<QueryRescorerBuilder> rescorerBuilders = new ArrayList<>(2);
    private boolean hasVector = false;
    private int windowSize;
    private final QueryBuilder queryBuilder;

    public ExpressionConverter(IPCondition ipCondition, int windowSize) throws Exception {
        this.windowSize = windowSize;
        this.extractVector(ipCondition);
        this.queryBuilder = this.createQueryBuilder(ipCondition);
    }

    // 判断是否有vector。condition 中的 hasVector 属性不准确，是给 searchImpl 处理返回数据用的
    private void extractVector(IPCondition ipCondition) {
        if (this.hasVector) return;

        if (ipCondition.isLeafCondition()) {
            this.hasVector = ipCondition instanceof VectorItem;
        } else {
            List<IPCondition> subConditions = ipCondition.getSubConditions();
            if (subConditions.size() > 0) {
                for (IPCondition subCondition : subConditions) {
                    this.extractVector(subCondition);
                }
            }
        }
    }

    // 转化为 queryBuilder
    private QueryBuilder createQueryBuilder(IPCondition ipCondition) throws Exception {
        if (ipCondition.isLeafCondition()) {
            AbstractItemConditionExt conditionExt = (AbstractItemConditionExt) ipCondition;
            QueryBuilder builder = createItemBuilder(conditionExt, false);

            if (conditionExt instanceof VectorItem) {
                VectorItem vectorItem = (VectorItem)conditionExt;
                builder = createItemBuilder(conditionExt, true);

                // 生成 rescore 语句，使用 gw_pq
                vectorItem.setRescore(true);
                QueryBuilder rescoreBuilder = createItemBuilder(vectorItem, true);
                this.createItemRescore(rescoreBuilder, vectorItem.isImage());
            }

            return builder;
        }

        List<IPCondition> subConditions = ipCondition.getSubConditions();
        if (subConditions == null || subConditions.isEmpty()) {
            return null;
        }

        QueryBuilder firstQueryBuilder = createQueryBuilder(subConditions.get(0));
        List<ItemConnector> connectors = ipCondition.getConnectors();
        if (connectors.isEmpty()) {
            return firstQueryBuilder;
        }

        BoolQueryBuilder preBoolBuilder = new BoolQueryBuilder();
        ItemConnector preConnector = null;
        for (int i = 0; i < connectors.size(); i++) {
            ItemConnector connector = connectors.get(i);

            // FIXED 2024-01-03 connectors 一般会比 subConditions 少一个
            // 当两者数量相同，且最后一个 connector = not 时，需要取反
            // 例如 not (b or c)
            if (i == subConditions.size() - 1 && connector == ItemConnector.ITEM_CONNECTOR_NOT) {
                return new BoolQueryBuilder().mustNot(preBoolBuilder);
            }

            IPCondition subCondition = subConditions.get(i + 1);
            QueryBuilder currQueryBuilder = createQueryBuilder(subCondition);
            // 没有前置连接符
            if (preConnector == null) {
                if (connector.equals(ItemConnector.ITEM_CONNECTOR_AND)) {
                    preBoolBuilder.must(firstQueryBuilder);
                    preBoolBuilder.must(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_OR)) {
                    preBoolBuilder.should(firstQueryBuilder);
                    preBoolBuilder.should(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_NOT)) {
                    preBoolBuilder.must(firstQueryBuilder);
                    preBoolBuilder.mustNot(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_XOR)) {
                    preBoolBuilder = queryBuilderHelper.xor(firstQueryBuilder, currQueryBuilder);
                }
            } else if (connector.equals(preConnector)) {
                // 有前置连接符，且与当前连接符一样
                if (connector.equals(ItemConnector.ITEM_CONNECTOR_AND)) {
                    preBoolBuilder.must(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_OR)) {
                    preBoolBuilder.should(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_NOT)) {
                    preBoolBuilder.mustNot(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_XOR)) {
                    preBoolBuilder = queryBuilderHelper.xor(preBoolBuilder, currQueryBuilder);
                }
            } else {
                // 有前置连接符，且与当前连接符 不一样
                BoolQueryBuilder _BoolBuilder = new BoolQueryBuilder();
                if (connector.equals(ItemConnector.ITEM_CONNECTOR_AND)) {
                    _BoolBuilder.must(preBoolBuilder);
                    _BoolBuilder.must(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_OR)) {
                    _BoolBuilder.should(preBoolBuilder);
                    _BoolBuilder.should(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_NOT)) {
                    _BoolBuilder.must(preBoolBuilder);
                    _BoolBuilder.mustNot(currQueryBuilder);
                } else if (connector.equals(ItemConnector.ITEM_CONNECTOR_XOR)) {
                    _BoolBuilder = queryBuilderHelper.xor(preBoolBuilder, currQueryBuilder);
                }
                preBoolBuilder = _BoolBuilder;
            }

            preConnector = connector;
        }

        return preBoolBuilder;
    }

    private QueryBuilder createItemBuilder(AbstractItemConditionExt ipCondition, boolean isVector) throws Exception {
        QueryBuilder query = ipCondition.accept(visitor);
        // FIXED 2023-12-13 先 nested 再 must_not，把 nested 移到 ItemVisitorImpl 中
//        if (ipCondition.isNestedField()) {
//            query = new NestedQueryBuilder(ipCondition.getNestedName(), query, ScoreMode.None);
//        }

        float boost = ipCondition.getBoost();
        if (boost > -1) {
            query.boost(boost);
        }

        // FIXED 2023-04-06 除了向量，其它都不参与打分
        if (this.hasVector && !isVector) {
            query.boost(0);
        }

        return query;
    }

    // FIXED 2023-06-01
    // 分为图像、语义两大类，每一类里面的多个条件用 dis_max 连接，两个类用多个 rescore 连接
    private void createItemRescore(QueryBuilder builder, boolean isImage) {
        if (isImage) {
            this.pictureBuilders.add(builder);
        } else {
            this.semanticBuilders.add(builder);
        }
    }

    public QueryBuilder getQueryBuilder() {
        return null == queryBuilder ? QueryBuilders.matchAllQuery() : queryBuilder;
    }

    // 分为图像、语义两大类，每一类里面的多个条件用 dis_max 连接，两个类用多个 rescore 连接
    public List<QueryRescorerBuilder> getRescorerBuilders() {
        this.generateRescore(this.pictureBuilders);
        this.generateRescore(this.semanticBuilders);

        return rescorerBuilders;
    }

    private void generateRescore(List<QueryBuilder> queryBuilders) {
        int size = queryBuilders.size();
        if (size > 0) {
            QueryBuilder builder = queryBuilders.get(0);
            if (size > 1) {

                DisMaxQueryBuilder disMaxQueryBuilder = new DisMaxQueryBuilder();
                for (QueryBuilder queryBuilder : queryBuilders) {
                    disMaxQueryBuilder.add(queryBuilder);
                }
                builder = disMaxQueryBuilder;
            }

            QueryRescorerBuilder rescorerBuilder = new QueryRescorerBuilder(builder).setScoreMode(QueryRescoreMode.Max)
                    .setQueryWeight(IPConditionConstants.RESCORE_QUERY_WEIGHT).windowSize(this.windowSize);
            this.rescorerBuilders.add(rescorerBuilder);
        }
    }
}