package cn.gwssi.isearch.plugins.search.rest.admin.indices;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import com.carrotsearch.hppc.cursors.ObjectCursor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsRequest;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.cluster.metadata.MappingMetadata;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.collect.ImmutableOpenMap;
import org.elasticsearch.common.logging.DeprecationLogger;
import org.elasticsearch.common.regex.Regex;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.util.set.Sets;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.indices.TypeMissingException;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static org.elasticsearch.rest.RestRequest.Method.GET;
import static org.elasticsearch.rest.RestRequest.Method.HEAD;

public class RestGetMappingAction extends BaseRestAction {

    private static final Logger logger = LogManager.getLogger(org.elasticsearch.rest.action.admin.indices.RestGetMappingAction.class);
    private static final DeprecationLogger deprecationLogger = new DeprecationLogger(logger);
    public static final String TYPES_DEPRECATION_MESSAGE = "[types removal] Using include_type_name in get" +
            " mapping requests is deprecated. The parameter will be removed in the next major version.";

//    public RestGetMappingAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestGetMappingAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "get_mapping_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/mapping"));
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/mappings"));
        routes.add(new RestHandler.Route(GET, "/{index}"+Path.ROOT+"/mapping"));
        routes.add(new RestHandler.Route(GET, "/{index}"+Path.ROOT+"/mappings"));
        routes.add(new RestHandler.Route(GET, "/{index}"+Path.ROOT+"/mapping/{type}"));
        routes.add(new RestHandler.Route(GET, "/{index}"+Path.ROOT+"/mappings/{type}"));
        routes.add(new RestHandler.Route(HEAD, "/{index}"+Path.ROOT+"/mapping/{type}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        final String[] indices = Strings.splitStringByCommaToArray(request.param("index"));
        final String[] types = request.paramAsStringArrayOrEmptyIfAll("type");
        boolean includeTypeName = request.paramAsBoolean(INCLUDE_TYPE_NAME_PARAMETER, DEFAULT_INCLUDE_TYPE_NAME_POLICY);

        if (request.method().equals(HEAD)) {
            deprecationLogger.deprecatedAndMaybeLog("get_mapping_types_removal",
                    "Type exists requests are deprecated, as types have been deprecated.");
        } else if (includeTypeName == false && types.length > 0) {
            throw new IllegalArgumentException("Types cannot be provided in get mapping requests, unless" +
                    " include_type_name is set to true.");
        }
        if (request.hasParam(INCLUDE_TYPE_NAME_PARAMETER)) {
            deprecationLogger.deprecatedAndMaybeLog("get_mapping_with_types", TYPES_DEPRECATION_MESSAGE);
        }

        final GetMappingsRequest getMappingsRequest = new GetMappingsRequest();
        getMappingsRequest.indices(indices).types(types);
        getMappingsRequest.indicesOptions(IndicesOptions.fromRequest(request, getMappingsRequest.indicesOptions()));
        getMappingsRequest.masterNodeTimeout(request.paramAsTime("master_timeout", getMappingsRequest.masterNodeTimeout()));
        getMappingsRequest.local(request.paramAsBoolean("local", getMappingsRequest.local()));
        try {
            return channel -> client.admin().indices().getMappings(getMappingsRequest, new RestBuilderListener<GetMappingsResponse>(channel) {
                @Override
                public RestResponse buildResponse(final GetMappingsResponse response, final XContentBuilder builder) throws Exception {
                    final ImmutableOpenMap<String, ImmutableOpenMap<String, MappingMetadata>> mappingsByIndex = response.getMappings();
                    if (mappingsByIndex.isEmpty() && types.length != 0) {
                        builder.close();
                        return new BytesRestResponse(channel, new TypeMissingException("_all", String.join(",", types)));
                    }
                    return new BytesRestResponse(RestStatus.OK, responseBuilder(mappingsByIndex,builder,response,request,types));
                }
            });
        }catch (Exception e){
            return channel -> channel.sendResponse(badResponse(e.getMessage()));
        }

    }

    private XContentBuilder responseBuilder(ImmutableOpenMap<String, ImmutableOpenMap<String, MappingMetadata>> mappingsByIndex,
                                            XContentBuilder builder,
                                            GetMappingsResponse response,
                                            RestRequest request,
                                            String[] types) throws IOException {
        final Set<String> typeNames = new HashSet<>();
        for (final ObjectCursor<ImmutableOpenMap<String, MappingMetadata>> cursor : mappingsByIndex.values()) {
            for (final ObjectCursor<String> inner : cursor.value.keys()) {
                typeNames.add(inner.value);
            }
        }

        final SortedSet<String> difference = Sets.sortedDifference(Arrays.stream(types).collect(Collectors.toSet()), typeNames);

        // now remove requested aliases that contain wildcards that are simple matches
        final List<String> matches = new ArrayList<>();
        outer:
        for (final String pattern : difference) {
            if (pattern.contains("*")) {
                for (final String typeName : typeNames) {
                    if (Regex.simpleMatch(pattern, typeName)) {
                        matches.add(pattern);
                        continue outer;
                    }
                }
            }
        }
        difference.removeAll(matches);

        final RestStatus status;
        builder.startObject();
        {
            if (difference.isEmpty()) {
            } else {
                status = RestStatus.NOT_FOUND;
                final String message = String.format(Locale.ROOT, "type" + (difference.size() == 1 ? "" : "s") +
                        " [%s] missing", Strings.collectionToCommaDelimitedString(difference));
                builder.field("error", message);
                builder.field("status", status.getStatus());
            }
            response.toXContent(builder, request);
        }
        builder.endObject();

        return builder;
    }
}
