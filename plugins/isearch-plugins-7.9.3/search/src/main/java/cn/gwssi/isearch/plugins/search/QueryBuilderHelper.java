package cn.gwssi.isearch.plugins.search;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

public class QueryBuilderHelper
{
    public BoolQueryBuilder xor(QueryBuilder f, QueryBuilder s)
    {
        return QueryBuilders.boolQuery()
                .should(QueryBuilders.boolQuery().must(f).mustNot(s))
                .should(QueryBuilders.boolQuery().mustNot(f).must(s));
    }
}
