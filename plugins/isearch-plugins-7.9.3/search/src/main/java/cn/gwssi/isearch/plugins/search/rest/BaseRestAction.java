package cn.gwssi.isearch.plugins.search.rest;

import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.rest.BaseRestHandler;
import org.elasticsearch.rest.BytesRestResponse;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.RestStatus;

import java.io.IOException;

public abstract class BaseRestAction extends BaseRestHandler
{
    protected BaseRestAction() {}

    protected BaseRestAction(Settings settings)
    {
        // FIXED 2021-01-12 此版本已经去掉了这个构造
//        super(settings);
    }

    protected boolean parametersNonNull(RestRequest restRequest, String... pNames)
    {
        String[] values = new String[pNames.length];

        for(int i=0;i<pNames.length;i++)
            values[i] = restRequest.param(pNames[i]);

        if (hasNullOrEmpty(values))
            return false;

        return true;
    }

    protected BytesRestResponse response(Status status, String msg) throws IOException
    {
        return new BytesRestResponse(RestStatus.OK, buildMessage(status, msg));
    }

    protected BytesRestResponse badResponse(String msg) throws IOException
    {
        return response(Status.ERROR, msg);
    }

    protected XContentBuilder buildMessage(Status status,String msg) throws IOException
    {
        return XContentFactory.jsonBuilder()
                .startObject()
                .field("status", status)
                .field("msg", msg)
                .endObject();
    }

    protected boolean hasNullOrEmpty(String... strings)
    {
        for (String str : strings)
        {
            if(Strings.isNullOrEmpty(str))
                return true;
        }

        return false;
    }

    protected enum Status
    {
        ERROR("error"),

        SUCC("success");

        private String value;

        Status(String value)
        {
            this.value = value;
        }


        @Override
        public String toString()
        {
            return this.value;
        }
    }
}
