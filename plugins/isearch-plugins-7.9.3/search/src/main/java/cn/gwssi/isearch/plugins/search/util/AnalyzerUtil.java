package cn.gwssi.isearch.plugins.search.util;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;
import tech.easynlp.elasticsearch.analysis.smartWildcard.SmartWildcardAnalyzer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AnalyzerUtil {

    public static List<String> analyzeText(String content) {
        List<String> contents = new ArrayList<>();

        Analyzer analyzer = new SmartWildcardAnalyzer();

        try {
            // 获取该分析器的词元流（token stream）
            TokenStream tokenStream = analyzer.tokenStream("content", content);
            tokenStream.reset(); // 重置词元流至开始位置

            // 获取词元流中的词项属性
            CharTermAttribute charTermAttribute = tokenStream.getAttribute(CharTermAttribute.class);

            // 分词结果
            while (tokenStream.incrementToken()) {
                contents.add(charTermAttribute.toString());
            }

            // 关闭词元流和分析器（如果需要的话）
            tokenStream.end();
            tokenStream.close();
        } catch (IOException e) {
            // do nothing
        }

        return contents;
    }

}
