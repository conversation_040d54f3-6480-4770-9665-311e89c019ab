package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.common.Strings;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;

import java.util.List;

public class Terms implements AggBuilder {
    private TermsAggregationBuilder termsBuilder;

    public Terms() {
    }

    public Terms(TermsAggregationBuilder termsBuilder) {
        this.termsBuilder = termsBuilder;
    }

    @Override
    public AggregationBuilder build() {
        return this.termsBuilder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        termsBuilder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        TermsAggregationBuilder builder = AggregationBuilders.terms(dim.getFieldName())
                .size(dim.getSize());
        if (dim.getMinDocCount() >= 0) {
            builder.minDocCount(dim.getMinDocCount());
        }
        if (dim.getFilterValue() != null && dim.getFilterValue().size() > 0) {
            builder.includeExclude(include(dim.getFilterValue()));
        }

        builder.shardSize(dim.getShardSize());

        BucketOrder order = AggBuilder.bucketOrder(dim.getAggrOrder());
        if (null != order) {
            builder.order(order);
        }

        if (Strings.isNullOrEmpty(dim.path())) {
            builder.field(dim.getFieldName());
            this.termsBuilder = builder;
            return this;
        } else {
            builder.field((dim.path() + "." + dim.getFieldName()));
            NestedAggregationBuilder nestedBuilder = AggregationBuilders.nested(dim.path(), dim.path());
            return new Nested(nestedBuilder, builder, dim.path());
        }
    }

    private IncludeExclude include(List<String> includes) {
        return new IncludeExclude(includes.toArray(new String[includes.size()]), null);
    }
}
