package cn.gwssi.isearch.plugins.search.rest.admin.cluster;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.elasticsearch.action.admin.cluster.state.ClusterStateRequest;
import org.elasticsearch.action.admin.cluster.state.ClusterStateResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.cluster.ClusterState;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.settings.SettingsFilter;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.*;

public class RestClusterStateAction extends BaseRestAction {

    private final SettingsFilter settingsFilter;
//    public RestClusterStateAction(Settings settings, RestController controller, SettingsFilter settingsFilter){
//        super(settings);
//        controller.registerHandler(this);
//        this.settingsFilter = settingsFilter;
//    }

    public RestClusterStateAction(SettingsFilter settingsFilter) {
        this.settingsFilter = settingsFilter;
    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "cluster_state_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.ROOT+"/cluster/state"));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.ROOT+"/cluster/state/{metric}"));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.ROOT+"/cluster/state/{metric}/{indices}"));

        return routes;
    }


    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        final ClusterStateRequest clusterStateRequest = createClusterStateRequest(request);
        settingsFilter.addFilterSettingParams(request);

        try {
            return channel -> client.admin().cluster().state(clusterStateRequest, new RestBuilderListener<ClusterStateResponse>(channel) {
                @Override
                public RestResponse buildResponse(ClusterStateResponse response, XContentBuilder builder) throws Exception {

                    return new BytesRestResponse(RestStatus.OK, responseBuilder(clusterStateRequest,response,builder,request));
                }
            });
        }catch (Exception e){
            return channel -> channel.sendResponse(badResponse(e.getMessage()));
        }

    }

    private ClusterStateRequest createClusterStateRequest(RestRequest request){
        ClusterStateRequest clusterStateRequest = Requests.clusterStateRequest();
        clusterStateRequest.indicesOptions(IndicesOptions.fromRequest(request, clusterStateRequest.indicesOptions()));
        clusterStateRequest.local(request.paramAsBoolean("local", clusterStateRequest.local()));
        clusterStateRequest.masterNodeTimeout(request.paramAsTime(Fields.MASTER_TIMEOUT, clusterStateRequest.masterNodeTimeout()));
        if (request.hasParam(Fields.WAIT_FOR_METADATA_VERSION)) {
            clusterStateRequest.waitForMetadataVersion(request.paramAsLong("wait_for_metadata_version", 0));
        }
        clusterStateRequest.waitForTimeout(request.paramAsTime(Fields.WAIT_FOR_TIMEOUT,
                ClusterStateRequest.DEFAULT_WAIT_FOR_NODE_TIMEOUT));

        final String[] indices = Strings.splitStringByCommaToArray(request.param("indices", "_all"));
        boolean isAllIndicesOnly = indices.length == 1 && "_all".equals(indices[0]);
        if (!isAllIndicesOnly) {
            clusterStateRequest.indices(indices);
        }

        if (request.hasParam("metric")) {
            EnumSet<ClusterState.Metric> metrics = ClusterState.Metric.parseString(request.param("metric"), true);
            // do not ask for what we do not need.
            clusterStateRequest.nodes(metrics.contains(ClusterState.Metric.NODES) || metrics.contains(ClusterState.Metric.MASTER_NODE));
            /*
             * there is no distinction in Java api between routing_table and routing_nodes, it's the same info set over the wire, one single
             * flag to ask for it
             */
            clusterStateRequest.routingTable(
                    metrics.contains(ClusterState.Metric.ROUTING_TABLE) || metrics.contains(ClusterState.Metric.ROUTING_NODES));
            clusterStateRequest.metadata(metrics.contains(ClusterState.Metric.METADATA));
            clusterStateRequest.blocks(metrics.contains(ClusterState.Metric.BLOCKS));
            clusterStateRequest.customs(metrics.contains(ClusterState.Metric.CUSTOMS));
        }
        return clusterStateRequest;
    }

    private XContentBuilder responseBuilder(ClusterStateRequest clusterStateRequest,
                                            ClusterStateResponse response,
                                            XContentBuilder builder,
                                            RestRequest request) throws IOException {
        builder.startObject();
        if (clusterStateRequest.waitForMetadataVersion() != null) {
            builder.field(Fields.WAIT_FOR_TIMED_OUT, response.isWaitForTimedOut());
        }
        builder.field(Fields.CLUSTER_NAME, response.getClusterName().value());
        response.getState().toXContent(builder, request);
        builder.endObject();
        return builder;
    }



    private static final Set<String> RESPONSE_PARAMS;

    static {
        final Set<String> responseParams = new HashSet<>();
        responseParams.add("metric");
        responseParams.addAll(Settings.FORMAT_PARAMS);
        RESPONSE_PARAMS = Collections.unmodifiableSet(responseParams);
    }

    @Override
    protected Set<String> responseParams() {
        return RESPONSE_PARAMS;
    }

    @Override
    public boolean canTripCircuitBreaker() {
        return false;
    }

    static final class Fields {
        static final String WAIT_FOR_TIMED_OUT = "wait_for_timed_out";
        static final String CLUSTER_NAME = "cluster_name";
        static final String WAIT_FOR_TIMEOUT = "wait_for_timeout";
        static final String WAIT_FOR_METADATA_VERSION = "wait_for_metadata_version";
        static final String MASTER_TIMEOUT = "master_timeout";
    }
}
