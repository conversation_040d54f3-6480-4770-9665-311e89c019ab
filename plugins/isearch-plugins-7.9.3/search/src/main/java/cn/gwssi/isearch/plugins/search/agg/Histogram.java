package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationHistogramDim;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.HistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSelectorPipelineAggregationBuilder;

import java.util.HashMap;
import java.util.Map;

public class Histogram implements AggBuilder {
    private HistogramAggregationBuilder builder;

    public Histogram() {}

    public Histogram(HistogramAggregationBuilder builder) {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build() {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggregationHistogramDim histogramDim = (AggregationHistogramDim)dim;
        HistogramAggregationBuilder builder = AggregationBuilders.histogram(dim.getFieldName());
        builder.interval(histogramDim.getInterval());
        builder.offset(histogramDim.getOffset());
        builder.keyed(histogramDim.isKeyed());

        // min 和 max 必须同时设置，否则 buckets 创建太多了，内存不够
        if (histogramDim.getMaxBound() != Double.MAX_VALUE && histogramDim.getMinBound() != Double.MIN_VALUE) {
            builder.extendedBounds(histogramDim.getMinBound(), histogramDim.getMaxBound());
        }

        // extended_bounds 并不能控制返回的桶，这里使用 bucket_selector 过滤一下返回的数据
        if (histogramDim.getMaxBound() != Double.MAX_VALUE || histogramDim.getMinBound() != Double.MIN_VALUE) {
            Map<String, String> param = new HashMap<>();
            param.put("param", "_key");

            String script = "";
            if (histogramDim.getMaxBound() != Double.MAX_VALUE) {
                script = "params.param <= " + histogramDim.getMaxBound();
            }
            if (histogramDim.getMinBound() != Double.MIN_VALUE) {
                script += (StringUtils.isNotBlank(script) ? " && " : "") + "params.param >= " + histogramDim.getMinBound();
            }

            BucketSelectorPipelineAggregationBuilder subBuilder = new BucketSelectorPipelineAggregationBuilder(dim.getFieldName(), param, Script.parse(script));
            builder.subAggregation(subBuilder);
        }

        if (Strings.isNullOrEmpty(dim.path())) {
            builder.field(dim.getFieldName());
            this.builder = builder;
            return this;
        } else {
            builder.field((dim.path() + "." + dim.getFieldName()));
            NestedAggregationBuilder nestedBuilder = AggregationBuilders.nested(dim.path(), dim.path());
            return new Nested(nestedBuilder, builder, dim.path());
        }
    }
}
