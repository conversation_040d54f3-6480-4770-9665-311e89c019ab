package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.delete.DeleteRequestBuilder;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestDeleteAction extends BaseRestAction
{
//    public RestDeleteAction(Settings settings, RestController controller)
//    {
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestDeleteAction() {}

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "delete-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.DELETE, Path.DELETE));
        routes.add(new RestHandler.Route(RestRequest.Method.DELETE, Path.DELETE+"/{index}/{type}/{id}"));

        return routes;
    }

    @Override
    protected BaseRestHandler.RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        boolean refresh = request.paramAsBoolean("refresh", true);

        String index = request.param("index");
        String type = request.param("type");
        String id    = request.param("id");

        if (hasNullOrEmpty(index, id))
        {
            return channel -> channel.sendResponse(badResponse("parameters missing : [index,id]"));
        }
        else
        {
            DeleteRequestBuilder requestBuilder = client.prepareDelete()
                    .setIndex(index)
                    .setId(id)
                    .setRefreshPolicy(refresh ? WriteRequest.RefreshPolicy.IMMEDIATE : WriteRequest.RefreshPolicy.NONE);

            return channel -> requestBuilder.execute(new RestBuilderListener<DeleteResponse>(channel)
            {
                @Override
                public RestResponse buildResponse(DeleteResponse deleteResponse, XContentBuilder builder) throws Exception
                {
                    builder.startObject()
                            .field("found", deleteResponse.status() == RestStatus.OK ? true : false)
                            .endObject();

                    return new BytesRestResponse(RestStatus.OK, builder);
                }
            });
        }
    }
}
