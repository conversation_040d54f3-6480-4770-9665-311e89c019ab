package cn.gwssi.isearch.plugins.search.collpase;

import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.collapse.InnerHitCondition;
import cn.gwssi.esplugins.dprm.query.DPRMQueryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.InnerHitBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.ScoreSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;

import java.util.List;

public class CollapseService {

    private static CollapseService service;

    private CollapseService() {
    }

    public static CollapseService getInstance() {
        if (null == service) {
            service = new CollapseService();
        }

        return service;
    }

    // 去重查询
    public QueryBuilder addDistinct(CollapseCondition collapse, QueryBuilder query) {
        String field = collapse.getField();
        SortFieldList sorts = collapse.getSorts();

        // 组内返回多少个，暂时用不上
//        int sizeCollapse = collapse.getSize();

        // 申请号合并，不需要处理
        if ("anm".equalsIgnoreCase(field)) {
            return this.dprmQuery(field, query, sorts);
        }

        // 其它合并，合并字段可能没有值，需要分成两个查询
        ExistsQueryBuilder existsBuilder = QueryBuilders.existsQuery(field);
        QueryBuilder innerBuilder = QueryBuilders.boolQuery().must(query).must(existsBuilder);
        QueryBuilder dprmBuilder = this.dprmQuery(field, innerBuilder, sorts);

        QueryBuilder outterBuilder = QueryBuilders.boolQuery().must(query).mustNot(existsBuilder);
        return QueryBuilders.boolQuery().should(dprmBuilder).should(outterBuilder);
    }

    public CollapseBuilder getCollapse(List<InnerHitCondition> inner) {
        InnerHitCondition innerHit = inner.get(0);

        String[] includes = this.list2Array(innerHit.getFieldList());
        FetchSourceContext source = new FetchSourceContext(true, includes, null);
        InnerHitBuilder builder = new InnerHitBuilder(innerHit.getName()).
                setFetchSourceContext(source).setFrom(innerHit.getFrom()).setSize(innerHit.getSize());

        SortFieldList sorts = innerHit.getSortFieldList();
        if (null != sorts) {
            SortBuilder sort = this.sortBuilder(sorts);
            if (null != sort) {
                builder.addSort(sort);
            }
        }

        CollapseBuilder collapseBuilder = new CollapseBuilder(innerHit.getName());
        collapseBuilder.setInnerHits(builder);
        return collapseBuilder;
    }

//    private void addCollapse(Condition condition, SearchRequestBuilder requestBuilder) {
//        CollapseCondition collapse = condition.getCollapseCondition();
//        if (null != collapse) {
//            CollapseBuilder collapseBuilder = new CollapseBuilder(collapse.getField());
//            List<InnerHitCondition> innerHits = collapse.getInnerHits();
//
//            if (null != innerHits && innerHits.size() > 0) {
//                List<InnerHitBuilder> innerHitBuilders = innerHits.stream().map(this::innerHitBuilder).collect(Collectors.toList());
//                collapseBuilder.setInnerHits(innerHitBuilders);
//            }
//
//            requestBuilder.setCollapse(collapseBuilder);
//        }
//    }
//
//    private InnerHitBuilder innerHitBuilder(InnerHitCondition innerHit) {
//        String[] includes = this.list2Array(innerHit.getFieldList());
//        FetchSourceContext source = new FetchSourceContext(true, includes, null);
//        InnerHitBuilder builder = new InnerHitBuilder(innerHit.getName()).
//                setFetchSourceContext(source).setFrom(innerHit.getFrom()).setSize(innerHit.getSize());
//
//        SortFieldList sorts = innerHit.getSortFieldList();
//        if (null != sorts) {
//            for (SortField sortField : sorts) {
//                builder.addSort(this.sortBuilder(sortField));
//            }
//        }
//
//        return builder;
//    }

    private DPRMQueryBuilder dprmQuery(String field, QueryBuilder query, SortFieldList sorts) {
        DPRMQueryBuilder builder = new DPRMQueryBuilder()
                .fieldName(field)
                .innerQuery(query);

        // 排序规则
        if (null != sorts && sorts.size() > 0) {
            for (SortField sort : sorts) {
                if (null != sort.getOrder()) {
                    builder.addSort(sort.getFieldName(), this.sortOrder(sort.getOrder()));
                } else {
                    builder.addSort(sort.getFieldName(), sort.getCustomOrder().split(","));
                }
            }
        }

        return builder;
    }

    // 只取第一个排序条件，跳过 _score 和 自定义排序
    private SortBuilder sortBuilder(SortFieldList sorts) {
        for (SortField sort : sorts) {
            String name = sort.getFieldName();

            // 跳过 _score 排序和自定义排序
            if (name.equals(ScoreSortBuilder.NAME) || StringUtils.isNotBlank(sort.getCustomOrder())) {
                continue;
            }

            return SortBuilders.fieldSort(name).order(sortOrder(sort.getOrder()));
        }

        return null;
    }

    private SortOrder sortOrder(SortField.IPSorting ipSorting) {
        switch (ipSorting) {
            case ASC:
                return SortOrder.ASC;
            case DESC:
                return SortOrder.DESC;
            default:
                return SortOrder.ASC;
        }
    }

    private String[] list2Array(List<String> list) {
        return (null == list || list.size() <= 0) ? null : list.toArray(new String[list.size()]);
    }

}
