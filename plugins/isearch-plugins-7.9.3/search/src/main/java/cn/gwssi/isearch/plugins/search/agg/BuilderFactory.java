package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.*;

import java.util.List;


public class BuilderFactory
{
    public AggBuilder builder(AggregationDim dim)
    {
        AggBuilder builder = sole(dim, false);

//        AggBuilder temp = builder;
//
//        AggregationDim child = dim.child();
//
//        while (child != null)
//        {
//            temp = temp.sub(sole(child, temp.isNested(), temp.path()));
//            child = child.child();
//        }

        // FIXED 2022-12-12 每一级的聚合条件可能是多个
        this.multi(dim.getAggregationDims(), builder);

        return builder;
    }

    private void multi(List<AggregationDim> children, AggBuilder parent) {
        if (children != null && children.size() > 0) {
            for (AggregationDim child : children) {
                AggBuilder sub = this.sole(child, parent.isNested());
                parent.sub(sub);
                this.multi(child.getAggregationDims(), sub);
            }
        }
    }

    private AggBuilder sole(AggregationDim dim, boolean fatherNested) {
        AggBuilder builder = this.basic(dim);

        if (fatherNested)
            return new Reverse(builder, "reverse_for_" + dim.getFieldName(), null);
        else
            return builder;
    }

    private AggBuilder basic(AggregationDim dim) {
        AggBuilder aggType = null;
        if (dim instanceof AggregationDateHistogramDim) {
            aggType = new DateHistogram();
        } else if (dim instanceof AggregationRangeDim) {
            aggType = new Range();
        } else if (dim instanceof AggregationDateRangeDim) {
            aggType = new DateRange();
        } else if (dim instanceof AggregationFiltersDim) {
            aggType = new Filters();
        } else if (dim instanceof AggeragationCardinalityDim) {
            // FIXED 2023-03-22 第三版，换成插件去做，实际上已经用不到了
            // FIEXED 2023-02-17 第二版，换成 metric_script 去统计
            aggType = new Cardinality();
//            aggType = new CardinalityScript();
        } else if (dim instanceof AggregationHistogramDim) {
            aggType = new Histogram();
        } else {
            aggType = new Terms();
        }

        return aggType.buildBasic(dim);
    }

}
