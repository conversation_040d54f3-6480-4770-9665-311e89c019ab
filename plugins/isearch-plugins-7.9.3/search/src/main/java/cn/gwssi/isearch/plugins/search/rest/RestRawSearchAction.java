package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestStatusToXContentListener;
import org.elasticsearch.rest.action.search.RestSearchAction;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.IntConsumer;

import static org.elasticsearch.rest.RestRequest.Method.GET;
import static org.elasticsearch.rest.RestRequest.Method.POST;

public class RestRawSearchAction extends BaseRestAction
{
//    public RestRawSearchAction(Settings settings, RestController controller)
//    {
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestRawSearchAction() {}

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "raw-search-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, Path.SEARCH));
        routes.add(new RestHandler.Route(POST, Path.SEARCH));
        routes.add(new RestHandler.Route(GET, Path.SEARCH+"/{index}"));
        routes.add(new RestHandler.Route(POST, Path.SEARCH+"/{index}"));
        routes.add(new RestHandler.Route(GET, Path.SEARCH+"/{index}/{type}"));
        routes.add(new RestHandler.Route(POST, Path.SEARCH+"/{index}/{type}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        SearchRequest searchRequest = new SearchRequest();
        IntConsumer setSize = size -> searchRequest.source().size(size);
        request.withContentOrSourceParamParserOrNull(parser ->
                RestSearchAction.parseSearchRequest(searchRequest, request, parser, setSize));

        return channel -> client.search(searchRequest, new RestStatusToXContentListener<>(channel));
    }
}
