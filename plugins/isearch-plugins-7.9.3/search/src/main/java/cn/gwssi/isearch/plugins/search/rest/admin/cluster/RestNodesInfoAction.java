package cn.gwssi.isearch.plugins.search.rest.admin.cluster;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.elasticsearch.action.admin.cluster.node.info.NodesInfoRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.settings.SettingsFilter;
import org.elasticsearch.common.util.ArrayUtils;
import org.elasticsearch.common.util.CollectionUtils;
import org.elasticsearch.common.util.set.Sets;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestActions;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.elasticsearch.rest.RestRequest.Method.GET;

public class RestNodesInfoAction extends BaseRestAction {

    private static final Set<String> ALLOWED_METRICS = Sets.newHashSet(
            "http",
            "ingest",
            "indices",
            "jvm",
            "os",
            "plugins",
            "process",
            "settings",
            "thread_pool",
            "transport");

    private final SettingsFilter settingsFilter;

//    public RestNodesInfoAction(Settings settings, RestController controller, SettingsFilter settingsFilter){
//        super(settings);
//        controller.registerHandler(this);
//        this.settingsFilter = settingsFilter;
//    }

    public RestNodesInfoAction(SettingsFilter settingsFilter) {
        this.settingsFilter = settingsFilter;
    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "node_info_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/nodes"));
        // this endpoint is used for metrics, not for node IDs, like /_nodes/fs
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/nodes/{nodeId}"));
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/nodes/{nodeId}/{metrics}"));
        // added this endpoint to be aligned with stats
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/nodes/{nodeId}/info/{metrics}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String[] nodeIds;
        Set<String> metrics;

        // special case like /_nodes/os (in this case os are metrics and not the nodeId)
        // still, /_nodes/_local (or any other node id) should work and be treated as usual
        // this means one must differentiate between allowed metrics and arbitrary node ids in the same place
        if (request.hasParam("nodeId") && !request.hasParam("metrics")) {
            Set<String> metricsOrNodeIds = Strings.tokenizeByCommaToSet(request.param("nodeId", "_all"));
            boolean isMetricsOnly = ALLOWED_METRICS.containsAll(metricsOrNodeIds);
            if (isMetricsOnly) {
                nodeIds = new String[]{"_all"};
                metrics = metricsOrNodeIds;
            } else {
                nodeIds = metricsOrNodeIds.toArray(new String[]{});
                metrics = Sets.newHashSet("_all");
            }
        } else {
            nodeIds = Strings.splitStringByCommaToArray(request.param("nodeId", "_all"));
            metrics = Strings.tokenizeByCommaToSet(request.param("metrics", "_all"));
        }

        final NodesInfoRequest nodesInfoRequest = new NodesInfoRequest(nodeIds);
        nodesInfoRequest.timeout(request.param("timeout"));
        // shortcut, don't do checks if only all is specified
        if (metrics.size() == 1 && metrics.contains("_all")) {
            nodesInfoRequest.all();
        } else {
            nodesInfoRequest.clear();

            // 自定义的 metric 和 系统 metric，取交集，否则添加会报错
            Set<String> allMetrics = NodesInfoRequest.Metric.allMetrics();
            Set<String> legalMetrics = new HashSet<>(metrics);
            legalMetrics.retainAll(allMetrics);
            nodesInfoRequest.addMetrics(legalMetrics.toArray(new String[legalMetrics.size()]));

//            nodesInfoRequest.settings(metrics.contains("settings"));
//            nodesInfoRequest.os(metrics.contains("os"));
//            nodesInfoRequest.process(metrics.contains("process"));
//            nodesInfoRequest.jvm(metrics.contains("jvm"));
//            nodesInfoRequest.threadPool(metrics.contains("thread_pool"));
//            nodesInfoRequest.transport(metrics.contains("transport"));
//            nodesInfoRequest.http(metrics.contains("http"));
//            nodesInfoRequest.plugins(metrics.contains("plugins"));
//            nodesInfoRequest.ingest(metrics.contains("ingest"));
//            nodesInfoRequest.indices(metrics.contains("indices"));
        }

        settingsFilter.addFilterSettingParams(request);

        return channel -> client.admin().cluster().nodesInfo(nodesInfoRequest, new RestActions.NodesResponseRestListener<>(channel));
    }

    @Override
    protected Set<String> responseParams() {
        return Settings.FORMAT_PARAMS;
    }

    @Override
    public boolean canTripCircuitBreaker() {
        return false;
    }
}
