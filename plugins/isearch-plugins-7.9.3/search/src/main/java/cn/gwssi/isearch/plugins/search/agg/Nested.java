package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;

public class Nested implements AggBuilder
{
    private NestedAggregationBuilder nestedBuilder;

    private AggregationBuilder childBuilder;

    private String path;

    public Nested(NestedAggregationBuilder nestedBuilder, AggregationBuilder childBuilder,String path)
    {

        // FIXED 2023-03-22 每一个 nested 都要增加 reverse，这样统计的才是外部文档的数量而不是 nested 文档的数量
        // FIXED 2024-11-28 [cardinality] cannot accept sub-aggregations
        if (!(childBuilder instanceof CardinalityAggregationBuilder)) {
            childBuilder.subAggregation(AggregationBuilders.reverseNested("for_doc_num"));
        }

        this.nestedBuilder = nestedBuilder;

        this.childBuilder = childBuilder;

        this.nestedBuilder.subAggregation(this.childBuilder);

        this.path = path;
    }

    @Override
    public AggregationBuilder build()
    {
        return this.nestedBuilder;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        return this;
    }

    @Override
    public <C extends AggBuilder> C sub(C child)
    {
        childBuilder.subAggregation(child.build());

        return child;
    }

    @Override
    public boolean isNested()
    {
        return true;
    }

    @Override
    public String path()
    {
        return this.path;
    }
}
