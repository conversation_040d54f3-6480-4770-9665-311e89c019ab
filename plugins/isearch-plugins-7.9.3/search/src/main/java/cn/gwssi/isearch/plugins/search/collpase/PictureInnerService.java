package cn.gwssi.isearch.plugins.search.collpase;

import cn.gwssi.isearch.plugins.common.search.items.AbstractItemConditionExt;
import cn.gwssi.isearch.plugins.common.search.items.InItem;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.PictureCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;

import java.util.List;

public class PictureInnerService {

    private static PictureInnerService service;

    private PictureInnerService() {
    }

    public static PictureInnerService getInstance() {
        if (null == service) {
            service = new PictureInnerService();
        }

        return service;
    }

    public void addInnerPicture(SearchRequestBuilder requestBuilder, IPCondition ipCondition, PictureCondition picture, int size) {
        if (!ipCondition.isLeafCondition() || !(ipCondition instanceof InItem)) {
            throw new IllegalArgumentException("不是图像的 inner 检索！");
        }

        // ids 条件
        InItem idsItem = (InItem) ipCondition;
        String[] ids = idsItem.getInValues();
        int idSize = ids.length;

        QueryBuilder query = QueryBuilders.idsQuery().addIds(ids);

        // 图像条件
        String field = picture.getImageColumnName();
        DisMaxQueryBuilder disMaxQuery = QueryBuilders.disMaxQuery();
        List<float[]> features = picture.getImageColumnFea();
        for (float[] feature : features) {
            PQQueryBuilder builder = new PQQueryBuilder().setFieldName(field + IPConditionConstants.DEFAULT_IMAGE_FIELD).setQVector(feature);
            disMaxQuery.add(builder);
        }

        // inner 条件
        NestedQueryBuilder nestedQuery = new NestedQueryBuilder(field, disMaxQuery, ScoreMode.Max);
        InnerHitBuilder innerHitBuilder = new InnerHitBuilder().setSize(size).setFetchSourceContext(FetchSourceContext.DO_NOT_FETCH_SOURCE);
        nestedQuery.innerHit(innerHitBuilder);

        query = QueryBuilders.boolQuery().filter(query).must(nestedQuery);
        requestBuilder.setQuery(query).setFrom(0).setSize(idSize).setFetchSource(false);
    }

}
