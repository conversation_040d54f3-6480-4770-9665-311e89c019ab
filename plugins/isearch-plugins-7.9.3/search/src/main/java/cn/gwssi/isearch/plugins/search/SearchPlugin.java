package cn.gwssi.isearch.plugins.search;

import cn.gwssi.esplugins.dprm.SDVKeywordFieldMapper;
import cn.gwssi.esplugins.dprm.query.DPRMQueryBuilder;
import cn.gwssi.esplugins.vector.core.KNNSettings;
import cn.gwssi.esplugins.vector.core.cache.LSHIndexCache;
import cn.gwssi.esplugins.vector.core.cache.PQIndexCache;
import cn.gwssi.esplugins.vector.core.support.lsh.AbsolutePathInPlugin;
import cn.gwssi.isearch.plugins.search.rest.*;
import cn.gwssi.isearch.plugins.search.rest.admin.cluster.*;
import cn.gwssi.isearch.plugins.search.rest.admin.indices.*;
import cn.gwssi.isearch.plugins.search.rest.stat.RestCountAction;
import cn.gwssi.isearch.plugins.search.settings.Value;
import cn.gwssi.patsearch.PatentSearchPlugin;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.SpecialPermission;
import org.elasticsearch.client.Client;
import org.elasticsearch.cluster.metadata.IndexNameExpressionResolver;
import org.elasticsearch.cluster.node.DiscoveryNodes;
import org.elasticsearch.cluster.service.ClusterService;
import org.elasticsearch.common.io.stream.NamedWriteableRegistry;
import org.elasticsearch.common.settings.*;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.env.Environment;
import org.elasticsearch.env.NodeEnvironment;
import org.elasticsearch.index.IndexSettings;
import org.elasticsearch.index.engine.EngineFactory;
import org.elasticsearch.index.engine.GWEngineFactory;
import org.elasticsearch.index.mapper.KNNFieldMapper;
import org.elasticsearch.index.mapper.KNNReadyFieldMapper;
import org.elasticsearch.index.mapper.Mapper;
import org.elasticsearch.index.queries.LSHQueryBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.plugins.ActionPlugin;
import org.elasticsearch.plugins.EnginePlugin;
import org.elasticsearch.plugins.MapperPlugin;
import org.elasticsearch.repositories.RepositoriesService;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.script.ScriptService;
import org.elasticsearch.threadpool.ThreadPool;
import org.elasticsearch.watcher.ResourceWatcherService;

import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.*;
import java.util.function.Supplier;

public class SearchPlugin extends PatentSearchPlugin implements ActionPlugin, MapperPlugin, org.elasticsearch.plugins.SearchPlugin, EnginePlugin
{
    private ExpressionService expressionService = new ExpressionServiceImpl();

    public SearchPlugin(final Settings settings)
    {
        String hash_file_path = settings.get(AbsolutePathInPlugin.HASH_PATH_CONFIG);

        if (hash_file_path != null)
        {
            AbsolutePathInPlugin.hashFilePath = hash_file_path;
        }

        String so_file_path = settings.get(AbsolutePathInPlugin.SO_PATH_CONFIG);

        if (so_file_path != null)
        {

            // 官网提供的help，我也不知道要检查什么
            SecurityManager sm = System.getSecurityManager();
            if (sm != null) {
                // unprivileged code such as scripts do not have SpecialPermission
                sm.checkPermission(new SpecialPermission());
            }

            // 如果 policy 是在 jre 中配置的，不需要这个
            // 但是 plugin-security.policy only be granted to the jars in your plugin，load 会报错
            AccessController.doPrivileged((PrivilegedAction<Object>) () -> {
                System.load(so_file_path);
                return null;
            });
        }

        String vector_window_size = settings.get(Value.VECTOR_WINDOW_SIZE);
        if (StringUtils.isNotBlank(vector_window_size))
        {
            Value.vector_window_size = Integer.parseInt(vector_window_size);
        }
    }

    @Override
    public Optional<EngineFactory> getEngineFactory(IndexSettings indexSettings) {
        return Optional.of(new GWEngineFactory());
    }

    @Override
    public Map<String, Mapper.TypeParser> getMappers()
    {
        Map<String, Mapper.TypeParser> mappers = new HashMap<>();

        mappers.put(KNNFieldMapper.CONTENT_TYPE, new KNNFieldMapper.KNNFieldTypeParser());
        mappers.put(KNNReadyFieldMapper.CONTENT_TYPE, new KNNReadyFieldMapper.IndexReadyFieldTypeParser());
        mappers.put(SDVKeywordFieldMapper.CONTENT_TYPE, new SDVKeywordFieldMapper.TypeParser());

        return mappers;
    }

    @Override
    public List<RestHandler> getRestHandlers(Settings settings, RestController restController, ClusterSettings clusterSettings, IndexScopedSettings indexScopedSettings, SettingsFilter settingsFilter, IndexNameExpressionResolver indexNameExpressionResolver, Supplier<DiscoveryNodes> nodesInCluster)
    {
        List<RestHandler> handlers = new ArrayList<>();
        handlers.add(new RestQueryAction(expressionService));
        handlers.add(new RestAnalysisAction(expressionService));
        handlers.add(new RestCountAction(expressionService));
        handlers.add(new RestDeleteAction());
        handlers.add(new RestIndexAction());
        handlers.add(new RestRawBulkAction(settings));
        handlers.add(new RestRawSearchAction());
        handlers.add(new RestUpdateAction());
        handlers.add(new RestClusterStateAction(settingsFilter));
        handlers.add(new RestClusterStatsAction());
        handlers.add(new RestNodesInfoAction(settingsFilter));
        handlers.add(new RestNodesStatsAction());
        handlers.add(new RestCreateIndexAction());
        handlers.add(new RestDeleteIndexAction());
        handlers.add(new RestGetIndicesAction());
        handlers.add(new RestGetMappingAction());
        handlers.add(new RestIndicesStatsAction());
        handlers.add(new RestPutMappingAction());
        handlers.add(new RestUpdateSettingsAction());
        handlers.add(new RestClusterUpdateSettingsAction());
        handlers.add(new RestClusterHealthAction());
        handlers.add(new RestIndexExistsAction());
        handlers.add(new RestSearchAction());
        handlers.add(new RestSearchScrollAction());
        handlers.add(new RestClearScrollAction());
        handlers.add(new RestMultiGetAction(settings));

        return handlers;
    }

    @Override
    public Collection<Object> createComponents(Client client, ClusterService clusterService, ThreadPool threadPool, ResourceWatcherService resourceWatcherService, ScriptService scriptService, NamedXContentRegistry xContentRegistry, Environment environment, NodeEnvironment nodeEnvironment, NamedWriteableRegistry namedWriteableRegistry, IndexNameExpressionResolver indexNameExpressionResolver, Supplier<RepositoriesService> repositoriesServiceSupplier)
    {
        PQIndexCache.setResourceWatcherService(resourceWatcherService);
        LSHIndexCache.setResourceWatcherService(resourceWatcherService);
        return Collections.emptyList();
    }

    @Override
    public List<QuerySpec<?>> getQueries()
    {
//        super.getQueries();

        List<QuerySpec<?>> querySpecs = new ArrayList<>();

        QuerySpec<LSHQueryBuilder> lshScanQuery = new QuerySpec<>(
                LSHQueryBuilder.QUERY_NAME,
                LSHQueryBuilder::new,
                new LSHQueryBuilder.Parser()
        );

        QuerySpec<PQQueryBuilder> pqExh = new QuerySpec<>(
                PQQueryBuilder.QUERY_NAME,
                PQQueryBuilder::new,
                new PQQueryBuilder.Parser()
        );

        QuerySpec<DPRMQueryBuilder> dprmQuery = new QuerySpec<>(
                DPRMQueryBuilder.NAME,
                DPRMQueryBuilder::new,
                DPRMQueryBuilder::fromXContent
        );

        querySpecs.add(lshScanQuery);
        querySpecs.add(pqExh);
        querySpecs.add(dprmQuery);

        querySpecs.addAll(super.getQueries());
        return querySpecs;
    }

    @Override
    public List<Setting<?>> getSettings()
    {
        List<Setting<?>> list = KNNSettings.getSettings();
        list.add(Setting.simpleString(Value.VECTOR_WINDOW_SIZE, Setting.Property.NodeScope));
        return list;
    }
}
