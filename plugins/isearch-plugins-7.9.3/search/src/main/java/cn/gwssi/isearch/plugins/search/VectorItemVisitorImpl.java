package cn.gwssi.isearch.plugins.search;

import cn.gwssi.isearch.plugins.common.search.item.ItemVisitor;
import cn.gwssi.isearch.plugins.common.search.items.*;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.queries.KNNQueryBuilder;
import org.elasticsearch.index.queries.LSHQueryBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.search.rescore.QueryRescoreMode;
import org.elasticsearch.search.rescore.QueryRescorerBuilder;

public class VectorItemVisitorImpl implements ItemVisitor<QueryRescorerBuilder> {

    @Override
    public QueryRescorerBuilder visit(EqItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(GteItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(GtItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(LteItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(LtItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(InItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(FreqItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(MatchItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(PhraseItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(PositionItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(RangeItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(WildcardItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(GeoItem item) {
        return null;
    }

    @Override
    public QueryRescorerBuilder visit(VectorItem item) {
        String field = item.getFieldName();
        float[] feature = item.getFeature();
        String field_vec = item.isImage() ? IPConditionConstants.DEFAULT_IMAGE_FIELD : IPConditionConstants.DEFAULT_SEMANTIC_FIELD;

        PQQueryBuilder builder = new PQQueryBuilder().setFieldName(field + field_vec).setQVector(feature);
        NestedQueryBuilder nestedQueryBuilder = new NestedQueryBuilder(field, builder, ScoreMode.Max);

        return new QueryRescorerBuilder(nestedQueryBuilder).setScoreMode(QueryRescoreMode.Max)
                .setQueryWeight(IPConditionConstants.RESCORE_QUERY_WEIGHT).windowSize(IPConditionConstants.DEFAULT_WINDOW_SIZE);
    }
}
