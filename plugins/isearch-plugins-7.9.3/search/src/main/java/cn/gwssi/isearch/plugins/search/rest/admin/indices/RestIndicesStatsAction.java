package cn.gwssi.isearch.plugins.search.rest.admin.indices;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.elasticsearch.action.admin.indices.stats.CommonStatsFlags;
import org.elasticsearch.action.admin.indices.stats.IndicesStatsRequest;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestToXContentListener;

import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;

import static org.elasticsearch.rest.RestRequest.Method.GET;

public class RestIndicesStatsAction extends BaseRestAction {

//    public RestIndicesStatsAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestIndicesStatsAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "indices_stats_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/stats"));
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/stats/{metric}"));
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/{index}/stats"));
        routes.add(new RestHandler.Route(GET, Path.ROOT+"/{index}/stats/{metric}"));

        return routes;
    }

    static final Map<String, Consumer<IndicesStatsRequest>> METRICS;

    static {
        Map<String, Consumer<IndicesStatsRequest>> metrics = new HashMap<>();
        for (CommonStatsFlags.Flag flag : CommonStatsFlags.Flag.values()) {
            metrics.put(flag.getRestName(), m -> m.flags().set(flag, true));
        }
        METRICS = Collections.unmodifiableMap(metrics);
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        IndicesStatsRequest indicesStatsRequest = new IndicesStatsRequest();
        indicesStatsRequest.indicesOptions(IndicesOptions.fromRequest(request, indicesStatsRequest.indicesOptions()));
        indicesStatsRequest.indices(Strings.splitStringByCommaToArray(request.param("index")));
        indicesStatsRequest.types(Strings.splitStringByCommaToArray(request.param("types")));

        Set<String> metrics = Strings.tokenizeByCommaToSet(request.param("metric", "_all"));
        // short cut, if no metrics have been specified in URI
        if (metrics.size() == 1 && metrics.contains("_all")) {
            indicesStatsRequest.all();
        } else if (metrics.contains("_all")) {
            throw new IllegalArgumentException(
                    String.format(Locale.ROOT,
                            "request [%s] contains _all and individual metrics [%s]",
                            request.path(),
                            request.param("metric")));
        } else {
            indicesStatsRequest.clear();
            // use a sorted set so the unrecognized parameters appear in a reliable sorted order
            final Set<String> invalidMetrics = new TreeSet<>();
            for (final String metric : metrics) {
                final Consumer<IndicesStatsRequest> consumer = METRICS.get(metric);
                if (consumer != null) {
                    consumer.accept(indicesStatsRequest);
                } else {
                    invalidMetrics.add(metric);
                }
            }

            if (!invalidMetrics.isEmpty()) {
                throw new IllegalArgumentException(unrecognized(request, invalidMetrics, METRICS.keySet(), "metric"));
            }
        }

        if (request.hasParam("groups")) {
            indicesStatsRequest.groups(Strings.splitStringByCommaToArray(request.param("groups")));
        }

        if (request.hasParam("types")) {
            indicesStatsRequest.types(Strings.splitStringByCommaToArray(request.param("types")));
        }

        if (indicesStatsRequest.completion() && (request.hasParam("fields") || request.hasParam("completion_fields"))) {
            indicesStatsRequest.completionFields(
                    request.paramAsStringArray("completion_fields", request.paramAsStringArray("fields", Strings.EMPTY_ARRAY)));
        }

        if (indicesStatsRequest.fieldData() && (request.hasParam("fields") || request.hasParam("fielddata_fields"))) {
            indicesStatsRequest.fieldDataFields(
                    request.paramAsStringArray("fielddata_fields", request.paramAsStringArray("fields", Strings.EMPTY_ARRAY)));
        }

        if (indicesStatsRequest.segments()) {
            indicesStatsRequest.includeSegmentFileSizes(request.paramAsBoolean("include_segment_file_sizes", false));
        }

        return channel -> client.admin().indices().stats(indicesStatsRequest, new RestToXContentListener<>(channel));
    }
}
