package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationFiltersDim;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregator;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;

import java.util.Collections;
import java.util.List;

public class Filters implements AggBuilder {
    private FiltersAggregationBuilder builder;

    public Filters() {
    }

    public Filters(FiltersAggregationBuilder builder) {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build() {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggregationFiltersDim filtersDim = (AggregationFiltersDim) dim;
        String field = dim.getFieldName();
        String path = dim.path();

        // 转为多个 filters 条件
        FiltersAggregator.KeyedFilter[] filers = filtersDim.getGroups().stream().map(group -> this.group(field, path, group)).toArray(FiltersAggregator.KeyedFilter[]::new);
        FiltersAggregationBuilder builder = AggregationBuilders.filters(dim.getFieldName(), filers);

        // filter 是不能设置 sort 和 size 的，通过 bucket_sort 聚合来控制
        FieldSortBuilder sort = AggBuilder.fieldOrder(dim.getAggrOrder());
        List<FieldSortBuilder> order = (null == sort) ? null : Collections.singletonList(sort);

        BucketSortPipelineAggregationBuilder sizeBuilder = PipelineAggregatorBuilders.bucketSort(dim.getFieldName(), order).size(dim.getSize());
        builder.subAggregation(sizeBuilder);

        this.builder = builder;
        return this;
    }

    // group 转为 filters 语句
    private FiltersAggregator.KeyedFilter group(String field, String path, AggregationFiltersDim.Group group) {
        QueryBuilder builder = new MatchAllQueryBuilder();

        List<String> values = group.getValues();
        if (null != values && values.size() > 0) {
            String[] groupFields = AggregationFiltersDim.getGroupFields().get(field.replace(IPConstants.FULL_FIELD_NAME_ENDWITH, ""));

            // FIXED 2023-12-13 支持普通字段
            if (null == groupFields || groupFields.length <= 0) {
                builder = this.toQuery(field, path, values);
            } else {
                builder = this.toQuery(groupFields, field.endsWith(IPConstants.FULL_FIELD_NAME_ENDWITH), path, values);
            }
        }

        return new FiltersAggregator.KeyedFilter(group.getKey(), builder);
    }

    // 转为 query 语句，用 bool 连接起来
    private QueryBuilder toQuery(String[] groupFields, boolean hasRaw, String path, List<String> values) {
        BoolQueryBuilder builder = new BoolQueryBuilder();

        for (String field : groupFields) {
            field += hasRaw ? IPConstants.FULL_FIELD_NAME_ENDWITH : "";
            builder.should(this.toQuery(field, path, values));
        }

        return builder;
    }

    // 转为 query 语句
    private QueryBuilder toQuery(String field, String path, List<String> values) {
        TermsQueryBuilder builder = QueryBuilders.termsQuery(field, values);
        if (StringUtils.isBlank(path)) {
            return builder;
        }

        field = path + "." + field;
        builder = QueryBuilders.termsQuery(field, values);
        return new NestedQueryBuilder(path, builder, ScoreMode.Max);
    }

//    // 转为 query 语句，用 should 连接多个条件
//    private QueryBuilder toQuery(String field, String path, List<String> values) {
//        BoolQueryBuilder builder = new BoolQueryBuilder();
//        values.stream().forEach(value -> {
//            builder.should(this.toQuery(field, path, value));
//        });
//
//        return builder;
//    }
//
//    // 转为 query 语句，区分 nested
//    private QueryBuilder toQuery(String field, String path, String value) {
//        MatchPhraseQueryBuilder builder = new MatchPhraseQueryBuilder(field, value);
//        if (StringUtils.isBlank(path)) {
//            return builder;
//        }
//
//        field = path + "." + field;
//        builder = new MatchPhraseQueryBuilder(field, value);
//        return new NestedQueryBuilder(path, builder, ScoreMode.Max);
//    }
}
