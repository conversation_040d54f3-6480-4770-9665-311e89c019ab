package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequestBuilder;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestUpdateAction extends BaseRestAction
{
//    public RestUpdateAction(Settings settings, RestController controller)
//    {
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestUpdateAction() {}

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "update-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.UPDATE));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.UPDATE+"/{index}/{type}/{id}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String index = request.param("index");
        String type  = request.param("type");
        String id    = request.param("id");

        if (hasNullOrEmpty(index, id))
        {
            return channel -> channel.sendResponse(badResponse("parameters missing : [index,id]"));
        }
        else
        {
            String source = request.content().utf8ToString();

            if(Strings.isNullOrEmpty(source))
            {
                return channel -> channel.sendResponse(badResponse("document missing."));
            }


            boolean refresh = request.paramAsBoolean("refresh", true);

            UpdateRequestBuilder requestBuilder = client.prepareUpdate()
                    .setIndex(index)
//                    .setType(type) //7.0去掉type
                    .setId(id)
                    .setDoc(source,XContentType.JSON)
                    .setRefreshPolicy(refresh ? WriteRequest.RefreshPolicy.IMMEDIATE : WriteRequest.RefreshPolicy.NONE);
            return channel -> requestBuilder.execute(new RestBuilderListener<UpdateResponse>(channel)
            {
                @Override
                public RestResponse buildResponse(UpdateResponse updateResponse, XContentBuilder builder) throws Exception
                {
                    builder.startObject()
                            .field("created", updateResponse.status() == RestStatus.CREATED ? true : false)
                            .endObject();

                    return new BytesRestResponse(RestStatus.OK, builder);
                }
            });
        }
    }
}
