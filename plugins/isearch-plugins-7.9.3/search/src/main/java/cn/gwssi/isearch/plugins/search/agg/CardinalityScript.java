package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.aggregation.AggeragationCardinalityDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.ScriptedMetricAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ScriptedMetricAggregationBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class CardinalityScript implements AggBuilder {

    private Script script_init = new Script(ScriptType.STORED, null, "script_for_collapse_agg_init", Collections.emptyMap());
    private Script script_map = new Script(ScriptType.STORED, null, "script_for_collapse_agg_map", Collections.emptyMap());
    private Script script_combine = new Script(ScriptType.STORED, null, "script_for_collapse_agg_combine", Collections.emptyMap());
    private Script script_reduce = new Script(ScriptType.STORED, null, "script_for_collapse_agg_reduce", Collections.emptyMap());

    private ScriptedMetricAggregationBuilder builder;

    public CardinalityScript() {}

    public CardinalityScript(ScriptedMetricAggregationBuilder builder) {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build() {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggeragationCardinalityDim cardinalityDim = (AggeragationCardinalityDim)dim;

        Map<String, Object> params = new HashMap<>();
        params.put("groupBy", cardinalityDim.getGroupBy());
        params.put("groupBy2", cardinalityDim.getGroupBy2());
        params.put("distinct", cardinalityDim.getFieldName());
        params.put("sort", cardinalityDim.getSort());
        params.put("isAsc", cardinalityDim.isAsc());

        this.builder = AggregationBuilders.scriptedMetric(IPConstants.CARDINALITY_AGG_NAME)
                .params(params)
                .initScript(script_init)
                .mapScript(script_map)
                .combineScript(script_combine)
                .reduceScript(script_reduce);

        return this;
    }
}
