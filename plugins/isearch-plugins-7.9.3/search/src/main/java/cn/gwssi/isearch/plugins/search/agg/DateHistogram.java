package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDHInterval;
import cn.gwssi.common.common.pojo.aggregation.AggregationDateHistogramDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.common.Strings;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;

public class DateHistogram implements AggBuilder {
    private DateHistogramAggregationBuilder builder;

    public DateHistogram(){}

    public DateHistogram(DateHistogramAggregationBuilder builder)
    {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build()
    {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child)
    {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested()
    {
        return false;
    }

    @Override
    public String path()
    {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggregationDateHistogramDim dateDim = (AggregationDateHistogramDim)dim;

        DateHistogramAggregationBuilder builder = AggregationBuilders.dateHistogram(dim.getFieldName())
                .dateHistogramInterval(histogramInterval(dateDim.getInterval()));
        if (null != dateDim.getZoneId()) {
            builder.timeZone(dateDim.getZoneId());
        }
        if (null != dateDim.getOffset()) {
            builder.offset(dateDim.getOffset());
        }
        if (null != dateDim.getFormat()) {
            builder.format(dateDim.getFormat());
        }
        if (dateDim.getMinDocCount() >= 0) {
            builder.minDocCount(dateDim.getMinDocCount());
        }

        BucketOrder order = AggBuilder.bucketOrder(dateDim.getAggrOrder());
        if (null != order) {
            builder.order(order);
        }

        // FIXED 2023-03-23 dateHistogram 是不能设置 size 的，通过 bucket_sort 聚合来控制 size
        BucketSortPipelineAggregationBuilder sizeBuilder = PipelineAggregatorBuilders.bucketSort(dim.getFieldName(), null).size(dim.getSize());
        builder.subAggregation(sizeBuilder);

        if (Strings.isNullOrEmpty(dim.path())) {
            builder.field(dim.getFieldName());
            this.builder = builder;
            return this;
        } else {
            builder.field((dim.path() + "." + dim.getFieldName()));
            NestedAggregationBuilder nestedBuilder = AggregationBuilders.nested(dim.path(), dim.path());
            return new Nested(nestedBuilder, builder, dim.path());
        }
    }

    private DateHistogramInterval histogramInterval(AggregationDHInterval interval) {
        if (interval.equals(AggregationDHInterval.SECOND)) {
            return DateHistogramInterval.SECOND;
        }
        if (interval.equals(AggregationDHInterval.MINUTE)) {
            return DateHistogramInterval.MINUTE;
        }
        if (interval.equals(AggregationDHInterval.HOUR)) {
            return DateHistogramInterval.HOUR;
        }
        if (interval.equals(AggregationDHInterval.DAY)) {
            return DateHistogramInterval.DAY;
        }
        if (interval.equals(AggregationDHInterval.WEEK)) {
            return DateHistogramInterval.WEEK;
        }
        if (interval.equals(AggregationDHInterval.MONTH)) {
            return DateHistogramInterval.MONTH;
        }
        if (interval.equals(AggregationDHInterval.QUARTER)) {
            return DateHistogramInterval.QUARTER;
        }
        if (interval.equals(AggregationDHInterval.YEAR)) {
            return DateHistogramInterval.YEAR;
        }

        return null;
    }
}
