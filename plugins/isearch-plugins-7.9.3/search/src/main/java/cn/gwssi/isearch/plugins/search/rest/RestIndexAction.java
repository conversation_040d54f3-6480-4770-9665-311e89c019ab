package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.index.IndexRequestBuilder;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestIndexAction extends BaseRestAction {
//    public RestIndexAction(Settings settings, RestController controller)
//    {
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestIndexAction() {
    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "index-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.INDEX));
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.INDEX + "/{index}/{type}"));
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.INDEX + "/{index}/{type}/{id}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String index = request.param("index");
        String type = request.param("type");

        if (hasNullOrEmpty(index)) {
            return channel -> channel.sendResponse(badResponse("parameters missing : [index]"));
        } else {
            String source = request.content().utf8ToString();

            if (Strings.isNullOrEmpty(source))
                return channel -> channel.sendResponse(badResponse("document missing."));


            IndexRequestBuilder requestBuilder = client.prepareIndex()
                    .setIndex(index)
                    //.setType(type)
                    .setSource(source, XContentType.JSON);


            String id = request.param("id");

            if (!Strings.isNullOrEmpty(id))
                requestBuilder.setId(id);

            return channel -> requestBuilder.execute(new RestBuilderListener<IndexResponse>(channel) {
                @Override
                public RestResponse buildResponse(IndexResponse indexResponse, XContentBuilder builder) throws Exception {
                    builder.startObject()
                            .field("created", indexResponse.status() == RestStatus.CREATED ? true : false)
                            .endObject();

                    return new BytesRestResponse(RestStatus.OK, builder);
                }
            });
        }
    }
}
