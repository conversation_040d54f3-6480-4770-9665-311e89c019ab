package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkShardRequest;
import org.elasticsearch.action.support.ActiveShardCount;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.cluster.metadata.MappingMetadata;
import org.elasticsearch.common.collect.ImmutableOpenMap;
import org.elasticsearch.common.logging.DeprecationLogger;
import org.elasticsearch.common.logging.Loggers;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.mapper.MapperService;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestStatusToXContentListener;
import org.elasticsearch.rest.action.document.RestBulkAction;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestRawBulkAction extends BaseRestAction
{
    private static final DeprecationLogger DEPRECATION_LOGGER =
            new DeprecationLogger(Loggers.getLogger(RestRawBulkAction.class,"custom"));

    private final boolean allowExplicitIndex;

//    public RestRawBulkAction(Settings settings, RestController controller)
//    {
//        super(settings);
//        controller.registerHandler(this);
//        this.allowExplicitIndex = MULTI_ALLOW_EXPLICIT_INDEX.get(settings);
//    }

    public RestRawBulkAction(Settings settings) {
        this.allowExplicitIndex = MULTI_ALLOW_EXPLICIT_INDEX.get(settings);
    }

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "bulk-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.BULK));
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.BULK));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.BULK+"/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.BULK+"/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.BULK+"/{index}/{type}"));
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, Path.BULK+"/{index}/{type}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        BulkRequest bulkRequest = Requests.bulkRequest();
        String defaultIndex = request.param("index");
        String defaultType = MapperService.SINGLE_MAPPING_NAME;
//        if (defaultType == null) {
//            defaultType = getType(client, defaultIndex);//MapperService.SINGLE_MAPPING_NAME;
//        } else {
//            DEPRECATION_LOGGER.deprecatedAndMaybeLog("bulk_with_types", RestBulkAction.TYPES_DEPRECATION_MESSAGE);
//        }
        String defaultRouting = request.param("routing");
        FetchSourceContext defaultFetchSourceContext = FetchSourceContext.parseFromRestRequest(request);
        String defaultPipeline = request.param("pipeline");
        String waitForActiveShards = request.param("wait_for_active_shards");
        if (waitForActiveShards != null) {
            bulkRequest.waitForActiveShards(ActiveShardCount.parseString(waitForActiveShards));
        }
        bulkRequest.timeout(request.paramAsTime("timeout", BulkShardRequest.DEFAULT_TIMEOUT));
        bulkRequest.setRefreshPolicy(request.param("refresh"));
        bulkRequest.add(request.requiredContent(), defaultIndex, defaultType, defaultRouting,
                defaultFetchSourceContext, defaultPipeline, allowExplicitIndex, request.getXContentType());

        return channel -> client.bulk(bulkRequest, new RestStatusToXContentListener<>(channel));
    }

    private String getType(NodeClient client,String index)
    {
        if (index == null)
        {
            return null;
        }

        ImmutableOpenMap<String, ImmutableOpenMap<String, MappingMetadata>> mappings = client.admin().indices().prepareGetMappings(index).get().getMappings();

        String type = mappings.keysIt().next();

        return type;
    }
}
