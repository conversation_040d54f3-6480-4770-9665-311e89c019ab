package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.ExpressionService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.Client;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;
import org.elasticsearch.rest.action.search.RestSearchAction;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RestQueryAction extends BaseRestAction {
    private ExpressionService expressionService;

    public RestQueryAction(ExpressionService expressionService) {
        this.expressionService = expressionService;
    }

//    public RestQueryAction(Settings settings, RestController controller, ExpressionService expressionService)
//    {
//        super(settings);
//        controller.registerHandler(this);
//        this.expressionService = expressionService;
//    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "query-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.QUERY));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.QUERY));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.QUERY + "/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.QUERY + "/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.QUERY + "/{index}/{type}"));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.QUERY + "/{index}/{type}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        SearchRequestBuilder searchRequestBuilder;
        try {
            searchRequestBuilder = getSearchRequestBuilder(request, client);

//            logger.info(searchRequestBuilder.toString());
        } catch (Exception e) {
            String errorMsg = this.extractError(e, 10);
            logger.info(errorMsg);
            return channel -> channel.sendResponse(badResponse(errorMsg));
        }

        return channel -> searchRequestBuilder.execute(new RestBuilderListener<SearchResponse>(channel) {
            @Override
            public RestResponse buildResponse(SearchResponse searchResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, responseBuilder(searchResponse, builder));
            }
        });
    }

    protected XContentBuilder responseBuilder(SearchResponse searchResponse, XContentBuilder builder) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put(RestSearchAction.TOTAL_HITS_AS_INT_PARAM, "true");
        ToXContent.MapParams mapParams = new ToXContent.MapParams(params);
        return searchResponse.toXContent(builder, mapParams);
    }

    protected SearchRequestBuilder getSearchRequestBuilder(RestRequest request, Client client) throws Exception {
        SearchRequestBuilder searchRequestBuilder = client.prepareSearch();

        //设置索引
        String index = request.param("index");
        searchRequestBuilder.setIndices(Strings.splitStringByCommaToArray(index));

        //必须消费掉，否则报错 contains unrecognized parameters
        String type = request.param("type");

        //总条数
        searchRequestBuilder.setTrackTotalHits(true);

        //计算score
        searchRequestBuilder.setTrackScores(request.paramAsBoolean("track_scores", true));

        //查询线程数，默认由 5 改为 20
        searchRequestBuilder.setMaxConcurrentShardRequests(request.paramAsInt("maxConcurrentShardRequests", 20));

        if (request.hasParam("batched_reduce_size")) {
            searchRequestBuilder.setBatchedReduceSize(request.paramAsInt("batched_reduce_size", SearchRequest.DEFAULT_BATCHED_REDUCE_SIZE));
        }
        if (request.hasParam("pre_filter_shard_size")) {
            searchRequestBuilder.setPreFilterShardSize(request.paramAsInt("pre_filter_shard_size", SearchRequest.DEFAULT_PRE_FILTER_SHARD_SIZE));
        }

        SearchRequest searchRequest = searchRequestBuilder.request();
        if (request.hasParam("allow_partial_search_results")) {
            // only set if we have the parameter passed to override the cluster-level default
            searchRequest.allowPartialSearchResults(request.paramAsBoolean("allow_partial_search_results", null));
        }

        // do not allow 'query_and_fetch' or 'dfs_query_and_fetch' search types
        // from the REST layer. these modes are an internal optimization and should
        // not be specified explicitly by the user.
        String searchType = request.param("search_type");
        if ("query_and_fetch".equals(searchType) || "dfs_query_and_fetch".equals(searchType)) {
            throw new IllegalArgumentException("Unsupported search type [" + searchType + "]");
        } else {
            searchRequest.searchType(searchType);
        }
        searchRequest.requestCache(request.paramAsBoolean("request_cache", searchRequest.requestCache()));

        searchRequest.routing(request.param("routing"));
        searchRequest.preference(request.param("preference"));
        searchRequest.indicesOptions(IndicesOptions.fromRequest(request, searchRequest.indicesOptions()));
        searchRequest.setCcsMinimizeRoundtrips(request.paramAsBoolean("ccs_minimize_roundtrips", searchRequest.isCcsMinimizeRoundtrips()));

        //提取condition中的检索信息
        String condition = request.content().utf8ToString();

        if (!Strings.isNullOrEmpty(condition))
            expressionService.extractCondition(condition, searchRequestBuilder);

        return searchRequestBuilder;
    }

    // 提取错误信息，只显示 10 行
    private String extractError(Exception e, int maxLength) {
        StackTraceElement[] stacks = e.getStackTrace();
        int length = Math.min(stacks.length, maxLength);
        StringBuilder errorMsg = new StringBuilder("转化为queryBuilder时出错：")
                .append(e.getMessage()).append("\r\n");
        for (int i = 0; i < length; i++) {
            errorMsg.append(stacks[i].toString()).append("\r\n");
        }

        return errorMsg.toString();
    }
}
