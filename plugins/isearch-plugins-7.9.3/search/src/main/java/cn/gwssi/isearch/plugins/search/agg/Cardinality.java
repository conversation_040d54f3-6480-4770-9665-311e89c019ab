package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.common.pojo.aggregation.AggeragationCardinalityDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.common.Strings;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.HistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;

public class Cardinality implements AggBuilder {
    private CardinalityAggregationBuilder builder;

    public Cardinality() {}

    public Cardinality(CardinalityAggregationBuilder builder) {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build() {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggeragationCardinalityDim cardinalityDim = (AggeragationCardinalityDim)dim;
        CardinalityAggregationBuilder builder = AggregationBuilders.cardinality(IPConstants.CARDINALITY_AGG_NAME);

        if (cardinalityDim.getPrecision() > 0) {
            builder.precisionThreshold(cardinalityDim.getPrecision());
        }

        if (Strings.isNullOrEmpty(dim.path())) {
            builder.field(dim.getFieldName());
            this.builder = builder;
            return this;
        } else {
            builder.field((dim.path() + "." + dim.getFieldName()));
            NestedAggregationBuilder nestedBuilder = AggregationBuilders.nested(dim.path(), dim.path());
            return new Nested(nestedBuilder, builder, dim.path());
        }
    }
}
