package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggrOrder;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;

public interface AggBuilder
{
    AggregationBuilder build();

    AggBuilder buildBasic(AggregationDim dim);

    <C extends AggBuilder> C sub(C child);

    boolean isNested();

    String path();

    // term 等使用的排序
    static BucketOrder bucketOrder(AggrOrder aggrOrder) {
        if (null == aggrOrder) return null;

        switch (aggrOrder) {
            case ORDER_TERM_ASC:
                return BucketOrder.key(true);
            case ORDER_COUNT_ASC:
                return BucketOrder.count(true);
            case ORDER_TERM_DESC:
                return BucketOrder.key(false);
            case ORDER_COUNT_DESC:
                return BucketOrder.count(false);
            default:
                return null;
        }
    }

    // range 等使用的排序，默认是 null
    static FieldSortBuilder fieldOrder(AggrOrder aggrOrder) {
        if (null == aggrOrder) return null;

        switch (aggrOrder) {
            case ORDER_TERM_ASC:
                return new FieldSortBuilder("_key").order(SortOrder.ASC);
            case ORDER_COUNT_ASC:
                return new FieldSortBuilder("_count").order(SortOrder.ASC);
            case ORDER_TERM_DESC:
                return new FieldSortBuilder("_key").order(SortOrder.DESC);
            case ORDER_COUNT_DESC:
                return new FieldSortBuilder("_count").order(SortOrder.DESC);
            default:
                return null;
        }
    }

}
