package cn.gwssi.isearch.plugins.search.rest;

import cn.gwssi.isearch.plugins.common.search.Path;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;
import org.elasticsearch.rest.action.RestStatusToXContentListener;
import org.elasticsearch.rest.action.search.RestSearchAction;
import org.elasticsearch.search.Scroll;

import java.io.IOException;
import java.util.*;

import static org.elasticsearch.common.unit.TimeValue.parseTimeValue;
import static org.elasticsearch.rest.RestRequest.Method.GET;
import static org.elasticsearch.rest.RestRequest.Method.POST;

public class RestSearchScrollAction extends BaseRestAction {

    private static final Set<String> RESPONSE_PARAMS = Collections.singleton(RestSearchAction.TOTAL_HITS_AS_INT_PARAM);

//    public RestSearchScrollAction(Settings settings, RestController controller){
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestSearchScrollAction() {
    }

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "scroll_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(GET, Path.ROOT + "/scroll"));
        routes.add(new RestHandler.Route(POST, Path.ROOT + "/scroll"));
        routes.add(new RestHandler.Route(GET, Path.ROOT + "/scroll/{scroll_id}"));
        routes.add(new RestHandler.Route(POST, Path.ROOT + "/scroll/{scroll_id}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String scrollId = request.param("scroll_id");
        SearchScrollRequest searchScrollRequest = new SearchScrollRequest();
        searchScrollRequest.scrollId(scrollId);
        String scroll = request.param("scroll");
        if (scroll != null) {
            searchScrollRequest.scroll(new Scroll(parseTimeValue(scroll, null, "scroll")));
        }

        request.withContentOrSourceParamParserOrNull(xContentParser -> {
            if (xContentParser != null) {
                // NOTE: if rest request with xcontent body has request parameters, values parsed from request body have the precedence
                try {
                    searchScrollRequest.fromXContent(xContentParser);
                } catch (IOException e) {
                    throw new IllegalArgumentException("Failed to parse request body", e);
                }
            }
        });

        return channel -> client.searchScroll(searchScrollRequest, new RestBuilderListener<SearchResponse>(channel) {
            @Override
            public RestResponse buildResponse(SearchResponse searchResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, responseBuilder(searchResponse, builder));
            }
        });
    }

    protected XContentBuilder responseBuilder(SearchResponse searchResponse, XContentBuilder builder) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put(RestSearchAction.TOTAL_HITS_AS_INT_PARAM, "true");
        ToXContent.MapParams mapParams = new ToXContent.MapParams(params);
        return searchResponse.toXContent(builder, mapParams);
    }

    @Override
    protected Set<String> responseParams() {
        return RESPONSE_PARAMS;
    }
}
