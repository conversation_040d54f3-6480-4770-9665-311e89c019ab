package cn.gwssi.isearch.plugins.search.rest.admin.indices;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.rest.BaseRestAction;
import org.apache.logging.log4j.LogManager;
import org.elasticsearch.action.admin.indices.mapping.put.PutMappingRequest;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.logging.DeprecationLogger;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentHelper;
import org.elasticsearch.index.mapper.MapperService;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;
import org.elasticsearch.rest.action.RestToXContentListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.elasticsearch.client.Requests.putMappingRequest;
import static org.elasticsearch.index.mapper.MapperService.isMappingSourceTyped;
import static org.elasticsearch.rest.RestRequest.Method.*;
import static org.elasticsearch.rest.RestRequest.Method.GET;

public class RestPutMappingAction extends BaseRestAction {

    private static final DeprecationLogger deprecationLogger = new DeprecationLogger(
            LogManager.getLogger(org.elasticsearch.rest.action.admin.indices.RestPutMappingAction.class));
    public static final String TYPES_DEPRECATION_MESSAGE = "[types removal] Using include_type_name in put " +
            "mapping requests is deprecated. The parameter will be removed in the next major version.";

//    public RestPutMappingAction(Settings settings, RestController controller) {
//        super(settings);
//        controller.registerHandler(this);
//    }

    public RestPutMappingAction() {}

    @Override
    public String getName() {
        return Path.ACTION_ROOT + "put_mapping_action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(PUT, "/{index}"+Path.ROOT+"/mapping/"));
        routes.add(new RestHandler.Route(PUT, "/{index}/{type}"+Path.ROOT+"/mapping"));
        routes.add(new RestHandler.Route(PUT, "/{index}"+Path.ROOT+"/mapping/{type}"));
        routes.add(new RestHandler.Route(PUT, Path.ROOT+"/mapping/{type}"));

        routes.add(new RestHandler.Route(POST, "/{index}"+Path.ROOT+"/mapping/"));
        routes.add(new RestHandler.Route(POST, "/{index}/{type}"+Path.ROOT+"/mapping"));
        routes.add(new RestHandler.Route(POST, "/{index}"+Path.ROOT+"/mapping/{type}"));
        routes.add(new RestHandler.Route(POST, Path.ROOT+"/mapping/{type}"));

        //register the same paths, but with plural form _mappings
        routes.add(new RestHandler.Route(PUT, "/{index}"+Path.ROOT+"/mappings/"));
        routes.add(new RestHandler.Route(PUT, "/{index}/{type}"+Path.ROOT+"/mappings"));
        routes.add(new RestHandler.Route(PUT, "/{index}"+Path.ROOT+"/mappings/{type}"));
        routes.add(new RestHandler.Route(PUT, Path.ROOT+"/mappings/{type}"));

        routes.add(new RestHandler.Route(POST, "/{index}"+Path.ROOT+"/mappings/"));
        routes.add(new RestHandler.Route(POST, "/{index}/{type}"+Path.ROOT+"/mappings"));
        routes.add(new RestHandler.Route(POST, "/{index}"+Path.ROOT+"/mappings/{type}"));
        routes.add(new RestHandler.Route(POST, Path.ROOT+"/mappings/{type}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        final boolean includeTypeName = request.paramAsBoolean(INCLUDE_TYPE_NAME_PARAMETER,
                DEFAULT_INCLUDE_TYPE_NAME_POLICY);
        if (request.hasParam(INCLUDE_TYPE_NAME_PARAMETER)) {
            deprecationLogger.deprecatedAndMaybeLog("put_mapping_with_types", TYPES_DEPRECATION_MESSAGE);
        }

        PutMappingRequest putMappingRequest = putMappingRequest(Strings.splitStringByCommaToArray(request.param("index")));

        final String type = request.param("type");
        putMappingRequest.type(includeTypeName ? type : MapperService.SINGLE_MAPPING_NAME);

        Map<String, Object> sourceAsMap = XContentHelper.convertToMap(request.requiredContent(), false,
                request.getXContentType()).v2();
        if (includeTypeName == false &&
                (type != null || isMappingSourceTyped(MapperService.SINGLE_MAPPING_NAME, sourceAsMap))) {
            throw new IllegalArgumentException("Types cannot be provided in put mapping requests, unless " +
                    "the include_type_name parameter is set to true.");
        }

        putMappingRequest.source(sourceAsMap);
        putMappingRequest.timeout(request.paramAsTime("timeout", putMappingRequest.timeout()));
        putMappingRequest.masterNodeTimeout(request.paramAsTime("master_timeout", putMappingRequest.masterNodeTimeout()));
        putMappingRequest.indicesOptions(IndicesOptions.fromRequest(request, putMappingRequest.indicesOptions()));
        return channel -> client.admin().indices().putMapping(putMappingRequest, new RestToXContentListener<>(channel));
    }
}
