package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDateRangeDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.common.Strings;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.range.DateRangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregator;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DateRange implements AggBuilder {
    private DateRangeAggregationBuilder builder;

    public DateRange() {
    }

    public DateRange(DateRangeAggregationBuilder builder) {
        this.builder = builder;
    }

    @Override
    public AggregationBuilder build() {
        return this.builder;
    }

    @Override
    public <C extends AggBuilder> C sub(C child) {
        builder.subAggregation(child.build());
        return child;
    }

    @Override
    public boolean isNested() {
        return false;
    }

    @Override
    public String path() {
        return null;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        AggregationDateRangeDim rangeDim = (AggregationDateRangeDim) dim;

        DateRangeAggregationBuilder builder = AggregationBuilders.dateRange(dim.getFieldName());
        List<AggregationDateRangeDim.Range> ranges = rangeDim.getRanges();
        if (ranges.size() > 0) {
            ranges.forEach(range -> builder.addRange(this.range(range)));
        }
        if (null != rangeDim.getFormat()) {
            builder.format(rangeDim.getFormat());
        }

        // FIXED 2023-03-23 dateRange 是不能设置 sort 和 size 的，通过 bucket_sort 聚合来控制
        FieldSortBuilder sort = AggBuilder.fieldOrder(dim.getAggrOrder());
        List<FieldSortBuilder> order = (null == sort) ? null : Collections.singletonList(sort);

        BucketSortPipelineAggregationBuilder sizeBuilder = PipelineAggregatorBuilders.bucketSort(dim.getFieldName(), order).size(dim.getSize());
        builder.subAggregation(sizeBuilder);

        if (Strings.isNullOrEmpty(dim.path())) {
            builder.field(dim.getFieldName());
            this.builder = builder;
            return this;
        } else {
            builder.field((dim.path() + "." + dim.getFieldName()));
            NestedAggregationBuilder nestedBuilder = AggregationBuilders.nested(dim.path(), dim.path());
            return new Nested(nestedBuilder, builder, dim.path());
        }
    }

    private RangeAggregator.Range range(AggregationDateRangeDim.Range range) {
        return new RangeAggregator.Range(range.getKey(), range.getFrom(), range.getTo());
    }
}
