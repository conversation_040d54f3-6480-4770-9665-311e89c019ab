package cn.gwssi.isearch.plugins.search.rest.stat;

import cn.gwssi.isearch.plugins.common.search.Path;
import cn.gwssi.isearch.plugins.search.ExpressionService;
import cn.gwssi.isearch.plugins.search.rest.RestQueryAction;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.rest.RestRequest;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestCountAction extends RestQueryAction
{
//    public RestCountAction(Settings settings, RestController controller, ExpressionService expressionService)
//    {
//        super(settings, controller, expressionService);
//    }

    public RestCountAction(ExpressionService expressionService) {
        super(expressionService);
    }

    @Override
    public String getName()
    {
        return Path.ACTION_ROOT + "count-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.COUNT));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.COUNT));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.COUNT+"/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.COUNT+"/{index}"));
        routes.add(new RestHandler.Route(RestRequest.Method.GET, Path.COUNT+"/{index}/{type}"));
        routes.add(new RestHandler.Route(RestRequest.Method.POST, Path.COUNT+"/{index}/{type}"));

        return routes;
    }

    @Override
    protected XContentBuilder responseBuilder(SearchResponse searchResponse, XContentBuilder builder) throws IOException
    {
        return builder.startObject()
                .field("count", searchResponse.getHits().getTotalHits().value)
                .endObject();
    }
}
