package cn.gwssi.isearch.plugins.search.agg;

import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import org.elasticsearch.common.Strings;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.ReverseNestedAggregationBuilder;

public class Reverse implements AggBuilder
{
    private ReverseNestedAggregationBuilder reverseNestedBuilder;

    private AggBuilder child;

    public Reverse(AggBuilder child,String name, String path)
    {
        this.reverseNestedBuilder = AggregationBuilders.reverseNested(name);

        if(!Strings.isNullOrEmpty(path))
            this.reverseNestedBuilder.path(path);

        this.child = child;

        reverseNestedBuilder.subAggregation(child.build());
    }

    @Override
    public AggregationBuilder build()
    {
        return reverseNestedBuilder;
    }

    @Override
    public AggBuilder buildBasic(AggregationDim dim) {
        return this;
    }

    @Override
    public <C extends AggBuilder> C sub(C child)
    {
        this.child.sub(child);
        return child;
    }

    @Override
    public boolean isNested()
    {
        return child.isNested();
    }

    @Override
    public String path()
    {
        return child.path();
    }
}
