<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.gwssi.isearch</groupId>
        <artifactId>plugins-7.9.3</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>plugins-search-7.9.3</artifactId>
    <version>${revision}</version>
    
    <dependencies>
        <dependency>
            <groupId>cn.gwssi.esplugins</groupId>
            <artifactId>vector-core</artifactId>
            <version>es7.9.3</version>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.esplugins</groupId>
            <artifactId>patsearch</artifactId>
            <version>es7.9.3</version>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>plugins-common-search</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.esplugins</groupId>
            <artifactId>dprm</artifactId>
            <version>0.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>tech.easynlp.elasticsearch</groupId>
            <artifactId>analysis-smart</artifactId>
            <version>7.9.3</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.isearch</groupId>-->
<!--            <artifactId>plugins-common-image</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

<!--        elasticsearch 的依赖使用 gradle，maven repository 没有这个包-->
<!--        使用插件安装到maven，然后shade打包才能打进去-->
<!--        <dependency>-->
<!--            <groupId>org.elasticsearch.plugin</groupId>-->
<!--            <artifactId>x-pack-analytics</artifactId>-->
<!--            <version>7.9.3</version>-->
<!--&lt;!&ndash;            <scope>system</scope>&ndash;&gt;-->
<!--&lt;!&ndash;            <systemPath>${basedir}/../../../libs/x-pack-analytics-7.9.3.jar</systemPath>&ndash;&gt;-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
<!--            没有这个 system 包不能打包到 shade-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-install-plugin</artifactId>-->
<!--                <version>2.5.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>install-file</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <file>libs/x-pack-analytics-7.9.3.jar</file>-->
<!--                            <groupId>org.elasticsearch.plugin</groupId>-->
<!--                            <artifactId>x-pack-analytics</artifactId>-->
<!--                            <version>7.9.3</version>-->
<!--                            <packaging>jar</packaging>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <artifactSet>
                                <excludes>
                                    <exclude>junit:junit</exclude>
                                    <exclude>org.apache.maven:lib:tests</exclude>
                                    <exclude>com.fasterxml.jackson.*:*</exclude>
                                    <!--                                    <exclude>*:commons-lang3</exclude>-->
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:log4j:*</exclude>
                                    <exclude>*:joda-time</exclude>
                                </excludes>
                            </artifactSet>

                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.DontIncludeResourceTransformer">
                                    <resource>*.*</resource>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
