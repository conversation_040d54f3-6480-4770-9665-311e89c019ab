package cn.gwssi.isearch.plugins.image;

import cn.gwssi.esplugins.vector.core.KNNSettings;
import cn.gwssi.esplugins.vector.core.cache.LSHIndexCache;
import cn.gwssi.esplugins.vector.core.cache.PQIndexCache;
import cn.gwssi.esplugins.vector.core.support.lsh.AbsolutePathInPlugin;
import org.elasticsearch.SpecialPermission;
import org.elasticsearch.client.Client;
import org.elasticsearch.cluster.metadata.IndexNameExpressionResolver;
import org.elasticsearch.cluster.service.ClusterService;
import org.elasticsearch.common.io.stream.NamedWriteableRegistry;
import org.elasticsearch.common.settings.Setting;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.env.Environment;
import org.elasticsearch.env.NodeEnvironment;
import org.elasticsearch.index.IndexSettings;
import org.elasticsearch.index.engine.EngineFactory;
import org.elasticsearch.index.engine.GWEngineFactory;
import org.elasticsearch.index.mapper.KNNFieldMapper;
import org.elasticsearch.index.mapper.KNNReadyFieldMapper;
import org.elasticsearch.index.mapper.Mapper;
import org.elasticsearch.index.queries.LSHQueryBuilder;
import org.elasticsearch.index.queries.PQQueryBuilder;
import org.elasticsearch.plugins.ActionPlugin;
import org.elasticsearch.plugins.EnginePlugin;
import org.elasticsearch.plugins.MapperPlugin;
import org.elasticsearch.plugins.SearchPlugin;
import org.elasticsearch.repositories.RepositoriesService;
import org.elasticsearch.script.ScriptService;
import org.elasticsearch.threadpool.ThreadPool;
import org.elasticsearch.watcher.ResourceWatcherService;

import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.*;
import java.util.function.Supplier;

public class VectorPlugin extends org.elasticsearch.plugins.Plugin implements MapperPlugin, SearchPlugin
{

    public VectorPlugin(final Settings settings)
    {
        String hash_file_path = settings.get(AbsolutePathInPlugin.HASH_PATH_CONFIG);

        if (hash_file_path != null)
        {
            AbsolutePathInPlugin.hashFilePath = hash_file_path;
        }

        String so_file_path = settings.get(AbsolutePathInPlugin.SO_PATH_CONFIG);

        if (so_file_path != null)
        {

            // 官网提供的help，我也不知道要检查什么
            SecurityManager sm = System.getSecurityManager();
            if (sm != null) {
                // unprivileged code such as scripts do not have SpecialPermission
                sm.checkPermission(new SpecialPermission());
            }

            // 如果 policy 是在 jre 中配置的，不需要这个
            // 但是 plugin-security.policy only be granted to the jars in your plugin，load 会报错
            AccessController.doPrivileged((PrivilegedAction<Object>) () -> {
                System.load(so_file_path);
                return null;
            });
        }
    }

    @Override
    public Map<String, Mapper.TypeParser> getMappers()
    {
        Map<String, Mapper.TypeParser> mappers = new HashMap<>();

        mappers.put(KNNFieldMapper.CONTENT_TYPE, new KNNFieldMapper.KNNFieldTypeParser());
        mappers.put(KNNReadyFieldMapper.CONTENT_TYPE, new KNNReadyFieldMapper.IndexReadyFieldTypeParser());

        return mappers;
    }

    @Override
    public List<QuerySpec<?>> getQueries()
    {
        List<QuerySpec<?>> querySpecs = new ArrayList<>();

        QuerySpec<LSHQueryBuilder> lshScanQuery = new QuerySpec<>(
                LSHQueryBuilder.QUERY_NAME,
                LSHQueryBuilder::new,
                new LSHQueryBuilder.Parser()
        );

        QuerySpec<PQQueryBuilder> pqExh = new QuerySpec<>(
                PQQueryBuilder.QUERY_NAME,
                PQQueryBuilder::new,
                new PQQueryBuilder.Parser()
        );

        querySpecs.add(lshScanQuery);
        querySpecs.add(pqExh);

        return querySpecs;
    }

    @Override
    public Collection<Object> createComponents(Client client, ClusterService clusterService, ThreadPool threadPool, ResourceWatcherService resourceWatcherService, ScriptService scriptService, NamedXContentRegistry xContentRegistry, Environment environment, NodeEnvironment nodeEnvironment, NamedWriteableRegistry namedWriteableRegistry, IndexNameExpressionResolver indexNameExpressionResolver, Supplier<RepositoriesService> repositoriesServiceSupplier)
    {
        PQIndexCache.setResourceWatcherService(resourceWatcherService);
        LSHIndexCache.setResourceWatcherService(resourceWatcherService);
        return Collections.emptyList();
    }

    @Override
    public List<Setting<?>> getSettings()
    {
        return KNNSettings.getSettings();
    }
}
