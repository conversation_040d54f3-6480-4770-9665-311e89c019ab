package cn.gwssi.isearch.scripts;


import cn.gwssi.isearch.scripts.factory.AggForCollapseLeafFactory;
import org.elasticsearch.script.AggregationScript;
import org.elasticsearch.script.ScriptContext;
import org.elasticsearch.script.ScriptEngine;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

public class AggScriptEngine implements ScriptEngine {

    private final static String LANG = "agg_script";
    private final static String SOURCE_NAME = "agg_for_collapse";

    @Override
    public String getType() {
        return LANG;
    }

    @Override
    public <FactoryType> FactoryType compile(String name, String code, ScriptContext<FactoryType> context, Map<String, String> params) {
        if (!context.equals(AggregationScript.CONTEXT)) {
            throw new IllegalArgumentException(getType() + " scripts cannot be used for context [" + context.name + "]");
        }

        if (SOURCE_NAME.equals(code)) {
            AggregationScript.Factory factory = AggForCollapseLeafFactory::new;
            return context.factoryClazz.cast(factory);
        }

        throw new IllegalArgumentException("Unknown script name " + code);
    }

    @Override
    public void close() throws IOException {

    }

    @Override
    public Set<ScriptContext<?>> getSupportedContexts() {
        return Collections.singleton(AggregationScript.CONTEXT);
    }
}
