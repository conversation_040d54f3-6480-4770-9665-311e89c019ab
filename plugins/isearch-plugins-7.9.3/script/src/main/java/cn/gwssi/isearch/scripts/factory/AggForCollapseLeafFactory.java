package cn.gwssi.isearch.scripts.factory;

import org.apache.lucene.index.LeafReaderContext;
import org.elasticsearch.script.AggregationScript;
import org.elasticsearch.search.lookup.SearchLookup;

import java.util.*;

public class AggForCollapseLeafFactory implements AggregationScript.LeafFactory {

    private final Map<String, Object> params;
    private final SearchLookup lookup;

    // 分组字段
    private final String groupBy;
    // 聚合字段
    private final String field;

    private Map<String, Set<String>> result = new HashMap<>();

    public AggForCollapseLeafFactory(Map<String, Object> params, SearchLookup lookup) {
        this.params = params;
        this.lookup = lookup;

        if (!params.containsKey("groupBy")) {
            throw new IllegalArgumentException("Missing parameter [groupBy]");
        }
        if (!params.containsKey("field")) {
            throw new IllegalArgumentException("Missing parameter [field]");
        }

        groupBy = params.get("groupBy").toString();
        field = params.get("field").toString();
    }

    @Override
    public AggregationScript newInstance(LeafReaderContext ctx) {
        return new AggregationScript(params, lookup, ctx) {
            @Override
            public Object execute() {
                long value1 = 0L;
                Object groupValue = lookup.source().get(groupBy);
                Object fieldValue = lookup.source().get(field);
                if (null != groupValue && null != fieldValue) {
                    String group = groupValue.toString();
                    String value = fieldValue.toString();

                    if (!result.containsKey(value)) {
                        result.put(value, new HashSet<>());
                    }

                    result.get(value).add(group);
                    value1 = result.get(value).size();
                }


                if (null != fieldValue && "S8".equals(fieldValue.toString())) {
                    System.out.println("================group===" + (null == groupValue ? "null" : groupValue.toString()));
                    System.out.println("================result" + result);
                    System.out.println("================value1" + value1);
                }

                return value1;
            }
        };
    }

    @Override
    public boolean needs_score() {
        return false;
    }
}
