package cn.gwssi.isearch.rescore;

import org.elasticsearch.plugins.Plugin;
import org.elasticsearch.plugins.SearchPlugin;
import org.elasticsearch.search.fetch.FetchSubPhase;

import java.util.Collections;
import java.util.List;

public class RescorePlugin extends Plugin implements SearchPlugin {

    @Override
    public List<FetchSubPhase> getFetchSubPhases(FetchPhaseConstructionContext context) {
        return Collections.singletonList(new RescoreFetchSubPhase());
    }

    @Override
    public List<SearchExtSpec<?>> getSearchExts() {
        return Collections.singletonList(
                new SearchExtSpec<>(
                        RescoreBuilder.FINAL_RESCORE_EXT,
                        RescoreBuilder::new,
                        RescoreBuilder::parseFromXContent));
    }

    @Override
    public List<RescorerSpec<?>> getRescorers() {
        return Collections.singletonList(
                new RescorerSpec<>(CustomRescoreBuilder.NAME, CustomRescoreBuilder::new, CustomRescoreBuilder::fromXContent));
    }
}