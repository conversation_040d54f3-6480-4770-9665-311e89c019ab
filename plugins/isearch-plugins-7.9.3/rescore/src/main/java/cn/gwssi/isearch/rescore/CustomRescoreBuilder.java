package cn.gwssi.isearch.rescore;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.common.Nullable;
import org.elasticsearch.common.ParseField;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.ConstructingObjectParser;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.index.fielddata.IndexFieldData;
import org.elasticsearch.index.query.QueryRewriteContext;
import org.elasticsearch.index.query.QueryShardContext;
import org.elasticsearch.search.rescore.QueryRescorer;
import org.elasticsearch.search.rescore.RescoreContext;
import org.elasticsearch.search.rescore.RescorerBuilder;

import java.io.IOException;
import java.util.Objects;

import static org.elasticsearch.common.xcontent.ConstructingObjectParser.constructorArg;
import static org.elasticsearch.common.xcontent.ConstructingObjectParser.optionalConstructorArg;

public class CustomRescoreBuilder extends RescorerBuilder<CustomRescoreBuilder> {
    public static final String NAME = "sth";

    protected static final Logger log = LogManager.getLogger(CustomRescoreBuilder.class);

    private final String field;
    private final String order;

    public CustomRescoreBuilder(StreamInput in) throws IOException {
        super(in);
        field = in.readString();
        order = in.readOptionalString();
    }

    public CustomRescoreBuilder(String field, String order) {
        this.field = field;
        this.order = order;
    }

    @Override
    protected void doWriteTo(StreamOutput out) throws IOException {
        out.writeString(field);
        out.writeOptionalString(order);
    }

    @Override
    public String getWriteableName() {
        return NAME;
    }

    @Override
    public RescorerBuilder<CustomRescoreBuilder> rewrite(QueryRewriteContext ctx) throws IOException {
        return this;
    }

    private static final ParseField KEY_FIELD = new ParseField("field");
    private static final ParseField KEY_ORDER = new ParseField("order");

    @Override
    protected void doXContent(XContentBuilder builder, Params params) throws IOException {
        builder.field(KEY_FIELD.getPreferredName(), field);
        if (order != null) {
            builder.field(KEY_ORDER.getPreferredName(), order);
        }
    }

    private static final ConstructingObjectParser<CustomRescoreBuilder, Void> PARSER = new ConstructingObjectParser<>(NAME,
            args -> new CustomRescoreBuilder((String) args[0], (String) args[1]));

    static {
        PARSER.declareString(constructorArg(), KEY_FIELD);
        PARSER.declareString(optionalConstructorArg(), KEY_ORDER);
    }

    public static CustomRescoreBuilder fromXContent(XContentParser parser) {
        return PARSER.apply(parser, null);
    }

    @Override
    public RescoreContext innerBuildContext(int windowSize, QueryShardContext context) throws IOException {
        IndexFieldData<?> field =
                this.field == null ? null : context.getForField(context.fieldMapper(this.field));
        return new CustomRescoreContext(windowSize, order, field);
    }

    @Override
    public boolean equals(Object obj) {
        if (!super.equals(obj)) {
            return false;
        }
        CustomRescoreBuilder other = (CustomRescoreBuilder) obj;
        return field.equals(other.field)
                && Objects.equals(order, other.order);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), field, order);
    }

    String field() {
        return field;
    }

    @Nullable
    String order() {
        return order;
    }

    private static class CustomRescoreContext extends RescoreContext {
        private final String order;
        @Nullable
        private final IndexFieldData<?> field;

        CustomRescoreContext(int windowSize, String order, @Nullable IndexFieldData<?> field) {
            super(windowSize, QueryRescorer.INSTANCE);
            this.order = order;
            this.field = field;
        }
    }
//
//    private static class CustomRescorer implements Rescorer {
//
//        private static final CustomRescorer INSTANCE = new CustomRescorer();
//        private RescoreContext rescoreContext;
//
//        private static String getTermFromFieldData(int topLevelDocId, AtomicFieldData fd,
//                                                   LeafReaderContext leaf, String fieldName) throws IOException {
//            String term = null;
//            if (fd instanceof SortedSetDVBytesAtomicFieldData) {
//                final SortedSetDocValues data = ((SortedSetDVBytesAtomicFieldData) fd).getOrdinalsValues();
//                if (data != null) {
//                    if (data.advanceExact(topLevelDocId - leaf.docBase)) {
//                        // document does have data for the field
//                        term = data.lookupOrd(data.nextOrd()).utf8ToString();
//                    }
//                }
//            } else if (fd instanceof AtomicNumericFieldData) {
//                final SortedNumericDocValues data = ((AtomicNumericFieldData) fd).getLongValues();
//                if (data != null) {
//                    if (!data.advanceExact(topLevelDocId - leaf.docBase)) {
//                        throw new IllegalArgumentException("document [" + topLevelDocId
//                                + "] does not have the field [" + fieldName + "]");
//                    }
//                    if (data.docValueCount() > 1) {
//                        throw new IllegalArgumentException("document [" + topLevelDocId
//                                + "] has more than one value for [" + fieldName + "]");
//                    }
//                    term = String.valueOf(data.nextValue());
//                }
//            }
//            return term;
//        }
//
//        @Override
//        public TopDocs rescore(TopDocs topDocs, IndexSearcher searcher, RescoreContext rescoreContext) throws IOException {
//            assert rescoreContext != null;
//            if (topDocs == null || topDocs.scoreDocs.length == 0) {
//                return topDocs;
//            }
//
//            // First take top slice of incoming docs, to be rescored:
//            TopDocs topNFirstPass = topN(topDocs, rescoreContext.getWindowSize());
//
//            Arrays.sort(topDocs.scoreDocs, new Comparator<ScoreDoc>() {
//                @Override
//                public int compare(ScoreDoc o1, ScoreDoc o2) {
//                    int cmp = Float.compare(o2.score, o1.score);
//                    return cmp == 0 ?  Integer.compare(o1.doc, o2.doc) : cmp;
//                }
//            });
//
//            return topNFirstPass;
//        }
//
//        @Override
//        public Explanation explain(int topLevelDocId, IndexSearcher searcher, RescoreContext rescoreContext, Explanation sourceExplanation) throws IOException {
//            if (sourceExplanation == null) {
//                // this should not happen but just in case
//                return Explanation.noMatch("nothing matched");
//            }
//            QueryRescorer.QueryRescoreContext rescore = (QueryRescorer.QueryRescoreContext) rescoreContext;
//            float primaryWeight = rescore.queryWeight();
//            Explanation prim;
//            if (sourceExplanation.isMatch()) {
//                prim = Explanation.match(
//                        sourceExplanation.getValue().floatValue() * primaryWeight,
//                        "product of:", sourceExplanation, Explanation.match(primaryWeight, "primaryWeight"));
//            } else {
//                prim = Explanation.noMatch("First pass did not match", sourceExplanation);
//            }
//            if (rescoreContext.isRescored(topLevelDocId)){
//                Explanation rescoreExplain = searcher.explain(rescore.query(), topLevelDocId);
//                // NOTE: we don't use Lucene's Rescorer.explain because we want to insert our own description with which ScoreMode was used.
//                //  Maybe we should add QueryRescorer.explainCombine to Lucene?
//                if (rescoreExplain != null && rescoreExplain.isMatch()) {
//                    float secondaryWeight = rescore.rescoreQueryWeight();
//                    Explanation sec = Explanation.match(
//                            rescoreExplain.getValue().floatValue() * secondaryWeight,
//                            "product of:",
//                            rescoreExplain, Explanation.match(secondaryWeight, "secondaryWeight"));
//                    QueryRescoreMode scoreMode = rescore.scoreMode();
//                    return Explanation.match(
//                            scoreMode.combine(prim.getValue().floatValue(), sec.getValue().floatValue()),
//                            scoreMode + " of:",
//                            prim, sec);
//                }
//            }
//            return prim;
//        }
//
//        /** Returns a new {@link TopDocs} with the topN from the incoming one, or the same TopDocs if the number of hits is already &lt;=
//         *  topN. */
//        private TopDocs topN(TopDocs in, int topN) {
//            if (in.scoreDocs.length < topN) {
//                return in;
//            }
//
//            ScoreDoc[] subset = new ScoreDoc[topN];
//            System.arraycopy(in.scoreDocs, 0, subset, 0, topN);
//
//            return new TopDocs(in.totalHits, subset);
//        }
//    }
}

