package cn.gwssi.isearch.rescore;

import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.search.SearchExtBuilder;
import org.elasticsearch.search.rescore.RescorerBuilder;

import java.io.IOException;
import java.util.Objects;

public class RescoreBuilder extends SearchExtBuilder {

    public static final String FINAL_RESCORE_EXT = "final-rescore";

    private final CustomRescoreBuilder rescorer;

    public RescoreBuilder(StreamInput in) {
        try {
            this.rescorer = new CustomRescoreBuilder(in);
        } catch (IOException ioException) {
            throw new RuntimeException(ioException);
        }
    }

    public RescoreBuilder(CustomRescoreBuilder rescorer) {
        this.rescorer = rescorer;
    }

    public CustomRescoreBuilder getRescorer() {
        return rescorer;
    }

    public static RescoreBuilder parseFromXContent(XContentParser parser) throws IOException {
        CustomRescoreBuilder rescorer = CustomRescoreBuilder.fromXContent(parser);
        return new RescoreBuilder(rescorer);
    }

    @Override
    public String getWriteableName() {
        return FINAL_RESCORE_EXT;
    }

    @Override
    public void writeTo(StreamOutput out) throws IOException {
        out.writeNamedWriteable(this);
    }

    @Override
    public XContentBuilder toXContent(XContentBuilder builder, ToXContent.Params params) throws IOException {
        builder.startObject(FINAL_RESCORE_EXT);
        rescorer.doXContent(builder, params);
        return builder.endObject();
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }

        if (other == null || getClass() != other.getClass()) {
            return false;
        }

        RescoreBuilder another = (RescoreBuilder) other;
        return Objects.equals(rescorer, another.rescorer);
    }

    @Override
    public int hashCode() {
        return Objects.hash(rescorer);
    }

}
