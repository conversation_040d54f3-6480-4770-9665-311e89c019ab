package cn.gwssi.isearch.rescore;

import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.TopDocs;
import org.elasticsearch.common.lucene.search.TopDocsAndMaxScore;
import org.elasticsearch.index.query.QueryShardContext;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.fetch.FetchSubPhase;
import org.elasticsearch.search.internal.SearchContext;
import org.elasticsearch.search.rescore.QueryRescorer;
import org.elasticsearch.search.rescore.RescoreContext;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class RescoreFetchSubPhase implements FetchSubPhase {

    @Override
    public void hitExecute(SearchContext context, HitContext hitContext) throws IOException {
        // TODO: maybe need to code this
    }

    @Override
    public void hitsExecute(SearchContext context, SearchHit[] hits) throws IOException {
        System.out.println("111");
        RescoreBuilder finalRescore = (RescoreBuilder) context.getSearchExt(RescoreBuilder.FINAL_RESCORE_EXT);
        System.out.println("222");
        if (hits.length == 0 || finalRescore == null) {
            return;
        }

        System.out.println("333");
        if (context.queryResult().hasConsumedTopDocs()) {
            return;
        }

        System.out.println("444");
        TopDocsAndMaxScore topDocs = context.queryResult().topDocs();
        TopDocs topDocs_ = topDocs.topDocs;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        Arrays.sort(hits, (o1, o2) -> {
            Object object1 = o1.getSourceAsMap().get("ad");
            long value1 = 0;
            if (null != object1) {
                try {
                    value1 = format.parse(object1.toString()).getTime();
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }

            Object object2 = o2.getSourceAsMap().get("ad");
            long value2 = 0;
            if (null != object1) {
                try {
                    value2 = format.parse(object2.toString()).getTime();
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }

            System.out.println("value1===========" + value1);
            System.out.println("value2===========" + value2);

            return (int) (value1 - value2);
        });

        Map<Integer, SearchHit> hitMap = new HashMap<>();
        for (int i = 0; i < hits.length; i++) {
            int doc = topDocs_.scoreDocs[i].doc;
            hits[i].getSourceAsMap().put("_score", topDocs_.scoreDocs[i].score);
            hitMap.put(doc, hits[i]);
        }
//
//        RescoreContext rescoreContext = finalRescore
//                .getRescorer()
//                .buildContext(new QueryShardContext(context.getQueryShardContext()));
//        topDocs_ = QueryRescorer.INSTANCE.rescore(topDocs_, context.searcher(), rescoreContext);
//

        TopDocs topNFirstPass = topN(topDocs_, 100);
        TopDocsAndMaxScore tops = new TopDocsAndMaxScore(topNFirstPass, topDocs.maxScore);
        context.queryResult().topDocs(tops, context.queryResult().sortValueFormats());
//
//        assert rescoreContext != null;
//        if (topDocs == null || topDocs.scoreDocs.length == 0) {
//            return topDocs;
//        }
//
//        // First take top slice of incoming docs, to be rescored:
//        TopDocs topNFirstPass = topN(topDocs_, 100);
//
//        Arrays.sort(topDocs.scoreDocs, new Comparator<ScoreDoc>() {
//            @Override
//            public int compare(ScoreDoc o1, ScoreDoc o2) {
//                int cmp = Float.compare(o2.score, o1.score);
//                return cmp == 0 ?  Integer.compare(o1.doc, o2.doc) : cmp;
//            }
//        });
//
//        /* Reorder hits in array according the new ordering */
//        for (int i = 0; i < hits.length; i++) {
//            int doc = topDocs_.scoreDocs[i].doc;
//            hits[i] = hitMap.get(doc);
//        }
    }

//    public void state() {
//        String pnk = doc['pnk'].value;
//        String pd = doc['pd'].value;
//        Map existMap = state.map.get(pnk);
//        if (null == existMap) {
//            Map inner = new HashMap();
//            inner.put("pid", doc.pid.value);
//            inner.put("pd", pd);
//            state.map.put(pnk, inner);
//        } else if (params.isAsc && pd > existMap.get("pd")) {
//            existMap.put("pid", doc.pid.value);
//            existMap.put("pd", pd);
//        }
//
//        Map uniqueValueMap = new TreeMap();
//        uniqueValueMap.putAll(states[0]);
//
//
//        for (int i = 1; i < states.length; i++) {
//            Map state = states[i];
//            if (null != state) {
//                for (key in state.keySet()) {
//                    Map existMap = uniqueValueMap.get(key);
//                    Map current = state.get(key);
//                    if (null == existMap || (params.isAsc && current.get("pd") > existMap.get("pd"))) {
//                        uniqueValueMap.put(key, current);
//                    }
//                }
//            }
//        }
//        return uniqueValueMap;
//    }

    /**
     * Returns a new {@link TopDocs} with the topN from the incoming one, or the same TopDocs if the number of hits is already &lt;=
     * topN.
     */
    private TopDocs topN(TopDocs in, int topN) {
        if (in.scoreDocs.length < topN) {
            return in;
        }

        ScoreDoc[] subset = new ScoreDoc[topN];
        System.arraycopy(in.scoreDocs, 0, subset, 0, topN);

        return new TopDocs(in.totalHits, subset);
    }

}
