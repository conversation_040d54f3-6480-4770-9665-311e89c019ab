<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.gwssi.isearch</groupId>
        <artifactId>plugins-7.9.3</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>plugins-rescore-7.9.3</artifactId>
    <version>${revision}</version>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <artifactSet>
                                <excludes>
                                    <exclude>junit:junit</exclude>
                                    <exclude>org.apache.maven:lib:tests</exclude>
                                    <exclude>com.fasterxml.jackson.*:*</exclude>
                                    <!--                                    <exclude>*:commons-lang3</exclude>-->
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:log4j:*</exclude>
                                    <exclude>*:joda-time</exclude>
                                </excludes>
                            </artifactSet>
                            
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.DontIncludeResourceTransformer">
                                    <resource>*.*</resource>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>