package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class DeletePrivilegesAction
    extends ActionType<DeletePrivilegesResponse>
{
    private static final String NAME = "cluster:admin/security/privilege/delete";

    private static final DeletePrivilegesAction INSTANCE = new DeletePrivilegesAction();

    private DeletePrivilegesAction()
    {
        super(NAME, DeletePrivilegesResponse::new);
    }

    public static final String actionName()
    {
        return NAME;
    }

    public static final DeletePrivilegesAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public DeletePrivilegesResponse newResponse()
//    {
//        return new DeletePrivilegesResponse();
//    }
}
