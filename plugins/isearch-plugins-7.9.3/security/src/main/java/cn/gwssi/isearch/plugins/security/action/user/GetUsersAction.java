package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class GetUsersAction
    extends ActionType<GetUsersResponse>
{
    private static final String NAME = "cluster:admin/girp/security/user/get";

    private static final GetUsersAction INSTANCE = new GetUsersAction();

    private GetUsersAction()
    {
        super(NAME, GetUsersResponse::new);
    }

    public static String actionName()
    {
        return NAME;
    }

    public static GetUsersAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public GetUsersResponse newResponse()
//    {
//        return new GetUsersResponse();
//    }
}
