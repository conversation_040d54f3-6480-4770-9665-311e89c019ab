package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import cn.gwssi.isearch.plugins.security.action.privilege.PutPrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.PutPrivilegesResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * 新增用户权限
 */
public class RestPutPrivilegesAction
    extends BaseRestHandler
{
    @Override
    public String getName()
    {
        return "rest-put-privileges-action";
    }

    @Inject
    public RestPutPrivilegesAction() {}

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.PUT, ConfigConstants.REST_PRIVILEGE_USERNAME));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String username = request.param("username");

        List<Privilege> toAdd = Privilege.from(request.content());

        PutPrivilegesRequestBuilder requestBuilder = new SecurityClient(client)
                .preparePutPrivileges().setUsername(username).setPrivileges(toAdd);

        return channel->requestBuilder.execute(new RestBuilderListener<PutPrivilegesResponse>(channel)
        {
            @Override
            public RestResponse buildResponse(PutPrivilegesResponse putPrivilegesResponse, XContentBuilder builder) throws Exception
            {
                return new BytesRestResponse(RestStatus.OK, putPrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
