package cn.gwssi.isearch.plugins.security.system;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.user.*;
import org.elasticsearch.action.ActionFuture;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest;
import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.Client;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;

public class NativeUserService implements UserService
{
    private Client client;

    private volatile boolean indexExists = false;

    @Inject
    public NativeUserService(Client client)
    {
        this.client=client;
    }

    @Override
    public void getUsers(GetUsersRequest request, final ActionListener<GetUsersResponse> listener) throws ExecutionException, InterruptedException
    {
        String[] usernames = request.getUsernames();

        if (usernames == null || usernames.length == 0)
        {
            this.client.search(this.client.prepareSearch(ConfigConstants.SECURITY_INDEX).setQuery(QueryBuilders.matchAllQuery()).setFrom(request.getFrom()).setSize(request.getSize()).request(), new ActionListener<SearchResponse>()
            {
                @Override
                public void onResponse(SearchResponse searchResponse)
                {
                    List<User> users = fromSearchResult(searchResponse);

                    long total = searchResponse.getHits().getTotalHits().value;

                    GetUsersResponse response = new GetUsersResponse();

                    response.setTotal(total);

                    response.setUsers(users.toArray(new User[0]));

                    listener.onResponse(response);
                }

                @Override
                public void onFailure(Exception e)
                {
                    listener.onFailure(e);
                }
            });
        }
        else if (usernames.length == 1)
        {
            List<User> users = new ArrayList<>();

            User user = getUser(usernames[0]);

            if(user!=User.NULL_USER)
                users.add(user);

            GetUsersResponse response = new GetUsersResponse();

            response.setTotal(users.size());

            response.setUsers(users.toArray(new User[0]));

            listener.onResponse(response);
        }
        else if (usernames.length > 1)
        {
            this.client.search(this.client.prepareSearch(ConfigConstants.SECURITY_INDEX).setQuery(QueryBuilders.idsQuery().addIds(usernames)).setFrom(request.getFrom()).setSize(request.getSize()).request(), new ActionListener<SearchResponse>()
            {
                @Override
                public void onResponse(SearchResponse searchResponse)
                {
                    GetUsersResponse response = new GetUsersResponse();

                    List<User> users = fromSearchResult(searchResponse);

                    response.setTotal(searchResponse.getHits().getTotalHits().value);

                    response.setUsers(users.toArray(new User[0]));

                    listener.onResponse(response);
                }

                @Override
                public void onFailure(Exception e)
                {
                    listener.onFailure(e);
                }
            });
        }
    }

    private List<User> fromSearchResult(SearchResponse response)
    {
        List<User> users = new ArrayList<>();

        if (response.getHits().getTotalHits().value == 0)
            ;
        else
        {
            SearchHit[] hits = response.getHits().getHits();

            for (SearchHit hit : hits)
            {
                users.add(User.fromSource(hit.getSourceAsMap()));
            }
        }

        return users;
    }

    @Override
    public User getUser(String username) throws ExecutionException, InterruptedException
    {
        synchronized (this)
        {
            if(!indexExists)
            {
                try
                {
                    if (!isIndexExists(".security"))
                    {
                        createIndex(".security");
                        this.indexExists = true;
                    }
                    else
                        this.indexExists = true;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }

        GetRequest getRequest =
//                this.client.prepareGet().setIndex(ConfigConstants.SECURITY_INDEX).setId(username).request();
                this.client.prepareGet(ConfigConstants.SECURITY_INDEX,ConfigConstants.SECURITY_DEFAULT_TYPE,username).request();


        ActionFuture<GetResponse> actionFuture = this.client.get(getRequest);

        GetResponse response = actionFuture.get();

        if(response.isSourceEmpty())
            return User.NULL_USER;
        else
            return User.fromSource(response.getSource());
    }

    @Override
    public void putUser(PutUserRequest request, final ActionListener<Boolean> listener) throws ExecutionException, InterruptedException, UserAlreadyExistsException
    {
        User user = getUser(request.getUser().getUsername());

        if(user!=User.NULL_USER)
            throw new UserAlreadyExistsException("user already exists");

        IndexRequest indexRequest = this.client.prepareIndex()
                .setIndex(ConfigConstants.SECURITY_INDEX)
                .setType(ConfigConstants.SECURITY_DEFAULT_TYPE)
                .setId(request.getUser().getUsername())
                .setSource(request.getUser().toSource()).request();
//                this.client.prepareIndex(ConfigConstants.SECURITY_INDEX, ConfigConstants.SECURITY_DEFAULT_TYPE, request.getUser().getUsername())

        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        this.client.index(indexRequest, new ActionListener<IndexResponse>()
        {
            @Override
            public void onResponse(IndexResponse indexResponse)
            {
                listener.onResponse(indexResponse.status() == RestStatus.CREATED);
            }

            @Override
            public void onFailure(Exception e)
            {
                listener.onFailure(e);
            }
        });
    }

    @Override
    public void deleteUser(DeleteUserRequest request, final ActionListener<Boolean> listener)
    {
        DeleteRequest deleteRequest = client.prepareDelete().setIndex(ConfigConstants.SECURITY_INDEX).setType(ConfigConstants.SECURITY_DEFAULT_TYPE)
                .setId(request.getUsername()).request();
                //client.prepareDelete(ConfigConstants.SECURITY_INDEX,ConfigConstants.SECURITY_TYPE_USER,request.getUsername()).request();

        deleteRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        this.client.delete(deleteRequest, new ActionListener<DeleteResponse>()
        {
            @Override
            public void onResponse(DeleteResponse deleteResponse)
            {
                listener.onResponse(deleteResponse.status() == RestStatus.OK);
            }

            @Override
            public void onFailure(Exception e)
            {
                listener.onFailure(e);
            }
        });
    }

    @Override
    public void postUser(PutUserRequest request, final ActionListener<Boolean> listener) throws ExecutionException, InterruptedException, UserNotFoundException
    {
        User user = getUser(request.getUser().getUsername());

        if(user==User.NULL_USER)
            throw new UserNotFoundException();

        UpdateRequest updateRequest = this.client.prepareUpdate()
                .setIndex(ConfigConstants.SECURITY_INDEX)
                .setType(ConfigConstants.SECURITY_DEFAULT_TYPE)
                .setId(request.getUser().getUsername())
                .setDoc(request.getUser().toSource()).request();
//                this.client.prepareUpdate(ConfigConstants.SECURITY_INDEX, ConfigConstants.SECURITY_TYPE_USER, request.getUser().getUsername())
//                .setDoc(request.getUser().toSource()).request();

        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        this.client.update(updateRequest, new ActionListener<UpdateResponse>()
        {
            @Override
            public void onResponse(UpdateResponse updateResponse)
            {
                listener.onResponse(updateResponse.status() == RestStatus.OK);
            }

            @Override
            public void onFailure(Exception e)
            {
                listener.onFailure(e);
            }
        });
    }

    @Override
    public void postUser(PostUserRequest request, final ActionListener<Boolean> listener) throws InterruptedException, ExecutionException, UserNotFoundException
    {
        User exist = checkUserNotFound(request.getUser().getUsername());

        User from = request.getUser();

        if(!Strings.isNullOrEmpty(request.getUser().getPassword()))
            exist.setPassword(request.getUser().getPassword());

        if(request.getUser().getFullName()!=null)
            exist.setFullName(request.getUser().getFullName());

        if(request.getUser().getEmail()!=null)
            exist.setEmail(request.getUser().getEmail());

        if(request.getUser().getPrivileges()!=null)
            exist.setPrivileges(request.getUser().getPrivileges());

        if(request.getUser().getDesc()!=null)
            exist.setDesc(request.getUser().getDesc());


        IndexRequest indexRequest = this.client.prepareIndex()
                .setIndex(ConfigConstants.SECURITY_INDEX)
                .setType(ConfigConstants.SECURITY_DEFAULT_TYPE)
                .setId(exist.getUsername())
                .setSource(exist.toSource()).request();
//                this.client.prepareIndex(ConfigConstants.SECURITY_INDEX, ConfigConstants.SECURITY_TYPE_USER, exist.getUsername())
//                .setSource(exist.toSource()).request();

        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        this.client.index(indexRequest, new ActionListener<IndexResponse>()
        {
            @Override
            public void onResponse(IndexResponse indexResponse)
            {
                listener.onResponse(indexResponse.status() == RestStatus.OK);
            }

            @Override
            public void onFailure(Exception e)
            {
                listener.onFailure(e);
            }
        });
    }


    /**
     * 检查用户是否已经存在，如果存在，则抛出异常
     * @param username
     * @throws ExecutionException
     * @throws InterruptedException
     * @throws UserAlreadyExistsException
     */
    private void checkUserAlreadyExists(String username) throws ExecutionException, InterruptedException, UserAlreadyExistsException
    {
        User user = getUser(username);

        if(user!=User.NULL_USER)
            throw new UserAlreadyExistsException();
    }

    /**
     * 检查用户是否不存在，如果不存在，则抛出异常，如果存在，则返回
     * @param username
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     * @throws UserNotFoundException
     */
    private User checkUserNotFound(String username) throws ExecutionException, InterruptedException, UserNotFoundException
    {
        User user = getUser(username);

        if(user==User.NULL_USER)
            throw new UserNotFoundException();

        return user;
    }

    private boolean createIndex(String indexName) throws ExecutionException, InterruptedException
    {
        CreateIndexRequest request = this.client.admin().indices().prepareCreate(indexName).request();

        ActionFuture<CreateIndexResponse> response = this.client.admin().indices()
                .create(request);

        return response.get().isAcknowledged();
    }

    private boolean isIndexExists(String indexName) throws ExecutionException, InterruptedException
    {
        IndicesExistsRequest request = this.client.admin().indices()
                .prepareExists(indexName)
                .request();

        ActionFuture<IndicesExistsResponse> response = this.client.admin().indices()
                .exists(request);

        return response.get().isExists();
    }
}
