package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.PostPrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.PostPrivilegesResponse;
import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * 修改用户权限
 */
public class RestPostPrivilegesAction
    extends BaseRestHandler
{
    @Override
    public String getName()
    {
        return "rest-post-privileges-action";
    }

    @Inject
    public RestPostPrivilegesAction() {}

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, ConfigConstants.REST_PRIVILEGE_USERNAME));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String username = request.param("username");

        List<Privilege> toModify = Privilege.from(request.content());

        PostPrivilegesRequestBuilder requestBuilder = new SecurityClient(client)
                .preparePostPrivileges().setUsername(username).setPrivileges(toModify);

        return channel->requestBuilder.execute(new RestBuilderListener<PostPrivilegesResponse>(channel)
        {
            @Override
            public RestResponse buildResponse(PostPrivilegesResponse postPrivilegesResponse, XContentBuilder builder) throws Exception
            {
                return new BytesRestResponse(RestStatus.OK, postPrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
