package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class GetPrivilegesAction
    extends ActionType<GetPrivilegesResponse>
{
    private static final String NAME = "cluster:admin/girp/security/privileges/get";

    private static final GetPrivilegesAction INSTANCE=new GetPrivilegesAction();

    public static String actionName()
    {
        return NAME;
    }

    public static GetPrivilegesAction instance()
    {
        return INSTANCE;
    }

    protected GetPrivilegesAction()
    {
        super(NAME, GetPrivilegesResponse::new);
    }

//    @Override
//    public GetPrivilegesResponse newResponse()
//    {
//        return new GetPrivilegesResponse();
//    }
}
