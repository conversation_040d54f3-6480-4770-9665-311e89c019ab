package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class PostUserAction
    extends ActionType<PostUserResponse>
{
    private static final String NAME = "cluster:admin/security/user/post";

    private static final PostUserAction INSTANCE = new PostUserAction();

    private PostUserAction()
    {
        super(NAME, PostUserResponse::new);
    }

    public static String actionName()
    {
        return NAME;
    }

    public static PostUserAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public PostUserResponse newResponse()
//    {
//        return new PostUserResponse();
//    }
}
