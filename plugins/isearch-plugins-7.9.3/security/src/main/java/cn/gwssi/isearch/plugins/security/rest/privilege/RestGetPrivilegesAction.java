package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.GetPrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.GetPrivilegesResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取用户权限
 */
public class RestGetPrivilegesAction extends BaseRestHandler
{

    @Inject
    public RestGetPrivilegesAction() {}

    @Override
    public String getName()
    {
        return "rest-get-privileges-action";
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.GET, ConfigConstants.REST_PRIVILEGE_USERNAME));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String username = request.param("username");

        SecurityClient securityClient = new SecurityClient(client);

        GetPrivilegesRequestBuilder requestBuilder = securityClient.prepareGetPrivileges(username);

        return channel->requestBuilder.execute(new RestBuilderListener<GetPrivilegesResponse>(channel)
        {
            @Override
            public RestResponse buildResponse(GetPrivilegesResponse getPrivilegesResponse, XContentBuilder builder) throws Exception
            {
                return new BytesRestResponse(RestStatus.OK, getPrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
