package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

import java.util.List;

public class PutPrivilegesRequestBuilder
    extends ActionRequestBuilder<PutPrivilegesRequest,PutPrivilegesResponse>
{
    public PutPrivilegesRequestBuilder(ElasticsearchClient client)
    {
        super(client, PutPrivilegesAction.instance(), new PutPrivilegesRequest());
    }

    public PutPrivilegesRequestBuilder setUsername(String username)
    {
        this.request.setUsername(username);
        return this;
    }

    public PutPrivilegesRequestBuilder setPrivileges(List<Privilege> privileges)
    {
        this.request.setPrivileges(privileges);
        return this;
    }
}
