package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

import java.util.List;

public class PostPrivilegesRequestBuilder
        extends ActionRequestBuilder<PostPrivilegesRequest, PostPrivilegesResponse>
{
    public PostPrivilegesRequestBuilder(ElasticsearchClient client)
    {
        super(client, PostPrivilegesAction.instance(), new PostPrivilegesRequest());
    }

    public PostPrivilegesRequestBuilder setUsername(String username)
    {
        this.request.setUsername(username);
        return this;
    }

    public PostPrivilegesRequestBuilder setPrivileges(List<Privilege> privileges)
    {
        this.request.setPrivileges(privileges);
        return this;
    }
}
