package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

import java.io.IOException;
import java.util.List;

public class DeletePrivilegesRequestBuilder
    extends ActionRequestBuilder<DeletePrivilegesRequest,DeletePrivilegesResponse>
{
    public DeletePrivilegesRequestBuilder(ElasticsearchClient client) throws IOException {
        super(client, DeletePrivilegesAction.instance(), new DeletePrivilegesRequest());
    }

    public DeletePrivilegesRequestBuilder setUsername(String username)
    {
        this.request.setUsername(username);
        return this;
    }

    public DeletePrivilegesRequestBuilder setPrivileges(List<Privilege> privileges)
    {
        this.request.setPrivileges(privileges);
        return this;
    }
}
