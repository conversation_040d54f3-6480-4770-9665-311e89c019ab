package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

public class DeleteUserRequestBuilder
    extends ActionRequestBuilder<DeleteUserRequest,DeleteUserResponse>
{
    public DeleteUserRequestBuilder(ElasticsearchClient client)
    {
        super(client, DeleteUserAction.instance(), new DeleteUserRequest());
    }

    public DeleteUserRequestBuilder setUsername(String username)
    {
        this.request.setUsername(username);
        return this;
    }
}
