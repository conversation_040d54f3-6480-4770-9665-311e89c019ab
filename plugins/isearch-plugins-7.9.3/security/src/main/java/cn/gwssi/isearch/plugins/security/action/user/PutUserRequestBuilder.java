package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

public class PutUserRequestBuilder
    extends ActionRequestBuilder<PutUserRequest,PutUserResponse>
{
    public PutUserRequestBuilder(ElasticsearchClient client)
    {
        super(client, PutUserAction.instance(), new PutUserRequest());
    }

    public PutUserRequestBuilder user(User user)
    {
        this.request.setUser(user);
        return this;
    }
}
