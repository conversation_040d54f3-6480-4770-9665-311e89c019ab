package cn.gwssi.isearch.plugins.security.filter;

import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.authz.AuthzService;
import org.elasticsearch.rest.*;

public class SecurityRestFilter
{
    private AuthcService authcService;

    private AuthzService authzService;

    public SecurityRestFilter(AuthcService authcService,AuthzService authzService)
    {
        this.authcService = authcService;

        this.authzService = authzService;
    }

    public RestHandler wrap(RestHandler original)
    {
        return (request, channel, client) ->
        {
            if (SecurityRestFilter.this.authcAndAuthz(request, channel))
            {
                original.handleRequest(request, channel, client);
            }
        };
    }

    private boolean authcAndAuthz(RestRequest request, RestChannel channel)
    {
        if(request.uri().contains("_cluster/health"))
            return true;

        try
        {
            authcService.authenticate(request);
        }
        catch (Exception e)
        {
            channel.sendResponse(new BytesRestResponse(RestStatus.FORBIDDEN,e.getMessage()));
            return false;
        }

        if (authzService.authorize(request))
        {
            return true;
        }
        else
            channel.sendResponse(new BytesRestResponse(RestStatus.UNAUTHORIZED, "has no authorization"));

        return false;
    }
}
