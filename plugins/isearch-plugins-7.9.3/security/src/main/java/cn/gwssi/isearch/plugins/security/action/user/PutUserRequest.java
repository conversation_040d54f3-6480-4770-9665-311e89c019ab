package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.action.ValidateActions;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;

public class PutUserRequest extends ActionRequest
{

    public PutUserRequest() {}

    public PutUserRequest(StreamInput in) throws IOException {
        super(in);

        this.user = User.readFrom(in);
    }

    private User user=new User();

    public User getUser()
    {
        return user;
    }

    public PutUserRequest setUser(User user)
    {
        this.user = user;
        return this;
    }

    @Override
    public ActionRequestValidationException validate()
    {
        if(this.user==null|| Strings.isNullOrEmpty(this.user.getUsername()))
            return ValidateActions.addValidationError("username is missing", null);

        if(Strings.isNullOrEmpty(this.user.getPassword()))
            return ValidateActions.addValidationError("password is missing", null);

        return null;
    }

//    @Override
//    public void readFrom(StreamInput in) throws IOException
//    {
//        super.readFrom(in);
//        this.user = User.readFrom(in);
//    }

    @Override
    public void writeTo(StreamOutput out) throws IOException
    {
        super.writeTo(out);
        User.writeTo(this.user,out);
    }
}
