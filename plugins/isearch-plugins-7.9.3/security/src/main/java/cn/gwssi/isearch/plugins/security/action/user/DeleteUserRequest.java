package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.action.ValidateActions;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;

public class DeleteUserRequest extends ActionRequest
{

    public DeleteUserRequest() {}

    public DeleteUserRequest(StreamInput in) throws IOException {
        super(in);
        setUsername(in.readString());
    }

    private String username;

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    @Override
    public ActionRequestValidationException validate()
    {
        return getUsername() == null ?
                ValidateActions.addValidationError("username is missing", null)
                : null;
    }

//    @Override
//    public void readFrom(StreamInput in) throws IOException
//    {
//        super.readFrom(in);
//        setUsername(in.readString());
//    }

    @Override
    public void writeTo(StreamOutput out) throws IOException
    {
        super.writeTo(out);
        out.writeString(getUsername());
    }
}
