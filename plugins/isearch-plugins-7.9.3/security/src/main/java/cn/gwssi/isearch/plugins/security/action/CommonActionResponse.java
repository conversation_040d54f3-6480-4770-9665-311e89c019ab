package cn.gwssi.isearch.plugins.security.action;

import org.elasticsearch.action.ActionResponse;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;

public abstract class CommonActionResponse<T extends CommonActionResponse<T>>
        extends ActionResponse implements ToXContent
{
    protected Status status= Status.SUCCESS;

    public CommonActionResponse() {}

    public CommonActionResponse(StreamInput in) throws IOException {
        super(in);
        this.status = Status.valueOf(in.readString());
        this.msg = in.readOptionalString();
    }

    protected String msg;

    public Status getStatus()
    {
        return status;
    }

    public T setStatus(Status status)
    {
        this.status = status;
        return (T) this;
    }

    public String getMsg()
    {
        return msg;
    }

    public T setMsg(String msg)
    {
        this.msg = msg;
        return (T) this;
    }

    protected abstract void writeResponseTo(XContentBuilder builder) throws IOException;

    @Override
    public XContentBuilder toXContent(XContentBuilder builder, Params params) throws IOException
    {
        builder.startObject();

        builder.field("status", this.status);

        if(this.msg!=null)
            builder.field("msg", this.msg);

        writeResponseTo(builder);

        builder.endObject();

        return builder;
    }

//    @Override
//    public void readFrom(StreamInput in) throws IOException
//    {
//        super.readFrom(in);
//        this.status = Status.valueOf(in.readString());
//        this.msg = in.readOptionalString();
//    }

    @Override
    public void writeTo(StreamOutput out) throws IOException
    {
//        super.writeTo(out);
        out.writeString(this.status.toString());
        out.writeOptionalString(this.msg);
    }

    public enum Status
    {
        SUCCESS,
        ERROR
    }
}
