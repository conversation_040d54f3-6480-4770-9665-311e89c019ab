package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.action.ValidateActions;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;

public class GetPrivilegesRequest
    extends ActionRequest
{

    public GetPrivilegesRequest() {}

    public GetPrivilegesRequest(StreamInput in) throws IOException {
        super(in);
        this.username = in.readString();
    }

    private String username;

    public String getUsername()
    {
        return username;
    }

    public GetPrivilegesRequest setUsername(String username)
    {
        this.username = username;
        return this;
    }

    @Override
    public ActionRequestValidationException validate()
    {
        if(Strings.isNullOrEmpty(this.username))
            return ValidateActions.addValidationError("username cannot be null or empty while getting privileges", null);

        return null;
    }

//    @Override
//    public void readFrom(StreamInput in) throws IOException
//    {
//        super.readFrom(in);
//
//        this.username = in.readString();
//    }

    @Override
    public void writeTo(StreamOutput out) throws IOException
    {
        super.writeTo(out);

        out.writeString(this.username);
    }
}
