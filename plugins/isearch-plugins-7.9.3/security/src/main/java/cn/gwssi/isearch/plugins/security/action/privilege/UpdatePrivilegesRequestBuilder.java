package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

import java.util.List;

public class UpdatePrivilegesRequestBuilder
        extends ActionRequestBuilder<UpdatePrivilegesRequest, UpdatePrivilegesResponse>
{
    public UpdatePrivilegesRequestBuilder(ElasticsearchClient client)
    {
        super(client, UpdatePrivilegesAction.instance(), new UpdatePrivilegesRequest());
    }

    public UpdatePrivilegesRequestBuilder setUsername(String username)
    {
        this.request().setUsername(username);
        return this;
    }

    public UpdatePrivilegesRequestBuilder setPrivileges(List<Privilege> privileges)
    {
        this.request().setPrivileges(privileges);
        return this;
    }
}
