package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class UpdatePrivilegesAction
    extends ActionType<UpdatePrivilegesResponse>
{
    private static final String NAME = "cluster:admin/girp/security/privilege/update";

    private static final UpdatePrivilegesAction INSTANCE = new UpdatePrivilegesAction();

    private UpdatePrivilegesAction()
    {
        super(NAME, UpdatePrivilegesResponse::new);
    }

    public static String actionName()
    {
        return NAME;
    }

    public static UpdatePrivilegesAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public UpdatePrivilegesResponse newResponse()
//    {
//        return new UpdatePrivilegesResponse();
//    }
}
