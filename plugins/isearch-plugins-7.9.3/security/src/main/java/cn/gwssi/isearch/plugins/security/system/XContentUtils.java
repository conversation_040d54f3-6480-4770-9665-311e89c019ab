package cn.gwssi.isearch.plugins.security.system;

import org.elasticsearch.ElasticsearchParseException;
import org.elasticsearch.common.xcontent.XContentParser;

import java.io.IOException;

public class XContentUtils
{
    private XContentUtils()
    {

    }

    public static void verifyObject(XContentParser parser) throws IOException
    {
        if(parser.currentToken()!= XContentParser.Token.START_OBJECT)
        {
            XContentParser.Token token=parser.nextToken();

            if(token!= XContentParser.Token.START_OBJECT)
                throw new ElasticsearchParseException("expect an object , but found token [{}]", new Object[]{parser.currentToken()});
        }
    }
}
