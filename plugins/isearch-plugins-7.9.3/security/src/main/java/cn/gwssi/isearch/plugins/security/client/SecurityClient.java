package cn.gwssi.isearch.plugins.security.client;

import cn.gwssi.isearch.plugins.security.action.privilege.*;
import cn.gwssi.isearch.plugins.security.action.user.DeleteUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.GetUsersRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.PutUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.client.ElasticsearchClient;

import java.io.IOException;

public class SecurityClient
{
    private final ElasticsearchClient client;

    public SecurityClient(ElasticsearchClient client)
    {
        this.client=client;
    }

    public DeleteUserRequestBuilder prepareDeleteUser(String username)
    {
        return new DeleteUserRequestBuilder(this.client).setUsername(username);
    }

    public GetUsersRequestBuilder prepareGetUsers(String... usernames)
    {
        return new GetUsersRequestBuilder(this.client).setUsernames(usernames);
    }

    public PutUserRequestBuilder preparePutUser(User user)
    {
        return new PutUserRequestBuilder(this.client).user(user);
    }

    public PostUserRequestBuilder preparePostUser()
    {
        return new PostUserRequestBuilder(this.client);
    }

    public GetPrivilegesRequestBuilder prepareGetPrivileges(String username)
    {
        return new GetPrivilegesRequestBuilder(this.client).setUsername(username);
    }

    public UpdatePrivilegesRequestBuilder prepareUpdatePrivileges()
    {
        return new UpdatePrivilegesRequestBuilder(this.client);
    }

    public DeletePrivilegesRequestBuilder prepareDeletePrivileges() throws IOException {
        return new DeletePrivilegesRequestBuilder(this.client);
    }

    public PutPrivilegesRequestBuilder preparePutPrivileges()
    {
        return new PutPrivilegesRequestBuilder(this.client);
    }

    public PostPrivilegesRequestBuilder preparePostPrivileges()
    {
        return new PostPrivilegesRequestBuilder(this.client);
    }
}
