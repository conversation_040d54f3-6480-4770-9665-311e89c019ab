package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class PutPrivilegesAction
    extends ActionType<PutPrivilegesResponse>
{
    private static final String NAME = "cluster:admin/security/privilege/put";

    private static final PutPrivilegesAction INSTANCE = new PutPrivilegesAction();

    private PutPrivilegesAction()
    {
        super(NAME, PutPrivilegesResponse::new);
    }

    public static String actionName()
    {
        return NAME;
    }

    public static PutPrivilegesAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public PutPrivilegesResponse newResponse()
//    {
//        return new PutPrivilegesResponse();
//    }
}
