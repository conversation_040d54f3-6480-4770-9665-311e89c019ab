package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportDeleteUserAction extends HandledTransportAction<DeleteUserRequest, DeleteUserResponse>
{
    private UserService userService;

    @Inject
    public TransportDeleteUserAction(UserService userService, ActionFilters actionFilters, TransportService transportService)
    {
        super(DeleteUserAction.actionName(), transportService, actionFilters, DeleteUserRequest::new);//DeleteUserRequest.class
        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, DeleteUserRequest request, ActionListener<DeleteUserResponse> listener)
    {
        userService.deleteUser(request, new ActionListener<Boolean>()
        {
            @Override
            public void onResponse(Boolean aBoolean)
            {
                DeleteUserResponse response = new DeleteUserResponse();

                if(!aBoolean)
                    response.setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found");

                listener.onResponse(response);
            }

            @Override
            public void onFailure(Exception e)
            {
                listener.onFailure(e);
            }
        });
    }
}
