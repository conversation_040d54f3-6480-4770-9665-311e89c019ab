package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportGetPrivilegesAction
    extends HandledTransportAction<GetPrivilegesRequest,GetPrivilegesResponse>
{
    private UserService userService;

    @Inject
    public TransportGetPrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters)
    {
        super(GetPrivilegesAction.actionName(), transportService, actionFilters, GetPrivilegesRequest::new);
        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, GetPrivilegesRequest request, ActionListener<GetPrivilegesResponse> listener)
    {
        try
        {
            User user = userService.getUser(request.getUsername());

            if(user==User.NULL_USER)
                listener.onResponse(new GetPrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"));
            else
                listener.onResponse(new GetPrivilegesResponse().setPrivileges(user.getPrivileges()));
        }
        catch (Exception e)
        {
            listener.onFailure(e);
        }
    }
}
