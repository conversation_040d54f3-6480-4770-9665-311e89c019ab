package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class PostPrivilegesAction
    extends ActionType<PostPrivilegesResponse>
{
    private static final String NAME = "cluster:admin/security/privilege/post";

    private static final PostPrivilegesAction INSTANCE = new PostPrivilegesAction();

    private PostPrivilegesAction()
    {
        super(NAME, PostPrivilegesResponse::new);
    }

    public static String actionName()
    {
        return NAME;
    }

    public static PostPrivilegesAction instance()
    {
        return INSTANCE;
    }

//    @Override
//    public PostPrivilegesResponse newResponse()
//    {
//        return new PostPrivilegesResponse();
//    }
}
