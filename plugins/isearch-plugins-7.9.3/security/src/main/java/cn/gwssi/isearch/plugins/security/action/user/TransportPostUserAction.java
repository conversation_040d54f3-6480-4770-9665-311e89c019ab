package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.UserNotFoundException;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportPostUserAction
    extends HandledTransportAction<PostUserRequest,PostUserResponse>
{
    private UserService userService;

    @Inject
    public TransportPostUserAction(UserService userService, TransportService transportService, ActionFilters actionFilters)
    {
        super(PostUserAction.actionName(), transportService, actionFilters, PostUserRequest::new);//PostUserRequest.class
        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, PostUserRequest request, final ActionListener<PostUserResponse> listener)
    {
        try
        {
            userService.postUser(request, new ActionListener<Boolean>()
            {
                @Override
                public void onResponse(Boolean aBoolean)
                {
                    listener.onResponse(new PostUserResponse().setStatus(aBoolean? CommonActionResponse.Status.SUCCESS: CommonActionResponse.Status.ERROR));
                }

                @Override
                public void onFailure(Exception e)
                {
                    listener.onFailure(e);
                }
            });
        }
        catch (UserNotFoundException e)
        {
            listener.onResponse(new PostUserResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"));
        }
        catch (Exception e)
        {
            listener.onFailure(e);
        }
    }
}
