package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequest;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportUpdatePrivilegesAction
    extends HandledTransportAction<UpdatePrivilegesRequest,UpdatePrivilegesResponse>
{
    private UserService userService;

    @Inject
    public TransportUpdatePrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters)
    {
        super(UpdatePrivilegesAction.actionName(), transportService, actionFilters, UpdatePrivilegesRequest::new);//UpdatePrivilegesRequest.class
        this.userService = userService;
    }

    @Override
    protected void doExecute(Task task, UpdatePrivilegesRequest request, final ActionListener<UpdatePrivilegesResponse> listener)
    {
        User user=null;

        try
        {
            user = userService.getUser(request.getUsername());

            if(user==User.NULL_USER)
            {
                listener.onResponse(new UpdatePrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"));
                return;
            }
        }
        catch (Exception ex)
        {
            listener.onFailure(ex);
        }

        user.setPrivileges(request.getPrivileges());


        try
        {
            userService.postUser(new PostUserRequest().setUser(user), new ActionListener<Boolean>()
            {
                @Override
                public void onResponse(Boolean aBoolean)
                {
                    if(aBoolean)
                        listener.onResponse(new UpdatePrivilegesResponse());
                    else
                        listener.onResponse(new UpdatePrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR));
                }

                @Override
                public void onFailure(Exception e)
                {
                    listener.onFailure(e);
                }
            });
        }
        catch (Exception ex)
        {
            listener.onFailure(ex);
        }

    }
}
