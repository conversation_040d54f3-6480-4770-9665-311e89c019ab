package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.action.ValidateActions;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;
import java.util.List;

public abstract class AbstractModifyPrivilegesRequest
    extends ActionRequest
{
    protected String username;

    protected List<Privilege> privileges;

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public List<Privilege> getPrivileges()
    {
        return privileges;
    }

    public void setPrivileges(List<Privilege> privileges)
    {
        this.privileges = privileges;
    }

    public AbstractModifyPrivilegesRequest() {}

    public AbstractModifyPrivilegesRequest(StreamInput in) throws IOException {
        super(in);

        this.username = in.readString();
        this.privileges = Privilege.listFrom(in);
    }

    @Override
    public ActionRequestValidationException validate()
    {
        return this.username == null
                ? ValidateActions.addValidationError("username is missing", null)
                : null;
    }

//    @Override
//    public void readFrom(StreamInput in) throws IOException
//    {
//        super.readFrom(in);
//
//        this.username = in.readString();
//        this.privileges = Privilege.listFrom(in);
//    }

    @Override
    public void writeTo(StreamOutput out) throws IOException
    {
        super.writeTo(out);

        out.writeString(this.username);
        Privilege.listTo(this.privileges,out);
    }
}
