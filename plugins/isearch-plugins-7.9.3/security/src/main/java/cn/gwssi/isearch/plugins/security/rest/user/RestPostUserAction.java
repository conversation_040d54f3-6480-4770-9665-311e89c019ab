package cn.gwssi.isearch.plugins.security.rest.user;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.PostUserResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RestPostUserAction
        extends BaseRestHandler
{
    @Override
    public String getName()
    {
        return "rest-post-user-action";
    }

    public RestPostUserAction() {}

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, ConfigConstants.REST_USER_USERNAME));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String username = request.param("username");

        User user = User.fromRequest(request.content());

        user.setUsername(username);

        SecurityClient securityClient = new SecurityClient(client);

        PostUserRequestBuilder postUserRequestBuilder = securityClient.preparePostUser().setUser(user);

        return channel->postUserRequestBuilder.execute(new RestBuilderListener<PostUserResponse>(channel)
        {
            @Override
            public RestResponse buildResponse(PostUserResponse postUserResponse, XContentBuilder builder) throws Exception
            {
                return new BytesRestResponse(RestStatus.OK, postUserResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
