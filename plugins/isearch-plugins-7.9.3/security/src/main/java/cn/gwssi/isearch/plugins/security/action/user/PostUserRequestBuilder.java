package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

public class PostUserRequestBuilder
    extends ActionRequestBuilder<PostUserRequest,PostUserResponse>
{
    public PostUserRequestBuilder(ElasticsearchClient client)
    {
        super(client, PostUserAction.instance(), new PostUserRequest());
    }

    public PostUserRequestBuilder setUser(User user)
    {
        this.request.setUser(user);
        return this;
    }
}
