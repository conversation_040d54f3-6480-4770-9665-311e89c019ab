package cn.gwssi.isearch.plugins.security;

import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.authc.AuthcServiceImpl;
import cn.gwssi.isearch.plugins.security.authz.AuthzService;
import cn.gwssi.isearch.plugins.security.authz.AuthzServiceImpl;
import cn.gwssi.isearch.plugins.security.system.NativeUserService;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.common.inject.AbstractModule;

public class ServiceModule extends AbstractModule
{
    @Override
    protected void configure()
    {
        bind(UserService.class).to(NativeUserService.class).asEagerSingleton();
        bind(AuthcService.class).to(AuthcServiceImpl.class);
        bind(AuthzService.class).to(AuthzServiceImpl.class);
    }
}
