package cn.gwssi.isearch.plugins.security.system;

import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.common.bytes.BytesReference;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.*;

import java.io.IOException;
import java.util.*;

public class User implements ToXContent
{
    public static final User NULL_USER = new User();

    public static final User SUPER_ADMIN = new User();

    private String username;
    private String password;
    private String fullName;
    private String email;
    private String desc;
    private List<Privilege> privileges;

    public User() {}

    public String getUsername()
    {
        return this.username;
    }

    public User setUsername(String username)
    {
        this.username = username;
        return this;
    }

    public String getPassword()
    {
        return this.password;
    }

    public User setPassword(String password)
    {
        this.password = password;
        return this;
    }

    public String getFullName()
    {
        return this.fullName;
    }

    public User setFullName(String fullName)
    {
        this.fullName = fullName;
        return this;
    }

    public String getEmail()
    {
        return this.email;
    }

    public User setEmail(String email)
    {
        this.email = email;
        return this;
    }

    public String getDesc()
    {
        return desc;
    }

    public User setDesc(String desc)
    {
        this.desc = desc;
        return this;
    }

    public List<Privilege> getPrivileges()
    {
        return this.privileges;
    }

    public User setPrivileges(List<Privilege> privileges)
    {
        this.privileges = privileges;
        return this;
    }

    @Override
    public XContentBuilder toXContent(XContentBuilder builder, Params params) throws IOException
    {
        return builder.value(this.toResponse());
    }

    public static User readFrom(StreamInput in) throws IOException
    {
        User user = new User()
                .setUsername(in.readString())
                .setPassword(in.readString())
                .setFullName(in.readOptionalString())
                .setEmail(in.readOptionalString())
                .setDesc(in.readOptionalString());

        if(in.readBoolean())
        {
            Map<String, Object> privileges = in.readMap();

            Iterator<Map.Entry<String,Object>> iterator=privileges.entrySet().iterator();

            List<Privilege> toAdd = new ArrayList<>();

            while (iterator.hasNext())
            {
                Map.Entry<String, Object> entry = iterator.next();

                toAdd.add(new Privilege(entry.getKey(), Privilege.OperationType.from(String.valueOf(entry.getValue()))));
            }

            user.setPrivileges(toAdd);
        }

        return user;
    }

    public static void writeTo(User user, StreamOutput out) throws IOException
    {
        out.writeString(user.getUsername());

        out.writeString(user.getPassword());

        out.writeOptionalString(user.getFullName());

        out.writeOptionalString(user.getEmail());

        out.writeOptionalString(user.getDesc());

        if(user.getPrivileges()==null||user.getPrivileges().size()==0)
            out.writeBoolean(false);
        else
        {
            out.writeBoolean(true);

            Map<String,Object> privileges=new LinkedHashMap<>();

            for(Privilege p : user.getPrivileges())
                privileges.put(p.getResource(), p.getOperation().toString());

            out.writeMap(privileges);
        }
    }

    public Map<String, Object> toResponse()
    {
        Map<String,Object> source=new LinkedHashMap<>();

        if(this.username!=null)
            source.put(Field.USERNAME, this.username);

        if(this.password!=null)
            source.put(Field.PASSWORD, this.password);

        if(this.fullName!=null)
            source.put(Field.FULL_NAME, this.fullName);

        if(this.email!=null)
            source.put(Field.EMAIL, this.email);

        if(this.desc!=null)
            source.put(Field.DESC, this.desc);

        if (this.privileges != null)
        {
            Map<String, String> ps = new LinkedHashMap<>();

            for(Privilege p : this.privileges)
            {
                ps.put(p.getResource(), p.getOperation().toString());
            }

            source.put(Field.PRIVILEGES, ps);
        }

        return source;
    }

    public Map<String, Object> toSource()
    {
        Map<String,Object> source=new LinkedHashMap<>();

        if(this.username!=null)
            source.put(Field.USERNAME, this.username);

        if(this.password!=null)
            source.put(Field.PASSWORD, this.password);

        if(this.fullName!=null)
            source.put(Field.FULL_NAME, this.fullName);

        if(this.email!=null)
            source.put(Field.EMAIL, this.email);

        if(this.desc!=null)
            source.put(Field.DESC, this.desc);

        if (this.privileges != null)
        {
            List<Map<String, String>> list = new ArrayList<>();

            //Map<String, String> ps = new LinkedHashMap<>();

            for(Privilege p : this.privileges)
            {
                //ps.put(p.getResource(), p.getOperation().toString());
                Map<String, String> map = new LinkedHashMap<>();
                map.put(Field.SOURCE_NAME, p.getResource());
                map.put(Field.PRIVILEGE_VALUE, p.getOperation().toString());
                list.add(map);
            }

            source.put(Field.PRIVILEGES, list);
        }

        return source;
    }

    /**
     * 获取User对象
     * @param source Map<String,Object>结构
     * @return
     */
    public static User fromSource(Map<String,Object> source)
    {
        User user = new User();

        if(source.get(Field.USERNAME)!=null)
            user.setUsername(String.valueOf(source.get(Field.USERNAME)));

        if(source.get(Field.PASSWORD)!=null)
            user.setPassword(String.valueOf(source.get(Field.PASSWORD)));

        if(source.get(Field.FULL_NAME)!=null)
            user.setFullName(String.valueOf(source.get(Field.FULL_NAME)));

        if(source.get(Field.EMAIL)!=null)
            user.setEmail(String.valueOf(source.get(Field.EMAIL)));

        if(source.get(Field.DESC)!=null)
            user.setDesc(String.valueOf(source.get(Field.DESC)));

        if (source.get(Field.PRIVILEGES) != null && source.get(Field.PRIVILEGES) instanceof Collection)
        {
//            Map<String, Object> privileges = (Map<String, Object>) (source.get(Field.PRIVILEGES));
//
//            Iterator<Map.Entry<String, Object>> iterator = privileges.entrySet().iterator();
//
//            List<Privilege> toAdd = new ArrayList<>();
//
//            while (iterator.hasNext())
//            {
//                Map.Entry<String, Object> entry = iterator.next();
//
//                Privilege p = new Privilege(entry.getKey(), Privilege.OperationType.from(String.valueOf(entry.getValue())));
//
//                toAdd.add(p);
//            }
//
//            user.setPrivileges(toAdd);

            List<Map<String, Object>> list = (List<Map<String, Object>>) (source.get(Field.PRIVILEGES));

            List<Privilege> toAdd = new ArrayList<>();

            if (list != null && list.size() > 0)
            {
                for(int i=0;i<list.size();i++)
                {
                    Map<String, Object> map = list.get(i);

                    Privilege p = new Privilege(map.get(Field.SOURCE_NAME).toString(), Privilege.OperationType.from(String.valueOf(map.get(Field.PRIVILEGE_VALUE))));

                    toAdd.add(p);
                }
            }

            user.setPrivileges(toAdd);
        }

        return user;
    }

    public static User fromRequest(BytesReference source) throws IOException
    {
        User user = new User();

        XContentType contentType = XContentType.JSON;
        XContentParser parser = contentType.xContent().createParser(
                NamedXContentRegistry.EMPTY, DeprecationHandler.THROW_UNSUPPORTED_OPERATION
                , source.toBytesRef().bytes
        );

        if(parser.currentToken()!= XContentParser.Token.START_OBJECT)
        {
            if(parser.nextToken()!= XContentParser.Token.START_OBJECT)
                throw new ElasticsearchException("expect an object , but found a value");
        }

        String fieldName=null;

        XContentParser.Token token;

        while ((token = parser.nextToken()) != null)
        {
            if(token== XContentParser.Token.FIELD_NAME)
            {
                fieldName = parser.currentName();
            }
            else if(token== XContentParser.Token.START_OBJECT)
            {
                if(fieldName.equalsIgnoreCase(Field.PRIVILEGES))
                {
                    Map<String, Object> map = parser.map();

                    if(map!=null)
                    {
                        List<Privilege> privileges = new ArrayList<>();

                        Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();

                        while (iterator.hasNext())
                        {
                            Map.Entry<String, Object> entry = iterator.next();

                            privileges.add(new Privilege(entry.getKey(), Privilege.OperationType.from(String.valueOf(entry.getValue()))));
                        }

                        user.setPrivileges(privileges);
                    }
                }
            }
            else if(token== XContentParser.Token.VALUE_STRING)
            {
                if(fieldName.equalsIgnoreCase(Field.USERNAME))
                    user.setUsername(parser.text());

                else if(fieldName.equalsIgnoreCase(Field.PASSWORD))
                    user.setPassword(parser.text());

                else if(fieldName.equalsIgnoreCase(Field.FULL_NAME))
                    user.setFullName(parser.text());

                else if(fieldName.equalsIgnoreCase(Field.EMAIL))
                    user.setEmail(parser.text());

                else if(fieldName.equalsIgnoreCase(Field.DESC))
                    user.setDesc(parser.text());
            }
            else
            {}

        }

        return user;
    }


    public Privilege.OperationType operationInResource(String resource)
    {
        if (this.privileges != null)
        {
            for(Privilege p : this.privileges)
            {
                if(p.getResource().equals(resource))
                    return p.getOperation();
            }
        }

        return Privilege.OperationType.NONE;
    }


    public interface Field
    {
        String USERNAME = "username";
        String PASSWORD = "password";
        String FULL_NAME = "fullName";
        String EMAIL = "email";
        String PRIVILEGES = "privileges";
        String DESC = "desc";
        String SOURCE_NAME = "sourceName";
        String PRIVILEGE_VALUE = "privilege";
    }
}
