package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

public class GetUsersRequestBuilder
    extends ActionRequestBuilder<GetUsersRequest,GetUsersResponse>
{
    public GetUsersRequestBuilder(ElasticsearchClient client)
    {
        super(client, GetUsersAction.instance(), new GetUsersRequest());
    }

    public GetUsersRequestBuilder setUsernames(String[] usernames)
    {
        this.request.setUsernames(usernames);
        return this;
    }

    public GetUsersRequestBuilder setFrom(int from)
    {
        this.request.setFrom(from);
        return this;
    }

    public GetUsersRequestBuilder setSize(int size)
    {
        this.request.setSize(size);
        return this;
    }
}
