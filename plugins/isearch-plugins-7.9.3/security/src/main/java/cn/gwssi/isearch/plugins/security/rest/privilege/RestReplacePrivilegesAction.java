package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import cn.gwssi.isearch.plugins.security.action.privilege.UpdatePrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.UpdatePrivilegesResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 整体替换用户权限
 * 针对同时存在增、删、改不同类型的批量操作
 */
public class RestReplacePrivilegesAction
    extends BaseRestHandler
{
    private UserService userService;

    @Override
    public String getName()
    {
        return "rest-replace-privileges-action";
    }

    @Inject
    public RestReplacePrivilegesAction(UserService userService)
    {
        this.userService = userService;
    }

    @Override
    public List<Route> routes() {
        List<Route> routes = new ArrayList<>();
        routes.add(new RestHandler.Route(RestRequest.Method.POST, "/_security/privilege/replace/{username}"));

        return routes;
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException
    {
        String username = request.param("username");

        List<Privilege> toReplace = Privilege.from(request.content());

        UpdatePrivilegesRequestBuilder requestBuilder = new SecurityClient(client)
                .prepareUpdatePrivileges()
                .setUsername(username)
                .setPrivileges(toReplace);

        return channel->requestBuilder.execute(new RestBuilderListener<UpdatePrivilegesResponse>(channel)
        {
            @Override
            public RestResponse buildResponse(UpdatePrivilegesResponse updatePrivilegesResponse, XContentBuilder builder) throws Exception
            {
                return new BytesRestResponse(RestStatus.OK, updatePrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
