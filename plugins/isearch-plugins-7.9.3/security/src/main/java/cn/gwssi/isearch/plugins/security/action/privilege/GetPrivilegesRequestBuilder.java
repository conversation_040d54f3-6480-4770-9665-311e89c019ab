package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

public class GetPrivilegesRequestBuilder
    extends ActionRequestBuilder<GetPrivilegesRequest,GetPrivilegesResponse>
{
    public GetPrivilegesRequestBuilder(ElasticsearchClient client)
    {
        super(client, GetPrivilegesAction.instance(), new GetPrivilegesRequest());
    }

    public GetPrivilegesRequestBuilder setUsername(String username)
    {
        this.request().setUsername(username);

        return this;
    }
}
