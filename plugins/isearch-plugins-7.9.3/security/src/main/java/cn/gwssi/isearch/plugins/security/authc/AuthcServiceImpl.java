package cn.gwssi.isearch.plugins.security.authc;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.rest.RestRequest;

public class AuthcServiceImpl implements AuthcService
{
    private UserService userService;

    @Inject
    public AuthcServiceImpl(UserService userService)
    {
        this.userService = userService;
    }

    @Override
    public User authenticate(RestRequest request) throws AuthcException
    {
        String username = request.header("un");

        if(username==null)
            username = request.param("un");


        String password = request.header("pw");

        if(password==null)
            password = request.param("pw");


        if(Strings.isNullOrEmpty(username))
            throw new AuthcException("username is missing");

        if(Strings.isNullOrEmpty(password))
            throw new AuthcException("password is missing");

        if (username.equals(ConfigConstants.superadmin_name)
                && password.equals(ConfigConstants.superadmin_password))
        {
            return User.SUPER_ADMIN;
        }

        User user;

        try
        {
            user = userService.getUser(username);
        }
        catch (Exception e)
        {
            throw new AuthcException("user not found");
        }

        if(user==User.NULL_USER)
            throw new AuthcException("user not found");

        if(!user.getPassword().equals(password))
            throw new AuthcException("password is wrong");

        return user;
    }
}
