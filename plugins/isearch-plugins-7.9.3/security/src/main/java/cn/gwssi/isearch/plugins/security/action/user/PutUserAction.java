package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class PutUserAction
    extends ActionType<PutUserResponse>
{
    private static final String NAME = "cluster:admin/girp/security/user/put";

    private static final PutUserAction INSTANCE=new PutUserAction();

    protected PutUserAction()
    {
        super(NAME, PutUserResponse::new);
    }

    public static PutUserAction instance()
    {
        return INSTANCE;
    }

    public static String actionName()
    {
        return NAME;
    }

//    @Override
//    public PutUserResponse newResponse()
//    {
//        return new PutUserResponse();
//    }
}
