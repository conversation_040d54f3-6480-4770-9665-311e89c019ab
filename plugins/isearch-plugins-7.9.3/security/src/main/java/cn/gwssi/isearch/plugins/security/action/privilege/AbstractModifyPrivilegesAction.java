package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.io.stream.Writeable;
import org.elasticsearch.transport.TransportService;

import java.util.function.Supplier;

public abstract class AbstractModifyPrivilegesAction<Request extends AbstractModifyPrivilegesRequest,Response extends CommonActionResponse<Response>>
    extends HandledTransportAction<Request,Response>
{
    protected UserService userService;

    protected AbstractModifyPrivilegesAction(UserService userService, String actionName, TransportService transportService, ActionFilters actionFilters, Writeable.Reader<Request> request)
    {
        super(actionName, transportService, actionFilters, request);

        this.userService = userService;
    }



    protected CheckUserResult checkUser(Request request,Response errorResponse, ActionListener<Response> listener)
    {
        User user;

        try
        {
            user = userService.getUser(request.getUsername());

            if(user==User.NULL_USER)
            {
                listener.onResponse(errorResponse);

                return new CheckUserResult().setExist(false);
            }
        }
        catch (Exception e)
        {
            listener.onFailure(e);
            return new CheckUserResult().setExist(false);
        }

        return new CheckUserResult().setExist(true).setUser(user);
    }

    public static class CheckUserResult
    {
        private boolean exist;

        private User user;

        public boolean isExist()
        {
            return exist;
        }

        public CheckUserResult setExist(boolean exist)
        {
            this.exist = exist;
            return this;
        }

        public User getUser()
        {
            return user;
        }

        public CheckUserResult setUser(User user)
        {
            this.user = user;
            return this;
        }
    }
}
