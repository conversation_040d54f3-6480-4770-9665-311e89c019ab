package cn.gwssi.isearch.plugins.security;

import java.util.HashSet;
import java.util.Set;

public final class ConfigConstants {
    public static boolean enabled = true;

    public static String superadmin_name = "gwssi";

    public static String superadmin_password = "gwssi_240329_!@_ly";

    public static Set<String> white_lists = new HashSet<>();


    public static final String SECURITY_INDEX = ".security";
    public static final String SECURITY_DEFAULT_TYPE = "_doc";

    public static final String SECURITY_TYPE_USER = "user";


    public static final String CONFIG_ROOT = "girp.security";

    public static final String PLUGIN_ENABLED = CONFIG_ROOT + ".enabled";

    public static final String SUPERADMIN = CONFIG_ROOT + ".superadmin";

    public static final String SUPERADMIN_NAME = SUPERADMIN + ".name";

    public static final String SUPERADMIN_PASSWORD = SUPERADMIN + ".password";


    public static final String HOSTS = CONFIG_ROOT + ".hosts";

    public static final String WHITE_LIST = HOSTS + ".whitelist";


    public static final String REST_ROOT = "/_security";

    public static final String REST_USER = REST_ROOT + "/user";

    public static final String REST_USER_USERNAME = REST_USER + "/{username}";

    public static final String REST_USER_USERNAMES = REST_USER + "/{usernames}";

    public static final String REST_PRIVILEGE = REST_ROOT + "/privilege";

    public static final String REST_PRIVILEGE_USERNAME = REST_PRIVILEGE + "/{username}";
}
