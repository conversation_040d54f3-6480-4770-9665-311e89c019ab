package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;

public class PostPrivilegesResponse
    extends CommonActionResponse<PostPrivilegesResponse>
{
    public PostPrivilegesResponse() {
        super();
    }

    public PostPrivilegesResponse(StreamInput in) throws IOException {
        super(in);
    }

    @Override
    protected void writeResponseTo(XContentBuilder builder) throws IOException
    {

    }
}
