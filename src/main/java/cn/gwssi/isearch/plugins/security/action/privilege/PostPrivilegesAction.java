package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class PostPrivilegesAction
        extends ActionType<PostPrivilegesResponse> {
    public static final String NAME = "cluster:admin/security/privilege/post";

    public static final PostPrivilegesAction INSTANCE = new PostPrivilegesAction();

    private PostPrivilegesAction() {
        super(NAME, PostPrivilegesResponse::new);
    }
}
