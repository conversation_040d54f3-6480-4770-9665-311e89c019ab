package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class PutUserAction
        extends ActionType<PutUserResponse> {
    public static final String NAME = "cluster:admin/girp/security/user/put";

    public static final PutUserAction INSTANCE = new PutUserAction();

    protected PutUserAction() {
        super(NAME, PutUserResponse::new);
    }
}
