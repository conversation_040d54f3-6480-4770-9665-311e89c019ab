package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequest;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

import java.util.ArrayList;
import java.util.List;

public class TransportPutPrivilegesAction
        extends HandledTransportAction<PutPrivilegesRequest, PutPrivilegesResponse> {
    private UserService userService;

    @Inject
    public TransportPutPrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
        super(PutPrivilegesAction.NAME, transportService, actionFilters, PutPrivilegesRequest::new);//PutPrivilegesRequest.class

        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, PutPrivilegesRequest request, ActionListener<PutPrivilegesResponse> listener) {
        User user = null;

        try {
            user = userService.getUser(request.getUsername());

            if (user == User.NULL_USER) {
                listener.onResponse(new PutPrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"));
                return;
            }
        } catch (Exception e) {
            listener.onFailure(e);
        }


        if (request.getPrivileges() == null || request.getPrivileges().size() == 0) {
            listener.onResponse(new PutPrivilegesResponse().setStatus(CommonActionResponse.Status.SUCCESS));
            return;
        } else {
            List<Privilege> last = new ArrayList<>();

            if (user.getPrivileges() == null || user.getPrivileges().size() == 0) {
                last = request.getPrivileges();
            } else {
                last.addAll(user.getPrivileges());

                for (Privilege p : request.getPrivileges()) {
                    boolean toAdd = true;
                    for (Privilege a : user.getPrivileges()) {
                        if (p.getResource().equals(a.getResource())) {
                            toAdd = false;
                            break;
                        }
                    }

                    if (toAdd)
                        last.add(p);
                }
            }

            user.setPrivileges(last);
        }

        try {
            userService.postUser(new PostUserRequest().setUser(user), new ActionListener<Boolean>() {
                @Override
                public void onResponse(Boolean aBoolean) {
                    listener.onResponse(new PutPrivilegesResponse().setStatus(aBoolean ? CommonActionResponse.Status.SUCCESS : CommonActionResponse.Status.ERROR));
                }

                @Override
                public void onFailure(Exception e) {
                    listener.onFailure(e);
                }
            });
        } catch (Exception e) {
            listener.onFailure(e);
        }
    }
}
