package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class GetUsersAction
        extends ActionType<GetUsersResponse> {
    public static final String NAME = "cluster:admin/girp/security/user/get";

    public static final GetUsersAction INSTANCE = new GetUsersAction();

    private GetUsersAction() {
        super(NAME, GetUsersResponse::new);
    }
}
