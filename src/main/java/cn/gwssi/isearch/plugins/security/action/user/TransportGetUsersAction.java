package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportGetUsersAction
        extends HandledTransportAction<GetUsersRequest, GetUsersResponse> {
    private UserService userService;

    @Inject
    public TransportGetUsersAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
        super(GetUsersAction.NAME, transportService, actionFilters, GetUsersRequest::new);//GetUsersRequest.class
        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, GetUsersRequest request, final ActionListener<GetUsersResponse> listener) {
        try {
            userService.getUsers(request, new ActionListener<GetUsersResponse>() {
                @Override
                public void onResponse(GetUsersResponse getUsersResponse) {
                    listener.onResponse(getUsersResponse);
                }

                @Override
                public void onFailure(Exception e) {
                    listener.onFailure(e);
                }
            });
        } catch (Exception e) {
            listener.onFailure(e);
        }
    }
}
