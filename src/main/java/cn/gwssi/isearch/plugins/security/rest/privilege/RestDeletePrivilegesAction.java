package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.DeletePrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.DeletePrivilegesResponse;
import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * 删除用户权限
 */
public class RestDeletePrivilegesAction
        extends BaseRestHandler {
    private UserService userService;

    @Override
    public String getName() {
        return "rest-delete-privileges-action";
    }

//    @Inject
//    public RestDeletePrivilegesAction(Settings settings, RestController controller, UserService userService) {
//        super(settings);
//
//        this.userService = userService;
//
//        controller.registerHandler(RestRequest.Method.DELETE, ConfigConstants.REST_PRIVILEGE_USERNAME, this);
//    }

    @Override
    public List<Route> routes() {
        return Collections.unmodifiableList(Arrays.asList(
                new Route(RestRequest.Method.DELETE, ConfigConstants.REST_PRIVILEGE_USERNAME)
        ));
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String username = request.param("username");

        List<Privilege> toDelete = Privilege.from(request.content());

        DeletePrivilegesRequestBuilder requestBuilder = new SecurityClient(client).prepareDeletePrivileges().setUsername(username).setPrivileges(toDelete);

        return channel -> requestBuilder.execute(new RestBuilderListener<DeletePrivilegesResponse>(channel) {
            @Override
            public RestResponse buildResponse(DeletePrivilegesResponse deletePrivilegesResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, deletePrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
