package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class PostUserAction
        extends ActionType<PostUserResponse> {
    public static final String NAME = "cluster:admin/security/user/post";

    public static final PostUserAction INSTANCE = new PostUserAction();

    private PostUserAction() {
        super(NAME, PostUserResponse::new);
    }
}
