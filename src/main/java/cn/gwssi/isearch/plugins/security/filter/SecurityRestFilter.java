package cn.gwssi.isearch.plugins.security.filter;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.authc.AuthcException;
import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.authz.AuthzService;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.rest.*;

import java.net.InetSocketAddress;

public class SecurityRestFilter {
    private AuthcService authcService;

    private AuthzService authzService;

    public SecurityRestFilter(AuthcService authcService, AuthzService authzService) {
        this.authcService = authcService;

        this.authzService = authzService;
    }

    public RestHandler wrap(RestHandler original) {
        return (request, channel, client) ->
        {
            if (SecurityRestFilter.this.authcAndAuthz(request, channel)) {
                original.handleRequest(request, channel, client);
            }
        };
    }

    private boolean authcAndAuthz(RestRequest request, RestChannel channel) {
        if (!ConfigConstants.enabled) {
            return true;
        }

        if (request.uri().contains("_cluster/health"))
            return true;

        if (addressInWhiteList(getRemoteHost(request))) {
            return true;
        }

        try {
            authcService.authenticate(request);
        } catch (Exception e) {
            ElasticsearchStatusException exception = new ElasticsearchStatusException("UNAUTHORIZED", RestStatus.UNAUTHORIZED);
            exception.addHeader("WWW-Authenticate", "Basic realm=\"My realm\"");
            throw exception;
        }

        if (authzService.authorize(request)) {
            return true;
        } else
            channel.sendResponse(new BytesRestResponse(RestStatus.UNAUTHORIZED, "has no authorization"));

        return false;
    }

    private boolean addressInWhiteList(String address) {
        return ConfigConstants.white_lists.contains(address);
    }

    private String getRemoteHost(RestRequest request) {
        InetSocketAddress address = request.getHttpChannel().getRemoteAddress();
        return address.getHostString();
    }
}
