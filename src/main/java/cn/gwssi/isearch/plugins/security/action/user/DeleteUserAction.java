package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionType;

public class DeleteUserAction
        extends ActionType<DeleteUserResponse> {
    public static final String NAME = "cluster:admin/girp/security/user/delete";
    public static final DeleteUserAction INSTANCE = new DeleteUserAction();

    private DeleteUserAction() {
        super(NAME, DeleteUserResponse::new);
    }
}
