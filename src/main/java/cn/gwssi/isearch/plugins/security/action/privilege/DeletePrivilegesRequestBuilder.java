package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequestBuilder;
import org.elasticsearch.client.ElasticsearchClient;

import java.util.List;

public class DeletePrivilegesRequestBuilder
        extends ActionRequestBuilder<DeletePrivilegesRequest, DeletePrivilegesResponse> {
    public DeletePrivilegesRequestBuilder(ElasticsearchClient client) {
        super(client, DeletePrivilegesAction.INSTANCE, new DeletePrivilegesRequest());
    }

    public DeletePrivilegesRequestBuilder setUsername(String username) {
        this.request.setUsername(username);
        return this;
    }

    public DeletePrivilegesRequestBuilder setPrivileges(List<Privilege> privileges) {
        this.request.setPrivileges(privileges);
        return this;
    }
}
