package cn.gwssi.isearch.plugins.security.action.user;

import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;

public class GetUsersRequest extends ActionRequest {
    private String[] usernames;

    private int from = 0;

    private int size = 20;

    public GetUsersRequest() {
        super();
    }

    public GetUsersRequest(StreamInput in) throws IOException {
        super(in);

        this.from = in.readInt();

        this.size = in.readInt();

        int size = in.readVInt();

        this.usernames = new String[size];

        for (int i = 0; i < size; i++)
            this.usernames[i] = in.readString();
    }

    public String[] getUsernames() {
        return usernames;
    }

    public void setUsernames(String[] usernames) {
        this.usernames = usernames;
    }

    public int getFrom() {
        return from;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    @Override
    public ActionRequestValidationException validate() {
        return null;
    }


    @Override
    public void writeTo(StreamOutput out) throws IOException {
        super.writeTo(out);

        out.writeInt(this.from);

        out.writeInt(this.size);

        if (this.usernames == null || this.usernames.length == 0)
            out.writeVInt(0);
        else {
            out.writeVInt(this.usernames.length);

            for (String username : this.usernames)
                out.writeString(username);
        }
    }
}
