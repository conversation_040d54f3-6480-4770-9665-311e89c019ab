package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class DeletePrivilegesAction
        extends ActionType<DeletePrivilegesResponse> {
    public static final String NAME = "cluster:admin/security/privilege/delete";

    public static final DeletePrivilegesAction INSTANCE = new DeletePrivilegesAction();

    private DeletePrivilegesAction() {
        super(NAME, DeletePrivilegesResponse::new);
    }
}
