package cn.gwssi.isearch.plugins.security.rest.user;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.user.DeleteUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.DeleteUserResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class RestDeleteUserAction extends BaseRestHandler {
    @Override
    public String getName() {
        return "rest-delete-user-action";
    }

//    @Inject
//    public RestDeleteUserAction(Settings settings, RestController controller) {
//        super(settings);
//
//        controller.registerHandler(RestRequest.Method.DELETE, ConfigConstants.REST_USER_USERNAME, this);
//    }

    @Override
    public List<Route> routes() {
        return Collections.unmodifiableList(Arrays.asList(
                new Route(RestRequest.Method.DELETE, ConfigConstants.REST_USER_USERNAME)
        ));
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String username = request.param("username");

        DeleteUserRequestBuilder requestBuilder = new SecurityClient(client).prepareDeleteUser(username);

        return channel -> requestBuilder.execute(new RestBuilderListener<DeleteUserResponse>(channel) {
            @Override
            public RestResponse buildResponse(DeleteUserResponse deleteUserResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, deleteUserResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
