package cn.gwssi.isearch.plugins.security.rest.user;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.user.PutUserRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.PutUserResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class RestPutUserAction extends BaseRestHandler {
    @Override
    public String getName() {
        return "rest-put-user-action";
    }

//    @Inject
//    public RestPutUserAction(Settings settings, RestController controller) {
//        super(settings);
//
//        controller.registerHandler(RestRequest.Method.PUT, ConfigConstants.REST_USER, this);
//    }

    @Override
    public List<Route> routes() {
        return Collections.unmodifiableList(Arrays.asList(
                new Route(RestRequest.Method.PUT, ConfigConstants.REST_USER)
        ));
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        final User user = User.fromRequest(request.content());

        PutUserRequestBuilder requestBuilder = new SecurityClient(client).preparePutUser(user);

        return channel -> requestBuilder.execute(new RestBuilderListener<PutUserResponse>(channel) {
            @Override
            public RestResponse buildResponse(PutUserResponse putUserResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, putUserResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
