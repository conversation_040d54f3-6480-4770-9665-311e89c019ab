package cn.gwssi.isearch.plugins.security.system;

import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.authz.AuthzService;
import org.elasticsearch.common.inject.Inject;

public class ServiceHelper
{
    private AuthcService authcService;

    private AuthzService authzService;

    @Inject
    public ServiceHelper(AuthcService authcService, AuthzService authzService)
    {
        this.authcService = authcService;
        this.authzService = authzService;

        // TODO: 19-5-24 注册filter
        //controller.registerFilter(new SecurityRestFilter(this.authcService,this.authzService));
    }
}
