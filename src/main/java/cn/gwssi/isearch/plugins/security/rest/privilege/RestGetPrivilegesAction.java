package cn.gwssi.isearch.plugins.security.rest.privilege;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.privilege.GetPrivilegesRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.privilege.GetPrivilegesResponse;
import cn.gwssi.isearch.plugins.security.client.SecurityClient;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 获取用户权限
 */
public class RestGetPrivilegesAction
        extends BaseRestHandler {
    @Override
    public String getName() {
        return "rest-get-privileges-action";
    }

//    @Inject
//    public RestGetPrivilegesAction(Settings settings, RestController controller) {
//        super(settings);
//
//        controller.registerHandler(RestRequest.Method.GET, ConfigConstants.REST_PRIVILEGE_USERNAME, this);
//    }

    @Override
    public List<Route> routes() {
        return Collections.unmodifiableList(Arrays.asList(
                new Route(RestRequest.Method.GET, ConfigConstants.REST_PRIVILEGE_USERNAME)
        ));
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String username = request.param("username");

        SecurityClient securityClient = new SecurityClient(client);

        GetPrivilegesRequestBuilder requestBuilder = securityClient.prepareGetPrivileges(username);

        return channel -> requestBuilder.execute(new RestBuilderListener<GetPrivilegesResponse>(channel) {
            @Override
            public RestResponse buildResponse(GetPrivilegesResponse getPrivilegesResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, getPrivilegesResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
