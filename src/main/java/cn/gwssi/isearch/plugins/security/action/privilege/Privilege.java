package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.common.bytes.BytesReference;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class Privilege implements ToXContent
{
    private String resource;

    private OperationType operation;

    public Privilege(String resource,OperationType operation)
    {
        this.resource=resource;
        this.operation=operation;
    }

    public String getResource()
    {
        return this.resource;
    }

    public Privilege setResource(String resource)
    {
        this.resource=resource;
        return this;
    }

    public OperationType getOperation()
    {
        return this.operation;
    }

    public Privilege setOperation(OperationType operation)
    {
        this.operation=operation;
        return this;
    }

    @Override
    public XContentBuilder toXContent(XContentBuilder builder, Params params) throws IOException
    {
        return builder.startObject().field(this.resource,this.getOperation().toString()).endObject();
    }

    @Override
    public boolean equals(Object other)
    {
        if(other instanceof Privilege)
        {
            Privilege otherPrivilege=(Privilege)other;

            if (this.resource.equals(otherPrivilege.resource)
                    && this.operation == otherPrivilege.operation)
            {
                return true;
            }
        }

        return false;
    }

    public boolean sameResource(Privilege other)
    {
        if(this.resource.equals(other.resource))
            return true;

        return false;
    }

    public static void writeTo(Privilege privilege, StreamOutput out) throws IOException
    {
        out.writeString(privilege.getResource());
        out.writeString(privilege.getOperation().toString());
    }

    public static Privilege readFrom(StreamInput in) throws IOException
    {
        return new Privilege(in.readString(), OperationType.from(in.readString()));
    }

    public static List<Privilege> from(BytesReference source) throws IOException
    {
        XContentType contentType = XContentType.JSON;
        XContentParser parser = contentType.xContent().createParser(
                NamedXContentRegistry.EMPTY,
                DeprecationHandler.THROW_UNSUPPORTED_OPERATION,
                source.toBytesRef().bytes
        );

        //XContentUtils.verifyObject(parser);

        String fieldName=null;

        XContentParser.Token token;

        List<Privilege> result = new ArrayList<>();

        while ((token=parser.nextToken())!= null)
        {
            if(token== XContentParser.Token.FIELD_NAME)
                fieldName = parser.currentName();

            else
            {
                if(token== XContentParser.Token.VALUE_STRING)
                    result.add(new Privilege(fieldName, OperationType.from(parser.text())));
            }
        }

        return result;
    }

    public static List<Privilege> listFrom(StreamInput in) throws IOException
    {
        List<Privilege> privileges = new ArrayList<>();

        int size = in.readVInt();

        if(size==0)
            ;
        else
        {
            for(int i=0;i<size;i++)
                privileges.add(Privilege.readFrom(in));
        }

        return privileges;
    }

    public static void listTo(List<Privilege> privileges, StreamOutput out) throws IOException
    {
        if(privileges==null)
            out.writeVInt(0);

        else
        {
            out.writeVInt(privileges.size());

            for(Privilege p : privileges)
                Privilege.writeTo(p,out);
        }
    }

    public enum OperationType
    {
        READ_ONLY,
        READ_WRITE,
        NONE;

        public static OperationType from(String operation)
        {
            OperationType mid = OperationType.NONE;

            for(OperationType type : OperationType.values())
            {
                if(type.toString().equalsIgnoreCase(operation))
                {
                    mid = type;
                    break;
                }
            }

            return mid;
        }
    }
}
