package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;

public class PostUserResponse
    extends CommonActionResponse<PostUserResponse>
{
    public PostUserResponse() {
        super();
    }

    public PostUserResponse(StreamInput in) throws IOException {
        super(in);
    }

    @Override
    protected void writeResponseTo(XContentBuilder builder)
    {

    }
}
