package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class UpdatePrivilegesAction
        extends ActionType<UpdatePrivilegesResponse> {
    public static final String NAME = "cluster:admin/girp/security/privilege/update";

    public static final UpdatePrivilegesAction INSTANCE = new UpdatePrivilegesAction();

    private UpdatePrivilegesAction() {
        super(NAME, UpdatePrivilegesResponse::new);
    }
}
