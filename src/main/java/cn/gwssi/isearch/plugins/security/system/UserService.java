package cn.gwssi.isearch.plugins.security.system;

import cn.gwssi.isearch.plugins.security.action.user.*;
import org.elasticsearch.action.ActionListener;

import java.util.concurrent.ExecutionException;

public interface UserService
{
    /**
     * <p>获取用户信息</p>
     *
     * <p>可以查询单个用户、多个用户、或所有用户信息({@link GetUsersRequest} 的usernames属性为空或空数组)。</p>
     *
     * @param request
     * @param listener
     * @throws ExecutionException
     * @throws InterruptedException
     */
    void getUsers(GetUsersRequest request, ActionListener<GetUsersResponse> listener) throws ExecutionException, InterruptedException;

    /**
     * <p>返回单个用户的信息</p>
     *
     * <p>多为程序内部使用。</p>
     *
     * <p>永远不返回null,当未查询到用户时，返回NULL_USER。</p>
     *
     * @param username
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    User getUser(String username) throws ExecutionException, InterruptedException;

    /**
     * <p>新增用户</p>
     *
     * <p>当存在相同用户名的时候，抛出异常。</p>
     *
     * @param request
     * @param listener
     * @throws ExecutionException
     * @throws InterruptedException
     * @throws UserAlreadyExistsException 用户已存在异常
     */
    void putUser(PutUserRequest request, ActionListener<Boolean> listener) throws ExecutionException, InterruptedException, UserAlreadyExistsException;

    /**
     * 根据用户名，删除用户信息
     *
     * @param request
     * @param listener
     */
    void deleteUser(DeleteUserRequest request, ActionListener<Boolean> listener);

    void postUser(PutUserRequest request, ActionListener<Boolean> listener) throws ExecutionException, InterruptedException, UserNotFoundException;

    /**
     *
     * <p>修改用户信息</p>
     *
     * <p>使用请求中字段的数据完全覆盖之前的数据，未设置的字段(不出现在请求中)不作处理。</p>
     *
     * <pre>
     *     {"password":"123","fullName":"123"}
     * </pre>
     *
     * <p>如上，会覆盖password和fullName字段，未设置的email字段保持不变。</p>
     *
     * <p>当不存在该用户时，抛出异常。</p>
     *
     * @param request
     * @param listener
     * @throws InterruptedException
     * @throws ExecutionException
     * @throws UserNotFoundException
     */
    void postUser(PostUserRequest request, ActionListener<Boolean> listener) throws InterruptedException, ExecutionException, UserNotFoundException;
}
