package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;

public class UpdatePrivilegesResponse
    extends CommonActionResponse<UpdatePrivilegesResponse>
{
    public UpdatePrivilegesResponse() {
        super();
    }

    public UpdatePrivilegesResponse(StreamInput in) throws IOException {
        super(in);
    }

    @Override
    protected void writeResponseTo(XContentBuilder builder) {}
}
