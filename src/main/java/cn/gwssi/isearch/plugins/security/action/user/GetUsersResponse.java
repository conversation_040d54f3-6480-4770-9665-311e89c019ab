package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;

public class GetUsersResponse extends CommonActionResponse<GetUsersResponse> {
    private User[] users;

    private long total = 0;

    public GetUsersResponse() {
        super();
    }

    public GetUsersResponse(StreamInput in) throws IOException {
        super(in);

        this.total = in.readLong();

        int size = in.readVInt();

        this.users = new User[size];

        for (int i = 0; i < size; i++)
            this.users[i] = User.readFrom(in);
    }

    public User[] getUsers() {
        return users;
    }

    public void setUsers(User[] users) {
        this.users = users;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    @Override
    protected void writeResponseTo(XContentBuilder builder) throws IOException {
        if (this.status == Status.SUCCESS) {
            builder.startObject("response").field("total", this.total);

            if (this.users != null)
                builder.array("users", this.users);

            builder.endObject();
        }
    }

    @Override
    public void writeTo(StreamOutput out) throws IOException {
        super.writeTo(out);

        out.writeLong(this.total);

        if (this.users == null) {
            out.writeVInt(0);
        } else {
            out.writeVInt(this.users.length);

            for (User user : this.users)
                User.writeTo(user, out);
        }
    }
}
