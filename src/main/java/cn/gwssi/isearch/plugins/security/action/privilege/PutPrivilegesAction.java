package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class PutPrivilegesAction
        extends ActionType<PutPrivilegesResponse> {
    public static final String NAME = "cluster:admin/security/privilege/put";

    public static final PutPrivilegesAction INSTANCE = new PutPrivilegesAction();

    private PutPrivilegesAction() {
        super(NAME, PutPrivilegesResponse::new);
    }
}
