package cn.gwssi.isearch.plugins.security.rest.user;

import cn.gwssi.isearch.plugins.security.ConfigConstants;
import cn.gwssi.isearch.plugins.security.action.user.GetUsersRequestBuilder;
import cn.gwssi.isearch.plugins.security.action.user.GetUsersResponse;
import org.elasticsearch.client.node.NodeClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.rest.*;
import org.elasticsearch.rest.action.RestBuilderListener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class RestGetUsersAction extends BaseRestHandler {
    @Override
    public String getName() {
        return "rest-get-users-action";
    }

//    @Inject
//    public RestGetUsersAction(Settings settings, RestController controller) {
//        super(settings);
//
//        controller.registerHandler(RestRequest.Method.GET, ConfigConstants.REST_USER, this);
//        controller.registerHandler(RestRequest.Method.GET, ConfigConstants.REST_USER_USERNAME, this);
//
//    }

    @Override
    public List<Route> routes() {
        return Collections.unmodifiableList(Arrays.asList(
                new Route(RestRequest.Method.GET, ConfigConstants.REST_USER),
                new Route(RestRequest.Method.GET, ConfigConstants.REST_USER_USERNAME)
        ));
    }

    @Override
    protected RestChannelConsumer prepareRequest(RestRequest request, NodeClient client) throws IOException {
        String[] usernames = request.paramAsStringArray("username", Strings.EMPTY_ARRAY);

        GetUsersRequestBuilder requestBuilder = new GetUsersRequestBuilder(client).setUsernames(usernames);

        requestBuilder.setFrom(request.paramAsInt("fromSource", 0));

        requestBuilder.setSize(request.paramAsInt("size", 20));

        return channel -> requestBuilder.execute(new RestBuilderListener<GetUsersResponse>(channel) {
            @Override
            public RestResponse buildResponse(GetUsersResponse getUsersResponse, XContentBuilder builder) throws Exception {
                return new BytesRestResponse(RestStatus.OK, getUsersResponse.toXContent(builder, ToXContent.EMPTY_PARAMS));
            }
        });
    }
}
