package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;
import org.elasticsearch.common.xcontent.XContentBuilder;

import java.io.IOException;
import java.util.List;

public class GetPrivilegesResponse
        extends CommonActionResponse<GetPrivilegesResponse> {
    private List<Privilege> privileges;

    public GetPrivilegesResponse() {
        super();
    }

    public GetPrivilegesResponse(StreamInput in) throws IOException {
        super(in);
        this.privileges = Privilege.listFrom(in);
    }

    public List<Privilege> getPrivileges() {
        return privileges;
    }

    public GetPrivilegesResponse setPrivileges(List<Privilege> privileges) {
        this.privileges = privileges;
        return this;
    }

    @Override
    protected void writeResponseTo(XContentBuilder builder) throws IOException {
        if (this.status == Status.ERROR)
            ;
        else {
            builder.startObject("response");
            if (this.privileges == null)
                builder.array("privileges", new Privilege[0]);
            else
                builder.array("privileges", this.privileges);
            builder.endObject();
        }
    }

    @Override
    public void writeTo(StreamOutput out) throws IOException {
        super.writeTo(out);

        Privilege.listTo(this.privileges, out);
    }
}
