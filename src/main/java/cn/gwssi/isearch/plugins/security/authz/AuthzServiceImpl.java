package cn.gwssi.isearch.plugins.security.authz;

import cn.gwssi.isearch.plugins.security.action.privilege.Privilege;
import cn.gwssi.isearch.plugins.security.authc.AuthcException;
import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.system.User;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.rest.RestRequest;

public class AuthzServiceImpl implements AuthzService
{
    private AuthcService authcService;

    @Inject
    public AuthzServiceImpl(AuthcService authcService)
    {
        this.authcService = authcService;
    }

    @Override
    public boolean authorize(RestRequest request)
    {
        User user=null;

        try
        {
            user = authcService.authenticate(request);
        }
        catch (AuthcException e)
        {
            return false;
        }

        if(user==User.SUPER_ADMIN)
            return true;


        RestRequest.Method method = request.method();

        String path = request.path();

        String[] elements = path.split("/");

        String resource = "";

        for(String e : elements)
        {
            if(!Strings.isNullOrEmpty(e))
            {
                resource = e;
                break;
            }
        }

        Privilege.OperationType canDo = user.operationInResource(resource);

        if(canDo== Privilege.OperationType.NONE)
            return false;
        else if(canDo== Privilege.OperationType.READ_WRITE)
            return true;
        else
        {
            if(method== RestRequest.Method.GET)
                return true;
            else if(method== RestRequest.Method.POST)
            {
                String endpoint = "";

                for(int i=elements.length-1;i>=0;i--)
                {
                    if(!Strings.isNullOrEmpty(elements[i]))
                    {
                        endpoint = elements[i];
                        break;
                    }
                }

                if (endpoint.equalsIgnoreCase("_search"))
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

    }
}
