package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionType;

public class GetPrivilegesAction
        extends ActionType<GetPrivilegesResponse> {
    public static final String NAME = "cluster:admin/girp/security/privileges/get";

    public static final GetPrivilegesAction INSTANCE = new GetPrivilegesAction();

    protected GetPrivilegesAction() {
        super(NAME, GetPrivilegesResponse::new);
    }

}
