package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequest;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.io.stream.Writeable;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportPostPrivilegesAction
        extends AbstractModifyPrivilegesAction<PostPrivilegesRequest, PostPrivilegesResponse> {
//    @Inject
//    public TransportPostPrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
//        super(userService, PostPrivilegesAction.NAME, transportService, actionFilters, PostPrivilegesRequest::new);//PostPrivilegesRequest.class
//    }

    @Inject
    public TransportPostPrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
        super(userService, PostPrivilegesAction.NAME, transportService, actionFilters, PostPrivilegesRequest::new);
    }


    @Override
    public void doExecute(Task task, PostPrivilegesRequest request, ActionListener<PostPrivilegesResponse> listener) {
        CheckUserResult checkUserResult = checkUser(request, new PostPrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"), listener);

        if (!checkUserResult.isExist())
            return;
        else {
            User user = checkUserResult.getUser();

            if (user.getPrivileges() == null
                    || user.getPrivileges().size() == 0
                    || request.getPrivileges() == null
                    || request.getPrivileges().size() == 0) {
                listener.onResponse(new PostPrivilegesResponse().setStatus(CommonActionResponse.Status.SUCCESS));
                return;
            } else {
                for (Privilege r : request.getPrivileges()) {
                    for (Privilege u : user.getPrivileges()) {
                        if (r.getResource().equals(u.getResource()))
                            u.setOperation(r.getOperation());
                    }
                }
            }

            try {
                userService.postUser(new PostUserRequest().setUser(user), new ActionListener<Boolean>() {
                    @Override
                    public void onResponse(Boolean aBoolean) {
                        listener.onResponse(new PostPrivilegesResponse().setStatus(aBoolean ? CommonActionResponse.Status.SUCCESS : CommonActionResponse.Status.ERROR));
                    }

                    @Override
                    public void onFailure(Exception e) {
                        listener.onFailure(e);
                    }
                });
            } catch (Exception e) {
                listener.onFailure(e);
            }
        }
    }
}
