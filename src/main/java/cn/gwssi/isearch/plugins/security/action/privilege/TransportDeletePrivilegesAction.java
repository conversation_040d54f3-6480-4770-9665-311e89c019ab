package cn.gwssi.isearch.plugins.security.action.privilege;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.action.user.PostUserRequest;
import cn.gwssi.isearch.plugins.security.system.User;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

import java.util.ArrayList;
import java.util.List;

public class TransportDeletePrivilegesAction
        extends HandledTransportAction<DeletePrivilegesRequest, DeletePrivilegesResponse> {
    private UserService userService;

    @Inject
    public TransportDeletePrivilegesAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
        super(DeletePrivilegesAction.NAME, transportService, actionFilters, DeletePrivilegesRequest::new);

        this.userService = userService;
    }

    @Override
    protected void doExecute(Task task, DeletePrivilegesRequest request, ActionListener<DeletePrivilegesResponse> listener) {
        User user = null;

        try {
            user = userService.getUser(request.getUsername());

            if (user == User.NULL_USER) {
                listener.onResponse(new DeletePrivilegesResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user not found"));
                return;
            }
        } catch (Exception e) {
            listener.onFailure(e);
        }


        if (user.getPrivileges() == null || request.getPrivileges() == null) {
            listener.onResponse(new DeletePrivilegesResponse());
            return;
        } else {
            List<Privilege> last = new ArrayList<>();

            for (Privilege p : user.getPrivileges()) {
                boolean toDelete = false;
                for (Privilege d : request.getPrivileges()) {
                    if (p.equals(d)) {
                        toDelete = true;
                        break;
                    }
                }

                if (!toDelete)
                    last.add(p);
            }

            user.setPrivileges(last);
        }

        try {
            userService.postUser(new PostUserRequest().setUser(user), new ActionListener<Boolean>() {
                @Override
                public void onResponse(Boolean aBoolean) {
                    listener.onResponse(new DeletePrivilegesResponse().setStatus(aBoolean ? CommonActionResponse.Status.SUCCESS : CommonActionResponse.Status.ERROR));
                }

                @Override
                public void onFailure(Exception e) {
                    listener.onFailure(e);
                }
            });
        } catch (Exception e) {
            listener.onFailure(e);
        }
    }
}
