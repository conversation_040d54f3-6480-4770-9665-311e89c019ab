package cn.gwssi.isearch.plugins.security.action.user;

import cn.gwssi.isearch.plugins.security.action.CommonActionResponse;
import cn.gwssi.isearch.plugins.security.system.UserAlreadyExistsException;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.support.ActionFilters;
import org.elasticsearch.action.support.HandledTransportAction;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.tasks.Task;
import org.elasticsearch.transport.TransportService;

public class TransportPutUserAction
        extends HandledTransportAction<PutUserRequest, PutUserResponse> {
    private UserService userService;

    @Inject
    public TransportPutUserAction(UserService userService, TransportService transportService, ActionFilters actionFilters) {
        super(PutUserAction.NAME, transportService, actionFilters, PutUserRequest::new);//PutUserRequest.class
        this.userService = userService;
    }

    @Override
    public void doExecute(Task task, PutUserRequest request, final ActionListener<PutUserResponse> listener) {
        try {
            userService.putUser(request, new ActionListener<Boolean>() {
                @Override
                public void onResponse(Boolean aBoolean) {
                    listener.onResponse(new PutUserResponse().setStatus(aBoolean ? CommonActionResponse.Status.SUCCESS : CommonActionResponse.Status.ERROR));
                }

                @Override
                public void onFailure(Exception e) {
                    listener.onFailure(e);
                }
            });
        } catch (UserAlreadyExistsException e) {
            listener.onResponse(new PutUserResponse().setStatus(CommonActionResponse.Status.ERROR).setMsg("user already exists"));
        } catch (Exception e) {
            listener.onFailure(e);
        }
    }
}
