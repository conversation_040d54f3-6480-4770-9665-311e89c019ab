package cn.gwssi.isearch.plugins.security.action.privilege;

import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionRequestValidationException;
import org.elasticsearch.action.ValidateActions;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.io.stream.StreamInput;
import org.elasticsearch.common.io.stream.StreamOutput;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class UpdatePrivilegesRequest
        extends ActionRequest {
    private String username;

    public UpdatePrivilegesRequest() {
        super();
    }

    public UpdatePrivilegesRequest(StreamInput in) throws IOException {
        super(in);
        this.username = in.readString();
        this.privileges = Privilege.listFrom(in);
    }

    private List<Privilege> privileges = new ArrayList<>();

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<Privilege> getPrivileges() {
        return privileges;
    }

    public void setPrivileges(List<Privilege> privileges) {
        this.privileges = privileges;
    }

    @Override
    public ActionRequestValidationException validate() {
        if (Strings.isNullOrEmpty(this.username))
            return ValidateActions.addValidationError("username is null or empty while updating privileges", null);

        return null;
    }

    @Override
    public void writeTo(StreamOutput out) throws IOException {
        super.writeTo(out);

        out.writeString(this.username);

        Privilege.listTo(this.privileges, out);
    }
}
