package cn.gwssi.isearch.plugins.security;

import cn.gwssi.isearch.plugins.security.action.privilege.*;
import cn.gwssi.isearch.plugins.security.action.user.*;
import cn.gwssi.isearch.plugins.security.authc.AuthcService;
import cn.gwssi.isearch.plugins.security.authc.AuthcServiceImpl;
import cn.gwssi.isearch.plugins.security.authz.AuthzService;
import cn.gwssi.isearch.plugins.security.authz.AuthzServiceImpl;
import cn.gwssi.isearch.plugins.security.filter.SecurityRestFilter;
import cn.gwssi.isearch.plugins.security.rest.privilege.*;
import cn.gwssi.isearch.plugins.security.rest.user.RestDeleteUserAction;
import cn.gwssi.isearch.plugins.security.rest.user.RestGetUsersAction;
import cn.gwssi.isearch.plugins.security.rest.user.RestPostUserAction;
import cn.gwssi.isearch.plugins.security.rest.user.RestPutUserAction;
import cn.gwssi.isearch.plugins.security.system.NativeUserService;
import cn.gwssi.isearch.plugins.security.system.UserService;
import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.ActionResponse;
import org.elasticsearch.client.Client;
import org.elasticsearch.cluster.metadata.IndexNameExpressionResolver;
import org.elasticsearch.cluster.node.DiscoveryNodes;
import org.elasticsearch.cluster.service.ClusterService;
import org.elasticsearch.common.inject.Inject;
import org.elasticsearch.common.inject.Module;
import org.elasticsearch.common.io.stream.NamedWriteableRegistry;
import org.elasticsearch.common.settings.*;
import org.elasticsearch.common.util.concurrent.ThreadContext;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.env.Environment;
import org.elasticsearch.env.NodeEnvironment;
import org.elasticsearch.plugins.ActionPlugin;
import org.elasticsearch.plugins.Plugin;
import org.elasticsearch.repositories.RepositoriesService;
import org.elasticsearch.rest.RestController;
import org.elasticsearch.rest.RestHandler;
import org.elasticsearch.script.ScriptService;
import org.elasticsearch.threadpool.ThreadPool;
import org.elasticsearch.watcher.ResourceWatcherService;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;

public class SecurityPlugin extends Plugin implements ActionPlugin {
    private final Settings settings;

    private Client localClient;

    private SecurityRestFilter restFilter;

    @Inject
    public SecurityPlugin(final Settings settings) {
        this.settings = settings;

        String enabled = System.getenv("girp_security_enabled");

        if (enabled == null)
            ConfigConstants.enabled = settings.getAsBoolean(ConfigConstants.PLUGIN_ENABLED, false);
        else
            ConfigConstants.enabled = Boolean.parseBoolean(enabled);

        String supername = System.getenv("girp_security_superadmin_name");

        if (supername == null)
            ConfigConstants.superadmin_name = settings.get(ConfigConstants.SUPERADMIN_NAME, ConfigConstants.superadmin_name);
        else
            ConfigConstants.superadmin_name = supername;


        String superpassword = System.getenv("girp_security_superadmin_password");

        if (superpassword == null)
            ConfigConstants.superadmin_password = settings.get(ConfigConstants.SUPERADMIN_PASSWORD, ConfigConstants.superadmin_password);
        else
            ConfigConstants.superadmin_password = superpassword;

        List<String> whitelists = settings.getAsList(ConfigConstants.WHITE_LIST);
        ConfigConstants.white_lists.addAll(whitelists);
    }

    @Override
    public Collection<Module> createGuiceModules() {
        return Collections.singletonList(new ServiceModule());
    }

//    @Override
//    public Collection<Object> createComponents(Client client, ClusterService clusterService, ThreadPool threadPool, ResourceWatcherService resourceWatcherService, ScriptService scriptService, NamedXContentRegistry xContentRegistry, Environment environment, NodeEnvironment nodeEnvironment, NamedWriteableRegistry namedWriteableRegistry) {
//        this.localClient = client;
//
//        return Collections.emptyList();
//    }

    @Override
    public Collection<Object> createComponents(Client client, ClusterService clusterService, ThreadPool threadPool, ResourceWatcherService resourceWatcherService, ScriptService scriptService, NamedXContentRegistry xContentRegistry, Environment environment, NodeEnvironment nodeEnvironment, NamedWriteableRegistry namedWriteableRegistry, IndexNameExpressionResolver indexNameExpressionResolver, Supplier<RepositoriesService> repositoriesServiceSupplier) {
        this.localClient = client;
        return super.createComponents(client, clusterService, threadPool, resourceWatcherService, scriptService, xContentRegistry, environment, nodeEnvironment, namedWriteableRegistry, indexNameExpressionResolver, repositoriesServiceSupplier);
    }

    @Override
    public List<RestHandler> getRestHandlers(Settings settings, RestController restController, ClusterSettings clusterSettings, IndexScopedSettings indexScopedSettings, SettingsFilter settingsFilter, IndexNameExpressionResolver indexNameExpressionResolver, Supplier<DiscoveryNodes> nodesInCluster) {
        List<RestHandler> handlers = new ArrayList<>();

        handlers.add(new RestDeleteUserAction());
        handlers.add(new RestGetUsersAction());
        handlers.add(new RestPostUserAction());
        handlers.add(new RestPutUserAction());

        handlers.add(new RestDeletePrivilegesAction());
        handlers.add(new RestGetPrivilegesAction());
        handlers.add(new RestPostPrivilegesAction());
        handlers.add(new RestPutPrivilegesAction());
        handlers.add(new RestReplacePrivilegesAction());

        return handlers;
    }

    @Override
    public UnaryOperator<RestHandler> getRestHandlerWrapper(ThreadContext threadContext) {
        UserService userService = new NativeUserService(localClient);
        AuthcService authcService = new AuthcServiceImpl(userService);
        AuthzService authzService = new AuthzServiceImpl(authcService);
        restFilter = new SecurityRestFilter(authcService, authzService);
        return (rh) -> restFilter.wrap(rh);
    }

    @Override
    public List<ActionHandler<? extends ActionRequest, ? extends ActionResponse>> getActions() {
        List<ActionHandler<? extends ActionRequest, ? extends ActionResponse>> list
                = new ArrayList<>();

        list.add(new ActionHandler<>(DeleteUserAction.INSTANCE, TransportDeleteUserAction.class));
        list.add(new ActionHandler<>(GetUsersAction.INSTANCE, TransportGetUsersAction.class));
        list.add(new ActionHandler<>(PostUserAction.INSTANCE, TransportPostUserAction.class));
        list.add(new ActionHandler<>(PutUserAction.INSTANCE, TransportPutUserAction.class));
        list.add(new ActionHandler<>(DeletePrivilegesAction.INSTANCE, TransportDeletePrivilegesAction.class));
        list.add(new ActionHandler<>(GetPrivilegesAction.INSTANCE, TransportGetPrivilegesAction.class));
        list.add(new ActionHandler<>(PostPrivilegesAction.INSTANCE, TransportPostPrivilegesAction.class));
        list.add(new ActionHandler<>(PutPrivilegesAction.INSTANCE, TransportPutPrivilegesAction.class));
        list.add(new ActionHandler<>(UpdatePrivilegesAction.INSTANCE, TransportUpdatePrivilegesAction.class));

        return list;
    }

    @Override
    public List<Setting<?>> getSettings() {
        List<Setting<?>> settings = new ArrayList<>();
        settings.add(Setting.boolSetting(ConfigConstants.PLUGIN_ENABLED, false, Setting.Property.NodeScope));
        settings.add(Setting.simpleString(ConfigConstants.SUPERADMIN_NAME, ConfigConstants.superadmin_name, Setting.Property.NodeScope));
        settings.add(Setting.simpleString(ConfigConstants.SUPERADMIN_PASSWORD, ConfigConstants.superadmin_password, Setting.Property.NodeScope));
        settings.add(Setting.listSetting(ConfigConstants.WHITE_LIST, new ArrayList<String>(), Function.identity(), Setting.Property.NodeScope));
        return settings;
    }
}
