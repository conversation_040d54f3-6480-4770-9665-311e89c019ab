<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.gwssi.isearch</groupId>
    <artifactId>isearch-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>common</module>
        <module>syntax</module>
        <module>humble</module>
        <module>plugins</module>
        <module>client</module>
        <module>entity</module>
    </modules>

    <properties>
        <nexus.id>beacon</nexus.id>
        <nexus.name>beacon</nexus.name>
        <nexus.url>http://nexus.e.cloud:8081/repository/maven-releases/</nexus.url>
        <nexus.snapshot.id>beacon-snapshot</nexus.snapshot.id>
        <nexus.snapshot.name>beacon-snapshot</nexus.snapshot.name>
        <nexus.snapshot.url>http://nexus.e.cloud:8081/repository/maven-snapshots/</nexus.snapshot.url>
        <nexus.central.id>beacon-central</nexus.central.id>
        <nexus.central.name>beacon-central</nexus.central.name>
        <nexus.central.url>http://nexus.e.cloud:8081/repository/maven-central/</nexus.central.url>
        
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        
        <!-- jar 版本号 -->
        <junit.version>5.8.2</junit.version>
        <httpcore.version>4.4.6</httpcore.version>
        <httpclient.version>4.5.3</httpclient.version>
        <httpAsyncClient.version>4.1.3</httpAsyncClient.version>
        <httpclientandroid.version>4.3.5.1</httpclientandroid.version>
        <slf4j.version>1.7.25</slf4j.version>
        <log4j.version>1.2.17</log4j.version>
        <log4j-api.version>2.7</log4j-api.version>
        <guava.version>20.0</guava.version>
        <jodaTime.version>2.10.1</jodaTime.version>
        <commons-lang.version>3.8</commons-lang.version>
        <commons-collections.version>4.4</commons-collections.version>
        <commons.io.version>2.6</commons.io.version>
        <commons-io.version>1.3.2</commons-io.version>
        <commons-codec>1.11</commons-codec>
        <commons-validator>1.7</commons-validator>
        <gson.version>2.8.5</gson.version>
        <fastjson.version>1.2.83</fastjson.version>
        <orgJson.version>chargebee-1.0</orgJson.version>
        <lombok.version>1.16.20</lombok.version>
        <antlr.version>4.11.1</antlr.version>
        <fst.version>2.56</fst.version>
        <revision>1.1-temp-SNAPSHOT</revision>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            
            <!-- ************************************** 日志，不参与打包 ************************************** -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j-api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j-api.version}</version>
                <scope>provided</scope>
            </dependency>
    
            <!-- ************************************** httpClient ************************************** -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpAsyncClient.version}</version>
            </dependency>
            
            <!--************************************** 工具包 ************************************** -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons-validator}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>org.json</artifactId>
                <version>${orgJson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${jodaTime.version}</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr.version}</version>
            </dependency>
            <dependency>
                <groupId>de.ruedigermoeller</groupId>
                <artifactId>fst</artifactId>
                <version>${fst.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>5.3.13</version>
                <scope>provided</scope>
            </dependency>
    
            <!--************************************** 本项目中的包 ************************************** -->
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>humble</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>syntax-antlr-builder</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>syntax-expr-builder</artifactId>
                <version>${revision}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>syntax-codec</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>syntax-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>syntax-fieldmapping-generator</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>plugins-common-search</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.isearch</groupId>
                <artifactId>entity</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!---用于发布版本的地址-->
    <distributionManagement>
        <repository>
            <id>${nexus.id}</id>
            <name>${nexus.name}</name>
            <url>${nexus.url}</url>
        </repository>
        <!--快照库-->
        <snapshotRepository>
            <id>${nexus.snapshot.id}</id>
            <name>${nexus.snapshot.name}</name>
            <url>${nexus.snapshot.url}</url>
        </snapshotRepository>
        <!--快照库-->
<!--        <snapshotRepository>-->
<!--            <id>外网</id>-->
<!--            <name>外网</name>-->
<!--            <url>http://1.203.97.79:8081/repository/maven-snapshots</url>-->
<!--        </snapshotRepository>-->
    </distributionManagement>
    <repositories>
        <repository>
            <id>${nexus.id}</id>
            <name>${nexus.name}</name>
            <url>${nexus.url}</url>
        </repository>
        <repository>
            <id>${nexus.snapshot.id}</id>
            <name>${nexus.snapshot.name}</name>
            <url>${nexus.snapshot.url}</url>
        </repository>
        <repository>
            <id>${nexus.central.id}</id>
            <name>${nexus.central.name}</name>
            <url>${nexus.central.url}</url>
        </repository>
        <repository>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spy</id>
            <name>Spy Repository</name>
            <layout>default</layout>
            <url>http://files.couchbase.com/maven2/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.1</version>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <flattenedPomFilename>flattened-pom.xml</flattenedPomFilename>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>