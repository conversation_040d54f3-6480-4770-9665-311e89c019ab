package cn.gwssi.test.serializers;


import cn.gwssi.DeserializerFactory;
import cn.gwssi.SerializerFactory;
import cn.gwssi.po.Person;
import cn.gwssi.po.User;
import org.nustaq.serialization.FSTConfiguration;

/**
 * Created by duq on 2019/3/14.
 */
public class Test {

    public static void main(String[] args){

        User user = new User();

        user.setName("aaa");
        user.setAge(11);


        Person person = new Person();

        person.setAge(1);
        person.setName("杜强");
        person.setSex("sex");
        user.addSubUser(person);
        FSTConfiguration fstConfiguration =  FSTConfiguration.createDefaultConfiguration();
        byte[] bytes = fstConfiguration.asByteArray(user);
        User user1 = (User) fstConfiguration.asObject(bytes);

        System.out.println(user1.getAge()+" "+user1.getName());

    }
}
