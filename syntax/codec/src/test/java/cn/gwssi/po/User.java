package cn.gwssi.po;

import cn.gwssi.syntax.condition.ScannerItemCondition;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by duq on 2019/3/18.
 */
public class User implements Serializable{

    private String name;

    private int age;

    List<User> subUsers = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public void addSubUser(User user){
        subUsers.add(user);
    }

    public List<User> getSubUsers() {
        return subUsers;
    }

    public void setSubUsers(List<User> subUsers) {
        this.subUsers = subUsers;
    }
}
