package cn.gwssi.deserializers;



import cn.gwssi.util.Pool;
import org.apache.commons.codec.binary.Base64;
import org.nustaq.serialization.FSTConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by duq on 2019/3/14.
 */
public class DeserializersImpl implements Deserializer{

    private static Pool<FSTConfiguration> fstConfigurationPool;

    public DeserializersImpl(Pool<FSTConfiguration> fstConfigurationPool){
        this.fstConfigurationPool = fstConfigurationPool;
    }

    @Override
    public <T> List<T> readFromByteArray(List<byte[]> bytesList, Class<T> tClass) {
        List<T> list = new ArrayList<T>();
        for(byte[] bs : bytesList) {
            T obj = readFromByteArray(bs, tClass);
            list.add(obj);
        }
        return list;
    }

    @Override
    public <T> T readFromByteArray(byte[] bytes, Class<T> tClass) {
        FSTConfiguration fstConfiguration = fstConfigurationPool.obtain();
        T obj = (T) fstConfiguration.asObject(bytes);
        return obj;
    }

    @Override
    public <T> T readFromByString(String string, Class<T> tClass) {
        T obj = (T) readFromByteArray(Base64.decodeBase64(string),tClass);
        return obj;
    }
}