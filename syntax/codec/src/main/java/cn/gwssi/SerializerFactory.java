package cn.gwssi;

import cn.gwssi.serializers.SerializersImpl;

/**
 * Created by duq on 2019/3/20.
 */
public class SerializerFactory extends FSTConfigurationFactory {

    private SerializerFactory() {
        super();
    }
    @Override
    public SerializerFactory setThreadSafe(boolean threadSafe) {
        addThreadSafe(threadSafe);
        return this;
    }
    @Override
    public SerializerFactory setSoftReferences(boolean softReferences) {
        addSoftReferences(softReferences);
        return this;
    }
    @Override
    public SerializerFactory setMaximunCapacity(int maximumCapacity) {
        addMaximumCapacity(maximumCapacity);
        return this;
    }

    @Override
    public SerializersImpl build() {
        SerializersImpl serializers = new SerializersImpl(getKryoPool());
        return serializers;
    }

    public static SerializerFactory builder(){
        return new SerializerFactory();
    }

}
