package cn.gwssi.serializers;

import java.util.List;

/**
 * Created by duq on 2019/3/20.
 */
public interface Serializer {

    /**
     * 序列化一个集合
     * @param pList
     * @param <T>
     * @return
     */
    <T> List<byte[]> writeObjectToByteArray(List<T> pList);


    /**
     * 序列化一个对象
     * @param t
     * @param <T>
     * @return
     */
    <T>byte[] writeObjectToByteArray(T t);

    <T>String writeObjectToString(T t);

}
