package cn.gwssi.serializers;



import cn.gwssi.util.Pool;
import org.apache.commons.codec.binary.Base64;
import org.nustaq.serialization.FSTConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by duq on 2019/3/14.
 */
public class SerializersImpl implements Serializer{

    private Pool<FSTConfiguration> fstConfigurationPool;

    public SerializersImpl(Pool<FSTConfiguration> fstConfiguration){
        this.fstConfigurationPool = fstConfiguration;
    }

    @Override
    public <T> List<byte[]> writeObjectToByteArray(List<T> pList) {
        if (pList == null || pList.size() <= 0) {
            return null;
        }
        List<byte[]> bytes = new ArrayList<>();
        for (T p : pList){
            bytes.add(writeObjectToByteArray(p));
        }
        return bytes;
    }

    @Override
    public <T> byte[] writeObjectToByteArray(T t) {
        FSTConfiguration fstConfiguration = fstConfigurationPool.obtain();
        return fstConfiguration.asByteArray(t);
    }

    @Override
    public <T> String writeObjectToString(T t) {
        byte[] bytes = writeObjectToByteArray(t);
        return Base64.encodeBase64String(bytes);
    }
}
