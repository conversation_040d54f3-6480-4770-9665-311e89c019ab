package cn.gwssi;

import cn.gwssi.deserializers.DeserializersImpl;

/**
 * Created by duq on 2019/3/20.
 */
public class DeserializerFactory extends FSTConfigurationFactory {

    private DeserializerFactory() {
        super();
    }

    @Override
    public DeserializerFactory setThreadSafe(boolean threadSafe) {
        addThreadSafe(threadSafe);
        return this;
    }

    @Override
    public DeserializerFactory setSoftReferences(boolean softReferences) {
        addSoftReferences(softReferences);
        return this;
    }

    @Override
    public DeserializerFactory setMaximunCapacity(int maximumCapacity) {
        addMaximumCapacity( maximumCapacity);
        return this;
    }

    public static DeserializerFactory builder(){
        return  new DeserializerFactory();
    }

    @Override
    public DeserializersImpl build() {
        return new DeserializersImpl(getKryoPool());
    }
}
