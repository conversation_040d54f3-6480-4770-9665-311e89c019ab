package cn.gwssi.util;

import java.lang.ref.SoftReference;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * Created by duq on 2019/3/20.
 */
public abstract class Pool<T> {

    private final Queue<T> freeObjects;
    private int peak;

    public Pool(boolean threadSafe, boolean softReferences){
        this(threadSafe, softReferences, Integer.MAX_VALUE);
    }

    public Pool(boolean threadSafe, boolean softReferences, final int maximumCapacity){
        Queue<T> queue;
        if (threadSafe){
            queue = new LinkedBlockingDeque<T>(maximumCapacity);
        }else if (softReferences){
            queue = new LinkedList() { // More efficient clean() than ArrayDeque.
                public boolean add (Object object) {
                    if (size() >= maximumCapacity) return false;
                    super.add(object);
                    return true;
                }
            };
        }else {
            queue = new ArrayDeque() {
                public boolean add (Object object) {
                    if (size() >= maximumCapacity) return false;
                    super.add(object);
                    return true;
                }
            };
        }
        freeObjects = softReferences ? new SoftReferenceQueue(queue) : queue;
    }

    abstract protected T create();

    public T obtain(){
        T object = freeObjects.poll();
        return  object != null ? object : create();
    }

    public void free(T object){
        if (object == null) throw new IllegalArgumentException("object cannot be null.");
        if (!freeObjects.offer(object) && freeObjects instanceof SoftReferenceQueue) {
            ((SoftReferenceQueue)freeObjects).cleanOne();
            freeObjects.offer(object);
        }
        peak = Math.max(peak, freeObjects.size());
        reset(object);
    }

    protected void reset (T object) {
        if (object instanceof Poolable) ((Poolable)object).reset();
    }


    static public interface Poolable {

        public void reset ();
    }

    public void clear () {
        freeObjects.clear();
    }

    public int getFree () {
        return freeObjects.size();
    }

    static class SoftReferenceQueue<T> implements Queue<T> {
        private Queue delegate;

        public SoftReferenceQueue (Queue delegate) {
            this.delegate = delegate;
        }

        public T poll () {
            while (true) {
                SoftReference<T> reference = (SoftReference<T>)delegate.poll();
                if (reference == null) return null;
                T object = reference.get();
                if (object != null) return object;
            }
        }

        public boolean offer (T e) {
            return delegate.add(new SoftReference(e));
        }

        public int size () {
            return delegate.size();
        }

        public void clear () {
            delegate.clear();
        }

        void cleanOne () {
            for (Iterator iter = delegate.iterator(); iter.hasNext();) {
                if (((SoftReference)iter.next()).get() == null) {
                    iter.remove();
                    break;
                }
            }
        }

        void clean () {
            for (Iterator iter = delegate.iterator(); iter.hasNext();)
                if (((SoftReference)iter.next()).get() == null) iter.remove();
        }

        public boolean add (T e) {
            return false;
        }

        public boolean isEmpty () {
            return false;
        }

        public boolean contains (Object o) {
            return false;
        }

        public Iterator<T> iterator () {
            return null;
        }

        public T remove () {
            return null;
        }

        public Object[] toArray () {
            return null;
        }

        public T element () {
            return null;
        }

        public T peek () {
            return null;
        }

        public <E> E[] toArray (E[] a) {
            return null;
        }

        public boolean remove (Object o) {
            return false;
        }

        public boolean containsAll (Collection c) {
            return false;
        }

        public boolean addAll (Collection<? extends T> c) {
            return false;
        }

        public boolean removeAll (Collection c) {
            return false;
        }

        public boolean retainAll (Collection c) {
            return false;
        }
    }

}
