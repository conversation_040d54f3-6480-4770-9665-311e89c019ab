Usage
------------
序列化一个对象：
``` java
 SerializersImpl serializers = SerializerFactory
                 .builder()
                 .setMaximunCapacity(10)
                 .setSoftReferences(true)//是否回收对象，默认为false
                 .setThreadSafe(true)//是否线程安全，默认为false，不安全
                 .build();
         byte[] bytes = serializers.writeObjectToByteArray(class);//序列化为数组
         String serializerStr = serializers.writeObjectToString(user);//序列化为字符串
```
反序列化：
```java
DeserializersImpl deserializers = DeserializerFactory
                .builder()
                .setMaximunCapacity(10)
                .setSoftReferences(true)
                .setThreadSafe(true)
                .build();
        MyClass myClass = deserializers.readFromByteArray(bytes, MyClass.class);
        MyClass myClass = deserializers.readFromByString(serializerStr, MyClass.class);
```
>`SerializerFactory`为链式调用结构，使用方法如下，在Maven依赖中增加以下依赖：
```xml
<dependency>
    <groupId>cn.gwssi.isearch</groupId>
    <artifactId>isearch-codec</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```
 