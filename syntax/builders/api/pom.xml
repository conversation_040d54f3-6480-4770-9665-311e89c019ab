<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>syntax-builders</artifactId>
        <groupId>cn.gwssi.isearch</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>syntax-api-builder</artifactId>
    <version>${revision}</version>
    
    <dependencies>
        <!-- 两种方式做切换 -->
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-antlr-builder</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.isearch</groupId>
            <artifactId>syntax-expr-builder</artifactId>
        </dependency>
    </dependencies>
    
    
</project>