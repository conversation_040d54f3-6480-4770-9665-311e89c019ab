// Generated from src/main/java/cn/gwssi/syntax/parser/expression/Expression.g4 by ANTLR 4.13.2
package cn.gwssi.syntax.parser.expression;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.*;
import org.antlr.v4.runtime.tree.*;
import java.util.List;
import java.util.Iterator;
import java.util.ArrayList;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class ExpressionParser extends Parser {
	static { RuntimeMetaData.checkVersion("4.13.2", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		T__0=1, VALUEESCAPE=2, CONNECTOR_ERROR=3, CONNECTOR_AND=4, CONNECTOR_OR=5, 
		CONNECTOR_XOR=6, CONNECTOR_NOT=7, CONNECTOR_IN=8, LOCATION_PRE_L=9, LOCATION_AND_L=10, 
		LOCATION_AND_E=11, STRING_QUOTES=12, UNICODE_STRING_QUOTES=13, STRING_QUOTES_SINGLE=14, 
		UNICODE_STRING_QUOTES_SINGLE=15, BRACKETS_L=16, BRACKETS_R=17, COMMA=18, 
		POWER=19, ESC=20, SPACE=21, EQ=22, EQA=23, NE=24, GT=25, GE=26, LT=27, 
		LE=28, IN=29, FREQ=30, PRE=31, EQU=32, SEN=33, SEG=34, AND_L=35, TRUECASE=36, 
		BOOST=37, INT=38, WORD=39, ANY=40, WS=41;
	public static final int
		RULE_expression = 0, RULE_statement = 1, RULE_stat = 2, RULE_statMulti = 3, 
		RULE_valueExpr = 4, RULE_field = 5, RULE_words = 6, RULE_valueFinal = 7, 
		RULE_valueIn = 8, RULE_valueInItem = 9, RULE_wordsAll = 10, RULE_wordsBoost = 11, 
		RULE_connector = 12, RULE_comparator = 13, RULE_comparator_in = 14, RULE_wordsWithBracket = 15, 
		RULE_location = 16, RULE_location_pre = 17, RULE_location_pre_l = 18, 
		RULE_location_pre_sen = 19, RULE_location_pre_seg = 20, RULE_location_equ = 21, 
		RULE_location_and = 22, RULE_location_and_e = 23, RULE_location_and_l = 24, 
		RULE_location_and_sen = 25, RULE_location_and_seg = 26, RULE_parenth_l = 27, 
		RULE_parenth_r = 28, RULE_quotedWords = 29, RULE_quotedWordsSingle = 30;
	private static String[] makeRuleNames() {
		return new String[] {
			"expression", "statement", "stat", "statMulti", "valueExpr", "field", 
			"words", "valueFinal", "valueIn", "valueInItem", "wordsAll", "wordsBoost", 
			"connector", "comparator", "comparator_in", "wordsWithBracket", "location", 
			"location_pre", "location_pre_l", "location_pre_sen", "location_pre_seg", 
			"location_equ", "location_and", "location_and_e", "location_and_l", "location_and_sen", 
			"location_and_seg", "parenth_l", "parenth_r", "quotedWords", "quotedWordsSingle"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'/'", null, null, null, null, null, null, null, null, null, null, 
			null, null, null, null, "'('", "')'", "','", "'^'", "'\\'", null, "'='", 
			"'+='", "'!='", "'>'", "'>='", "'<'", "'<='"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, null, "VALUEESCAPE", "CONNECTOR_ERROR", "CONNECTOR_AND", "CONNECTOR_OR", 
			"CONNECTOR_XOR", "CONNECTOR_NOT", "CONNECTOR_IN", "LOCATION_PRE_L", "LOCATION_AND_L", 
			"LOCATION_AND_E", "STRING_QUOTES", "UNICODE_STRING_QUOTES", "STRING_QUOTES_SINGLE", 
			"UNICODE_STRING_QUOTES_SINGLE", "BRACKETS_L", "BRACKETS_R", "COMMA", 
			"POWER", "ESC", "SPACE", "EQ", "EQA", "NE", "GT", "GE", "LT", "LE", "IN", 
			"FREQ", "PRE", "EQU", "SEN", "SEG", "AND_L", "TRUECASE", "BOOST", "INT", 
			"WORD", "ANY", "WS"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}

	@Override
	public String getGrammarFileName() { return "Expression.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public ATN getATN() { return _ATN; }

	public ExpressionParser(TokenStream input) {
		super(input);
		_interp = new ParserATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ExpressionContext extends ParserRuleContext {
		public StatementContext statement() {
			return getRuleContext(StatementContext.class,0);
		}
		public TerminalNode EOF() { return getToken(ExpressionParser.EOF, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public ExpressionContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_expression; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterExpression(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitExpression(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitExpression(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ExpressionContext expression() throws RecognitionException {
		ExpressionContext _localctx = new ExpressionContext(_ctx, getState());
		enterRule(_localctx, 0, RULE_expression);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(62);
			statement();
			setState(66);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(63);
				match(SPACE);
				}
				}
				setState(68);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(69);
			match(EOF);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatementContext extends ParserRuleContext {
		public StatementContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_statement; }
	 
		public StatementContext() { }
		public void copyFrom(StatementContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatMultipleContext extends StatementContext {
		public StatMultiContext statMulti() {
			return getRuleContext(StatMultiContext.class,0);
		}
		public StatMultipleContext(StatementContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatMultiple(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatMultiple(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatMultiple(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatSingleContext extends StatementContext {
		public StatContext stat() {
			return getRuleContext(StatContext.class,0);
		}
		public StatSingleContext(StatementContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatSingle(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatementContext statement() throws RecognitionException {
		StatementContext _localctx = new StatementContext(_ctx, getState());
		enterRule(_localctx, 2, RULE_statement);
		try {
			setState(73);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,1,_ctx) ) {
			case 1:
				_localctx = new StatSingleContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(71);
				stat();
				}
				break;
			case 2:
				_localctx = new StatMultipleContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(72);
				statMulti();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatContext extends ParserRuleContext {
		public StatContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_stat; }
	 
		public StatContext() { }
		public void copyFrom(StatContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatFinalContext extends StatContext {
		public FieldContext field() {
			return getRuleContext(FieldContext.class,0);
		}
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public StatFinalContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatFinal(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatFinal(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatFinal(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatInContext extends StatContext {
		public FieldContext field() {
			return getRuleContext(FieldContext.class,0);
		}
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueInContext valueIn() {
			return getRuleContext(ValueInContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public StatInContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatIn(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatIn(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatIn(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatNestedContext extends StatContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public StatementContext statement() {
			return getRuleContext(StatementContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public StatNestedContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatNested(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatNested(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatNested(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatOnlyValueContext extends StatContext {
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public StatOnlyValueContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatOnlyValue(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatOnlyValue(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatOnlyValue(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatContext stat() throws RecognitionException {
		StatContext _localctx = new StatContext(_ctx, getState());
		enterRule(_localctx, 4, RULE_stat);
		try {
			setState(90);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,2,_ctx) ) {
			case 1:
				_localctx = new StatNestedContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(75);
				parenth_l();
				setState(76);
				statement();
				setState(77);
				parenth_r();
				}
				break;
			case 2:
				_localctx = new StatFinalContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(79);
				field();
				setState(80);
				comparator();
				setState(81);
				valueExpr(0);
				}
				break;
			case 3:
				_localctx = new StatInContext(_localctx);
				enterOuterAlt(_localctx, 3);
				{
				setState(83);
				field();
				setState(84);
				comparator_in();
				setState(85);
				parenth_l();
				setState(86);
				valueIn();
				setState(87);
				parenth_r();
				}
				break;
			case 4:
				_localctx = new StatOnlyValueContext(_localctx);
				enterOuterAlt(_localctx, 4);
				{
				setState(89);
				valueExpr(0);
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatMultiContext extends ParserRuleContext {
		public StatMultiContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_statMulti; }
	 
		public StatMultiContext() { }
		public void copyFrom(StatMultiContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatSerialContext extends StatMultiContext {
		public List<StatContext> stat() {
			return getRuleContexts(StatContext.class);
		}
		public StatContext stat(int i) {
			return getRuleContext(StatContext.class,i);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public StatSerialContext(StatMultiContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatSerial(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatSerial(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatSerial(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatMultiContext statMulti() throws RecognitionException {
		StatMultiContext _localctx = new StatMultiContext(_ctx, getState());
		enterRule(_localctx, 6, RULE_statMulti);
		int _la;
		try {
			_localctx = new StatSerialContext(_localctx);
			enterOuterAlt(_localctx, 1);
			{
			setState(92);
			stat();
			setState(96); 
			_errHandler.sync(this);
			_la = _input.LA(1);
			do {
				{
				{
				setState(93);
				connector();
				setState(94);
				stat();
				}
				}
				setState(98); 
				_errHandler.sync(this);
				_la = _input.LA(1);
			} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0) );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueExprContext extends ParserRuleContext {
		public ValueExprContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueExpr; }
	 
		public ValueExprContext() { }
		public void copyFrom(ValueExprContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNotContext extends ValueExprContext {
		public TerminalNode CONNECTOR_NOT() { return getToken(ExpressionParser.CONNECTOR_NOT, 0); }
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public ValueNotContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNot(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNot(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNot(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueTruecaseContext extends ValueExprContext {
		public TerminalNode TRUECASE() { return getToken(ExpressionParser.TRUECASE, 0); }
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public ValueTruecaseContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueTruecase(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueTruecase(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueTruecase(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNestedContext extends ValueExprContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public ValueNestedContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNested(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNested(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNested(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueSerialContext extends ValueExprContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public List<ValueExprContext> valueExpr() {
			return getRuleContexts(ValueExprContext.class);
		}
		public ValueExprContext valueExpr(int i) {
			return getRuleContext(ValueExprContext.class,i);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public ValueSerialContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueSerial(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueSerial(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueSerial(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueFreqContext extends ValueExprContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public TerminalNode FREQ() { return getToken(ExpressionParser.FREQ, 0); }
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public ValueFreqContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueFreq(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueFreq(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueFreq(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueLocationContext extends ValueExprContext {
		public List<ValueExprContext> valueExpr() {
			return getRuleContexts(ValueExprContext.class);
		}
		public ValueExprContext valueExpr(int i) {
			return getRuleContext(ValueExprContext.class,i);
		}
		public LocationContext location() {
			return getRuleContext(LocationContext.class,0);
		}
		public ValueLocationContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueLocation(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueLocation(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueLocation(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueBoostContext extends ValueExprContext {
		public TerminalNode BOOST() { return getToken(ExpressionParser.BOOST, 0); }
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public List<WordsBoostContext> wordsBoost() {
			return getRuleContexts(WordsBoostContext.class);
		}
		public WordsBoostContext wordsBoost(int i) {
			return getRuleContext(WordsBoostContext.class,i);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public ValueBoostContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueBoost(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueBoost(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueBoost(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNormalContext extends ValueExprContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public ValueNormalContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNormal(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNormal(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNormal(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueExprContext valueExpr() throws RecognitionException {
		return valueExpr(0);
	}

	private ValueExprContext valueExpr(int _p) throws RecognitionException {
		ParserRuleContext _parentctx = _ctx;
		int _parentState = getState();
		ValueExprContext _localctx = new ValueExprContext(_ctx, _parentState);
		ValueExprContext _prevctx = _localctx;
		int _startState = 8;
		enterRecursionRule(_localctx, 8, RULE_valueExpr, _p);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(142);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,6,_ctx) ) {
			case 1:
				{
				_localctx = new ValueNestedContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;

				setState(101);
				parenth_l();
				setState(102);
				valueExpr(0);
				setState(103);
				parenth_r();
				}
				break;
			case 2:
				{
				_localctx = new ValueNotContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(105);
				match(CONNECTOR_NOT);
				setState(106);
				valueExpr(7);
				}
				break;
			case 3:
				{
				_localctx = new ValueFreqContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(107);
				valueFinal();
				setState(108);
				match(FREQ);
				setState(109);
				comparator();
				setState(110);
				match(INT);
				}
				break;
			case 4:
				{
				_localctx = new ValueTruecaseContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(112);
				match(TRUECASE);
				setState(113);
				parenth_l();
				setState(114);
				valueFinal();
				setState(115);
				parenth_r();
				}
				break;
			case 5:
				{
				_localctx = new ValueBoostContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(117);
				match(BOOST);
				setState(118);
				parenth_l();
				setState(119);
				wordsBoost();
				setState(125);
				_errHandler.sync(this);
				_la = _input.LA(1);
				while ((((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0)) {
					{
					{
					setState(120);
					connector();
					setState(121);
					wordsBoost();
					}
					}
					setState(127);
					_errHandler.sync(this);
					_la = _input.LA(1);
				}
				setState(128);
				parenth_r();
				}
				break;
			case 6:
				{
				_localctx = new ValueSerialContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(130);
				parenth_l();
				setState(131);
				valueExpr(0);
				setState(135); 
				_errHandler.sync(this);
				_la = _input.LA(1);
				do {
					{
					{
					setState(132);
					connector();
					setState(133);
					valueExpr(0);
					}
					}
					setState(137); 
					_errHandler.sync(this);
					_la = _input.LA(1);
				} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0) );
				setState(139);
				parenth_r();
				}
				break;
			case 7:
				{
				_localctx = new ValueNormalContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(141);
				valueFinal();
				}
				break;
			}
			_ctx.stop = _input.LT(-1);
			setState(150);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,7,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					if ( _parseListeners!=null ) triggerExitRuleEvent();
					_prevctx = _localctx;
					{
					{
					_localctx = new ValueLocationContext(new ValueExprContext(_parentctx, _parentState));
					pushNewRecursionContext(_localctx, _startState, RULE_valueExpr);
					setState(144);
					if (!(precpred(_ctx, 1))) throw new FailedPredicateException(this, "precpred(_ctx, 1)");
					{
					setState(145);
					location();
					setState(146);
					valueExpr(0);
					}
					}
					} 
				}
				setState(152);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,7,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			unrollRecursionContexts(_parentctx);
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class FieldContext extends ParserRuleContext {
		public List<WordsContext> words() {
			return getRuleContexts(WordsContext.class);
		}
		public WordsContext words(int i) {
			return getRuleContext(WordsContext.class,i);
		}
		public FieldContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_field; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterField(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitField(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitField(this);
			else return visitor.visitChildren(this);
		}
	}

	public final FieldContext field() throws RecognitionException {
		FieldContext _localctx = new FieldContext(_ctx, getState());
		enterRule(_localctx, 10, RULE_field);
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(154); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					setState(153);
					words();
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(156); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,8,_ctx);
			} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsContext extends ParserRuleContext {
		public TerminalNode WORD() { return getToken(ExpressionParser.WORD, 0); }
		public TerminalNode IN() { return getToken(ExpressionParser.IN, 0); }
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public TerminalNode SPACE() { return getToken(ExpressionParser.SPACE, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public TerminalNode VALUEESCAPE() { return getToken(ExpressionParser.VALUEESCAPE, 0); }
		public WordsWithBracketContext wordsWithBracket() {
			return getRuleContext(WordsWithBracketContext.class,0);
		}
		public WordsContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_words; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsContext words() throws RecognitionException {
		WordsContext _localctx = new WordsContext(_ctx, getState());
		enterRule(_localctx, 12, RULE_words);
		try {
			setState(166);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,9,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(158);
				match(WORD);
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(159);
				match(T__0);
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(160);
				match(IN);
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(161);
				comparator_in();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(162);
				match(SPACE);
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(163);
				match(INT);
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(164);
				match(VALUEESCAPE);
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(165);
				wordsWithBracket();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueFinalContext extends ParserRuleContext {
		public ValueFinalContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueFinal; }
	 
		public ValueFinalContext() { }
		public void copyFrom(ValueFinalContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueQuotedContext extends ValueFinalContext {
		public QuotedWordsContext quotedWords() {
			return getRuleContext(QuotedWordsContext.class,0);
		}
		public ValueQuotedContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueQuoted(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueQuoted(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueQuoted(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueQuotedSingleContext extends ValueFinalContext {
		public QuotedWordsSingleContext quotedWordsSingle() {
			return getRuleContext(QuotedWordsSingleContext.class,0);
		}
		public ValueQuotedSingleContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueQuotedSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueQuotedSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueQuotedSingle(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueWordsContext extends ValueFinalContext {
		public List<WordsAllContext> wordsAll() {
			return getRuleContexts(WordsAllContext.class);
		}
		public WordsAllContext wordsAll(int i) {
			return getRuleContext(WordsAllContext.class,i);
		}
		public ValueWordsContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueFinalContext valueFinal() throws RecognitionException {
		ValueFinalContext _localctx = new ValueFinalContext(_ctx, getState());
		enterRule(_localctx, 14, RULE_valueFinal);
		try {
			int _alt;
			setState(175);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,11,_ctx) ) {
			case 1:
				_localctx = new ValueQuotedContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(168);
				quotedWords();
				}
				break;
			case 2:
				_localctx = new ValueQuotedSingleContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(169);
				quotedWordsSingle();
				}
				break;
			case 3:
				_localctx = new ValueWordsContext(_localctx);
				enterOuterAlt(_localctx, 3);
				{
				setState(171); 
				_errHandler.sync(this);
				_alt = 1;
				do {
					switch (_alt) {
					case 1:
						{
						{
						setState(170);
						wordsAll();
						}
						}
						break;
					default:
						throw new NoViableAltException(this);
					}
					setState(173); 
					_errHandler.sync(this);
					_alt = getInterpreter().adaptivePredict(_input,10,_ctx);
				} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueInContext extends ParserRuleContext {
		public List<ValueInItemContext> valueInItem() {
			return getRuleContexts(ValueInItemContext.class);
		}
		public ValueInItemContext valueInItem(int i) {
			return getRuleContext(ValueInItemContext.class,i);
		}
		public List<TerminalNode> COMMA() { return getTokens(ExpressionParser.COMMA); }
		public TerminalNode COMMA(int i) {
			return getToken(ExpressionParser.COMMA, i);
		}
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public ValueInContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueIn; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueIn(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueIn(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueIn(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueInContext valueIn() throws RecognitionException {
		ValueInContext _localctx = new ValueInContext(_ctx, getState());
		enterRule(_localctx, 16, RULE_valueIn);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(177);
			valueInItem();
			setState(188);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==COMMA) {
				{
				{
				setState(178);
				match(COMMA);
				setState(182);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,12,_ctx);
				while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
					if ( _alt==1 ) {
						{
						{
						setState(179);
						match(SPACE);
						}
						} 
					}
					setState(184);
					_errHandler.sync(this);
					_alt = getInterpreter().adaptivePredict(_input,12,_ctx);
				}
				setState(185);
				valueInItem();
				}
				}
				setState(190);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueInItemContext extends ParserRuleContext {
		public ValueInItemContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueInItem; }
	 
		public ValueInItemContext() { }
		public void copyFrom(ValueInItemContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueInQuotedSingleContext extends ValueInItemContext {
		public QuotedWordsSingleContext quotedWordsSingle() {
			return getRuleContext(QuotedWordsSingleContext.class,0);
		}
		public ValueInQuotedSingleContext(ValueInItemContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueInQuotedSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueInQuotedSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueInQuotedSingle(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueInWordsContext extends ValueInItemContext {
		public List<WordsAllContext> wordsAll() {
			return getRuleContexts(WordsAllContext.class);
		}
		public WordsAllContext wordsAll(int i) {
			return getRuleContext(WordsAllContext.class,i);
		}
		public ValueInWordsContext(ValueInItemContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueInWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueInWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueInWords(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueInQuotedContext extends ValueInItemContext {
		public QuotedWordsContext quotedWords() {
			return getRuleContext(QuotedWordsContext.class,0);
		}
		public ValueInQuotedContext(ValueInItemContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueInQuoted(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueInQuoted(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueInQuoted(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueInItemContext valueInItem() throws RecognitionException {
		ValueInItemContext _localctx = new ValueInItemContext(_ctx, getState());
		enterRule(_localctx, 18, RULE_valueInItem);
		try {
			int _alt;
			setState(198);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,15,_ctx) ) {
			case 1:
				_localctx = new ValueInQuotedContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(191);
				quotedWords();
				}
				break;
			case 2:
				_localctx = new ValueInQuotedSingleContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(192);
				quotedWordsSingle();
				}
				break;
			case 3:
				_localctx = new ValueInWordsContext(_localctx);
				enterOuterAlt(_localctx, 3);
				{
				setState(194); 
				_errHandler.sync(this);
				_alt = 1;
				do {
					switch (_alt) {
					case 1:
						{
						{
						setState(193);
						wordsAll();
						}
						}
						break;
					default:
						throw new NoViableAltException(this);
					}
					setState(196); 
					_errHandler.sync(this);
					_alt = getInterpreter().adaptivePredict(_input,14,_ctx);
				} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsAllContext extends ParserRuleContext {
		public WordsContext words() {
			return getRuleContext(WordsContext.class,0);
		}
		public WordsWithBracketContext wordsWithBracket() {
			return getRuleContext(WordsWithBracketContext.class,0);
		}
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public TerminalNode VALUEESCAPE() { return getToken(ExpressionParser.VALUEESCAPE, 0); }
		public TerminalNode SPACE() { return getToken(ExpressionParser.SPACE, 0); }
		public TerminalNode ANY() { return getToken(ExpressionParser.ANY, 0); }
		public TerminalNode POWER() { return getToken(ExpressionParser.POWER, 0); }
		public TerminalNode BOOST() { return getToken(ExpressionParser.BOOST, 0); }
		public TerminalNode TRUECASE() { return getToken(ExpressionParser.TRUECASE, 0); }
		public TerminalNode ESC() { return getToken(ExpressionParser.ESC, 0); }
		public WordsAllContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsAll; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsAll(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsAll(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsAll(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsAllContext wordsAll() throws RecognitionException {
		WordsAllContext _localctx = new WordsAllContext(_ctx, getState());
		enterRule(_localctx, 20, RULE_wordsAll);
		try {
			setState(211);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,16,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(200);
				words();
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(201);
				wordsWithBracket();
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(202);
				comparator();
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(203);
				comparator_in();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(204);
				match(VALUEESCAPE);
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(205);
				match(SPACE);
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(206);
				match(ANY);
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(207);
				match(POWER);
				}
				break;
			case 9:
				enterOuterAlt(_localctx, 9);
				{
				setState(208);
				match(BOOST);
				}
				break;
			case 10:
				enterOuterAlt(_localctx, 10);
				{
				setState(209);
				match(TRUECASE);
				}
				break;
			case 11:
				enterOuterAlt(_localctx, 11);
				{
				setState(210);
				match(ESC);
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsBoostContext extends ParserRuleContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public TerminalNode POWER() { return getToken(ExpressionParser.POWER, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public WordsBoostContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsBoost; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsBoost(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsBoost(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsBoost(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsBoostContext wordsBoost() throws RecognitionException {
		WordsBoostContext _localctx = new WordsBoostContext(_ctx, getState());
		enterRule(_localctx, 22, RULE_wordsBoost);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(213);
			valueFinal();
			setState(214);
			match(POWER);
			setState(215);
			match(INT);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ConnectorContext extends ParserRuleContext {
		public TerminalNode CONNECTOR_ERROR() { return getToken(ExpressionParser.CONNECTOR_ERROR, 0); }
		public TerminalNode CONNECTOR_AND() { return getToken(ExpressionParser.CONNECTOR_AND, 0); }
		public TerminalNode CONNECTOR_OR() { return getToken(ExpressionParser.CONNECTOR_OR, 0); }
		public TerminalNode CONNECTOR_XOR() { return getToken(ExpressionParser.CONNECTOR_XOR, 0); }
		public TerminalNode CONNECTOR_NOT() { return getToken(ExpressionParser.CONNECTOR_NOT, 0); }
		public ConnectorContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_connector; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterConnector(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitConnector(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitConnector(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ConnectorContext connector() throws RecognitionException {
		ConnectorContext _localctx = new ConnectorContext(_ctx, getState());
		enterRule(_localctx, 24, RULE_connector);
		try {
			setState(223);
			_errHandler.sync(this);
			switch (_input.LA(1)) {
			case CONNECTOR_ERROR:
				enterOuterAlt(_localctx, 1);
				{
				setState(217);
				match(CONNECTOR_ERROR);
				notifyErrorListeners(_ctx.getStart(), Constant.Error.CONNECTOR_SERIAL, null);
				}
				break;
			case CONNECTOR_AND:
				enterOuterAlt(_localctx, 2);
				{
				setState(219);
				match(CONNECTOR_AND);
				}
				break;
			case CONNECTOR_OR:
				enterOuterAlt(_localctx, 3);
				{
				setState(220);
				match(CONNECTOR_OR);
				}
				break;
			case CONNECTOR_XOR:
				enterOuterAlt(_localctx, 4);
				{
				setState(221);
				match(CONNECTOR_XOR);
				}
				break;
			case CONNECTOR_NOT:
				enterOuterAlt(_localctx, 5);
				{
				setState(222);
				match(CONNECTOR_NOT);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ComparatorContext extends ParserRuleContext {
		public TerminalNode EQ() { return getToken(ExpressionParser.EQ, 0); }
		public TerminalNode EQA() { return getToken(ExpressionParser.EQA, 0); }
		public TerminalNode NE() { return getToken(ExpressionParser.NE, 0); }
		public TerminalNode GT() { return getToken(ExpressionParser.GT, 0); }
		public TerminalNode GE() { return getToken(ExpressionParser.GE, 0); }
		public TerminalNode LT() { return getToken(ExpressionParser.LT, 0); }
		public TerminalNode LE() { return getToken(ExpressionParser.LE, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public ComparatorContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_comparator; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterComparator(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitComparator(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitComparator(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ComparatorContext comparator() throws RecognitionException {
		ComparatorContext _localctx = new ComparatorContext(_ctx, getState());
		enterRule(_localctx, 26, RULE_comparator);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(228);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(225);
				match(SPACE);
				}
				}
				setState(230);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(231);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 532676608L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(235);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,19,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(232);
					match(SPACE);
					}
					} 
				}
				setState(237);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,19,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Comparator_inContext extends ParserRuleContext {
		public TerminalNode CONNECTOR_IN() { return getToken(ExpressionParser.CONNECTOR_IN, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Comparator_inContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_comparator_in; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterComparator_in(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitComparator_in(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitComparator_in(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Comparator_inContext comparator_in() throws RecognitionException {
		Comparator_inContext _localctx = new Comparator_inContext(_ctx, getState());
		enterRule(_localctx, 28, RULE_comparator_in);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(241);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(238);
				match(SPACE);
				}
				}
				setState(243);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(244);
			match(CONNECTOR_IN);
			setState(248);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,21,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(245);
					match(SPACE);
					}
					} 
				}
				setState(250);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,21,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsWithBracketContext extends ParserRuleContext {
		public List<TerminalNode> WORD() { return getTokens(ExpressionParser.WORD); }
		public TerminalNode WORD(int i) {
			return getToken(ExpressionParser.WORD, i);
		}
		public List<TerminalNode> INT() { return getTokens(ExpressionParser.INT); }
		public TerminalNode INT(int i) {
			return getToken(ExpressionParser.INT, i);
		}
		public List<TerminalNode> ANY() { return getTokens(ExpressionParser.ANY); }
		public TerminalNode ANY(int i) {
			return getToken(ExpressionParser.ANY, i);
		}
		public List<Parenth_lContext> parenth_l() {
			return getRuleContexts(Parenth_lContext.class);
		}
		public Parenth_lContext parenth_l(int i) {
			return getRuleContext(Parenth_lContext.class,i);
		}
		public List<Parenth_rContext> parenth_r() {
			return getRuleContexts(Parenth_rContext.class);
		}
		public Parenth_rContext parenth_r(int i) {
			return getRuleContext(Parenth_rContext.class,i);
		}
		public WordsWithBracketContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsWithBracket; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsWithBracket(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsWithBracket(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsWithBracket(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsWithBracketContext wordsWithBracket() throws RecognitionException {
		WordsWithBracketContext _localctx = new WordsWithBracketContext(_ctx, getState());
		enterRule(_localctx, 30, RULE_wordsWithBracket);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(251);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 1924145348608L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(257); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					setState(257);
					_errHandler.sync(this);
					switch ( getInterpreter().adaptivePredict(_input,22,_ctx) ) {
					case 1:
						{
						setState(252);
						parenth_l();
						}
						break;
					case 2:
						{
						setState(253);
						parenth_r();
						}
						break;
					case 3:
						{
						setState(254);
						match(WORD);
						}
						break;
					case 4:
						{
						setState(255);
						match(INT);
						}
						break;
					case 5:
						{
						setState(256);
						match(ANY);
						}
						break;
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(259); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,23,_ctx);
			} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
			setState(261);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 1924145348608L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class LocationContext extends ParserRuleContext {
		public Location_preContext location_pre() {
			return getRuleContext(Location_preContext.class,0);
		}
		public Location_pre_lContext location_pre_l() {
			return getRuleContext(Location_pre_lContext.class,0);
		}
		public Location_pre_senContext location_pre_sen() {
			return getRuleContext(Location_pre_senContext.class,0);
		}
		public Location_pre_segContext location_pre_seg() {
			return getRuleContext(Location_pre_segContext.class,0);
		}
		public Location_equContext location_equ() {
			return getRuleContext(Location_equContext.class,0);
		}
		public Location_andContext location_and() {
			return getRuleContext(Location_andContext.class,0);
		}
		public Location_and_eContext location_and_e() {
			return getRuleContext(Location_and_eContext.class,0);
		}
		public Location_and_lContext location_and_l() {
			return getRuleContext(Location_and_lContext.class,0);
		}
		public Location_and_senContext location_and_sen() {
			return getRuleContext(Location_and_senContext.class,0);
		}
		public Location_and_segContext location_and_seg() {
			return getRuleContext(Location_and_segContext.class,0);
		}
		public LocationContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation(this);
			else return visitor.visitChildren(this);
		}
	}

	public final LocationContext location() throws RecognitionException {
		LocationContext _localctx = new LocationContext(_ctx, getState());
		enterRule(_localctx, 32, RULE_location);
		try {
			setState(273);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,24,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(263);
				location_pre();
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(264);
				location_pre_l();
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(265);
				location_pre_sen();
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(266);
				location_pre_seg();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(267);
				location_equ();
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(268);
				location_and();
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(269);
				location_and_e();
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(270);
				location_and_l();
				}
				break;
			case 9:
				enterOuterAlt(_localctx, 9);
				{
				setState(271);
				location_and_sen();
				}
				break;
			case 10:
				enterOuterAlt(_localctx, 10);
				{
				setState(272);
				location_and_seg();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_preContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_preContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_preContext location_pre() throws RecognitionException {
		Location_preContext _localctx = new Location_preContext(_ctx, getState());
		enterRule(_localctx, 34, RULE_location_pre);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(275);
			match(SPACE);
			setState(276);
			match(PRE);
			setState(277);
			match(INT);
			setState(278);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_lContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_PRE_L() { return getToken(ExpressionParser.LOCATION_PRE_L, 0); }
		public Location_pre_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_lContext location_pre_l() throws RecognitionException {
		Location_pre_lContext _localctx = new Location_pre_lContext(_ctx, getState());
		enterRule(_localctx, 36, RULE_location_pre_l);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(280);
			match(SPACE);
			setState(281);
			match(LOCATION_PRE_L);
			setState(282);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_senContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode SEN() { return getToken(ExpressionParser.SEN, 0); }
		public Location_pre_senContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_sen; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_sen(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_sen(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_sen(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_senContext location_pre_sen() throws RecognitionException {
		Location_pre_senContext _localctx = new Location_pre_senContext(_ctx, getState());
		enterRule(_localctx, 38, RULE_location_pre_sen);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(284);
			match(SPACE);
			setState(285);
			match(PRE);
			setState(286);
			match(SEN);
			setState(287);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_segContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode SEG() { return getToken(ExpressionParser.SEG, 0); }
		public Location_pre_segContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_seg; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_seg(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_seg(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_seg(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_segContext location_pre_seg() throws RecognitionException {
		Location_pre_segContext _localctx = new Location_pre_segContext(_ctx, getState());
		enterRule(_localctx, 40, RULE_location_pre_seg);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(289);
			match(SPACE);
			setState(290);
			match(PRE);
			setState(291);
			match(SEG);
			setState(292);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_equContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode EQU() { return getToken(ExpressionParser.EQU, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_equContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_equ; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_equ(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_equ(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_equ(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_equContext location_equ() throws RecognitionException {
		Location_equContext _localctx = new Location_equContext(_ctx, getState());
		enterRule(_localctx, 42, RULE_location_equ);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(294);
			match(SPACE);
			setState(295);
			match(EQU);
			setState(296);
			match(INT);
			setState(297);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_andContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_andContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_andContext location_and() throws RecognitionException {
		Location_andContext _localctx = new Location_andContext(_ctx, getState());
		enterRule(_localctx, 44, RULE_location_and);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(299);
			match(SPACE);
			setState(300);
			match(AND_L);
			setState(301);
			match(INT);
			setState(302);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_eContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_AND_E() { return getToken(ExpressionParser.LOCATION_AND_E, 0); }
		public Location_and_eContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_e; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_e(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_e(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_e(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_eContext location_and_e() throws RecognitionException {
		Location_and_eContext _localctx = new Location_and_eContext(_ctx, getState());
		enterRule(_localctx, 46, RULE_location_and_e);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(304);
			match(SPACE);
			setState(305);
			match(LOCATION_AND_E);
			setState(306);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_lContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_AND_L() { return getToken(ExpressionParser.LOCATION_AND_L, 0); }
		public Location_and_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_lContext location_and_l() throws RecognitionException {
		Location_and_lContext _localctx = new Location_and_lContext(_ctx, getState());
		enterRule(_localctx, 48, RULE_location_and_l);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(308);
			match(SPACE);
			setState(309);
			match(LOCATION_AND_L);
			setState(310);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_senContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode SEN() { return getToken(ExpressionParser.SEN, 0); }
		public Location_and_senContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_sen; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_sen(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_sen(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_sen(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_senContext location_and_sen() throws RecognitionException {
		Location_and_senContext _localctx = new Location_and_senContext(_ctx, getState());
		enterRule(_localctx, 50, RULE_location_and_sen);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(312);
			match(SPACE);
			setState(313);
			match(AND_L);
			setState(314);
			match(SEN);
			setState(315);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_segContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode SEG() { return getToken(ExpressionParser.SEG, 0); }
		public Location_and_segContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_seg; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_seg(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_seg(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_seg(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_segContext location_and_seg() throws RecognitionException {
		Location_and_segContext _localctx = new Location_and_segContext(_ctx, getState());
		enterRule(_localctx, 52, RULE_location_and_seg);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(317);
			match(SPACE);
			setState(318);
			match(AND_L);
			setState(319);
			match(SEG);
			setState(320);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Parenth_lContext extends ParserRuleContext {
		public TerminalNode BRACKETS_L() { return getToken(ExpressionParser.BRACKETS_L, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Parenth_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_parenth_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterParenth_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitParenth_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitParenth_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Parenth_lContext parenth_l() throws RecognitionException {
		Parenth_lContext _localctx = new Parenth_lContext(_ctx, getState());
		enterRule(_localctx, 54, RULE_parenth_l);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(325);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(322);
				match(SPACE);
				}
				}
				setState(327);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(328);
			match(BRACKETS_L);
			setState(332);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,26,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(329);
					match(SPACE);
					}
					} 
				}
				setState(334);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,26,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Parenth_rContext extends ParserRuleContext {
		public TerminalNode BRACKETS_R() { return getToken(ExpressionParser.BRACKETS_R, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Parenth_rContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_parenth_r; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterParenth_r(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitParenth_r(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitParenth_r(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Parenth_rContext parenth_r() throws RecognitionException {
		Parenth_rContext _localctx = new Parenth_rContext(_ctx, getState());
		enterRule(_localctx, 56, RULE_parenth_r);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(338);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(335);
				match(SPACE);
				}
				}
				setState(340);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(341);
			match(BRACKETS_R);
			setState(345);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,28,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(342);
					match(SPACE);
					}
					} 
				}
				setState(347);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,28,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class QuotedWordsContext extends ParserRuleContext {
		public TerminalNode STRING_QUOTES() { return getToken(ExpressionParser.STRING_QUOTES, 0); }
		public TerminalNode UNICODE_STRING_QUOTES() { return getToken(ExpressionParser.UNICODE_STRING_QUOTES, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public QuotedWordsContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_quotedWords; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterQuotedWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitQuotedWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitQuotedWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final QuotedWordsContext quotedWords() throws RecognitionException {
		QuotedWordsContext _localctx = new QuotedWordsContext(_ctx, getState());
		enterRule(_localctx, 58, RULE_quotedWords);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(351);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(348);
				match(SPACE);
				}
				}
				setState(353);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(354);
			_la = _input.LA(1);
			if ( !(_la==STRING_QUOTES || _la==UNICODE_STRING_QUOTES) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(358);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,30,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(355);
					match(SPACE);
					}
					} 
				}
				setState(360);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,30,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class QuotedWordsSingleContext extends ParserRuleContext {
		public TerminalNode STRING_QUOTES_SINGLE() { return getToken(ExpressionParser.STRING_QUOTES_SINGLE, 0); }
		public TerminalNode UNICODE_STRING_QUOTES_SINGLE() { return getToken(ExpressionParser.UNICODE_STRING_QUOTES_SINGLE, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public QuotedWordsSingleContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_quotedWordsSingle; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterQuotedWordsSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitQuotedWordsSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitQuotedWordsSingle(this);
			else return visitor.visitChildren(this);
		}
	}

	public final QuotedWordsSingleContext quotedWordsSingle() throws RecognitionException {
		QuotedWordsSingleContext _localctx = new QuotedWordsSingleContext(_ctx, getState());
		enterRule(_localctx, 60, RULE_quotedWordsSingle);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(364);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(361);
				match(SPACE);
				}
				}
				setState(366);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(367);
			_la = _input.LA(1);
			if ( !(_la==STRING_QUOTES_SINGLE || _la==UNICODE_STRING_QUOTES_SINGLE) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(371);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,32,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(368);
					match(SPACE);
					}
					} 
				}
				setState(373);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,32,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	public boolean sempred(RuleContext _localctx, int ruleIndex, int predIndex) {
		switch (ruleIndex) {
		case 4:
			return valueExpr_sempred((ValueExprContext)_localctx, predIndex);
		}
		return true;
	}
	private boolean valueExpr_sempred(ValueExprContext _localctx, int predIndex) {
		switch (predIndex) {
		case 0:
			return precpred(_ctx, 1);
		}
		return true;
	}

	public static final String _serializedATN =
		"\u0004\u0001)\u0177\u0002\u0000\u0007\u0000\u0002\u0001\u0007\u0001\u0002"+
		"\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004\u0007\u0004\u0002"+
		"\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007\u0007\u0007\u0002"+
		"\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b\u0007\u000b\u0002"+
		"\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002\u000f\u0007\u000f"+
		"\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002\u0012\u0007\u0012"+
		"\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002\u0015\u0007\u0015"+
		"\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002\u0018\u0007\u0018"+
		"\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002\u001b\u0007\u001b"+
		"\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002\u001e\u0007\u001e"+
		"\u0001\u0000\u0001\u0000\u0005\u0000A\b\u0000\n\u0000\f\u0000D\t\u0000"+
		"\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0003\u0001J\b\u0001"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0003\u0002[\b\u0002\u0001\u0003"+
		"\u0001\u0003\u0001\u0003\u0001\u0003\u0004\u0003a\b\u0003\u000b\u0003"+
		"\f\u0003b\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0005\u0004|\b\u0004\n\u0004\f\u0004\u007f\t\u0004\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0004"+
		"\u0004\u0088\b\u0004\u000b\u0004\f\u0004\u0089\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0003\u0004\u008f\b\u0004\u0001\u0004\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0005\u0004\u0095\b\u0004\n\u0004\f\u0004\u0098\t\u0004\u0001"+
		"\u0005\u0004\u0005\u009b\b\u0005\u000b\u0005\f\u0005\u009c\u0001\u0006"+
		"\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006"+
		"\u0001\u0006\u0003\u0006\u00a7\b\u0006\u0001\u0007\u0001\u0007\u0001\u0007"+
		"\u0004\u0007\u00ac\b\u0007\u000b\u0007\f\u0007\u00ad\u0003\u0007\u00b0"+
		"\b\u0007\u0001\b\u0001\b\u0001\b\u0005\b\u00b5\b\b\n\b\f\b\u00b8\t\b\u0001"+
		"\b\u0005\b\u00bb\b\b\n\b\f\b\u00be\t\b\u0001\t\u0001\t\u0001\t\u0004\t"+
		"\u00c3\b\t\u000b\t\f\t\u00c4\u0003\t\u00c7\b\t\u0001\n\u0001\n\u0001\n"+
		"\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0003"+
		"\n\u00d4\b\n\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\f\u0001"+
		"\f\u0001\f\u0001\f\u0001\f\u0001\f\u0003\f\u00e0\b\f\u0001\r\u0005\r\u00e3"+
		"\b\r\n\r\f\r\u00e6\t\r\u0001\r\u0001\r\u0005\r\u00ea\b\r\n\r\f\r\u00ed"+
		"\t\r\u0001\u000e\u0005\u000e\u00f0\b\u000e\n\u000e\f\u000e\u00f3\t\u000e"+
		"\u0001\u000e\u0001\u000e\u0005\u000e\u00f7\b\u000e\n\u000e\f\u000e\u00fa"+
		"\t\u000e\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f\u0001"+
		"\u000f\u0004\u000f\u0102\b\u000f\u000b\u000f\f\u000f\u0103\u0001\u000f"+
		"\u0001\u000f\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010"+
		"\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010\u0003\u0010"+
		"\u0112\b\u0010\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0011"+
		"\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013"+
		"\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0014\u0001\u0014\u0001\u0014"+
		"\u0001\u0014\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0015"+
		"\u0001\u0015\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0016"+
		"\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0018\u0001\u0018"+
		"\u0001\u0018\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u0019\u0001\u0019"+
		"\u0001\u0019\u0001\u001a\u0001\u001a\u0001\u001a\u0001\u001a\u0001\u001a"+
		"\u0001\u001b\u0005\u001b\u0144\b\u001b\n\u001b\f\u001b\u0147\t\u001b\u0001"+
		"\u001b\u0001\u001b\u0005\u001b\u014b\b\u001b\n\u001b\f\u001b\u014e\t\u001b"+
		"\u0001\u001c\u0005\u001c\u0151\b\u001c\n\u001c\f\u001c\u0154\t\u001c\u0001"+
		"\u001c\u0001\u001c\u0005\u001c\u0158\b\u001c\n\u001c\f\u001c\u015b\t\u001c"+
		"\u0001\u001d\u0005\u001d\u015e\b\u001d\n\u001d\f\u001d\u0161\t\u001d\u0001"+
		"\u001d\u0001\u001d\u0005\u001d\u0165\b\u001d\n\u001d\f\u001d\u0168\t\u001d"+
		"\u0001\u001e\u0005\u001e\u016b\b\u001e\n\u001e\f\u001e\u016e\t\u001e\u0001"+
		"\u001e\u0001\u001e\u0005\u001e\u0172\b\u001e\n\u001e\f\u001e\u0175\t\u001e"+
		"\u0001\u001e\u0000\u0001\b\u001f\u0000\u0002\u0004\u0006\b\n\f\u000e\u0010"+
		"\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.02468:<\u0000\u0004"+
		"\u0001\u0000\u0016\u001c\u0001\u0000&(\u0001\u0000\f\r\u0001\u0000\u000e"+
		"\u000f\u019e\u0000>\u0001\u0000\u0000\u0000\u0002I\u0001\u0000\u0000\u0000"+
		"\u0004Z\u0001\u0000\u0000\u0000\u0006\\\u0001\u0000\u0000\u0000\b\u008e"+
		"\u0001\u0000\u0000\u0000\n\u009a\u0001\u0000\u0000\u0000\f\u00a6\u0001"+
		"\u0000\u0000\u0000\u000e\u00af\u0001\u0000\u0000\u0000\u0010\u00b1\u0001"+
		"\u0000\u0000\u0000\u0012\u00c6\u0001\u0000\u0000\u0000\u0014\u00d3\u0001"+
		"\u0000\u0000\u0000\u0016\u00d5\u0001\u0000\u0000\u0000\u0018\u00df\u0001"+
		"\u0000\u0000\u0000\u001a\u00e4\u0001\u0000\u0000\u0000\u001c\u00f1\u0001"+
		"\u0000\u0000\u0000\u001e\u00fb\u0001\u0000\u0000\u0000 \u0111\u0001\u0000"+
		"\u0000\u0000\"\u0113\u0001\u0000\u0000\u0000$\u0118\u0001\u0000\u0000"+
		"\u0000&\u011c\u0001\u0000\u0000\u0000(\u0121\u0001\u0000\u0000\u0000*"+
		"\u0126\u0001\u0000\u0000\u0000,\u012b\u0001\u0000\u0000\u0000.\u0130\u0001"+
		"\u0000\u0000\u00000\u0134\u0001\u0000\u0000\u00002\u0138\u0001\u0000\u0000"+
		"\u00004\u013d\u0001\u0000\u0000\u00006\u0145\u0001\u0000\u0000\u00008"+
		"\u0152\u0001\u0000\u0000\u0000:\u015f\u0001\u0000\u0000\u0000<\u016c\u0001"+
		"\u0000\u0000\u0000>B\u0003\u0002\u0001\u0000?A\u0005\u0015\u0000\u0000"+
		"@?\u0001\u0000\u0000\u0000AD\u0001\u0000\u0000\u0000B@\u0001\u0000\u0000"+
		"\u0000BC\u0001\u0000\u0000\u0000CE\u0001\u0000\u0000\u0000DB\u0001\u0000"+
		"\u0000\u0000EF\u0005\u0000\u0000\u0001F\u0001\u0001\u0000\u0000\u0000"+
		"GJ\u0003\u0004\u0002\u0000HJ\u0003\u0006\u0003\u0000IG\u0001\u0000\u0000"+
		"\u0000IH\u0001\u0000\u0000\u0000J\u0003\u0001\u0000\u0000\u0000KL\u0003"+
		"6\u001b\u0000LM\u0003\u0002\u0001\u0000MN\u00038\u001c\u0000N[\u0001\u0000"+
		"\u0000\u0000OP\u0003\n\u0005\u0000PQ\u0003\u001a\r\u0000QR\u0003\b\u0004"+
		"\u0000R[\u0001\u0000\u0000\u0000ST\u0003\n\u0005\u0000TU\u0003\u001c\u000e"+
		"\u0000UV\u00036\u001b\u0000VW\u0003\u0010\b\u0000WX\u00038\u001c\u0000"+
		"X[\u0001\u0000\u0000\u0000Y[\u0003\b\u0004\u0000ZK\u0001\u0000\u0000\u0000"+
		"ZO\u0001\u0000\u0000\u0000ZS\u0001\u0000\u0000\u0000ZY\u0001\u0000\u0000"+
		"\u0000[\u0005\u0001\u0000\u0000\u0000\\`\u0003\u0004\u0002\u0000]^\u0003"+
		"\u0018\f\u0000^_\u0003\u0004\u0002\u0000_a\u0001\u0000\u0000\u0000`]\u0001"+
		"\u0000\u0000\u0000ab\u0001\u0000\u0000\u0000b`\u0001\u0000\u0000\u0000"+
		"bc\u0001\u0000\u0000\u0000c\u0007\u0001\u0000\u0000\u0000de\u0006\u0004"+
		"\uffff\uffff\u0000ef\u00036\u001b\u0000fg\u0003\b\u0004\u0000gh\u0003"+
		"8\u001c\u0000h\u008f\u0001\u0000\u0000\u0000ij\u0005\u0007\u0000\u0000"+
		"j\u008f\u0003\b\u0004\u0007kl\u0003\u000e\u0007\u0000lm\u0005\u001e\u0000"+
		"\u0000mn\u0003\u001a\r\u0000no\u0005&\u0000\u0000o\u008f\u0001\u0000\u0000"+
		"\u0000pq\u0005$\u0000\u0000qr\u00036\u001b\u0000rs\u0003\u000e\u0007\u0000"+
		"st\u00038\u001c\u0000t\u008f\u0001\u0000\u0000\u0000uv\u0005%\u0000\u0000"+
		"vw\u00036\u001b\u0000w}\u0003\u0016\u000b\u0000xy\u0003\u0018\f\u0000"+
		"yz\u0003\u0016\u000b\u0000z|\u0001\u0000\u0000\u0000{x\u0001\u0000\u0000"+
		"\u0000|\u007f\u0001\u0000\u0000\u0000}{\u0001\u0000\u0000\u0000}~\u0001"+
		"\u0000\u0000\u0000~\u0080\u0001\u0000\u0000\u0000\u007f}\u0001\u0000\u0000"+
		"\u0000\u0080\u0081\u00038\u001c\u0000\u0081\u008f\u0001\u0000\u0000\u0000"+
		"\u0082\u0083\u00036\u001b\u0000\u0083\u0087\u0003\b\u0004\u0000\u0084"+
		"\u0085\u0003\u0018\f\u0000\u0085\u0086\u0003\b\u0004\u0000\u0086\u0088"+
		"\u0001\u0000\u0000\u0000\u0087\u0084\u0001\u0000\u0000\u0000\u0088\u0089"+
		"\u0001\u0000\u0000\u0000\u0089\u0087\u0001\u0000\u0000\u0000\u0089\u008a"+
		"\u0001\u0000\u0000\u0000\u008a\u008b\u0001\u0000\u0000\u0000\u008b\u008c"+
		"\u00038\u001c\u0000\u008c\u008f\u0001\u0000\u0000\u0000\u008d\u008f\u0003"+
		"\u000e\u0007\u0000\u008ed\u0001\u0000\u0000\u0000\u008ei\u0001\u0000\u0000"+
		"\u0000\u008ek\u0001\u0000\u0000\u0000\u008ep\u0001\u0000\u0000\u0000\u008e"+
		"u\u0001\u0000\u0000\u0000\u008e\u0082\u0001\u0000\u0000\u0000\u008e\u008d"+
		"\u0001\u0000\u0000\u0000\u008f\u0096\u0001\u0000\u0000\u0000\u0090\u0091"+
		"\n\u0001\u0000\u0000\u0091\u0092\u0003 \u0010\u0000\u0092\u0093\u0003"+
		"\b\u0004\u0000\u0093\u0095\u0001\u0000\u0000\u0000\u0094\u0090\u0001\u0000"+
		"\u0000\u0000\u0095\u0098\u0001\u0000\u0000\u0000\u0096\u0094\u0001\u0000"+
		"\u0000\u0000\u0096\u0097\u0001\u0000\u0000\u0000\u0097\t\u0001\u0000\u0000"+
		"\u0000\u0098\u0096\u0001\u0000\u0000\u0000\u0099\u009b\u0003\f\u0006\u0000"+
		"\u009a\u0099\u0001\u0000\u0000\u0000\u009b\u009c\u0001\u0000\u0000\u0000"+
		"\u009c\u009a\u0001\u0000\u0000\u0000\u009c\u009d\u0001\u0000\u0000\u0000"+
		"\u009d\u000b\u0001\u0000\u0000\u0000\u009e\u00a7\u0005\'\u0000\u0000\u009f"+
		"\u00a7\u0005\u0001\u0000\u0000\u00a0\u00a7\u0005\u001d\u0000\u0000\u00a1"+
		"\u00a7\u0003\u001c\u000e\u0000\u00a2\u00a7\u0005\u0015\u0000\u0000\u00a3"+
		"\u00a7\u0005&\u0000\u0000\u00a4\u00a7\u0005\u0002\u0000\u0000\u00a5\u00a7"+
		"\u0003\u001e\u000f\u0000\u00a6\u009e\u0001\u0000\u0000\u0000\u00a6\u009f"+
		"\u0001\u0000\u0000\u0000\u00a6\u00a0\u0001\u0000\u0000\u0000\u00a6\u00a1"+
		"\u0001\u0000\u0000\u0000\u00a6\u00a2\u0001\u0000\u0000\u0000\u00a6\u00a3"+
		"\u0001\u0000\u0000\u0000\u00a6\u00a4\u0001\u0000\u0000\u0000\u00a6\u00a5"+
		"\u0001\u0000\u0000\u0000\u00a7\r\u0001\u0000\u0000\u0000\u00a8\u00b0\u0003"+
		":\u001d\u0000\u00a9\u00b0\u0003<\u001e\u0000\u00aa\u00ac\u0003\u0014\n"+
		"\u0000\u00ab\u00aa\u0001\u0000\u0000\u0000\u00ac\u00ad\u0001\u0000\u0000"+
		"\u0000\u00ad\u00ab\u0001\u0000\u0000\u0000\u00ad\u00ae\u0001\u0000\u0000"+
		"\u0000\u00ae\u00b0\u0001\u0000\u0000\u0000\u00af\u00a8\u0001\u0000\u0000"+
		"\u0000\u00af\u00a9\u0001\u0000\u0000\u0000\u00af\u00ab\u0001\u0000\u0000"+
		"\u0000\u00b0\u000f\u0001\u0000\u0000\u0000\u00b1\u00bc\u0003\u0012\t\u0000"+
		"\u00b2\u00b6\u0005\u0012\u0000\u0000\u00b3\u00b5\u0005\u0015\u0000\u0000"+
		"\u00b4\u00b3\u0001\u0000\u0000\u0000\u00b5\u00b8\u0001\u0000\u0000\u0000"+
		"\u00b6\u00b4\u0001\u0000\u0000\u0000\u00b6\u00b7\u0001\u0000\u0000\u0000"+
		"\u00b7\u00b9\u0001\u0000\u0000\u0000\u00b8\u00b6\u0001\u0000\u0000\u0000"+
		"\u00b9\u00bb\u0003\u0012\t\u0000\u00ba\u00b2\u0001\u0000\u0000\u0000\u00bb"+
		"\u00be\u0001\u0000\u0000\u0000\u00bc\u00ba\u0001\u0000\u0000\u0000\u00bc"+
		"\u00bd\u0001\u0000\u0000\u0000\u00bd\u0011\u0001\u0000\u0000\u0000\u00be"+
		"\u00bc\u0001\u0000\u0000\u0000\u00bf\u00c7\u0003:\u001d\u0000\u00c0\u00c7"+
		"\u0003<\u001e\u0000\u00c1\u00c3\u0003\u0014\n\u0000\u00c2\u00c1\u0001"+
		"\u0000\u0000\u0000\u00c3\u00c4\u0001\u0000\u0000\u0000\u00c4\u00c2\u0001"+
		"\u0000\u0000\u0000\u00c4\u00c5\u0001\u0000\u0000\u0000\u00c5\u00c7\u0001"+
		"\u0000\u0000\u0000\u00c6\u00bf\u0001\u0000\u0000\u0000\u00c6\u00c0\u0001"+
		"\u0000\u0000\u0000\u00c6\u00c2\u0001\u0000\u0000\u0000\u00c7\u0013\u0001"+
		"\u0000\u0000\u0000\u00c8\u00d4\u0003\f\u0006\u0000\u00c9\u00d4\u0003\u001e"+
		"\u000f\u0000\u00ca\u00d4\u0003\u001a\r\u0000\u00cb\u00d4\u0003\u001c\u000e"+
		"\u0000\u00cc\u00d4\u0005\u0002\u0000\u0000\u00cd\u00d4\u0005\u0015\u0000"+
		"\u0000\u00ce\u00d4\u0005(\u0000\u0000\u00cf\u00d4\u0005\u0013\u0000\u0000"+
		"\u00d0\u00d4\u0005%\u0000\u0000\u00d1\u00d4\u0005$\u0000\u0000\u00d2\u00d4"+
		"\u0005\u0014\u0000\u0000\u00d3\u00c8\u0001\u0000\u0000\u0000\u00d3\u00c9"+
		"\u0001\u0000\u0000\u0000\u00d3\u00ca\u0001\u0000\u0000\u0000\u00d3\u00cb"+
		"\u0001\u0000\u0000\u0000\u00d3\u00cc\u0001\u0000\u0000\u0000\u00d3\u00cd"+
		"\u0001\u0000\u0000\u0000\u00d3\u00ce\u0001\u0000\u0000\u0000\u00d3\u00cf"+
		"\u0001\u0000\u0000\u0000\u00d3\u00d0\u0001\u0000\u0000\u0000\u00d3\u00d1"+
		"\u0001\u0000\u0000\u0000\u00d3\u00d2\u0001\u0000\u0000\u0000\u00d4\u0015"+
		"\u0001\u0000\u0000\u0000\u00d5\u00d6\u0003\u000e\u0007\u0000\u00d6\u00d7"+
		"\u0005\u0013\u0000\u0000\u00d7\u00d8\u0005&\u0000\u0000\u00d8\u0017\u0001"+
		"\u0000\u0000\u0000\u00d9\u00da\u0005\u0003\u0000\u0000\u00da\u00e0\u0006"+
		"\f\uffff\uffff\u0000\u00db\u00e0\u0005\u0004\u0000\u0000\u00dc\u00e0\u0005"+
		"\u0005\u0000\u0000\u00dd\u00e0\u0005\u0006\u0000\u0000\u00de\u00e0\u0005"+
		"\u0007\u0000\u0000\u00df\u00d9\u0001\u0000\u0000\u0000\u00df\u00db\u0001"+
		"\u0000\u0000\u0000\u00df\u00dc\u0001\u0000\u0000\u0000\u00df\u00dd\u0001"+
		"\u0000\u0000\u0000\u00df\u00de\u0001\u0000\u0000\u0000\u00e0\u0019\u0001"+
		"\u0000\u0000\u0000\u00e1\u00e3\u0005\u0015\u0000\u0000\u00e2\u00e1\u0001"+
		"\u0000\u0000\u0000\u00e3\u00e6\u0001\u0000\u0000\u0000\u00e4\u00e2\u0001"+
		"\u0000\u0000\u0000\u00e4\u00e5\u0001\u0000\u0000\u0000\u00e5\u00e7\u0001"+
		"\u0000\u0000\u0000\u00e6\u00e4\u0001\u0000\u0000\u0000\u00e7\u00eb\u0007"+
		"\u0000\u0000\u0000\u00e8\u00ea\u0005\u0015\u0000\u0000\u00e9\u00e8\u0001"+
		"\u0000\u0000\u0000\u00ea\u00ed\u0001\u0000\u0000\u0000\u00eb\u00e9\u0001"+
		"\u0000\u0000\u0000\u00eb\u00ec\u0001\u0000\u0000\u0000\u00ec\u001b\u0001"+
		"\u0000\u0000\u0000\u00ed\u00eb\u0001\u0000\u0000\u0000\u00ee\u00f0\u0005"+
		"\u0015\u0000\u0000\u00ef\u00ee\u0001\u0000\u0000\u0000\u00f0\u00f3\u0001"+
		"\u0000\u0000\u0000\u00f1\u00ef\u0001\u0000\u0000\u0000\u00f1\u00f2\u0001"+
		"\u0000\u0000\u0000\u00f2\u00f4\u0001\u0000\u0000\u0000\u00f3\u00f1\u0001"+
		"\u0000\u0000\u0000\u00f4\u00f8\u0005\b\u0000\u0000\u00f5\u00f7\u0005\u0015"+
		"\u0000\u0000\u00f6\u00f5\u0001\u0000\u0000\u0000\u00f7\u00fa\u0001\u0000"+
		"\u0000\u0000\u00f8\u00f6\u0001\u0000\u0000\u0000\u00f8\u00f9\u0001\u0000"+
		"\u0000\u0000\u00f9\u001d\u0001\u0000\u0000\u0000\u00fa\u00f8\u0001\u0000"+
		"\u0000\u0000\u00fb\u0101\u0007\u0001\u0000\u0000\u00fc\u0102\u00036\u001b"+
		"\u0000\u00fd\u0102\u00038\u001c\u0000\u00fe\u0102\u0005\'\u0000\u0000"+
		"\u00ff\u0102\u0005&\u0000\u0000\u0100\u0102\u0005(\u0000\u0000\u0101\u00fc"+
		"\u0001\u0000\u0000\u0000\u0101\u00fd\u0001\u0000\u0000\u0000\u0101\u00fe"+
		"\u0001\u0000\u0000\u0000\u0101\u00ff\u0001\u0000\u0000\u0000\u0101\u0100"+
		"\u0001\u0000\u0000\u0000\u0102\u0103\u0001\u0000\u0000\u0000\u0103\u0101"+
		"\u0001\u0000\u0000\u0000\u0103\u0104\u0001\u0000\u0000\u0000\u0104\u0105"+
		"\u0001\u0000\u0000\u0000\u0105\u0106\u0007\u0001\u0000\u0000\u0106\u001f"+
		"\u0001\u0000\u0000\u0000\u0107\u0112\u0003\"\u0011\u0000\u0108\u0112\u0003"+
		"$\u0012\u0000\u0109\u0112\u0003&\u0013\u0000\u010a\u0112\u0003(\u0014"+
		"\u0000\u010b\u0112\u0003*\u0015\u0000\u010c\u0112\u0003,\u0016\u0000\u010d"+
		"\u0112\u0003.\u0017\u0000\u010e\u0112\u00030\u0018\u0000\u010f\u0112\u0003"+
		"2\u0019\u0000\u0110\u0112\u00034\u001a\u0000\u0111\u0107\u0001\u0000\u0000"+
		"\u0000\u0111\u0108\u0001\u0000\u0000\u0000\u0111\u0109\u0001\u0000\u0000"+
		"\u0000\u0111\u010a\u0001\u0000\u0000\u0000\u0111\u010b\u0001\u0000\u0000"+
		"\u0000\u0111\u010c\u0001\u0000\u0000\u0000\u0111\u010d\u0001\u0000\u0000"+
		"\u0000\u0111\u010e\u0001\u0000\u0000\u0000\u0111\u010f\u0001\u0000\u0000"+
		"\u0000\u0111\u0110\u0001\u0000\u0000\u0000\u0112!\u0001\u0000\u0000\u0000"+
		"\u0113\u0114\u0005\u0015\u0000\u0000\u0114\u0115\u0005\u001f\u0000\u0000"+
		"\u0115\u0116\u0005&\u0000\u0000\u0116\u0117\u0005\u0015\u0000\u0000\u0117"+
		"#\u0001\u0000\u0000\u0000\u0118\u0119\u0005\u0015\u0000\u0000\u0119\u011a"+
		"\u0005\t\u0000\u0000\u011a\u011b\u0005\u0015\u0000\u0000\u011b%\u0001"+
		"\u0000\u0000\u0000\u011c\u011d\u0005\u0015\u0000\u0000\u011d\u011e\u0005"+
		"\u001f\u0000\u0000\u011e\u011f\u0005!\u0000\u0000\u011f\u0120\u0005\u0015"+
		"\u0000\u0000\u0120\'\u0001\u0000\u0000\u0000\u0121\u0122\u0005\u0015\u0000"+
		"\u0000\u0122\u0123\u0005\u001f\u0000\u0000\u0123\u0124\u0005\"\u0000\u0000"+
		"\u0124\u0125\u0005\u0015\u0000\u0000\u0125)\u0001\u0000\u0000\u0000\u0126"+
		"\u0127\u0005\u0015\u0000\u0000\u0127\u0128\u0005 \u0000\u0000\u0128\u0129"+
		"\u0005&\u0000\u0000\u0129\u012a\u0005\u0015\u0000\u0000\u012a+\u0001\u0000"+
		"\u0000\u0000\u012b\u012c\u0005\u0015\u0000\u0000\u012c\u012d\u0005#\u0000"+
		"\u0000\u012d\u012e\u0005&\u0000\u0000\u012e\u012f\u0005\u0015\u0000\u0000"+
		"\u012f-\u0001\u0000\u0000\u0000\u0130\u0131\u0005\u0015\u0000\u0000\u0131"+
		"\u0132\u0005\u000b\u0000\u0000\u0132\u0133\u0005\u0015\u0000\u0000\u0133"+
		"/\u0001\u0000\u0000\u0000\u0134\u0135\u0005\u0015\u0000\u0000\u0135\u0136"+
		"\u0005\n\u0000\u0000\u0136\u0137\u0005\u0015\u0000\u0000\u01371\u0001"+
		"\u0000\u0000\u0000\u0138\u0139\u0005\u0015\u0000\u0000\u0139\u013a\u0005"+
		"#\u0000\u0000\u013a\u013b\u0005!\u0000\u0000\u013b\u013c\u0005\u0015\u0000"+
		"\u0000\u013c3\u0001\u0000\u0000\u0000\u013d\u013e\u0005\u0015\u0000\u0000"+
		"\u013e\u013f\u0005#\u0000\u0000\u013f\u0140\u0005\"\u0000\u0000\u0140"+
		"\u0141\u0005\u0015\u0000\u0000\u01415\u0001\u0000\u0000\u0000\u0142\u0144"+
		"\u0005\u0015\u0000\u0000\u0143\u0142\u0001\u0000\u0000\u0000\u0144\u0147"+
		"\u0001\u0000\u0000\u0000\u0145\u0143\u0001\u0000\u0000\u0000\u0145\u0146"+
		"\u0001\u0000\u0000\u0000\u0146\u0148\u0001\u0000\u0000\u0000\u0147\u0145"+
		"\u0001\u0000\u0000\u0000\u0148\u014c\u0005\u0010\u0000\u0000\u0149\u014b"+
		"\u0005\u0015\u0000\u0000\u014a\u0149\u0001\u0000\u0000\u0000\u014b\u014e"+
		"\u0001\u0000\u0000\u0000\u014c\u014a\u0001\u0000\u0000\u0000\u014c\u014d"+
		"\u0001\u0000\u0000\u0000\u014d7\u0001\u0000\u0000\u0000\u014e\u014c\u0001"+
		"\u0000\u0000\u0000\u014f\u0151\u0005\u0015\u0000\u0000\u0150\u014f\u0001"+
		"\u0000\u0000\u0000\u0151\u0154\u0001\u0000\u0000\u0000\u0152\u0150\u0001"+
		"\u0000\u0000\u0000\u0152\u0153\u0001\u0000\u0000\u0000\u0153\u0155\u0001"+
		"\u0000\u0000\u0000\u0154\u0152\u0001\u0000\u0000\u0000\u0155\u0159\u0005"+
		"\u0011\u0000\u0000\u0156\u0158\u0005\u0015\u0000\u0000\u0157\u0156\u0001"+
		"\u0000\u0000\u0000\u0158\u015b\u0001\u0000\u0000\u0000\u0159\u0157\u0001"+
		"\u0000\u0000\u0000\u0159\u015a\u0001\u0000\u0000\u0000\u015a9\u0001\u0000"+
		"\u0000\u0000\u015b\u0159\u0001\u0000\u0000\u0000\u015c\u015e\u0005\u0015"+
		"\u0000\u0000\u015d\u015c\u0001\u0000\u0000\u0000\u015e\u0161\u0001\u0000"+
		"\u0000\u0000\u015f\u015d\u0001\u0000\u0000\u0000\u015f\u0160\u0001\u0000"+
		"\u0000\u0000\u0160\u0162\u0001\u0000\u0000\u0000\u0161\u015f\u0001\u0000"+
		"\u0000\u0000\u0162\u0166\u0007\u0002\u0000\u0000\u0163\u0165\u0005\u0015"+
		"\u0000\u0000\u0164\u0163\u0001\u0000\u0000\u0000\u0165\u0168\u0001\u0000"+
		"\u0000\u0000\u0166\u0164\u0001\u0000\u0000\u0000\u0166\u0167\u0001\u0000"+
		"\u0000\u0000\u0167;\u0001\u0000\u0000\u0000\u0168\u0166\u0001\u0000\u0000"+
		"\u0000\u0169\u016b\u0005\u0015\u0000\u0000\u016a\u0169\u0001\u0000\u0000"+
		"\u0000\u016b\u016e\u0001\u0000\u0000\u0000\u016c\u016a\u0001\u0000\u0000"+
		"\u0000\u016c\u016d\u0001\u0000\u0000\u0000\u016d\u016f\u0001\u0000\u0000"+
		"\u0000\u016e\u016c\u0001\u0000\u0000\u0000\u016f\u0173\u0007\u0003\u0000"+
		"\u0000\u0170\u0172\u0005\u0015\u0000\u0000\u0171\u0170\u0001\u0000\u0000"+
		"\u0000\u0172\u0175\u0001\u0000\u0000\u0000\u0173\u0171\u0001\u0000\u0000"+
		"\u0000\u0173\u0174\u0001\u0000\u0000\u0000\u0174=\u0001\u0000\u0000\u0000"+
		"\u0175\u0173\u0001\u0000\u0000\u0000!BIZb}\u0089\u008e\u0096\u009c\u00a6"+
		"\u00ad\u00af\u00b6\u00bc\u00c4\u00c6\u00d3\u00df\u00e4\u00eb\u00f1\u00f8"+
		"\u0101\u0103\u0111\u0145\u014c\u0152\u0159\u015f\u0166\u016c\u0173";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}