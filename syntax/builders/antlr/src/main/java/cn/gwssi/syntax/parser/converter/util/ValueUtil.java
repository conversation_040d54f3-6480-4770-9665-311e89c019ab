package cn.gwssi.syntax.parser.converter.util;

import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueFreq;
import cn.gwssi.syntax.condition.scannerValue.ValueRange;
import cn.gwssi.syntax.condition.scannerValue.tool.ValueTool;
import cn.gwssi.syntax.parser.converter.error.ErrorMsg;
import cn.gwssi.syntax.parser.converter.error.ParseCancelWithDetailException;
import cn.gwssi.syntax.parser.converter.visitor.StatVisitor;
import cn.gwssi.syntax.parser.converter.visitor.StringValueVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.antlr.v4.runtime.ParserRuleContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValueUtil {

    // 临时替换使用
    private final String DONT_SPLIT = "_DON'T_SPLIT_";

    private final ValueTool tool = ValueTool.getInstance();
    private final StatVisitor visitor;
    private final StringValueVisitor strVisitor;

    public ValueUtil(StatVisitor statVisitor, StringValueVisitor strVisitor) {
        this.visitor = statVisitor;
        this.strVisitor = strVisitor;
    }

    private final static Pattern PATTERN_TO = Pattern.compile(IPConditionConstants.KEYWORDS_TO, Pattern.CASE_INSENSITIVE);
    private final static Pattern PATTERN_TO_SUB = Pattern.compile(IPConditionConstants.KEYWORDS_TO_SUB, Pattern.CASE_INSENSITIVE);

    // in，要拆分为多个
    public Value in(ExpressionParser.ValueInContext ctx, String value) {
        value = value.trim();
        if (StringUtils.isBlank(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.VALUE_NULL);
            throw new ParseCancelWithDetailException(error);
        }

        // trim，去重
        value = value.replaceAll("\\\\,", DONT_SPLIT);
        String[] values = Arrays.stream(value.split(",")).map(item -> item.replaceAll(DONT_SPLIT, ",").trim()).distinct().toArray(String[]::new);

        Value finalValue = new Value(values);
        finalValue.setIn(true);
        return finalValue;
    }

    // 双引号的内容
    public Value quoteWords(ExpressionParser.ValueQuotedContext ctx, String value) {
        Value finalValue = new Value(value);

        if (!tool.isQuotation(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, IPConditionConstants.KEYWORDS_QUOTES_DOUBLE, Constant.Error.QUOTATION_NOT_MATCH);
            throw new ParseCancelWithDetailException(error);
        } else {
//            value = StringUtils.strip(value, IPConditionConstants.KEYWORDS_QUOTES_DOUBLE);
            // FIXED 2025-04-08 去除头尾的引号，strip 会去除多个相同字符
            value = value.substring(1);
            value = value.substring(0, value.length() - 1);
            finalValue.setHasQuotation(true);
            finalValue.setValue(value);
        }

        // 去除引号后再判断
        if (StringUtils.isBlank(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.VALUE_NULL);
            throw new ParseCancelWithDetailException(error);
        }

        this.addValue(value);
        return finalValue;
    }

    // 单引号的内容
    public Value quoteWordsSingle(ExpressionParser.ValueQuotedSingleContext ctx, String value) {
        if (!tool.isQuotationSingle(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, IPConditionConstants.KEYWORDS_QUOTES_SINGLE, Constant.Error.QUOTATION_NOT_MATCH);
            throw new ParseCancelWithDetailException(error);
        }

        // 去除单引号
//        value = StringUtils.strip(value, IPConditionConstants.KEYWORDS_QUOTES_SINGLE);
        // FIXED 2025-04-08 去除头尾的引号，strip 会去除多个相同字符
        value = value.substring(1);
        value = value.substring(0, value.length() - 1);
        return this.words(ctx, value, true);
    }

    // 通配符、to、中英文
    public Value words(ParserRuleContext ctx, String value, boolean onlyWildcard) {
        Value finalValue = new Value(value);
        if (StringUtils.isBlank(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.VALUE_NULL);
            throw new ParseCancelWithDetailException(error);
        }

        // 是否是向量
        String field = strVisitor.getProperty(Constant.Property.FIELD);
        boolean isVector = "R".equalsIgnoreCase(field) || "P".equalsIgnoreCase(field) || "RAD".equalsIgnoreCase(field) || "RPD".equalsIgnoreCase(field);

        // 向量的内容，不需要再判断，直接使用
        if (isVector) {
            this.addValue(value);
            return finalValue;
        }

        // 单引号中的内容，跳过 to 解析
        // TO 先用简单正则判断，然后用另一个正则获取分组。正则 (.*) 在判断图片base64时有问题会引发死循环
        if (!onlyWildcard && PATTERN_TO.matcher(value).find()) {
            Matcher matcher_to_sub = PATTERN_TO_SUB.matcher(value);
            if (matcher_to_sub.find()) {
                String valueP = matcher_to_sub.group(1);
                String valueS = matcher_to_sub.group(2);
                ValueRange range = new ValueRange(valueP, valueS);
                finalValue.setRange(true);
                finalValue.setRange(range);

                this.addValue(valueP);
                this.addValue(valueS);
                return finalValue;
            }
        }

        // 中文
        boolean hasZh = tool.hasZh(value);
        if (hasZh) {
            finalValue.setHasZh(true);
        }

        // 通配符
        if (tool.hasWildCard(value)) {
            finalValue.setHasWildCard(true);

//            // FIXED 2023-12-06 去掉限制
//            // 包含通配符时，不能中英文混合
//            if (hasZh && tool.hasEn(value)) {
//                ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.WILDCARD_CANNOT_MIX);
//                throw new ParseCancelWithDetailException(error);
//            }
        }

        this.addValue(value);
        return finalValue;
    }

    // 词频 freq
    public Value freq(ExpressionParser.ValueFreqContext ctx, String value, String comparator) {
        // 判断次数
        String text = ctx.INT().getText().trim();
        int count = Integer.parseInt(text);
        if (count <= 0) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.NUMBER_GT_0);
            throw new ParseCancelWithDetailException(error);
        }

        // 支持英文通配符，不支持中文通配符
        boolean hasWildcard = tool.hasWildCard(value);
        boolean hasZh = tool.hasZh(value);
        if (hasWildcard && hasZh) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.FREQ_WILDCARD_ZH);
            throw new ParseCancelWithDetailException(error);
        }

        Value finalValue = new Value();
        ItemOperator operator = ItemOperator.getOperator(comparator);
        ValueFreq freq = new ValueFreq(value, operator, count, hasWildcard, hasZh);

        finalValue.setFreq(true);
        finalValue.setFreq(freq);

        this.addValue(value);
        return finalValue;
    }

    // truecase
    public Value truecase(ExpressionParser.ValueTruecaseContext ctx, String value) {
        if (StringUtils.isBlank(value)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.VALUE_NULL);
            throw new ParseCancelWithDetailException(error);
        }

        Value finalValue = new Value(value);
        finalValue.setTrueCase(true);

        this.addValue(value);
        return finalValue;
    }

    public Value boost(ExpressionParser.WordsBoostContext ctx, Value value, float boost) {
        if (boost < 0 || boost > 10) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.BOOST_VALUE_RANGE);
            throw new ParseCancelWithDetailException(error);
        }

        value.setBoost(boost);
        return value;
    }

    private void addValue(String value) {
        visitor.addValue(value);
        visitor.addKeyValue(strVisitor.getProperty(Constant.Property.FIELD), value);
    }
}
