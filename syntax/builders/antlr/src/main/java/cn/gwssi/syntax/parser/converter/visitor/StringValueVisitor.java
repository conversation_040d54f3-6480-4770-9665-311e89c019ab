package cn.gwssi.syntax.parser.converter.visitor;

import cn.gwssi.syntax.condition.parser.ParserEscape;
import cn.gwssi.syntax.parser.converter.util.Constant;
import cn.gwssi.syntax.parser.expression.ExpressionBaseVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;

import java.util.HashMap;
import java.util.Map;

public class StringValueVisitor extends ExpressionBaseVisitor<String> {

    private Map<String, String> property = new HashMap<>();

    @Override
    public String visitField(ExpressionParser.FieldContext ctx) {
        String value = ctx.getText().trim().toLowerCase();
        this.setPropertyOfField(value);
        return value;
    }

    @Override
    public String visitComparator(ExpressionParser.ComparatorContext ctx) {
        String comparator = ctx.getText().trim().toLowerCase();
        this.setPropertyOfComparator(comparator);
        return comparator;
    }

    @Override
    public String visitComparator_in(ExpressionParser.Comparator_inContext ctx) {
        String comparator = ctx.getText().trim().toLowerCase();
        this.setPropertyOfComparator(comparator);
        return comparator;
    }

    @Override
    public String visitConnector(ExpressionParser.ConnectorContext ctx) {
        return ctx.getText().trim().toUpperCase();
    }

    @Override
    public String visitValueIn(ExpressionParser.ValueInContext ctx) {
        String value = ctx.getText().trim();
        return ParserEscape.escapeRemove(value);
    }

    @Override
    public String visitValueQuoted(ExpressionParser.ValueQuotedContext ctx) {
        String value = ctx.getText().trim();
        return ParserEscape.escapeRemoveQuotes(value);
    }

    @Override
    public String visitValueQuotedSingle(ExpressionParser.ValueQuotedSingleContext ctx) {
        String value = ctx.getText().trim();
        return ParserEscape.escapeRemoveQuotesSingle(value);
    }

    @Override
    public String visitValueWords(ExpressionParser.ValueWordsContext ctx) {
        String value = ctx.getText().trim();
        return ParserEscape.escapeRemove(value);
    }

    @Override
    public String visitValueLocation(ExpressionParser.ValueLocationContext ctx) {
        String value = ctx.getText().trim();
        return ParserEscape.escapeRemove(value);
    }

    @Override
    public String visitLocation(ExpressionParser.LocationContext ctx) {
        return ctx.getText().trim();
    }

    public String getProperty(String property) {
        return this.property.get(property);
    }

    public void setPropertyOfField(String field) {
        field = ParserEscape.escapeRemove(field).trim().toLowerCase();
        this.property.put(Constant.Property.FIELD, field);
    }

    public void setPropertyOfComparator(String comparator) {
        this.property.put(Constant.Property.COMPARATOR, comparator.trim().toLowerCase());
    }

}
