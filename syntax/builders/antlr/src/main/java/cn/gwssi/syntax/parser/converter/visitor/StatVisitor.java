package cn.gwssi.syntax.parser.converter.visitor;

import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.ScannerItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;
import cn.gwssi.syntax.parser.converter.entity.LocationValue;
import cn.gwssi.syntax.parser.converter.error.ErrorMsg;
import cn.gwssi.syntax.parser.converter.error.ParseCancelWithDetailException;
import cn.gwssi.syntax.parser.converter.util.Constant;
import cn.gwssi.syntax.parser.converter.util.IPConditionUtil;
import cn.gwssi.syntax.parser.converter.util.ValueLocationUtil;
import cn.gwssi.syntax.parser.expression.ExpressionBaseVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import org.antlr.v4.runtime.RuleContext;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * <p>
 * 解析过程中，除了 fragment、token， 慎用 {@link RuleContext#getText} 方法
 * 这个方法返回的值可能包含了空格，大小写也可能是混写
 * 一定要使用的话，要注意处理空格、大小写
 * <p>
 * IPCondition 是一个树形结构，叶子节点是 {@link ScannerItemCondition}
 * 解析 stat 时不生成叶子节点，只组织父子关系
 * 生成叶子节点的操作，放在解析 value 的时候，每个 value 生成一个 ScannerItemCondition
 * 例如：解析 a = (b and c)
 * 1、解析 stat 时，设置当前字段a，当前操作符=
 * 2、解析 b 时，获取当前字段a，当前操作符=，当前值b，生成叶子节点 ScannerItemCondition
 * 3、解析 c 时，获取当前字段a，当前操作符=，当前值c，生成叶子节点 ScannerItemCondition
 */
public class StatVisitor extends ExpressionBaseVisitor<IPCondition> {

    /**
     * 提取表达式的值，例如：a = (b or c or d)，提取 [b,c,d]
     * 1、单引号提取，例如：'b' 提取 b
     * 2、truecase提取，例如：truecase(b) 提取 b
     * 3、freq提取，例如：b/freq 提取 b
     * 4、to忽略，例如：1 to 3 不提取
     * 5、通配符忽略，例如：b%c 不提取
     * 6、位置提取，例如 b pre/2 c 提取 [b,c]
     */
    private final Set<String> values = new HashSet<>();

    /**
     * 提取表达式的值和字段
     */
    private final Multimap<String, String> keyValues = ArrayListMultimap.create();

    private final StringValueVisitor strVisitor = new StringValueVisitor();
    private final ItemValueVisitor itemVisitor = new ItemValueVisitor(strVisitor, this);
    private final LocationVisitor locationVisitor = new LocationVisitor();
    private final IPConditionUtil util = new IPConditionUtil(strVisitor);
    private final ValueLocationUtil locationUtil = new ValueLocationUtil(strVisitor, itemVisitor, locationVisitor, util);

    // 默认字段
    private String defaultField;

    public StatVisitor() {}
    public StatVisitor(String defaultField) {
        this.defaultField = defaultField;
    }

    /********************************** 解析 stat，只组织父子关系，不生成叶子节点 **********************************/

    /**
     * 直接走到下一层处理，例如：
     * A=B
     */
    @Override
    public IPCondition visitExpression(ExpressionParser.ExpressionContext ctx) {
        return this.visit(ctx.statement());
    }

//    /**
//     * 直接走到下一层处理，例如：
//     * (A=B OR C=D)
//     */
//    @Override
//    public IPCondition visitStatMultiNested(ExpressionParser.StatMultiNestedContext ctx) {
//        return this.visit(ctx.statMulti());
//    }

    /**
     * 直接走到下一层处理，例如：
     * (A=B)
     */
    @Override
    public IPCondition visitStatNested(ExpressionParser.StatNestedContext ctx) {
        return this.visit(ctx.statement());
    }

    /**
     * 解析的实际入口。上层的 statNested、statSerial 等都会落到这个 visitor。例如：
     * A=B
     */
    @Override
    public IPCondition visitStatFinal(ExpressionParser.StatFinalContext ctx) {
        IPCondition condition = new IPCondition();

        // visitor 模式必须手动调用，才能访问到
        // 作为解析的入口，调用一次就可以了
        String field = strVisitor.visit(ctx.field());
        String comparator = strVisitor.visit(ctx.comparator());

        // 记录子表达式的位置
        // 这样处理不是很好，应该分开记录字段、操作符、值的位置
        // 但是字段、操作符目前不是对象，值对象new的地方太多，有些值在new的时候已经没有ctx了，所以记在子表达式里
        // 有个想法，或许可以解决这个问题。TODO：
        // 这里记录一下字段、操作符的位置，值对象new的时候记录值的位置，然后在转为 StatementItemCondition 的做合并
        // 上一级的三个位置和本级的三个位置做合并，本级的位置覆盖上一级的位置
        ConditionOffset fieldOffset = ErrorMsg.getInstance().extractOffset(ctx.field());
        ConditionOffset comparatorOffset = ErrorMsg.getInstance().extractOffset(ctx.comparator());
        ConditionOffset valueOffset = ErrorMsg.getInstance().extractOffset(ctx.valueExpr());

        // 如果操作符是 +=，替换为 =，拆分成 or
        // 例如 A,B+=C，拆分为 A=C or B=C
        if (comparator.equals(ItemOperator.ITEM_OPERATOR_OR.getName())) {
            // 操作符替换为 =
            strVisitor.setPropertyOfComparator(ItemOperator.ITEM_OPERATOR_EQ.getName());

            String[] fields = field.split(",");
            for (String everyField : fields) {
                strVisitor.setPropertyOfField(everyField);
                IPCondition subCondition = this.visit(ctx.valueExpr());

                // 修改 fieldOffSet，使用分隔后的位置
                fieldOffset = this.splitOffset(fieldOffset, field, everyField);
                this.setOffset(subCondition, fieldOffset, comparatorOffset, valueOffset);
                condition.addSubCondition(subCondition);
            }
            for (int i = 0; i < fields.length - 1; i++) {
                condition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
            }
        } else {
            IPCondition subCondition = this.visit(ctx.valueExpr());
            this.setOffset(subCondition, fieldOffset, comparatorOffset, valueOffset);
            condition.addSubCondition(subCondition);
        }

        return condition;
    }

    /**
     * 多个连续的stat。例如：
     * A=B or C=D or E=F
     * 类似 {@link #visitValueSerial}
     */
    @Override
    public IPCondition visitStatSerial(ExpressionParser.StatSerialContext ctx) {
        IPCondition ipCondition = new IPCondition();
        List<ExpressionParser.StatContext> stats = ctx.stat();
        List<ExpressionParser.ConnectorContext> connectors = ctx.connector();

        // 所有stat
        for (ExpressionParser.StatContext stat : stats) {
            IPCondition subCondition = this.visit(stat);
            ipCondition.addSubCondition(subCondition);
        }
        // 所有连接符
        for (ExpressionParser.ConnectorContext connector : connectors) {
            String connectorText = strVisitor.visit(connector);
            ItemConnector itemConnector = ItemConnector.getConnector(connectorText);
            ipCondition.addConnector(itemConnector);
        }

        return ipCondition;
    }

    /**
     * 使用 in 的stat，解析的另一个入口。例如：
     * A,B IN (C, D, E)
     */
    @Override
    public IPCondition visitStatIn(ExpressionParser.StatInContext ctx) {
        String field = strVisitor.visit(ctx.field());
        strVisitor.visit(ctx.comparator_in());
        Value value = itemVisitor.visit(ctx.valueIn());

        // 记录子表达式的位置
        ConditionOffset fieldOffset = ErrorMsg.getInstance().extractOffset(ctx.field());
        ConditionOffset comparatorOffset = ErrorMsg.getInstance().extractOffset(ctx.comparator_in());
        ConditionOffset valueOffset = ErrorMsg.getInstance().extractOffset(ctx.valueIn());

        if (!field.contains(",")) {
            IPCondition condition = util.geneIPCondition(value);
            this.setOffset(condition, fieldOffset, comparatorOffset, valueOffset);
            return condition;
        }

        // 处理多字段
        IPCondition condition = new IPCondition();
        String[] fields = field.split(",");
        for (String everyField : fields) {
            strVisitor.setPropertyOfField(everyField);
            IPCondition subCondition = util.geneIPCondition(value);

            // 修改 fieldOffSet，使用分隔后的位置
            fieldOffset = this.splitOffset(fieldOffset, field, everyField);
            this.setOffset(subCondition, fieldOffset, comparatorOffset, valueOffset);
            condition.addSubCondition(subCondition);
        }
        for (int i = 0; i < fields.length - 1; i++) {
            condition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
        }

        return condition;
    }

    /**
     * 没有字段的使用默认字段，例如：
     * A
     */
    @Override
    public IPCondition visitStatOnlyValue(ExpressionParser.StatOnlyValueContext ctx) {
        if (StringUtils.isEmpty(defaultField)) {
            ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.DEFAULT_FIELD_CANNOT_NULL);
            throw new ParseCancelWithDetailException(error);
        }

        strVisitor.setPropertyOfField(defaultField);
        strVisitor.setPropertyOfComparator(ItemOperator.ITEM_OPERATOR_EQ.getName());
        IPCondition condition = this.visit(ctx.valueExpr());

        // 记录子表达式的位置
        ConditionOffset valueOffset = ErrorMsg.getInstance().extractOffset(ctx.valueExpr());
        condition.setOffsetOf(IPConditionConstants.ConditionType.VALUE, valueOffset);

        return condition;
    }

    /********************************** 解析 value，生成叶子节点 **********************************/

    /**
     * 直接走到下一层处理，例如：
     * (B or C)
     */
    @Override
    public IPCondition visitValueNested(ExpressionParser.ValueNestedContext ctx) {
        return this.visit(ctx.valueExpr());
    }

    /**
     * 解析简单值。例如：
     * A=B 中的 B
     */
    @Override
    public IPCondition visitValueNormal(ExpressionParser.ValueNormalContext ctx) {
        Value value = itemVisitor.visit(ctx.valueFinal());
        return util.geneIPCondition(value);
    }

    /**
     * 解析 not 值。例如：
     * not (A and B)
     */
    @Override
    public IPCondition visitValueNot(ExpressionParser.ValueNotContext ctx) {
        IPCondition condition = this.visit(ctx.valueExpr());

        if (condition.isLeafCondition()) {
            // FIXED 2024-01-03 取反
            ((ScannerItemCondition)condition).setReverse(true);
        } else {
            // FIXED 2024-01-03 connectors 一般会比 subConditions 少一个
            // 当两者数量相同，且最后一个 connector = not 时，需要取反
            // 例如 not (b or c)
            condition.addConnector(ItemConnector.ITEM_CONNECTOR_NOT);
        }

        return condition;
    }

    /**
     * 解析词频。例如：
     * A=B /freq > 4
     */
    @Override
    public IPCondition visitValueFreq(ExpressionParser.ValueFreqContext ctx) {
        Value value = itemVisitor.visit(ctx);
        return util.geneIPCondition(value);
    }

    /**
     * 解析truecase。例如：
     * A=truecase(B)
     */
    @Override
    public IPCondition visitValueTruecase(ExpressionParser.ValueTruecaseContext ctx) {
        Value value = itemVisitor.visit(ctx);
        return util.geneIPCondition(value);
    }

    /**
     * 解析 boost。例如：
     * A=boost(B^4 AND C^1)
     * 类似 {@link #visitStatSerial}
     */
    @Override
    public IPCondition visitValueBoost(ExpressionParser.ValueBoostContext ctx) {
        IPCondition condition = new IPCondition();

        // 设置字段和操作符，处理虚拟字段的时候要用到
        util.setConditionField(condition);
        util.setConditionComparator(condition);

        List<ExpressionParser.ConnectorContext> connectors = ctx.connector();
        List<ExpressionParser.WordsBoostContext> words = ctx.wordsBoost();

        // 所有value
        for (ExpressionParser.WordsBoostContext word : words) {
            Value value = itemVisitor.visit(word);
            IPCondition subCondition = util.geneIPCondition(value);
            condition.addSubCondition(subCondition);
        }
        // 所有连接符
        for (ExpressionParser.ConnectorContext connector : connectors) {
            String connectorText = strVisitor.visit(connector);
            ItemConnector itemConnector = ItemConnector.getConnector(connectorText);
            condition.addConnector(itemConnector);
        }

        return condition;
    }

    /**
     * 解析多值，例如
     * (b or c)
     * (b not (c or (d and e) or f))
     * 类似 {@link #visitStatSerial}
     */
    @Override
    public IPCondition visitValueSerial(ExpressionParser.ValueSerialContext ctx) {
        IPCondition condition = new IPCondition();

        // 设置字段和操作符，处理虚拟字段的时候要用到
        util.setConditionField(condition);
        util.setConditionComparator(condition);

        List<ExpressionParser.ValueExprContext> values = ctx.valueExpr();
        List<ExpressionParser.ConnectorContext> connectors = ctx.connector();

        // 所有value
        for (ExpressionParser.ValueExprContext value : values) {
            IPCondition subCondition = this.visit(value);
            condition.addSubCondition(subCondition);
        }
        // 所有连接符
        for (ExpressionParser.ConnectorContext connector : connectors) {
            String connectorText = strVisitor.visit(connector);
            ItemConnector itemConnector = ItemConnector.getConnector(connectorText);
            condition.addConnector(itemConnector);
        }

        return condition;
    }

//    /**
//     * 解析 not 值。例如：
//     * a not b
//     * a not (b and (c or d))
//     */
//    @Override
//    public IPCondition visitValueNot(ExpressionParser.ValueNotContext ctx) {
//        IPCondition condition = new IPCondition();
//
//        // 例如：a not (b and (c or d)) 中的 a
//        Value value = itemVisitor.visit(ctx.valueFinal());
//        IPCondition preCondition = util.geneIPCondition(value);
//        // 例如：a not (b and (c or d)) 中的 (b and (c or d))
//        IPCondition suffixCondition = this.visit(ctx.valueExpr());
//
//        condition.addSubCondition(preCondition);
//        condition.addSubCondition(suffixCondition);
//        condition.addConnector(ItemConnector.ITEM_CONNECTOR_NOT);
//
//        return condition;
//    }

    /**
     * 解析位置运算
     * 一、对于单一位置运算：
     * 1、前后布尔运算符相同，都是 or 或都是 and
     * 2、前后布尔运算符只有 or and
     * 3、布尔运算符连接的值都是简单的 A、B、C，没有嵌套布尔运算或位置运算
     * 例如：
     * a pre/1 b
     * (a or b) pre/1 c
     * a pre/1 (b or c)
     * (a or b) pre/1 (c or d)
     * 二、对于连续位置运算，值必须是简单的 a、b、c、((a))，不能嵌套其它复杂值，例如：
     * a pre/1 b equ/2 c
     * ((a)) pre/1 (b) equ/2 c
     */
    @Override
    public IPCondition visitValueLocation(ExpressionParser.ValueLocationContext ctx) {
        List<ExpressionParser.LocationContext> locations = new ArrayList<>();
        List<ExpressionParser.ValueExprContext> values = new ArrayList<>();
        // FIXED 2022-12-29 由于语法文件变更，这里手动扫描全部位置运算
        locationUtil.findAllLocations(ctx, locations, values);

        // 单一位置运算，1、检查运算符，2、检查值，3、变形
        if (locations.size() == 1) {
            if (values.size() != 2) {
                ParserError error = ErrorMsg.getInstance().format(ctx, "", Constant.Error.LOCATION_VALUE_ERROR);
                throw new ParseCancelWithDetailException(error);
            }

            // 对于单一位置运算，检查运算符，检查值
            LocationValue frontLocation = locationUtil.checkEveryValue(values.get(0));
            LocationValue endLocation = locationUtil.checkEveryValue(values.get(1));

            // 按情况变形
            ValueLocation locationOperator = locationVisitor.visit(locations.get(0));
            // 例如：a pre/1 b
            if (frontLocation.isSimple() && endLocation.isSimple()) {
                Value frontValue = frontLocation.getValues().get(0);
                Value endValue = endLocation.getValues().get(0);
                Value itemValue = util.geneItemValue(frontValue, endValue, locationOperator);
                return util.geneIPCondition(itemValue);
            }

            // 例如：a pre/1 (b or c)
            // (a or b) pre/1 c
            // (a or b) pre/1 (c or d)
            return locationUtil.reCompose(ctx, frontLocation, endLocation, locationOperator);
        } else {
            // 连续位置运算，检查运算符是否合法，检查值是否合法，例如：a pre/1 b equ/2 c
            List<ValueLocation> locationList = locationUtil.checkOperatorForSerial(ctx, locations);
            List<Value> valueList = locationUtil.checkValueForSerial(values);

            Value itemValue = new Value(valueList);
            itemValue.setLocation(true);
            itemValue.setLocations(locationList);

            return util.geneIPCondition(itemValue);
        }
    }

    /********************************** 异常处理 **********************************/

//    @Override
//    public IPCondition visitStatNestedError3(ExpressionParser.StatNestedError3Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, false));
//    }
//    @Override
//    public IPCondition visitValueNestedError1(ExpressionParser.ValueNestedError1Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, true));
//    }
//    @Override
//    public IPCondition visitValueNestedError3(ExpressionParser.ValueNestedError3Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, false));
//    }
//    @Override
//    public IPCondition visitValueTruecaseError1(ExpressionParser.ValueTruecaseError1Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, true));
//    }
//    @Override
//    public IPCondition visitValueTruecaseError3(ExpressionParser.ValueTruecaseError3Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, false));
//    }
//    @Override
//    public IPCondition visitValueBoostError1(ExpressionParser.ValueBoostError1Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, true));
//    }
//    @Override
//    public IPCondition visitValueBoostError3(ExpressionParser.ValueBoostError3Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, false));
//    }
//    @Override
//    public IPCondition visitValueSerialError3(ExpressionParser.ValueSerialError3Context ctx) {
//        throw new ParseCancelWithDetailException(ErrorMsg.getInstance().format(ctx, false));
//    }

    public void addValue(String value) {
        this.values.add(value);
    }

    public void addValues(Set<String> value) {
        this.values.addAll(value);
    }

    public Set<String> getValues() {
        return this.values;
    }

    public void addKeyValue(String key, String value) {
        keyValues.put(key, value);
    }

    public Multimap<String, String> getKeyValues() {
        return this.keyValues;
    }

    private void setOffset(IPCondition condition, ConditionOffset fieldOffset, ConditionOffset comparatorOffset, ConditionOffset valueOffset) {
        condition.setOffsetOf(IPConditionConstants.ConditionType.FIELD, fieldOffset);
        condition.setOffsetOf(IPConditionConstants.ConditionType.COMPARATOR, comparatorOffset);
        condition.setOffsetOf(IPConditionConstants.ConditionType.VALUE, valueOffset);
    }

    // 多值字段修改 fieldOffset
    private ConditionOffset splitOffset(ConditionOffset fieldOffset, String field, String everyField) {
        int start = field.indexOf(everyField);
        int end = start + everyField.length();

        ConditionOffset offset = new ConditionOffset();
        offset.setStartLine(fieldOffset.getStartLine());
        offset.setEndLine(fieldOffset.getEndLine());
        offset.setStartInLine(start);
        offset.setEndInLine(end);
        offset.setStart(start);
        offset.setEnd(end);
        offset.setChars(everyField);

        return offset;
    }

}
