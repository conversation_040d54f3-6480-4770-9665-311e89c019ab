package cn.gwssi.syntax.parser.converter.error;

import org.antlr.v4.runtime.NoViableAltException;
import org.antlr.v4.runtime.Parser;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.TokenStream;

public class RecognitionWithDetailException extends NoViableAltException {

    private String chars;
    private String message;

    public RecognitionWithDetailException(NoViableAltException e) {
        super((Parser) e.getRecognizer(), (TokenStream)e.getInputStream(), e.getStartToken(), e.getOffendingToken(), e.getDeadEndConfigs(), (ParserRuleContext) e.getCtx());
    }

    public String getChars() {
        return chars;
    }

    public void setChars(String chars) {
        this.chars = chars;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
