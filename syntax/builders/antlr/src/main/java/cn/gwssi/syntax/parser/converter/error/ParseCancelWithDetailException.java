package cn.gwssi.syntax.parser.converter.error;

import cn.gwssi.syntax.condition.parser.ParserError;
import org.antlr.v4.runtime.misc.ParseCancellationException;

public class ParseCancelWithDetailException extends ParseCancellationException {

    private ParserError parserError;

    public ParseCancelWithDetailException() {
        super();
    }

    public ParseCancelWithDetailException(String message) {
        super(message);
    }

    public ParseCancelWithDetailException(Throwable cause) {
        super(cause);
    }

    public ParseCancelWithDetailException(String message, Throwable cause) {
        super(message, cause);
    }

    public ParseCancelWithDetailException(ParserError parserError) {
        super(parserError.getWholeMsg());
        this.parserError = parserError;
    }

    public ParserError getParserError() {
        return parserError;
    }

    public void setParserError(ParserError parserError) {
        this.parserError = parserError;
    }
}
