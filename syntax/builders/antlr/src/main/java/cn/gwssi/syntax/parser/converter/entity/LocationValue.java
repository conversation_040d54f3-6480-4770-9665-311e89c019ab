package cn.gwssi.syntax.parser.converter.entity;

import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.scannerValue.Value;

import java.util.List;

public class LocationValue {

    private boolean isSimple;
    private ItemConnector connector;
    private List<Value> values;

    public LocationValue() {
        this.isSimple = true;
        this.connector = null;
    }

    public LocationValue(boolean isSimple, ItemConnector connector) {
        this.isSimple = isSimple;
        this.connector = connector;
    }

    public boolean isSimple() {
        return isSimple;
    }

    public void setSimple(boolean simple) {
        isSimple = simple;
    }

    public ItemConnector getConnector() {
        return connector;
    }

    public void setConnector(String connector) {
        this.connector = ItemConnector.getConnector(connector);
    }

    public List<Value> getValues() {
        return values;
    }

    public void setValues(List<Value> values) {
        this.values = values;
    }
}
