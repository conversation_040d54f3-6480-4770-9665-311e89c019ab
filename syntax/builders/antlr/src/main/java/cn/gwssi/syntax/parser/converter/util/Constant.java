package cn.gwssi.syntax.parser.converter.util;

public class Constant {

    public static class Error {

        public static final String LOCATION_ERROR = "无法解析的位置运算符！";
        public static final String LOCATION_VALUE_ERROR = "无法解析的位置运算！";
        public static final String LOCATION_NO_ORDER_CANNOT_SERIAL = "连续使用位置运算符时，不支持无序位置运算符！";
        public static final String LOCATION_SEG_SEN_CANNOT_SERIAL = "连续使用位置运算符时，同句/同段不能和其他位置算符混用！";
        public static final String LOCATION_NESTED_CANNOT_SERIAL = "连续使用位置运算符时，不支持其它运算的嵌套！";
        public static final String LOCATION_VALUE_CANNOT_NESTED = "位置运算两边的值，不支持嵌套运算！";
        public static final String LOCATION_VALUE_CONNECTOR_MUST_EQUAL = "位置运算两边的值，逻辑运算符必须完全相同！";
        public static final String LOCATION_VALUE_CONNECTOR_MUST_LEGAL = "位置运算两边的值，逻辑运算符只支持 and、or！";

        public static final String CONNECTOR_SERIAL = "逻辑运算符不能连续使用！";
        public static final String VALUE_NULL = "值不能为空！";
        public static final String QUOTATION_NOT_MATCH = "引号不匹配！";
        public static final String NUMBER_GT_0 = "次数必须大于0！";
        public static final String BRACKET_NOT_MATCH = "括号不匹配！";
        public static final String CONNECTOR_ERROR = "逻辑运算符不能结尾！";
        public static final String COMPARATOR_ERROR = "非法的操作符！";
        public static final String ESCAPE_ERROR = "非法的转义符！";

        public static final String WILDCARD_CANNOT_MIX = "通配符不能使用中英文混合！";
        public static final String FREQ_WILDCARD_ZH = "频率算符不能使用中文通配符！";
        public static final String BOOST_VALUE_RANGE = "权重的值必须在0~10之间！";

        public static final String DEFAULT_FIELD_CANNOT_NULL = "使用了默认字段，但默认字段为空！";

    }

    public static class Property {

        public static final String FIELD = "field";
        public static final String COMPARATOR = "comparator";

    }

    public enum Field {

        P("image"),
        R("tacdcn_vec"),
        RAD("ad"),
        RPD("pd"),
        ZH("CN", "tacdcn_vec"),
        EN("EN", "tacden_vec"),
        JP("JP", "tacdjp_vec");

        String name;
        String field;
        Field(String name) {
            this.name = name;
        }
        Field(String name, String field) {
            this.name = name;
            this.field = field;
        }

        public String getName() {
            return this.name;
        }

        public static String getField(String name) {
            for (Field field : Field.values()) {
                if (field.name.equals(name.toUpperCase())) {
                    return field.field;
                }
            }

            return ZH.field;
        }

    }

}
