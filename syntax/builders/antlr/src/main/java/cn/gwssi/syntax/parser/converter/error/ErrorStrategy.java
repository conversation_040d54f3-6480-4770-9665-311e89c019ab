package cn.gwssi.syntax.parser.converter.error;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.IntervalSet;

/**
 * 逻辑从 DefaultErrorStrategy 中拷贝
 */
public class ErrorStrategy extends DefaultErrorStrategy {

    @Override
    protected void reportNoViableAlternative(Parser recognizer, NoViableAltException e) {
        TokenStream tokens = recognizer.getInputStream();
        String input;
        if (tokens != null) {
            if (e.getStartToken().getType() == Token.EOF) {
                input = "意外结束！";
            } else {
                input = tokens.getText(e.getStartToken(), e.getOffendingToken());
                RecognitionWithDetailException exception = new RecognitionWithDetailException(e);
                exception.setChars(this.escapeWSAndQuote(input));
                exception.setMessage("包含非法字符!");
                recognizer.notifyErrorListeners(e.getOffendingToken(), input, exception);
                return;
            }
        } else {
            input = "表达式不完整！";
        }

        recognizer.notifyErrorListeners(e.getOffendingToken(), input, e);
    }

    @Override
    protected void reportInputMismatch(Parser recognizer, InputMismatchException e) {
        String msg = "表达式不完整！";
        recognizer.notifyErrorListeners(e.getOffendingToken(), msg, e);
    }

    @Override
    protected void reportFailedPredicate(Parser recognizer, FailedPredicateException e) {
//        String ruleName = recognizer.getRuleNames()[recognizer._ctx.getRuleIndex()];
        int index = recognizer.getContext().getRuleIndex();
        String ruleName = recognizer.getRuleNames()[index];
        String msg = "规则 " + ruleName + " " + e.getMessage();
        recognizer.notifyErrorListeners(e.getOffendingToken(), msg, e);
    }

    @Override
    protected void reportUnwantedToken(Parser recognizer) {
        if (inErrorRecoveryMode(recognizer)) {
            return;
        }

        beginErrorCondition(recognizer);

        Token t = recognizer.getCurrentToken();
        String msg = "表达式不完整！";
        recognizer.notifyErrorListeners(t, msg, null);
    }

    @Override
    protected void reportMissingToken(Parser recognizer) {
        if (inErrorRecoveryMode(recognizer)) {
            return;
        }

        beginErrorCondition(recognizer);

        Token t = recognizer.getCurrentToken();
        IntervalSet expecting = getExpectedTokens(recognizer);
        String msg = "缺失 " + expecting.toString(recognizer.getVocabulary());

        recognizer.notifyErrorListeners(t, msg, null);
    }

    @Override
    public String escapeWSAndQuote(String s) {
		if ( s==null ) return s;
        s = s.replace("\n","\\n");
        s = s.replace("\r","\\r");
        s = s.replace("\t","\\t");
        return s;
    }

}
