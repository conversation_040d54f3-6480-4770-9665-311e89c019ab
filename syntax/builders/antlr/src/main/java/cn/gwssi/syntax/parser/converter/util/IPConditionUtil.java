package cn.gwssi.syntax.parser.converter.util;

import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.ScannerItemCondition;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;
import cn.gwssi.syntax.parser.converter.visitor.StringValueVisitor;

public class IPConditionUtil {

    private final StringValueVisitor visitor;

    public IPConditionUtil(StringValueVisitor visitor) {
        this.visitor = visitor;
    }

    /**
     * condition 设置字段
     */
    public void setConditionField(IPCondition condition) {
        condition.setSerialField(visitor.getProperty(Constant.Property.FIELD));
    }

    /**
     * condition 设置操作符，是不是 != 操作符
     */
    public void setConditionComparator(IPCondition condition) {
        condition.setEqualOrNot(!ItemOperator.ITEM_OPERATOR_NE.getName().equals(visitor.getProperty(Constant.Property.COMPARATOR)));
    }

    /**
     * 生成 ipCondition
     */
    public IPCondition geneIPCondition(Value value) {
        String field = visitor.getProperty(Constant.Property.FIELD);
        String comparator = visitor.getProperty(Constant.Property.COMPARATOR);
        return this.geneIPCondition(field, value, comparator);
    }

    /**
     * 生成位置运算符的 itemValue
     */
    public Value geneItemValue(Value frontValue, Value endValue, ValueLocation location) {
        Value itemValue = new Value();

        itemValue.addValue(frontValue);
        itemValue.addValue(endValue);
        itemValue.addLocation(location);
        itemValue.setLocation(true);
        return itemValue;
    }

    // 生成 ipCondition
    private IPCondition geneIPCondition(String field, Value value, String comparator) {
        ItemOperator operator = ItemOperator.getOperator(comparator);

        ScannerItemCondition item = new ScannerItemCondition();
        item.setFieldName(field);
        item.setItemValue(value);
        item.setOperator(operator);

        return item;
    }

}
