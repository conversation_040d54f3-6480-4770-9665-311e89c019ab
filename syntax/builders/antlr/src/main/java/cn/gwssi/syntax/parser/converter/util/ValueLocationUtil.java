package cn.gwssi.syntax.parser.converter.util;

import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;
import cn.gwssi.syntax.parser.converter.entity.LocationValue;
import cn.gwssi.syntax.parser.converter.error.ErrorMsg;
import cn.gwssi.syntax.parser.converter.error.ParseCancelWithDetailException;
import cn.gwssi.syntax.parser.converter.visitor.ItemValueVisitor;
import cn.gwssi.syntax.parser.converter.visitor.LocationVisitor;
import cn.gwssi.syntax.parser.converter.visitor.StringValueVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.antlr.v4.runtime.ParserRuleContext;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 位置运算相关处理
 * 1、检查位置运算符
 * 2、检查值
 */
public class ValueLocationUtil {

    private final StringValueVisitor visitor;
    private final ItemValueVisitor valueVisitor;
    private final LocationVisitor locationVisitor;
    private IPConditionUtil util;

    public ValueLocationUtil(StringValueVisitor visitor, ItemValueVisitor valueVisitor, LocationVisitor locationVisitor, IPConditionUtil util) {
        this.visitor = visitor;
        this.valueVisitor = valueVisitor;
        this.locationVisitor = locationVisitor;
        this.util = util;
    }

    /***********************************递归扫描全部位置运算***********************************/
    /**
     * 由于 antlr 的限制，left recursion 且前后没有其它符号的情况下，位置运算不能解析成连续的，只能解析成嵌套的，所以这里手动解析
     * 例如：a pre/1 b and/2 c 会解析成树形，而不是列表
     */
    public void findAllLocations(ExpressionParser.ValueLocationContext ctx, List<ExpressionParser.LocationContext> locations, List<ExpressionParser.ValueExprContext> values) {
//        ExpressionParser.LocationContext location = ctx.location();
//        locations.add(location);

        int index = 0;
        List<ExpressionParser.ValueExprContext> valueList = ctx.valueExpr();
        for (ExpressionParser.ValueExprContext value : valueList) {
            this.findAllLocations(value, locations, values);

            // FIXED 2025-03-25 位置运算符要在两个值之间添加
            // 这里有个前提，valueList 的长度是2，见 Expression.g4 文件第 50 行注释
            if (++index == 1) {
                locations.add(ctx.location());
            }
        }
    }

    /**
     * 解析位置算符两遍的值
     * 只支持以下四种类型，其余类型不支持
     */
    private void findAllLocations(ExpressionParser.ValueExprContext ctx, List<ExpressionParser.LocationContext> locations, List<ExpressionParser.ValueExprContext> values) {
        if (ctx instanceof ExpressionParser.ValueNormalContext) {
            values.add(ctx);
        } else if (ctx instanceof ExpressionParser.ValueSerialContext) {
            values.add(ctx);
        } else if (ctx instanceof ExpressionParser.ValueNestedContext) {
            this.findAllLocations(((ExpressionParser.ValueNestedContext) ctx).valueExpr(), locations, values);
        } else if (ctx instanceof ExpressionParser.ValueLocationContext) {
            this.findAllLocations((ExpressionParser.ValueLocationContext)ctx, locations, values);
        } else {
            ParserError error = this.checkErrorMsg(ctx, Constant.Error.LOCATION_VALUE_CANNOT_NESTED);
            throw new ParseCancelWithDetailException(error);
        }
    }

    /***********************************检查运算符***********************************/
    /**
     * 对于连续的位置运算，检查运算符是否合法，必须都是有序，或者都是无序
     * 例如：
     * 支持：A pre/1 B equ/1 C
     * 支持：A pre/sen B pre/seg C
     * 不支持：A pre/1 B and/1 C
     */
    public List<ValueLocation> checkOperatorForSerial(ExpressionParser.ValueLocationContext ctx, List<ExpressionParser.LocationContext> locations) {
        List<ValueLocation> values = new ArrayList<>();
        Set<Boolean> types = new HashSet<>();

        // 解析位置运算符的类型
        for (ExpressionParser.LocationContext location : locations) {
            ValueLocation value = locationVisitor.visit(location);
            Boolean type = value.isInOrder();
            values.add(value);
            types.add(type);
        }

        // 只能有一种类型
        if (types.size() > 1) {
            ParserError error = this.checkErrorMsg(ctx, Constant.Error.LOCATION_NO_ORDER_CANNOT_SERIAL);
            throw new ParseCancelWithDetailException(error);
        }

        return values;
    }

    /***********************************检查值***********************************/
    /**
     * 对于连续位置运算，值必须是简单的 a、b、c、((a))，不能嵌套其它复杂值，例如：
     * a
     * ((a))
     * 不合法的值，例如：
     * a or b
     * a pre/1 b
     * a not b
     */
    public List<Value> checkValueForSerial(List<ExpressionParser.ValueExprContext> values) {
        List<Value> valueList = new ArrayList<>();

        for (ExpressionParser.ValueExprContext value : values) {
            ParserError error = this.checkErrorMsg(value, Constant.Error.LOCATION_NESTED_CANNOT_SERIAL);
            valueList.add(this.checkAndExtract(value, error));
        }
        return valueList;
    }

    /**
     * 检查运算符两边的值，必须是简单值，或简单布尔运算。例如：
     * a
     * ((a))
     * a or b or c
     * 不合法的值，例如：
     * (a or b) or c
     * a or b and c
     * a pre/1 b
     */
    public LocationValue checkEveryValue(ExpressionParser.ValueExprContext value) {
        ParserError error = this.checkErrorMsg(value, Constant.Error.LOCATION_VALUE_CANNOT_NESTED);
        LocationValue locationValue = new LocationValue();
        List<Value> locationValues = new ArrayList<>();

        // 对于嵌套值，先取嵌套的内容，再去处理
        // 例如：(a) 取到 a，(((a or b))) 取到 a or b
        if (value instanceof ExpressionParser.ValueNestedContext) {
            value = ((ExpressionParser.ValueNestedContext) value).valueExpr();
            while (true) {
                if (value instanceof ExpressionParser.ValueNestedContext) {
                    value = ((ExpressionParser.ValueNestedContext) value).valueExpr();
                } else if (value instanceof ExpressionParser.ValueNormalContext
                        || value instanceof ExpressionParser.ValueSerialContext) {
                    break;
                } else {
                    throw new ParseCancelWithDetailException(error);
                }
            }
        }

        // 对于布尔运算，两边必须是简单值，不能再有嵌套。例如：a or b
        if (value instanceof ExpressionParser.ValueSerialContext) {
            ExpressionParser.ValueSerialContext serialValue = (ExpressionParser.ValueSerialContext) value;

            // 处理连接符，所有必须相同，必须是 and/or
            List<ExpressionParser.ConnectorContext> subConnectors = serialValue.connector();
            String connector = this.checkSubConnector(subConnectors);

            // 处理值，必须是简单值
            List<ExpressionParser.ValueExprContext> subValues = serialValue.valueExpr();
            for (ExpressionParser.ValueExprContext subValue : subValues) {
                Value normalValue = this.checkAndExtract(subValue, error);
                locationValues.add(normalValue);
            }

            locationValue.setConnector(connector);
            locationValue.setSimple(false);
        } else if (value instanceof ExpressionParser.ValueNormalContext) {
            Value text = valueVisitor.visit(value);
            locationValues.add(text);
        } else {
            throw new ParseCancelWithDetailException(error);
        }

        locationValue.setValues(locationValues);
        return locationValue;
    }

    // 检查连接符是否一致，必须都是 and 或 or
    private String checkSubConnector(List<ExpressionParser.ConnectorContext> connectors) {
        ExpressionParser.ConnectorContext firstConnector = connectors.get(0);
        String firstValue = visitor.visit(firstConnector);

        // 检查是否与第一个一致
        for (int i = 1, length = connectors.size(); i < length; i++) {
            ExpressionParser.ConnectorContext connector = connectors.get(i);
            String value = visitor.visit(connector);
            if (!value.equals(firstValue)) {
                ParserError error = this.checkErrorMsg(connector, Constant.Error.LOCATION_VALUE_CONNECTOR_MUST_EQUAL);
                throw new ParseCancelWithDetailException(error);
            }
        }

        // 检查是否 and or
        if (!firstValue.equals(ItemConnector.ITEM_CONNECTOR_AND.getName())
                && !firstValue.equals(ItemConnector.ITEM_CONNECTOR_OR.getName())) {
            ParserError error = this.checkErrorMsg(firstConnector, Constant.Error.LOCATION_VALUE_CONNECTOR_MUST_LEGAL);
            throw new ParseCancelWithDetailException(error);
        }

        return firstValue;
    }

    /**
     * 判断是否简单值，并提取。例如：
     * a
     * ((a))
     * 不合法的值，例如：
     * a or b
     * a not b
     * a pre/1 b
     */
    private Value checkAndExtract(ExpressionParser.ValueExprContext value, ParserError error) {
        // 如果是带了括号的值，递归找到最终值，例如 ((a)) 找到 a
        if (value instanceof ExpressionParser.ValueNestedContext) {
            value = ((ExpressionParser.ValueNestedContext) value).valueExpr();
            while (true) {
                if (value instanceof ExpressionParser.ValueNestedContext) {
                    value = ((ExpressionParser.ValueNestedContext) value).valueExpr();
                } else if (value instanceof ExpressionParser.ValueNormalContext) {
                    break;
                } else {
                    throw new ParseCancelWithDetailException(error);
                }
            }
        }

        if (value instanceof ExpressionParser.ValueNormalContext) {
            return valueVisitor.visit(value);
        } else {
            throw new ParseCancelWithDetailException(error);
        }
    }

    /***********************************值重新组合***********************************/
    /**
     * 用乘法分配律做重新组合
     * 例如：(a or b) pre/1 (c or d) 变形为 (a pre/1 c) or (a pre/1 d) or (b pre/1 c) or (b pre/1 d)
     */
    public IPCondition reCompose(ExpressionParser.ValueLocationContext ctx, LocationValue frontLocation, LocationValue endLocation, ValueLocation locationOperator) {
        IPCondition ipCondition = new IPCondition();
        // 设置字段和操作符，处理虚拟字段的时候要用到
        util.setConditionField(ipCondition);
        util.setConditionComparator(ipCondition);

        List<Value> frontValues = frontLocation.getValues();
        List<Value> endValues = endLocation.getValues();

        // 前后都是布尔运算时，检查前后运算符是否相同
        if (!frontLocation.isSimple() && !endLocation.isSimple()
                && !frontLocation.getConnector().equals(endLocation.getConnector())) {
            ParserError error = this.checkErrorMsg(ctx, Constant.Error.LOCATION_VALUE_CONNECTOR_MUST_EQUAL);
            throw new ParseCancelWithDetailException(error);
        }

        // 重新组合位置运算
        for (Value front : frontValues) {
            for (Value end : endValues) {
                Value itemValue = util.geneItemValue(front, end, locationOperator);
                IPCondition subCondition = util.geneIPCondition(itemValue);
                ipCondition.addSubCondition(subCondition);
            }
        }

        // 获取运算符
        ItemConnector connector = frontLocation.getConnector();
        if (null == connector) {
            connector = endLocation.getConnector();
        }

        // 布尔运算符，比值少一个
        int size = frontValues.size() * endValues.size() - 1;
        for (int i = 0; i < size; i++) {
            ipCondition.addConnector(connector);
        }

        return ipCondition;
    }

    // 生成错误信息
    private ParserError checkErrorMsg(ParserRuleContext context, String reason) {
        String text = visitor.visit(context);
        return ErrorMsg.getInstance().format(context, text, reason);
    }

}
