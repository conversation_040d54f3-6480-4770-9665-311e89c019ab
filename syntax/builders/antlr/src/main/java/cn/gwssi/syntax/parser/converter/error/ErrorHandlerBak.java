package cn.gwssi.syntax.parser.converter.error;

import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.apache.commons.lang3.StringUtils;

public class ErrorHandlerBak extends BaseErrorListener {

    @Override
    public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol, int line, int charPositionInLine, String msg, RecognitionException e) throws ParseCancellationException {
        ConditionOffset offset = null;
        String reason = null;
//        int endPosition = -1;

        // 猜测原因
        if (recognizer instanceof ExpressionParser) {
            ParserRuleContext context = ((ExpressionParser) recognizer).getRuleContext();
            String value = context.getText().trim();
            if (context instanceof ExpressionParser.StatContext
                    || context instanceof ExpressionParser.StatementContext
                    || context instanceof ExpressionParser.ExpressionContext) {
                reason = "子表达式不完整或包含非法字符";
            } else if (context instanceof ExpressionParser.ComparatorContext) {
                reason = "字段包含非法字符或缺少操作符";
            } else if (context instanceof ExpressionParser.ValueExprContext) {
                if (StringUtils.isEmpty(value)) {
                    reason = "值为空";
                }
            }

            offset = ErrorMsg.getInstance().extractOffset(context);
        }

        // 猜测字符。offendingSymbol 是出现异常之后的字符，一般都不准。
        String token = null;
        if (offendingSymbol instanceof Token) {
            Token offending = (Token) offendingSymbol;
            if (offending.getType() != IntStream.EOF) {
                token = offending.getText();
            } else {
                if (null == reason) {
                    reason = "意外结束";
                }
            }

            if (StringUtils.isNotBlank(token)) {
                if (null == offset) {
                    offset = new ConditionOffset();
                }

                offset.setStartLine(offending.getLine());
                offset.setStartInLine(offending.getCharPositionInLine());
                offset.setStart(offending.getStartIndex());
            }

//                // 一般情况下，stopIndex 和 startIndex 是相等的，取 stopIndex 是无意义的
//                if (offending.getStopIndex() != charPositionInLine) {
//                    endPosition = offending.getStopIndex() + 1;
//                }

        }

        // 猜测结束位置
        if (null == offset) {
//            // 如果换行的话，直接加上字符数量是不对的，这个计算有点鸡肋
//            if (endPosition == -1 && StringUtils.isNotBlank(token)) {
//                endPosition = charPositionInLine + token.length();
//            }

            offset = new ConditionOffset();
            offset.setStartLine(line);
            offset.setStartInLine(charPositionInLine);
        }

//        offset.setEndInLine(endPosition);

        if (null != token) {
            token = token.trim();
        } else if (e instanceof NoViableAltException) {
            token = ((NoViableAltException)e).getStartToken().getText();
        }

        // 对于括号不匹配的异常，单独处理
        if (token.contains("(") || token.contains(")")) {
            // 只保留一个括号，去掉无关字符
            // 只保留唯一的位置
            if (token.startsWith("(") || token.startsWith(")")) {
                reason = "括号不匹配";
                token = token.startsWith("(") ? "(" : ")";
                offset.setEndLine(offset.getStartLine());
                offset.setEndInLine(offset.getStartInLine());
            } else if (token.endsWith("(") || token.endsWith(")")) {
                reason = "括号不匹配";
                token = token.endsWith("(") ? "(" : ")";
                offset.setStartLine(offset.getEndLine());
                offset.setStartInLine(offset.getEndInLine());
            }
        } else if ("\\".equals(token)) {
            reason = "转义符不能单独使用";
        }

        ParserError error = ErrorMsg.getInstance().format(offset, token, reason);
        throw new ParseCancelWithDetailException(error);
    }

}
