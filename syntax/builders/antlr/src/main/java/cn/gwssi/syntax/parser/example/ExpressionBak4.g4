grammar ExpressionBak4;

// 入口
expression
  : statement EOF
  ;

// 单个或多个表达式
statement
  : stat # statSingle
  | statMulti # statMultiple
  ;

// 单个表达式
stat
  : '(' statement ')' # statNested
  | field comparator valueExpr # statFinal
  ;

// 多个表达式
// 为什么单独写一条呢？This is a sad story :(
statMulti
  : stat (connector stat)+ # statSerial
  ;

// 值的表达式，操作符后面的部分。包括下面的 valueMulti
valueExpr
  : '(' valueExpr ')' # valueNested
  // 这个要放在 valueNormal 之前，否则 not 会被解析成 stat
  // 这个和 valueSerial 有点类似，区别在于连接符只处理 not
  | valueFinal CONNECTOR_NOT valueExpr # valueNot
  // 频率，例如 clock/freq >= 4
  | valueFinal FREQ comparator INT # valueFreq
  // truecase，例如 truecase(c)
  | TRUECASE '(' valueFinal ')' # valueTruecase
  // 单值，例如 a1 或带空格的 'a 1'
  | valueFinal # valueNormal
  // 对于多个值必须带括号，例如不允许 a=b or c，必须写成 a=(b or c)
  | '(' valueExpr (connector valueExpr)+ ')' # valueSerial
  // 位置运算，必须带括号
  | '(' valueExpr (location valueExpr)+ ')' # valueLocation
  ;

// 字段
field
  : words+
  ;

// 字段，可以包含空格，虽然空格已经被 skip
words
  : WORD
  | SPACE
  | INT
  ;

// 值，可以包含引号，各种特殊符号
valueFinal
  : quotedWords # valueQuoted
  | wordsAll+ # valueWords
  ;

// 值，可以包含空格、各种特殊符号、比较符，不能包含小括号
wordsAll
  : words
  | comparator
  | SPACE
  | ANY
  ;

// 连接符
connector
  : CONNECTOR_AND
  | CONNECTOR_OR
  | CONNECTOR_XOR
  | CONNECTOR_NOT
  ;
CONNECTOR_AND: SPACE AND SPACE ;
CONNECTOR_OR: SPACE OR SPACE ;
CONNECTOR_XOR: SPACE XOR SPACE ;
CONNECTOR_NOT: SPACE NOT SPACE | NOT SPACE ;

// 比较符
comparator
  : SPACE* (EQ | EQA | NE | GT | GE | LT | LE) SPACE*
  ;

// 位置符
location
  : location_pre
  | location_pre_l
  | location_pre_sen
  | location_pre_seg
  | location_equ
  | location_and
  | location_and_e
  | location_and_l
  | location_and_sen
  | location_and_seg
  ;

// 写成 rule 而不是 token 的原因，是为了解析 INT 方便
location_pre: SPACE PRE INT SPACE;
location_pre_l: SPACE LOCATION_PRE_L SPACE;
location_pre_sen: SPACE PRE SEN SPACE;
location_pre_seg: SPACE PRE SEG SPACE;
location_equ: SPACE EQU INT SPACE;
location_and: SPACE AND_L INT SPACE;
location_and_e: SPACE LOCATION_AND_E SPACE;
location_and_l: SPACE LOCATION_AND_L SPACE;
location_and_sen: SPACE AND_L SEN SPACE;
location_and_seg: SPACE AND_L SEG SPACE;

// 后面带字母的要单独写，否则 1L 会识别为 WORD 类型
LOCATION_PRE_L: PRE INT ([Ll]);
LOCATION_AND_L: AND_L INT ([Ll]);
LOCATION_AND_E: AND_L INT '#';

// 引号包裹的内容，允许各种符号和 \" \'
quotedWords
  : SPACE* (
    STRING_QUOTES
    | UNICODE_STRING_QUOTES
    | STRING_QUOTES_SINGLE
    | UNICODE_STRING_QUOTES_SINGLE
  ) SPACE*
  ;
STRING_QUOTES
  : '"' (~'"' | '\\' [\\"()])* '"'
  ;
UNICODE_STRING_QUOTES
  : 'U&"' ( ~'"' | '\\' [\\"()])* '"'
  ;
STRING_QUOTES_SINGLE
  : '\'' ( ~'\'' | '\\' [\\'()])* '\''
  ;
UNICODE_STRING_QUOTES_SINGLE
  : 'U&\'' ( ~'\'' | '\\' [\\'()])* '\''
  ;

// 一些常量
BRACKETS_L: '(' ;
BRACKETS_R: ')' ;
SPACE: (' ')+ ;
EQ: '=' ;
EQA: '+=' ;
NE: '!=' ;
GT: '>' ;
GE: '>=' ;
LT: '<' ;
LE: '<=' ;
FREQ: ('/freq') ;
// fragment 使用规则：1、java代码中不会调用的 2、rule没有直接使用的
fragment
AND: ([Aa][Nn][Dd]) ;
fragment
OR: ([Oo][Rr]) ;
fragment
NOT: ([Nn][Oo][Tt]) ;
fragment
XOR: ([Xx][Oo][Rr]) ;
PRE: ([Pp][Rr][Ee]'/') ;
EQU: ([Ee][Qq][Uu]'/') ;
SEN: ([Ss][Ee][Nn]) ;
SEG: ([Ss][Ee][Gg]) ;
AND_L: AND '/' ;
TRUECASE: ([Tt][Rr][Uu][Ee][Cc][Aa][Ss][Ee]);

// 数字
INT: DIGIT+ ;
fragment
DIGIT: [0-9] ;
// 字段，限制字符和符号，不能随意输入
WORD: [a-zA-Z0-9\u4e00-\u9fa5_,]+ ;
// 除 skip 之外的任意字符
ANY: ~[\t\r\n] ;

WS: [ \t\r\n]+ -> skip ;
