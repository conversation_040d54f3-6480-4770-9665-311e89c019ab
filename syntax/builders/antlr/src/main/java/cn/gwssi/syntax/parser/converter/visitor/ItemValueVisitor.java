package cn.gwssi.syntax.parser.converter.visitor;

import cn.gwssi.syntax.condition.parser.ParserEscape;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.parser.converter.util.Constant;
import cn.gwssi.syntax.parser.converter.util.ValueUtil;
import cn.gwssi.syntax.parser.expression.ExpressionBaseVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;

public class ItemValueVisitor extends ExpressionBaseVisitor<Value> {

    private StringValueVisitor visitor;
    private ValueUtil util;

    public ItemValueVisitor() {}
    public ItemValueVisitor(StringValueVisitor visitor, StatVisitor statVisitor) {
        this.visitor = visitor;
        this.util = new ValueUtil(statVisitor, visitor);
    }

    @Override
    public Value visitValueIn(ExpressionParser.ValueInContext ctx) {
        String value = visitor.visit(ctx);
        return util.in(ctx, value);
    }

    @Override
    public Value visitValueQuoted(ExpressionParser.ValueQuotedContext ctx) {
        String value = visitor.visit(ctx);
        return util.quoteWords(ctx, value);
    }

    @Override
    public Value visitValueQuotedSingle(ExpressionParser.ValueQuotedSingleContext ctx) {
        String value = ctx.getText().trim();
        return util.quoteWordsSingle(ctx, value);
    }

    @Override
    public Value visitValueWords(ExpressionParser.ValueWordsContext ctx) {
        String value = visitor.visit(ctx);
        return util.words(ctx, value, false);
    }

    @Override
    public Value visitValueFreq(ExpressionParser.ValueFreqContext ctx) {
        String value = visitor.visit(ctx.valueFinal());
        String comparator = visitor.visit(ctx.comparator());
        return util.freq(ctx, value, comparator);
    }

    @Override
    public Value visitValueTruecase(ExpressionParser.ValueTruecaseContext ctx) {
        String value = visitor.visit(ctx.valueFinal());
        return util.truecase(ctx, value);
    }

    @Override
    public Value visitWordsBoost(ExpressionParser.WordsBoostContext ctx) {
        Value value = this.visit(ctx.valueFinal());
        String count = ctx.INT().getText().trim();
        return util.boost(ctx, value, Float.parseFloat(count));
    }

}
