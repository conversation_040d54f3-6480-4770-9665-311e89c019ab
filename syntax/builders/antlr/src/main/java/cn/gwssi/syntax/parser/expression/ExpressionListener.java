// Generated from java-escape by ANTLR 4.11.1
package cn.gwssi.syntax.parser.expression;
import org.antlr.v4.runtime.tree.ParseTreeListener;

/**
 * This interface defines a complete listener for a parse tree produced by
 * {@link ExpressionParser}.
 */
public interface ExpressionListener extends ParseTreeListener {
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#expression}.
	 * @param ctx the parse tree
	 */
	void enterExpression(ExpressionParser.ExpressionContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#expression}.
	 * @param ctx the parse tree
	 */
	void exitExpression(ExpressionParser.ExpressionContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statSingle}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 */
	void enterStatSingle(ExpressionParser.StatSingleContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statSingle}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 */
	void exitStatSingle(ExpressionParser.StatSingleContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statMultiple}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 */
	void enterStatMultiple(ExpressionParser.StatMultipleContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statMultiple}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 */
	void exitStatMultiple(ExpressionParser.StatMultipleContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statNested}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void enterStatNested(ExpressionParser.StatNestedContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statNested}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void exitStatNested(ExpressionParser.StatNestedContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statFinal}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void enterStatFinal(ExpressionParser.StatFinalContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statFinal}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void exitStatFinal(ExpressionParser.StatFinalContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statIn}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void enterStatIn(ExpressionParser.StatInContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statIn}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void exitStatIn(ExpressionParser.StatInContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statOnlyValue}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void enterStatOnlyValue(ExpressionParser.StatOnlyValueContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statOnlyValue}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 */
	void exitStatOnlyValue(ExpressionParser.StatOnlyValueContext ctx);
	/**
	 * Enter a parse tree produced by the {@code statSerial}
	 * labeled alternative in {@link ExpressionParser#statMulti}.
	 * @param ctx the parse tree
	 */
	void enterStatSerial(ExpressionParser.StatSerialContext ctx);
	/**
	 * Exit a parse tree produced by the {@code statSerial}
	 * labeled alternative in {@link ExpressionParser#statMulti}.
	 * @param ctx the parse tree
	 */
	void exitStatSerial(ExpressionParser.StatSerialContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueNot}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueNot(ExpressionParser.ValueNotContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueNot}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueNot(ExpressionParser.ValueNotContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueTruecase}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueTruecase(ExpressionParser.ValueTruecaseContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueTruecase}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueTruecase(ExpressionParser.ValueTruecaseContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueNested}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueNested(ExpressionParser.ValueNestedContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueNested}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueNested(ExpressionParser.ValueNestedContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueSerial}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueSerial(ExpressionParser.ValueSerialContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueSerial}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueSerial(ExpressionParser.ValueSerialContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueFreq}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueFreq(ExpressionParser.ValueFreqContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueFreq}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueFreq(ExpressionParser.ValueFreqContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueLocation}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueLocation(ExpressionParser.ValueLocationContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueLocation}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueLocation(ExpressionParser.ValueLocationContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueBoost}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueBoost(ExpressionParser.ValueBoostContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueBoost}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueBoost(ExpressionParser.ValueBoostContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueNormal}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void enterValueNormal(ExpressionParser.ValueNormalContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueNormal}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 */
	void exitValueNormal(ExpressionParser.ValueNormalContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#field}.
	 * @param ctx the parse tree
	 */
	void enterField(ExpressionParser.FieldContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#field}.
	 * @param ctx the parse tree
	 */
	void exitField(ExpressionParser.FieldContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#words}.
	 * @param ctx the parse tree
	 */
	void enterWords(ExpressionParser.WordsContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#words}.
	 * @param ctx the parse tree
	 */
	void exitWords(ExpressionParser.WordsContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueQuoted}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void enterValueQuoted(ExpressionParser.ValueQuotedContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueQuoted}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void exitValueQuoted(ExpressionParser.ValueQuotedContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueQuotedSingle}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void enterValueQuotedSingle(ExpressionParser.ValueQuotedSingleContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueQuotedSingle}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void exitValueQuotedSingle(ExpressionParser.ValueQuotedSingleContext ctx);
	/**
	 * Enter a parse tree produced by the {@code valueWords}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void enterValueWords(ExpressionParser.ValueWordsContext ctx);
	/**
	 * Exit a parse tree produced by the {@code valueWords}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 */
	void exitValueWords(ExpressionParser.ValueWordsContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#valueIn}.
	 * @param ctx the parse tree
	 */
	void enterValueIn(ExpressionParser.ValueInContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#valueIn}.
	 * @param ctx the parse tree
	 */
	void exitValueIn(ExpressionParser.ValueInContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#wordsAll}.
	 * @param ctx the parse tree
	 */
	void enterWordsAll(ExpressionParser.WordsAllContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#wordsAll}.
	 * @param ctx the parse tree
	 */
	void exitWordsAll(ExpressionParser.WordsAllContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#wordsBoost}.
	 * @param ctx the parse tree
	 */
	void enterWordsBoost(ExpressionParser.WordsBoostContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#wordsBoost}.
	 * @param ctx the parse tree
	 */
	void exitWordsBoost(ExpressionParser.WordsBoostContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#connector}.
	 * @param ctx the parse tree
	 */
	void enterConnector(ExpressionParser.ConnectorContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#connector}.
	 * @param ctx the parse tree
	 */
	void exitConnector(ExpressionParser.ConnectorContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#comparator}.
	 * @param ctx the parse tree
	 */
	void enterComparator(ExpressionParser.ComparatorContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#comparator}.
	 * @param ctx the parse tree
	 */
	void exitComparator(ExpressionParser.ComparatorContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#comparator_in}.
	 * @param ctx the parse tree
	 */
	void enterComparator_in(ExpressionParser.Comparator_inContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#comparator_in}.
	 * @param ctx the parse tree
	 */
	void exitComparator_in(ExpressionParser.Comparator_inContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#wordsWithBracket}.
	 * @param ctx the parse tree
	 */
	void enterWordsWithBracket(ExpressionParser.WordsWithBracketContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#wordsWithBracket}.
	 * @param ctx the parse tree
	 */
	void exitWordsWithBracket(ExpressionParser.WordsWithBracketContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location}.
	 * @param ctx the parse tree
	 */
	void enterLocation(ExpressionParser.LocationContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location}.
	 * @param ctx the parse tree
	 */
	void exitLocation(ExpressionParser.LocationContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_pre}.
	 * @param ctx the parse tree
	 */
	void enterLocation_pre(ExpressionParser.Location_preContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_pre}.
	 * @param ctx the parse tree
	 */
	void exitLocation_pre(ExpressionParser.Location_preContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_pre_l}.
	 * @param ctx the parse tree
	 */
	void enterLocation_pre_l(ExpressionParser.Location_pre_lContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_pre_l}.
	 * @param ctx the parse tree
	 */
	void exitLocation_pre_l(ExpressionParser.Location_pre_lContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_pre_sen}.
	 * @param ctx the parse tree
	 */
	void enterLocation_pre_sen(ExpressionParser.Location_pre_senContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_pre_sen}.
	 * @param ctx the parse tree
	 */
	void exitLocation_pre_sen(ExpressionParser.Location_pre_senContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_pre_seg}.
	 * @param ctx the parse tree
	 */
	void enterLocation_pre_seg(ExpressionParser.Location_pre_segContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_pre_seg}.
	 * @param ctx the parse tree
	 */
	void exitLocation_pre_seg(ExpressionParser.Location_pre_segContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_equ}.
	 * @param ctx the parse tree
	 */
	void enterLocation_equ(ExpressionParser.Location_equContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_equ}.
	 * @param ctx the parse tree
	 */
	void exitLocation_equ(ExpressionParser.Location_equContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_and}.
	 * @param ctx the parse tree
	 */
	void enterLocation_and(ExpressionParser.Location_andContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_and}.
	 * @param ctx the parse tree
	 */
	void exitLocation_and(ExpressionParser.Location_andContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_and_e}.
	 * @param ctx the parse tree
	 */
	void enterLocation_and_e(ExpressionParser.Location_and_eContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_and_e}.
	 * @param ctx the parse tree
	 */
	void exitLocation_and_e(ExpressionParser.Location_and_eContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_and_l}.
	 * @param ctx the parse tree
	 */
	void enterLocation_and_l(ExpressionParser.Location_and_lContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_and_l}.
	 * @param ctx the parse tree
	 */
	void exitLocation_and_l(ExpressionParser.Location_and_lContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_and_sen}.
	 * @param ctx the parse tree
	 */
	void enterLocation_and_sen(ExpressionParser.Location_and_senContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_and_sen}.
	 * @param ctx the parse tree
	 */
	void exitLocation_and_sen(ExpressionParser.Location_and_senContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#location_and_seg}.
	 * @param ctx the parse tree
	 */
	void enterLocation_and_seg(ExpressionParser.Location_and_segContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#location_and_seg}.
	 * @param ctx the parse tree
	 */
	void exitLocation_and_seg(ExpressionParser.Location_and_segContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#parenth_l}.
	 * @param ctx the parse tree
	 */
	void enterParenth_l(ExpressionParser.Parenth_lContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#parenth_l}.
	 * @param ctx the parse tree
	 */
	void exitParenth_l(ExpressionParser.Parenth_lContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#parenth_r}.
	 * @param ctx the parse tree
	 */
	void enterParenth_r(ExpressionParser.Parenth_rContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#parenth_r}.
	 * @param ctx the parse tree
	 */
	void exitParenth_r(ExpressionParser.Parenth_rContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#quotedWords}.
	 * @param ctx the parse tree
	 */
	void enterQuotedWords(ExpressionParser.QuotedWordsContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#quotedWords}.
	 * @param ctx the parse tree
	 */
	void exitQuotedWords(ExpressionParser.QuotedWordsContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExpressionParser#quotedWordsSingle}.
	 * @param ctx the parse tree
	 */
	void enterQuotedWordsSingle(ExpressionParser.QuotedWordsSingleContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExpressionParser#quotedWordsSingle}.
	 * @param ctx the parse tree
	 */
	void exitQuotedWordsSingle(ExpressionParser.QuotedWordsSingleContext ctx);
}