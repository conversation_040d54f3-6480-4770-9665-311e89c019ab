grammar ExpressionBak2;

stat
  : '(' stat ')' # statNested
  | field comparator valueExpr # statNormal
  | stat connector stat # statMulti
  ;

// 值的表达式，操作符后面的部分。包括下面的 valueMulti 和 valueOperate
valueExpr
  : '(' valueExpr ')' # valueNested
  // 这个要放在 valueNormal 之前，否则 not 会被解析成 stat
  // 这个和 valueConnect 有点类似，区别在于连接符只处理 not
  | valueFinal CONNECTOR_NOT valueExpr # valueNot
  // 简单值，例如 a1 或带空格的 'a 1'
  | valueFinal # valueNormal
  // 带操作符的不支持括号，例如不允许 a=(a>1)，必须写成 a=b>1
//  | valueFinal valueOperate+ # valueOperator
  // 对于多个值必须带括号，例如不允许 a=b or c，必须写成 a=(b or c)
//  | '(' valueFinal valueMulti+ ')' # valueMultiply
  | '(' valueExpr valueMulti+ ')' # valueMultiply
  ;

// 字段
field
  : words ;

// 字段，可以包含空格，虽然空格已经被 skip
words
  : WORD (SPACE* WORD)*
  ;

// 多值
valueMulti
  // 简单的多值，例如 b or c
  : connector valueFinal # valueMultiNormal
  // 嵌套多层的多值，例如 b not (c or (d and e) or f)
  | connector '(' valueExpr* valueMulti* ')' # valueSerails
  ;

// 带操作符的值，例如 a > 1
valueOperate
  : comparator valueFinal
  ;

// 值，可以包含引号，各种特殊符号
valueFinal
  : quotedWords
  | wordsAll+
  ;

// 值，可以包含各种特殊符号、比较符，不能包含小括号
wordsAll
  : words
  | comparator
  | SPACE
  | ANY
  ;

// 连接符
connector
  : CONNECTOR_AND
  | CONNECTOR_OR
  | CONNECTOR_XOR
  | CONNECTOR_NOT
  ;
CONNECTOR_AND: SPACE AND SPACE ;
CONNECTOR_OR: SPACE OR SPACE ;
CONNECTOR_XOR: SPACE XOR SPACE ;
CONNECTOR_NOT: SPACE NOT SPACE | NOT SPACE ;

// 比较符
comparator
 : EQ | EQA | NE | GT | GE | LT | LE
 ;

// 引号包裹的内容
quotedWords
  : STRING_QUOTES
  | UNICODE_STRING_QUOTES
  | STRING_QUOTES_SINGLE
  | UNICODE_STRING_QUOTES_SINGLE
  ;
STRING_QUOTES
  : '"' ( ~'"' | '""' )* '"'
  ;
UNICODE_STRING_QUOTES
  : 'U&"' ( ~'"' | '""' )* '"'
  ;
STRING_QUOTES_SINGLE
  : '\'' ( ~'\'' | '\'\'' )* '\''
  ;
UNICODE_STRING_QUOTES_SINGLE
  : 'U&\'' ( ~'\'' | '\'\'' )* '\''
  ;

// const
BRACKETS_L: '(' ;
BRACKETS_R: ')' ;
SPACE: (' ')+ ;
fragment
AND: ([Aa][Nn][Dd]) ;
fragment
OR: ([Oo][Rr]) ;
fragment
NOT: ([Nn][Oo][Tt]) ;
fragment
XOR: ([Xx][Oo][Rr]) ;
EQ: '=' ;
EQA: '+=' ;
NE: '!=' ;
GT: '>' ;
GE: '>=' ;
LT: '<' ;
LE: '<=' ;

// 字段，限制字符和符号，不能随意输入
WORD: [a-zA-Z0-9\u4e00-\u9fa5_,]+ ;
// 除 skip 之外的任意字符
ANY: ~[\t\r\n] ;

WS: [ \t\r\n]+ -> skip ;
