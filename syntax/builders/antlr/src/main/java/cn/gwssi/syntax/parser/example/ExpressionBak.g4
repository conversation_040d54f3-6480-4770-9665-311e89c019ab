grammar ExpressionBak;

//stat: '(' stat ')'
//      | stat CONNECTOR_AND stat
//      | stat CONNECTOR_OR stat
//      | stat CONNECTOR_XOR stat
//      | CONNECTOR_NOT stat
//      | '(' expr ')'
//      | expr CONNECTOR_AND expr
//      | expr CONNECTOR_OR expr
//      | expr CONNECTOR_XOR expr
//      | CONNECTOR_NOT expr ;

stat:
  '(' stat ')' # nestedStat
  | field OPERATOR valueExpr # normalStat
  | CONNECTOR_NOT stat  # notStat
  | stat CONNECTOR_NOT stat # notStat2
  | stat CONNECTOR_AND stat # andStat
  | stat CONNECTOR_OR stat  # orStat
  | stat CONNECTOR_XOR stat # xorStat
  ;
fieldExpr:
  field # normalField
//  | '(' stat ')' # nestedStat
  ;
valueExpr:
//  '(' valueExpr ')' # nestedValue
  field CONNECTOR_NOT valueExpr # notValues
  | field # normalValue
  | field value3+ # opratorValue
  | '(' field value2+ ')' # connectorValue
//  | '(' valueExpr ')' # nestedValue
//  | '(' stat ')' #nestedStatValue
  ;

value2:
//  '(' valueExpr ')' # ne
  connector field # normalValue2
//  | connector '(' field value2+ ')' # continuesValue
  | connector '(' valueExpr* value2* ')' # continuesValue2
  ;

value3:
  OPERATOR field
  ;

field:
  NAME (SPACE* NAME)*
  ;
//value:
//  ~(CONNECTOR_AND | CONNECTOR_OR)+
//  ;

//WORDS:
//  NAME (' ')* NAME;

connector:
  CONNECTOR_AND
  | CONNECTOR_OR
  | CONNECTOR_XOR
  | CONNECTOR_NOT
  ;

// 连接符
CONNECTOR_AND: SPACE AND SPACE ;
CONNECTOR_OR: SPACE OR SPACE ;
CONNECTOR_XOR: SPACE XOR SPACE ;
CONNECTOR_NOT: SPACE NOT SPACE | NOT SPACE ;

// 操作符
OPERATOR:
//  (' ')* ('=' | '!=' | '>' | '<' | '>=' | '<=') (' ')*
  '=' | '!=' | '>' | '<' | '>=' | '<='
  ;

BRACKETS_L: '(' ;
BRACKETS_R: ')' ;
SPACE: (' ')+ ;
//fragment
AND: ([Aa][Nn][Dd]) ;
//fragment
OR: ([Oo][Rr]) ;
//fragment
NOT: ([Nn][Oo][Tt]) ;
//fragment
XOR: ([Xx][Oo][Rr]) ;

//NAME: . ;
NAME: [a-zA-Z0-9]+ ;
//VALUE: STRING
//      | NUMBER
//      | INT
//      | BOOLEAN
//      | DIGIT;

//fragment
//STRING: '"' ('\\"'|.)*? '"' ;
//NUMBER: '-'? ('.' DIGIT+ | DIGIT+ ('.' DIGIT*)? ) ;
//INT: [0-9]+ ;
//BOOLEAN: 'true' | 'false' ;
//DIGIT: [0-9] ;

WS: [\t\r\n]+ -> skip ;
//SPACE: [ ]+ -> channel(HIDDEN);

