package cn.gwssi.syntax.parser.converter.visitor;

import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.ValueLocation;
import cn.gwssi.syntax.parser.expression.ExpressionBaseVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.apache.commons.lang3.StringUtils;

public class LocationVisitor extends ExpressionBaseVisitor<ValueLocation> {

    @Override
    public ValueLocation visitLocation_pre(ExpressionParser.Location_preContext ctx) {
        String text = ctx.INT().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_LE, true, Integer.parseInt(text));
    }

    @Override
    public ValueLocation visitLocation_pre_l(ExpressionParser.Location_pre_lContext ctx) {
        String text = ctx.LOCATION_PRE_L().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, true, this.extractNum(text));
    }

    @Override
    public ValueLocation visitLocation_pre_sen(ExpressionParser.Location_pre_senContext ctx) {
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, true, 0, ValueLocation.SenSeg.SEN);
    }

    @Override
    public ValueLocation visitLocation_pre_seg(ExpressionParser.Location_pre_segContext ctx) {
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, true, 0, ValueLocation.SenSeg.SEG);
    }

    @Override
    public ValueLocation visitLocation_equ(ExpressionParser.Location_equContext ctx) {
        String text = ctx.INT().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_EQ, true, Integer.parseInt(text));
    }

    @Override
    public ValueLocation visitLocation_and(ExpressionParser.Location_andContext ctx) {
        String text = ctx.INT().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_LE, false, Integer.parseInt(text));
    }

    @Override
    public ValueLocation visitLocation_and_e(ExpressionParser.Location_and_eContext ctx) {
        String text = ctx.LOCATION_AND_E().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_EQ, false, this.extractNum(text));
    }

    @Override
    public ValueLocation visitLocation_and_l(ExpressionParser.Location_and_lContext ctx) {
        String text = ctx.LOCATION_AND_L().getText().trim();
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, false, this.extractNum(text));
    }

    @Override
    public ValueLocation visitLocation_and_sen(ExpressionParser.Location_and_senContext ctx) {
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, false, 0, ValueLocation.SenSeg.SEN);
    }

    @Override
    public ValueLocation visitLocation_and_seg(ExpressionParser.Location_and_segContext ctx) {
        return new ValueLocation(ItemOperator.ITEM_OPERATOR_GE, false, 0, ValueLocation.SenSeg.SEG);
    }

    // 提取数字，例如：pre/1L 提取 1
    private int extractNum(String str) {
        return Integer.parseInt(str.substring(4, str.length() - 1));
    }

}
