grammar chat;

start
    : expression EOF
    ;

expression
    : orExpr
    ;

orExpr
    : andExpr (OR andExpr)*
    ;

andExpr
    : subExpr (AND subExpr)*
    ;

subExpr
    : LPAREN expression RPAREN
    | field OPERATOR value
    ;

field
    : ID
    ;

value
    : STRING
    ;

AND
    : 'and' {isNotFollowedByLetterOrDigit()}?
    ;

OR
    : 'or'
    ;

OPERATOR
    : '=' | '>'
    ;

LPAREN
    : '('
    ;

RPAREN
    : ')'
    ;

ID
    : ~[and+\s=()]+
    ;

STRING
    : '"' (ESC | ~["\\])* '"'
    ;

fragment ESC
    : '\\' .
    ;

// Semantic predicate to check if the following character is a letter or digit
@parser::members {
    public boolean isFollowedByLetterOrDigit() {
        return Character.isLetterOrDigit(_input.LA(1));
    }
}

// Semantic predicate to check if the following character is NOT a letter or digit
@parser::members {
    public boolean isNotFollowedByLetterOrDigit() {
        return !isFollowedByLetterOrDigit();
    }
}
