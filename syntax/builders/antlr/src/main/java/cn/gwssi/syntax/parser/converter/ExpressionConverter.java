package cn.gwssi.syntax.parser.converter;

import cn.gwssi.common.exception.IPErrorException;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.converter.error.ErrorHandler;
import cn.gwssi.syntax.parser.converter.error.ErrorStrategy;
import cn.gwssi.syntax.parser.converter.error.ParseCancelWithDetailException;
import cn.gwssi.syntax.parser.converter.visitor.StatVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionLexer;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import org.antlr.v4.runtime.ANTLRInputStream;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.DefaultErrorStrategy;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Set;

public class ExpressionConverter {

    private static ExpressionConverter converter;
    private ExpressionConverter() {}

    public static ExpressionConverter getInstance() {
        if (null == converter) {
            converter = new ExpressionConverter();
        }

        return converter;
    }

    /**
     * 表达式转为 IPCondition
     */
    public IPCondition doFilter(String expression, String defaultField) throws IPException {
        if (StringUtils.isBlank(expression)) {
            return new IPCondition();
        }

        IPCondition condition;
        try {
            condition = this.convert(expression, defaultField);
        } catch (Exception e) {
            e.printStackTrace();
            throw new IPErrorException("000000", e.getMessage());
        }

        return condition;
    }

    /**
     * 表达式检查
     */
    public ParserError doCheck(String expression, String defaultField) {
        if (StringUtils.isBlank(expression)) {
            return null;
        }

        try {
            this.convert(expression, defaultField);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof ParseCancelWithDetailException) {
                return ((ParseCancelWithDetailException)e).getParserError();
            }

            return new ParserError();
        }

        return null;
    }

    /**
     * 表达式提取值
     */
    public Set<String> doExtract(String expression) throws IPException {
        if (StringUtils.isBlank(expression)) {
            return Collections.emptySet();
        }

        Set<String> values;
        try {
            ParseTree tree = this.parse(expression);
            StatVisitor visitor = new StatVisitor("mbi");
            visitor.visit(tree);
            values = visitor.getValues();
        } catch (Exception e) {
            e.printStackTrace();
            throw new IPErrorException("000000", e.getMessage());
        }

        return values;
    }

    /**
     * 表达式提取值
     */
    public Multimap<String, String> doExtract(String expression, String defaultField) throws IPException {
        if (StringUtils.isBlank(expression)) {
            return ArrayListMultimap.create();
        }

        Multimap<String, String> values;
        try {
            ParseTree tree = this.parse(expression);
            StatVisitor visitor = new StatVisitor(defaultField);
            visitor.visit(tree);
            values = visitor.getKeyValues();
        } catch (Exception e) {
            e.printStackTrace();
            throw new IPErrorException("000000", e.getMessage());
        }

        return values;
    }

    private IPCondition convert(String expression, String defaultField) {
        ParseTree tree = this.parse(expression);
        StatVisitor visitor = new StatVisitor(defaultField);
        IPCondition condition = visitor.visit(tree);

        // 后面的几步处理都需要树形机构
        // 如果表达式为 "xxx"，解析的 condition 只有叶子节点，后面处理的时候会报错。所以在这里就加上父节点
        if (condition.isLeafCondition()) {
            IPCondition ipCondition = new IPCondition();
            ipCondition.addSubCondition(condition);
            return ipCondition;
        } else {
            return condition;
        }
    }

    private ParseTree parse(String expression) {
        expression = escapeExpr(expression);

        ANTLRInputStream input = new ANTLRInputStream(expression);
        ExpressionLexer lexer = new ExpressionLexer(input);
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        ExpressionParser parser = new ExpressionParser(tokens);

        /**
         * 原异常提示为英文，默认情况下，所有类型的错误，处理都在 {@link DefaultErrorStrategy} 这个类
         * 如果要汉化，修改 {@link ErrorStrategy} 就够了
         * 这里不做汉化，把错误类型归纳了一下，在 listener 中 throw 出来了
         */
        parser.setErrorHandler(new ErrorStrategy());
        parser.removeErrorListeners();
        parser.addErrorListener(new ErrorHandler());

        return parser.expression();
    }

    // 处理特殊字符
    private String escapeExpr(String expression) {
        return expression.replaceAll("\u00A0"," ");
    }

}
