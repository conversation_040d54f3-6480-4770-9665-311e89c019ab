// Generated from src/main/java/cn/gwssi/syntax/parser/expression/Expression.g4 by ANTLR 4.13.2
package cn.gwssi.syntax.parser.expression;
import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class ExpressionLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.13.2", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		T__0=1, VALUEESCAPE=2, CONNECTOR_ERROR=3, CONNECTOR_AND=4, CONNECTOR_OR=5, 
		CONNECTOR_XOR=6, CONNECTOR_NOT=7, CONNECTOR_IN=8, LOCATION_PRE_L=9, LOCATION_AND_L=10, 
		LOCATION_AND_E=11, STRING_QUOTES=12, UNICODE_STRING_QUOTES=13, STRING_QUOTES_SINGLE=14, 
		UNICODE_STRING_QUOTES_SINGLE=15, BRACKETS_L=16, BRACKETS_R=17, POWER=18, 
		ESC=19, SPACE=20, EQ=21, EQA=22, NE=23, GT=24, GE=25, LT=26, LE=27, IN=28, 
		FREQ=29, PRE=30, EQU=31, SEN=32, SEG=33, AND_L=34, TRUECASE=35, BOOST=36, 
		INT=37, WORD=38, ANY=39, WS=40;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	private static String[] makeRuleNames() {
		return new String[] {
			"T__0", "VALUEESCAPE", "CONNECTOR_ERROR", "CONNECTOR_AND", "CONNECTOR_OR", 
			"CONNECTOR_XOR", "CONNECTOR_NOT", "CONNECTOR_IN", "LOCATION_PRE_L", "LOCATION_AND_L", 
			"LOCATION_AND_E", "STRING_QUOTES", "UNICODE_STRING_QUOTES", "STRING_QUOTES_SINGLE", 
			"UNICODE_STRING_QUOTES_SINGLE", "BRACKETS_L", "BRACKETS_R", "POWER", 
			"ESC", "SPACE", "EQ", "EQA", "NE", "GT", "GE", "LT", "LE", "IN", "FREQ", 
			"AND", "OR", "NOT", "XOR", "PRE", "EQU", "SEN", "SEG", "AND_L", "TRUECASE", 
			"BOOST", "INT", "DIGIT", "WORD", "ANY", "WS"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'/'", null, null, null, null, null, null, null, null, null, null, 
			null, null, null, null, "'('", "')'", "'^'", "'\\'", null, "'='", "'+='", 
			"'!='", "'>'", "'>='", "'<'", "'<='"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, null, "VALUEESCAPE", "CONNECTOR_ERROR", "CONNECTOR_AND", "CONNECTOR_OR", 
			"CONNECTOR_XOR", "CONNECTOR_NOT", "CONNECTOR_IN", "LOCATION_PRE_L", "LOCATION_AND_L", 
			"LOCATION_AND_E", "STRING_QUOTES", "UNICODE_STRING_QUOTES", "STRING_QUOTES_SINGLE", 
			"UNICODE_STRING_QUOTES_SINGLE", "BRACKETS_L", "BRACKETS_R", "POWER", 
			"ESC", "SPACE", "EQ", "EQA", "NE", "GT", "GE", "LT", "LE", "IN", "FREQ", 
			"PRE", "EQU", "SEN", "SEG", "AND_L", "TRUECASE", "BOOST", "INT", "WORD", 
			"ANY", "WS"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public ExpressionLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "Expression.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\u0004\u0000(\u0143\u0006\uffff\uffff\u0002\u0000\u0007\u0000\u0002\u0001"+
		"\u0007\u0001\u0002\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004"+
		"\u0007\u0004\u0002\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007"+
		"\u0007\u0007\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b"+
		"\u0007\u000b\u0002\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002"+
		"\u000f\u0007\u000f\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002"+
		"\u0012\u0007\u0012\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002"+
		"\u0015\u0007\u0015\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002"+
		"\u0018\u0007\u0018\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002"+
		"\u001b\u0007\u001b\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002"+
		"\u001e\u0007\u001e\u0002\u001f\u0007\u001f\u0002 \u0007 \u0002!\u0007"+
		"!\u0002\"\u0007\"\u0002#\u0007#\u0002$\u0007$\u0002%\u0007%\u0002&\u0007"+
		"&\u0002\'\u0007\'\u0002(\u0007(\u0002)\u0007)\u0002*\u0007*\u0002+\u0007"+
		"+\u0002,\u0007,\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001"+
		"\u0001\u0001\u0001\u0001\u0001\u0003\u0001c\b\u0001\u0001\u0002\u0001"+
		"\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0003\u0002j\b\u0002\u0001"+
		"\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0003\u0002q\b"+
		"\u0002\u0004\u0002s\b\u0002\u000b\u0002\f\u0002t\u0001\u0002\u0001\u0002"+
		"\u0001\u0003\u0001\u0003\u0001\u0003\u0001\u0003\u0001\u0004\u0001\u0004"+
		"\u0001\u0004\u0001\u0004\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005"+
		"\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006"+
		"\u0001\u0006\u0003\u0006\u008c\b\u0006\u0001\u0007\u0001\u0007\u0001\u0007"+
		"\u0001\u0007\u0001\b\u0001\b\u0001\b\u0001\b\u0001\t\u0001\t\u0001\t\u0001"+
		"\t\u0001\n\u0001\n\u0001\n\u0001\n\u0001\u000b\u0001\u000b\u0001\u000b"+
		"\u0001\u000b\u0001\u000b\u0005\u000b\u00a3\b\u000b\n\u000b\f\u000b\u00a6"+
		"\t\u000b\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001\f\u0001\f\u0001"+
		"\f\u0001\f\u0001\f\u0001\f\u0005\f\u00b2\b\f\n\f\f\f\u00b5\t\f\u0001\f"+
		"\u0001\f\u0001\r\u0001\r\u0001\r\u0001\r\u0001\r\u0005\r\u00be\b\r\n\r"+
		"\f\r\u00c1\t\r\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000e\u0001"+
		"\u000e\u0001\u000e\u0001\u000e\u0001\u000e\u0001\u000e\u0005\u000e\u00cd"+
		"\b\u000e\n\u000e\f\u000e\u00d0\t\u000e\u0001\u000e\u0001\u000e\u0001\u000f"+
		"\u0001\u000f\u0001\u0010\u0001\u0010\u0001\u0011\u0001\u0011\u0001\u0012"+
		"\u0001\u0012\u0001\u0013\u0004\u0013\u00dd\b\u0013\u000b\u0013\f\u0013"+
		"\u00de\u0001\u0014\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0015\u0001"+
		"\u0016\u0001\u0016\u0001\u0016\u0001\u0017\u0001\u0017\u0001\u0018\u0001"+
		"\u0018\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u001a\u0001\u001a\u0001"+
		"\u001a\u0001\u001b\u0001\u001b\u0001\u001b\u0001\u001c\u0001\u001c\u0001"+
		"\u001c\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001d\u0001\u001d\u0001"+
		"\u001d\u0001\u001d\u0001\u001e\u0001\u001e\u0001\u001e\u0001\u001f\u0001"+
		"\u001f\u0001\u001f\u0001\u001f\u0001 \u0001 \u0001 \u0001 \u0001!\u0001"+
		"!\u0001!\u0001!\u0001!\u0001\"\u0001\"\u0001\"\u0001\"\u0001\"\u0001#"+
		"\u0001#\u0001#\u0001#\u0001$\u0001$\u0001$\u0001$\u0001%\u0001%\u0001"+
		"%\u0001&\u0001&\u0001&\u0001&\u0001&\u0001&\u0001&\u0001&\u0001&\u0001"+
		"\'\u0001\'\u0001\'\u0001\'\u0001\'\u0001\'\u0001(\u0004(\u0130\b(\u000b"+
		"(\f(\u0131\u0001)\u0001)\u0001*\u0004*\u0137\b*\u000b*\f*\u0138\u0001"+
		"+\u0001+\u0001,\u0004,\u013e\b,\u000b,\f,\u013f\u0001,\u0001,\u0000\u0000"+
		"-\u0001\u0001\u0003\u0002\u0005\u0003\u0007\u0004\t\u0005\u000b\u0006"+
		"\r\u0007\u000f\b\u0011\t\u0013\n\u0015\u000b\u0017\f\u0019\r\u001b\u000e"+
		"\u001d\u000f\u001f\u0010!\u0011#\u0012%\u0013\'\u0014)\u0015+\u0016-\u0017"+
		"/\u00181\u00193\u001a5\u001b7\u001c9\u001d;\u0000=\u0000?\u0000A\u0000"+
		"C\u001eE\u001fG I!K\"M#O$Q%S\u0000U&W\'Y(\u0001\u0000\u001a\u0002\u0000"+
		"\"\"\'\'\u0002\u0000LLll\u0001\u0000\"\"\u0003\u0000\"\"()\\\\\u0001\u0000"+
		"\'\'\u0002\u0000\')\\\\\u0002\u0000IIii\u0002\u0000NNnn\u0002\u0000FF"+
		"ff\u0002\u0000RRrr\u0002\u0000EEee\u0002\u0000QQqq\u0002\u0000AAaa\u0002"+
		"\u0000DDdd\u0002\u0000OOoo\u0002\u0000TTtt\u0002\u0000XXxx\u0002\u0000"+
		"PPpp\u0002\u0000UUuu\u0002\u0000SSss\u0002\u0000GGgg\u0002\u0000CCcc\u0002"+
		"\u0000BBbb\u0001\u000009\b\u0000--09A[]]__az\u4e00\u8000\u9fa5\u8000\uff08"+
		"\u8000\uff09\u0002\u0000\t\n\r\r\u0154\u0000\u0001\u0001\u0000\u0000\u0000"+
		"\u0000\u0003\u0001\u0000\u0000\u0000\u0000\u0005\u0001\u0000\u0000\u0000"+
		"\u0000\u0007\u0001\u0000\u0000\u0000\u0000\t\u0001\u0000\u0000\u0000\u0000"+
		"\u000b\u0001\u0000\u0000\u0000\u0000\r\u0001\u0000\u0000\u0000\u0000\u000f"+
		"\u0001\u0000\u0000\u0000\u0000\u0011\u0001\u0000\u0000\u0000\u0000\u0013"+
		"\u0001\u0000\u0000\u0000\u0000\u0015\u0001\u0000\u0000\u0000\u0000\u0017"+
		"\u0001\u0000\u0000\u0000\u0000\u0019\u0001\u0000\u0000\u0000\u0000\u001b"+
		"\u0001\u0000\u0000\u0000\u0000\u001d\u0001\u0000\u0000\u0000\u0000\u001f"+
		"\u0001\u0000\u0000\u0000\u0000!\u0001\u0000\u0000\u0000\u0000#\u0001\u0000"+
		"\u0000\u0000\u0000%\u0001\u0000\u0000\u0000\u0000\'\u0001\u0000\u0000"+
		"\u0000\u0000)\u0001\u0000\u0000\u0000\u0000+\u0001\u0000\u0000\u0000\u0000"+
		"-\u0001\u0000\u0000\u0000\u0000/\u0001\u0000\u0000\u0000\u00001\u0001"+
		"\u0000\u0000\u0000\u00003\u0001\u0000\u0000\u0000\u00005\u0001\u0000\u0000"+
		"\u0000\u00007\u0001\u0000\u0000\u0000\u00009\u0001\u0000\u0000\u0000\u0000"+
		"C\u0001\u0000\u0000\u0000\u0000E\u0001\u0000\u0000\u0000\u0000G\u0001"+
		"\u0000\u0000\u0000\u0000I\u0001\u0000\u0000\u0000\u0000K\u0001\u0000\u0000"+
		"\u0000\u0000M\u0001\u0000\u0000\u0000\u0000O\u0001\u0000\u0000\u0000\u0000"+
		"Q\u0001\u0000\u0000\u0000\u0000U\u0001\u0000\u0000\u0000\u0000W\u0001"+
		"\u0000\u0000\u0000\u0000Y\u0001\u0000\u0000\u0000\u0001[\u0001\u0000\u0000"+
		"\u0000\u0003]\u0001\u0000\u0000\u0000\u0005d\u0001\u0000\u0000\u0000\u0007"+
		"x\u0001\u0000\u0000\u0000\t|\u0001\u0000\u0000\u0000\u000b\u0080\u0001"+
		"\u0000\u0000\u0000\r\u008b\u0001\u0000\u0000\u0000\u000f\u008d\u0001\u0000"+
		"\u0000\u0000\u0011\u0091\u0001\u0000\u0000\u0000\u0013\u0095\u0001\u0000"+
		"\u0000\u0000\u0015\u0099\u0001\u0000\u0000\u0000\u0017\u009d\u0001\u0000"+
		"\u0000\u0000\u0019\u00a9\u0001\u0000\u0000\u0000\u001b\u00b8\u0001\u0000"+
		"\u0000\u0000\u001d\u00c4\u0001\u0000\u0000\u0000\u001f\u00d3\u0001\u0000"+
		"\u0000\u0000!\u00d5\u0001\u0000\u0000\u0000#\u00d7\u0001\u0000\u0000\u0000"+
		"%\u00d9\u0001\u0000\u0000\u0000\'\u00dc\u0001\u0000\u0000\u0000)\u00e0"+
		"\u0001\u0000\u0000\u0000+\u00e2\u0001\u0000\u0000\u0000-\u00e5\u0001\u0000"+
		"\u0000\u0000/\u00e8\u0001\u0000\u0000\u00001\u00ea\u0001\u0000\u0000\u0000"+
		"3\u00ed\u0001\u0000\u0000\u00005\u00ef\u0001\u0000\u0000\u00007\u00f2"+
		"\u0001\u0000\u0000\u00009\u00f5\u0001\u0000\u0000\u0000;\u00fb\u0001\u0000"+
		"\u0000\u0000=\u00ff\u0001\u0000\u0000\u0000?\u0102\u0001\u0000\u0000\u0000"+
		"A\u0106\u0001\u0000\u0000\u0000C\u010a\u0001\u0000\u0000\u0000E\u010f"+
		"\u0001\u0000\u0000\u0000G\u0114\u0001\u0000\u0000\u0000I\u0118\u0001\u0000"+
		"\u0000\u0000K\u011c\u0001\u0000\u0000\u0000M\u011f\u0001\u0000\u0000\u0000"+
		"O\u0128\u0001\u0000\u0000\u0000Q\u012f\u0001\u0000\u0000\u0000S\u0133"+
		"\u0001\u0000\u0000\u0000U\u0136\u0001\u0000\u0000\u0000W\u013a\u0001\u0000"+
		"\u0000\u0000Y\u013d\u0001\u0000\u0000\u0000[\\\u0005/\u0000\u0000\\\u0002"+
		"\u0001\u0000\u0000\u0000]b\u0003%\u0012\u0000^c\u0003%\u0012\u0000_c\u0003"+
		"\u001f\u000f\u0000`c\u0003!\u0010\u0000ac\u0007\u0000\u0000\u0000b^\u0001"+
		"\u0000\u0000\u0000b_\u0001\u0000\u0000\u0000b`\u0001\u0000\u0000\u0000"+
		"ba\u0001\u0000\u0000\u0000c\u0004\u0001\u0000\u0000\u0000di\u0003\'\u0013"+
		"\u0000ej\u0003;\u001d\u0000fj\u0003=\u001e\u0000gj\u0003A \u0000hj\u0003"+
		"?\u001f\u0000ie\u0001\u0000\u0000\u0000if\u0001\u0000\u0000\u0000ig\u0001"+
		"\u0000\u0000\u0000ih\u0001\u0000\u0000\u0000jr\u0001\u0000\u0000\u0000"+
		"kp\u0003\'\u0013\u0000lq\u0003;\u001d\u0000mq\u0003=\u001e\u0000nq\u0003"+
		"A \u0000oq\u0003?\u001f\u0000pl\u0001\u0000\u0000\u0000pm\u0001\u0000"+
		"\u0000\u0000pn\u0001\u0000\u0000\u0000po\u0001\u0000\u0000\u0000qs\u0001"+
		"\u0000\u0000\u0000rk\u0001\u0000\u0000\u0000st\u0001\u0000\u0000\u0000"+
		"tr\u0001\u0000\u0000\u0000tu\u0001\u0000\u0000\u0000uv\u0001\u0000\u0000"+
		"\u0000vw\u0003\'\u0013\u0000w\u0006\u0001\u0000\u0000\u0000xy\u0003\'"+
		"\u0013\u0000yz\u0003;\u001d\u0000z{\u0003\'\u0013\u0000{\b\u0001\u0000"+
		"\u0000\u0000|}\u0003\'\u0013\u0000}~\u0003=\u001e\u0000~\u007f\u0003\'"+
		"\u0013\u0000\u007f\n\u0001\u0000\u0000\u0000\u0080\u0081\u0003\'\u0013"+
		"\u0000\u0081\u0082\u0003A \u0000\u0082\u0083\u0003\'\u0013\u0000\u0083"+
		"\f\u0001\u0000\u0000\u0000\u0084\u0085\u0003\'\u0013\u0000\u0085\u0086"+
		"\u0003?\u001f\u0000\u0086\u0087\u0003\'\u0013\u0000\u0087\u008c\u0001"+
		"\u0000\u0000\u0000\u0088\u0089\u0003?\u001f\u0000\u0089\u008a\u0003\'"+
		"\u0013\u0000\u008a\u008c\u0001\u0000\u0000\u0000\u008b\u0084\u0001\u0000"+
		"\u0000\u0000\u008b\u0088\u0001\u0000\u0000\u0000\u008c\u000e\u0001\u0000"+
		"\u0000\u0000\u008d\u008e\u0003\'\u0013\u0000\u008e\u008f\u00037\u001b"+
		"\u0000\u008f\u0090\u0003\'\u0013\u0000\u0090\u0010\u0001\u0000\u0000\u0000"+
		"\u0091\u0092\u0003C!\u0000\u0092\u0093\u0003Q(\u0000\u0093\u0094\u0007"+
		"\u0001\u0000\u0000\u0094\u0012\u0001\u0000\u0000\u0000\u0095\u0096\u0003"+
		"K%\u0000\u0096\u0097\u0003Q(\u0000\u0097\u0098\u0007\u0001\u0000\u0000"+
		"\u0098\u0014\u0001\u0000\u0000\u0000\u0099\u009a\u0003K%\u0000\u009a\u009b"+
		"\u0003Q(\u0000\u009b\u009c\u0005#\u0000\u0000\u009c\u0016\u0001\u0000"+
		"\u0000\u0000\u009d\u00a4\u0005\"\u0000\u0000\u009e\u00a3\b\u0002\u0000"+
		"\u0000\u009f\u00a0\u0003%\u0012\u0000\u00a0\u00a1\u0007\u0003\u0000\u0000"+
		"\u00a1\u00a3\u0001\u0000\u0000\u0000\u00a2\u009e\u0001\u0000\u0000\u0000"+
		"\u00a2\u009f\u0001\u0000\u0000\u0000\u00a3\u00a6\u0001\u0000\u0000\u0000"+
		"\u00a4\u00a2\u0001\u0000\u0000\u0000\u00a4\u00a5\u0001\u0000\u0000\u0000"+
		"\u00a5\u00a7\u0001\u0000\u0000\u0000\u00a6\u00a4\u0001\u0000\u0000\u0000"+
		"\u00a7\u00a8\u0005\"\u0000\u0000\u00a8\u0018\u0001\u0000\u0000\u0000\u00a9"+
		"\u00aa\u0005U\u0000\u0000\u00aa\u00ab\u0005&\u0000\u0000\u00ab\u00ac\u0005"+
		"\"\u0000\u0000\u00ac\u00b3\u0001\u0000\u0000\u0000\u00ad\u00b2\b\u0002"+
		"\u0000\u0000\u00ae\u00af\u0003%\u0012\u0000\u00af\u00b0\u0007\u0003\u0000"+
		"\u0000\u00b0\u00b2\u0001\u0000\u0000\u0000\u00b1\u00ad\u0001\u0000\u0000"+
		"\u0000\u00b1\u00ae\u0001\u0000\u0000\u0000\u00b2\u00b5\u0001\u0000\u0000"+
		"\u0000\u00b3\u00b1\u0001\u0000\u0000\u0000\u00b3\u00b4\u0001\u0000\u0000"+
		"\u0000\u00b4\u00b6\u0001\u0000\u0000\u0000\u00b5\u00b3\u0001\u0000\u0000"+
		"\u0000\u00b6\u00b7\u0005\"\u0000\u0000\u00b7\u001a\u0001\u0000\u0000\u0000"+
		"\u00b8\u00bf\u0005\'\u0000\u0000\u00b9\u00be\b\u0004\u0000\u0000\u00ba"+
		"\u00bb\u0003%\u0012\u0000\u00bb\u00bc\u0007\u0005\u0000\u0000\u00bc\u00be"+
		"\u0001\u0000\u0000\u0000\u00bd\u00b9\u0001\u0000\u0000\u0000\u00bd\u00ba"+
		"\u0001\u0000\u0000\u0000\u00be\u00c1\u0001\u0000\u0000\u0000\u00bf\u00bd"+
		"\u0001\u0000\u0000\u0000\u00bf\u00c0\u0001\u0000\u0000\u0000\u00c0\u00c2"+
		"\u0001\u0000\u0000\u0000\u00c1\u00bf\u0001\u0000\u0000\u0000\u00c2\u00c3"+
		"\u0005\'\u0000\u0000\u00c3\u001c\u0001\u0000\u0000\u0000\u00c4\u00c5\u0005"+
		"U\u0000\u0000\u00c5\u00c6\u0005&\u0000\u0000\u00c6\u00c7\u0005\'\u0000"+
		"\u0000\u00c7\u00ce\u0001\u0000\u0000\u0000\u00c8\u00cd\b\u0004\u0000\u0000"+
		"\u00c9\u00ca\u0003%\u0012\u0000\u00ca\u00cb\u0007\u0005\u0000\u0000\u00cb"+
		"\u00cd\u0001\u0000\u0000\u0000\u00cc\u00c8\u0001\u0000\u0000\u0000\u00cc"+
		"\u00c9\u0001\u0000\u0000\u0000\u00cd\u00d0\u0001\u0000\u0000\u0000\u00ce"+
		"\u00cc\u0001\u0000\u0000\u0000\u00ce\u00cf\u0001\u0000\u0000\u0000\u00cf"+
		"\u00d1\u0001\u0000\u0000\u0000\u00d0\u00ce\u0001\u0000\u0000\u0000\u00d1"+
		"\u00d2\u0005\'\u0000\u0000\u00d2\u001e\u0001\u0000\u0000\u0000\u00d3\u00d4"+
		"\u0005(\u0000\u0000\u00d4 \u0001\u0000\u0000\u0000\u00d5\u00d6\u0005)"+
		"\u0000\u0000\u00d6\"\u0001\u0000\u0000\u0000\u00d7\u00d8\u0005^\u0000"+
		"\u0000\u00d8$\u0001\u0000\u0000\u0000\u00d9\u00da\u0005\\\u0000\u0000"+
		"\u00da&\u0001\u0000\u0000\u0000\u00db\u00dd\u0005 \u0000\u0000\u00dc\u00db"+
		"\u0001\u0000\u0000\u0000\u00dd\u00de\u0001\u0000\u0000\u0000\u00de\u00dc"+
		"\u0001\u0000\u0000\u0000\u00de\u00df\u0001\u0000\u0000\u0000\u00df(\u0001"+
		"\u0000\u0000\u0000\u00e0\u00e1\u0005=\u0000\u0000\u00e1*\u0001\u0000\u0000"+
		"\u0000\u00e2\u00e3\u0005+\u0000\u0000\u00e3\u00e4\u0005=\u0000\u0000\u00e4"+
		",\u0001\u0000\u0000\u0000\u00e5\u00e6\u0005!\u0000\u0000\u00e6\u00e7\u0005"+
		"=\u0000\u0000\u00e7.\u0001\u0000\u0000\u0000\u00e8\u00e9\u0005>\u0000"+
		"\u0000\u00e90\u0001\u0000\u0000\u0000\u00ea\u00eb\u0005>\u0000\u0000\u00eb"+
		"\u00ec\u0005=\u0000\u0000\u00ec2\u0001\u0000\u0000\u0000\u00ed\u00ee\u0005"+
		"<\u0000\u0000\u00ee4\u0001\u0000\u0000\u0000\u00ef\u00f0\u0005<\u0000"+
		"\u0000\u00f0\u00f1\u0005=\u0000\u0000\u00f16\u0001\u0000\u0000\u0000\u00f2"+
		"\u00f3\u0007\u0006\u0000\u0000\u00f3\u00f4\u0007\u0007\u0000\u0000\u00f4"+
		"8\u0001\u0000\u0000\u0000\u00f5\u00f6\u0005/\u0000\u0000\u00f6\u00f7\u0007"+
		"\b\u0000\u0000\u00f7\u00f8\u0007\t\u0000\u0000\u00f8\u00f9\u0007\n\u0000"+
		"\u0000\u00f9\u00fa\u0007\u000b\u0000\u0000\u00fa:\u0001\u0000\u0000\u0000"+
		"\u00fb\u00fc\u0007\f\u0000\u0000\u00fc\u00fd\u0007\u0007\u0000\u0000\u00fd"+
		"\u00fe\u0007\r\u0000\u0000\u00fe<\u0001\u0000\u0000\u0000\u00ff\u0100"+
		"\u0007\u000e\u0000\u0000\u0100\u0101\u0007\t\u0000\u0000\u0101>\u0001"+
		"\u0000\u0000\u0000\u0102\u0103\u0007\u0007\u0000\u0000\u0103\u0104\u0007"+
		"\u000e\u0000\u0000\u0104\u0105\u0007\u000f\u0000\u0000\u0105@\u0001\u0000"+
		"\u0000\u0000\u0106\u0107\u0007\u0010\u0000\u0000\u0107\u0108\u0007\u000e"+
		"\u0000\u0000\u0108\u0109\u0007\t\u0000\u0000\u0109B\u0001\u0000\u0000"+
		"\u0000\u010a\u010b\u0007\u0011\u0000\u0000\u010b\u010c\u0007\t\u0000\u0000"+
		"\u010c\u010d\u0007\n\u0000\u0000\u010d\u010e\u0005/\u0000\u0000\u010e"+
		"D\u0001\u0000\u0000\u0000\u010f\u0110\u0007\n\u0000\u0000\u0110\u0111"+
		"\u0007\u000b\u0000\u0000\u0111\u0112\u0007\u0012\u0000\u0000\u0112\u0113"+
		"\u0005/\u0000\u0000\u0113F\u0001\u0000\u0000\u0000\u0114\u0115\u0007\u0013"+
		"\u0000\u0000\u0115\u0116\u0007\n\u0000\u0000\u0116\u0117\u0007\u0007\u0000"+
		"\u0000\u0117H\u0001\u0000\u0000\u0000\u0118\u0119\u0007\u0013\u0000\u0000"+
		"\u0119\u011a\u0007\n\u0000\u0000\u011a\u011b\u0007\u0014\u0000\u0000\u011b"+
		"J\u0001\u0000\u0000\u0000\u011c\u011d\u0003;\u001d\u0000\u011d\u011e\u0005"+
		"/\u0000\u0000\u011eL\u0001\u0000\u0000\u0000\u011f\u0120\u0007\u000f\u0000"+
		"\u0000\u0120\u0121\u0007\t\u0000\u0000\u0121\u0122\u0007\u0012\u0000\u0000"+
		"\u0122\u0123\u0007\n\u0000\u0000\u0123\u0124\u0007\u0015\u0000\u0000\u0124"+
		"\u0125\u0007\f\u0000\u0000\u0125\u0126\u0007\u0013\u0000\u0000\u0126\u0127"+
		"\u0007\n\u0000\u0000\u0127N\u0001\u0000\u0000\u0000\u0128\u0129\u0007"+
		"\u0016\u0000\u0000\u0129\u012a\u0007\u000e\u0000\u0000\u012a\u012b\u0007"+
		"\u000e\u0000\u0000\u012b\u012c\u0007\u0013\u0000\u0000\u012c\u012d\u0007"+
		"\u000f\u0000\u0000\u012dP\u0001\u0000\u0000\u0000\u012e\u0130\u0003S)"+
		"\u0000\u012f\u012e\u0001\u0000\u0000\u0000\u0130\u0131\u0001\u0000\u0000"+
		"\u0000\u0131\u012f\u0001\u0000\u0000\u0000\u0131\u0132\u0001\u0000\u0000"+
		"\u0000\u0132R\u0001\u0000\u0000\u0000\u0133\u0134\u0007\u0017\u0000\u0000"+
		"\u0134T\u0001\u0000\u0000\u0000\u0135\u0137\u0007\u0018\u0000\u0000\u0136"+
		"\u0135\u0001\u0000\u0000\u0000\u0137\u0138\u0001\u0000\u0000\u0000\u0138"+
		"\u0136\u0001\u0000\u0000\u0000\u0138\u0139\u0001\u0000\u0000\u0000\u0139"+
		"V\u0001\u0000\u0000\u0000\u013a\u013b\b\u0019\u0000\u0000\u013bX\u0001"+
		"\u0000\u0000\u0000\u013c\u013e\u0007\u0019\u0000\u0000\u013d\u013c\u0001"+
		"\u0000\u0000\u0000\u013e\u013f\u0001\u0000\u0000\u0000\u013f\u013d\u0001"+
		"\u0000\u0000\u0000\u013f\u0140\u0001\u0000\u0000\u0000\u0140\u0141\u0001"+
		"\u0000\u0000\u0000\u0141\u0142\u0006,\u0000\u0000\u0142Z\u0001\u0000\u0000"+
		"\u0000\u0012\u0000bipt\u008b\u00a2\u00a4\u00b1\u00b3\u00bd\u00bf\u00cc"+
		"\u00ce\u00de\u0131\u0138\u013f\u0001\u0006\u0000\u0000";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}