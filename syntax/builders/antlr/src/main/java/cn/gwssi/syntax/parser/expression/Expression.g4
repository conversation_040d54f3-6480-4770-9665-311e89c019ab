grammar Expression;

// 入口
expression
  : statement SPACE* EOF
  ;

// 单个或多个表达式
statement
  : stat # statSingle
  | statMulti # statMultiple
  ;

// 单个表达式
stat
  : parenth_l statement parenth_r # statNested
  | field comparator valueExpr # statFinal
  | field comparator_in parenth_l valueIn parenth_r # statIn
  | valueExpr # statOnlyValue
  ;

// 多个表达式
// 为什么单独写一条呢？This is a sad story :(
// 与 #valueSerial 对比：语句前后有其它符号时，只有一层连续运算；没有时，解析为多层嵌套运算
statMulti
  : stat (connector stat)+ # statSerial
  ;

// 值的表达式，操作符后面的部分
valueExpr
  : parenth_l valueExpr parenth_r # valueNested
  // 这个要放在 valueNormal 之前，否则 not 会被解析成 stat
  // 这个和 valueSerial 有点类似，区别在于连接符只处理 not
  // FIXED 2023-02-28 使用 valueSerial 的规则，这个不能要了，not外层也要加括号
  // 例如：a=b not c=d。其中 c=d 会被解析成 value 而不是 stat
//  | valueFinal CONNECTOR_NOT valueExpr # valueNot
  // FIXED 2024-01-03 not，例如 not a
  | CONNECTOR_NOT valueExpr # valueNot
  // 频率，例如 clock/freq >= 4
  | valueFinal FREQ comparator INT # valueFreq
  // truecase，例如 truecase(c)
  | TRUECASE parenth_l valueFinal parenth_r # valueTruecase
  // boost，例如 boost(c^5)
  | BOOST parenth_l wordsBoost (connector wordsBoost)* parenth_r # valueBoost
  // 对于多个值必须带括号，例如不允许 a=b or c，必须写成 a=(b or c)
  | parenth_l valueExpr (connector valueExpr)+ parenth_r # valueSerial
  // 单值，例如 a1 或带空格的 'a 1'
  | valueFinal # valueNormal
  // 位置运算，必须带括号
  // FIXED 2022-12-29 去掉了括号
  // 更严格的语法见 ExpressionBak5.g4 文件，但是 visitor 太太太难写了 :(:(:(
  // 去掉括号后可以与 #valueSerial 对比：这里没办法解析成连续运算，只能是树型的、嵌套的
  // 每一层嵌套要读出来，拉平，手动解析为连续运算
  | valueExpr (location valueExpr) # valueLocation
  ;

// 字段
field
  : words+
  ;

// 字段，可以包含空格，虽然空格已经被 skip
words
  : WORD
  | '/'
  | IN
  | comparator_in
  | SPACE
  | INT
  | VALUEESCAPE
  | wordsWithBracket
  ;

// 值，可以包含引号，各种特殊符号
valueFinal
  : quotedWords # valueQuoted
  | quotedWordsSingle # valueQuotedSingle
  | wordsAll+ # valueWords
  ;

// 值，可以包含各种特殊符号，用于 in 表达式
// 支持逗号分隔的值列表，例如 "b","c","d" 或 b,c,d
valueIn
  : valueInItem (COMMA SPACE* valueInItem)*
  ;

// in 表达式中的单个值项
valueInItem
  : quotedWords # valueInQuoted
  | quotedWordsSingle # valueInQuotedSingle
  | wordsAll+ # valueInWords
  ;

// 值，可以包含空格、各种特殊符号、操作符，不能包含小括号
wordsAll
  : words
  | wordsWithBracket
//  | leftNotOprBracket
//  | rightNotOprBracket
//  | quotedWords
//  | quotedWordsSingle
  | comparator
  | comparator_in
  | VALUEESCAPE
  | SPACE
  | ANY
  | POWER
  | BOOST
  | TRUECASE
  | ESC
  ;

// 值，包含权重
wordsBoost: valueFinal POWER INT;

// 值，转义一些特殊符号
// FIXED 2023-12-18 增加单引号和双引号转义，rule 改为 token，否则可能会解析为 quotedWords，例如 a= b\"c"
VALUEESCAPE
  : ESC (ESC | BRACKETS_L | BRACKETS_R | '"' | '\'')
  ;

// 连接符
connector
  : CONNECTOR_ERROR {notifyErrorListeners(_ctx.getStart(), Constant.Error.CONNECTOR_SERIAL, null);}
//  | CONNECTOR_AND_ERROR {notifyErrorListeners(_ctx.getStart(), Constant.Error.CONNECTOR_ERROR, null);}
  | CONNECTOR_AND
  | CONNECTOR_OR
  | CONNECTOR_XOR
  | CONNECTOR_NOT
  ;
CONNECTOR_ERROR: SPACE (AND | OR | XOR | NOT) (SPACE (AND | OR | XOR | NOT))+ SPACE ;
CONNECTOR_AND: SPACE AND SPACE ;
CONNECTOR_OR: SPACE OR SPACE ;
CONNECTOR_XOR: SPACE XOR SPACE ;
CONNECTOR_NOT: SPACE NOT SPACE | NOT SPACE ;
CONNECTOR_IN: SPACE IN SPACE;
//CONNECTOR_AND_ERROR: SPACE AND (')' | EOF);
// FIXED 2023-06-08 这样会影响 AND_L 的识别，不能单独写成一个 token
//CONNECTOR_ALL: SPACE (AND | OR | XOR | NOT) ;

// 操作符
comparator
  : SPACE* (EQ | EQA | NE | GT | GE | LT | LE) SPACE*
  ;

// 操作符，in
comparator_in
  : SPACE* CONNECTOR_IN SPACE*
  ;

wordsWithBracket
  : (WORD | INT | ANY) (parenth_l | parenth_r | WORD | INT | ANY)+ (WORD | INT | ANY)
  ;
//leftNotOprBracket : SPACE* ~(EQ | EQA | NE | GT | GE | LT | LE | IN | BRACKETS_L) SPACE* '(';
//rightNotOprBracket : ')'~(CONNECTOR_AND | CONNECTOR_OR | CONNECTOR_XOR | CONNECTOR_NOT | BRACKETS_R);

// 位置符
location
  : location_pre
  | location_pre_l
  | location_pre_sen
  | location_pre_seg
  | location_equ
  | location_and
  | location_and_e
  | location_and_l
  | location_and_sen
  | location_and_seg
  ;

// 写成 rule 而不是 token 的原因，是为了解析 INT 方便
location_pre: SPACE PRE INT SPACE;
location_pre_l: SPACE LOCATION_PRE_L SPACE;
location_pre_sen: SPACE PRE SEN SPACE;
location_pre_seg: SPACE PRE SEG SPACE;
location_equ: SPACE EQU INT SPACE;
location_and: SPACE AND_L INT SPACE;
location_and_e: SPACE LOCATION_AND_E SPACE;
location_and_l: SPACE LOCATION_AND_L SPACE;
location_and_sen: SPACE AND_L SEN SPACE;
location_and_seg: SPACE AND_L SEG SPACE;

// 后面带字母的要单独写，否则 1L 会识别为 WORD 类型
LOCATION_PRE_L: PRE INT ([Ll]);
LOCATION_AND_L: AND_L INT ([Ll]);
LOCATION_AND_E: AND_L INT '#';

// 带空格的小括号
parenth_l: SPACE* BRACKETS_L SPACE*;
parenth_r: SPACE* BRACKETS_R SPACE*;

// 引号包裹的内容，允许各种符号和 \" \'
quotedWords
  : SPACE* (
    STRING_QUOTES
    | UNICODE_STRING_QUOTES
  ) SPACE*
  ;
quotedWordsSingle
  : SPACE* (
    STRING_QUOTES_SINGLE
    | UNICODE_STRING_QUOTES_SINGLE
  ) SPACE*
  ;
STRING_QUOTES
  : '"' (~'"' | ESC [\\"()])* '"'
  ;
UNICODE_STRING_QUOTES
  : 'U&"' ( ~'"' | ESC [\\"()])* '"'
  ;
STRING_QUOTES_SINGLE
  : '\'' ( ~'\'' | ESC [\\'()])* '\''
  ;
UNICODE_STRING_QUOTES_SINGLE
  : 'U&\'' ( ~'\'' | ESC [\\'()])* '\''
  ;

// 一些常量
BRACKETS_L: '(' ;
BRACKETS_R: ')' ;
COMMA: ',' ;
POWER: '^' ;
ESC: '\\' ;
SPACE: (' ')+ ;
EQ: '=' ;
EQA: '+=' ;
NE: '!=' ;
GT: '>' ;
GE: '>=' ;
LT: '<' ;
LE: '<=' ;
IN: ([Ii][Nn]) ;
FREQ: ('/'[Ff][Rr][Ee][Qq]) ;
// fragment 使用规则：1、java代码中不会调用的 2、rule没有直接使用的
fragment
AND: ([Aa][Nn][Dd]) ;
fragment
OR: ([Oo][Rr]) ;
fragment
NOT: ([Nn][Oo][Tt]) ;
fragment
XOR: ([Xx][Oo][Rr]) ;
PRE: ([Pp][Rr][Ee]'/') ;
EQU: ([Ee][Qq][Uu]'/') ;
SEN: ([Ss][Ee][Nn]) ;
SEG: ([Ss][Ee][Gg]) ;
AND_L: AND '/' ;
TRUECASE: ([Tt][Rr][Uu][Ee][Cc][Aa][Ss][Ee]);
BOOST: ([Bb][Oo][Oo][Ss][Tt]);

// 数字
INT: DIGIT+ ;
fragment
DIGIT: [0-9] ;
// 字段，限制字符和符号，不能随意输入
// 分别为：字母、数字、汉字、下划线_、斜杠/、短杠-、中文括号（）、英文中括号[]
// 其中 斜杠/ 放在 field 中处理了
// 注意：移除了逗号，让逗号能被正确识别为 COMMA token
WORD: [a-zA-Z0-9\u4e00-\u9fa5_\u002D\uff08\uff09\u005B\u005D]+;
// 除 skip 之外的任意字符
ANY: ~[\t\r\n] ;

WS: [\t\r\n]+ -> skip ;
