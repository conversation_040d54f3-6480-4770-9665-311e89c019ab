package cn.gwssi.syntax.parser.converter.error;

import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.converter.util.Constant;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ErrorHandler extends BaseErrorListener {

    private static final List<Integer> CONNECTORS = new ArrayList<>();
    private static final List<Integer> COMPARATORS = new ArrayList<>();
    private static final List<Integer> BRACKETS = new ArrayList<>();
    private static final List<Integer> QUOTES = new ArrayList<>();

    static {
        CONNECTORS.add(ExpressionParser.CONNECTOR_AND);
        CONNECTORS.add(ExpressionParser.CONNECTOR_OR);
        CONNECTORS.add(ExpressionParser.CONNECTOR_XOR);
        CONNECTORS.add(ExpressionParser.CONNECTOR_NOT);

        COMPARATORS.add(ExpressionParser.EQ);
        COMPARATORS.add(ExpressionParser.EQA);
        COMPARATORS.add(ExpressionParser.NE);
        COMPARATORS.add(ExpressionParser.GT);
        COMPARATORS.add(ExpressionParser.GE);
        COMPARATORS.add(ExpressionParser.LT);
        COMPARATORS.add(ExpressionParser.LE);

        BRACKETS.add(ExpressionParser.BRACKETS_L);
        BRACKETS.add(ExpressionParser.BRACKETS_R);

        QUOTES.add(ExpressionParser.STRING_QUOTES);
        QUOTES.add(ExpressionParser.UNICODE_STRING_QUOTES);
        QUOTES.add(ExpressionParser.STRING_QUOTES_SINGLE);
        QUOTES.add(ExpressionParser.UNICODE_STRING_QUOTES_SINGLE);
    }

    /**
     * 表达式异常字符猜测，有优先级：
     * 1、直接抛出异常的，最准的
     * 2、offendingSymbol 出现异常之后的字符，一般都不准了
     * 3、parser 中提取的字符，一般是一串字符，不再是单个了。这时候已经没办法猜测原因了，直接抛出去
     * 提取位置分为两种：
     * 单字符异常，结束位置不获取，直接使用开始位置；非单字符异常，获取结束位置
     */
    @Override
    public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol, int line, int charPositionInLine, String msg, RecognitionException e) throws ParseCancellationException {
        ConditionOffset offset = null;
        String token = null;

        // 猜测字符。扫描到错误时直接抛出异常的字符，最准的
        if (e instanceof RecognitionWithDetailException) {
            RecognitionWithDetailException exception = (RecognitionWithDetailException) e;
            token = exception.getChars();
            msg = exception.getMessage();
            offset = this.extractOffset(exception.getStartToken());
        }

        // 猜测字符。异常中提取字符
        if (StringUtils.isBlank(token) && e instanceof NoViableAltException) {
            token = ((NoViableAltException)e).getStartToken().getText();
            offset = this.extractOffset(((NoViableAltException) e).getStartToken());
        }

        // 猜测字符。offendingSymbol 是出现异常之后的字符，一般都不准。
        if (offendingSymbol instanceof Token) {
            // 如果是结束符，取前一个字符
            Token offending = this.currentOrPreviousToken(recognizer, (Token) offendingSymbol);
            int type = offending.getType();

            // 猜测字符，从可能的错误token猜测
            if (StringUtils.isBlank(token)) {
                // 经过上面的处理，offending 基本不可能是结束符
                if (offending.getType() != IntStream.EOF) {
                    token = offending.getText();
                } else if (null == msg) {
                    msg = "意外结束";
                }

                offset = this.extractOffsetWithEnd(offending);
            }

            // 连接符错误
            if (CONNECTORS.contains(type)) {
                ParserError error = this.extractError(offending, Constant.Error.CONNECTOR_ERROR);
                throw new ParseCancelWithDetailException(error);
            }

            // 操作符错误
            if (COMPARATORS.contains(type)) {
                ParserError error = this.extractError(offending, token, Constant.Error.COMPARATOR_ERROR);
                throw new ParseCancelWithDetailException(error);
            }

            // 括号不匹配
            if (BRACKETS.contains(type)) {
                ParserError error = this.extractError(offending, token, Constant.Error.BRACKET_NOT_MATCH);
                throw new ParseCancelWithDetailException(error);
            }

            // 引号不匹配
            if (QUOTES.contains(type)) {
                offset = new ConditionOffset(line, line, charPositionInLine, charPositionInLine, charPositionInLine, charPositionInLine);
                token = this.extractFromToken(token, charPositionInLine);
                ParserError error = ErrorMsg.getInstance().format(offset, token, Constant.Error.QUOTATION_NOT_MATCH);
                throw new ParseCancelWithDetailException(error);
            }

            if (type == IntStream.EOF && charPositionInLine > 0) {
                --charPositionInLine;
            }
        }

        // 猜测字符。从context中提取字符
        if (StringUtils.isBlank(token)) {
            if (recognizer instanceof ExpressionParser) {
                ParserRuleContext context = ((ExpressionParser) recognizer).getRuleContext();
                token = context.getText();
                offset = ErrorMsg.getInstance().extractOffset(context);
            }
        }

        // 猜测完成，如果没有猜测到，使用 antlr 提供的异常位置
        if (null == offset) {
            offset = new ConditionOffset(line, line, charPositionInLine, charPositionInLine, charPositionInLine, charPositionInLine);
        }

        // 单独处理几种错误
        if (StringUtils.isNotBlank(token)) {
            // 括号错误
            if (token.contains("(") || token.contains(")")) {
                // 只保留一个括号，去掉无关字符，只保留唯一的位置
                // 1、右括号多余 2、3、左括号多余，或者右括号缺失
                if (token.endsWith("))")) {
                    token = ")";
                    offset.setStartLine(offset.getEndLine());
                    offset.setStartInLine(offset.getEndInLine());
                } else if (token.startsWith("(") || token.startsWith(")")) {
                    token = token.startsWith("(") ? "(" : ")";
                    offset.setEndLine(offset.getStartLine());
                    offset.setEndInLine(offset.getStartInLine());
                } else if (token.endsWith("(") || token.endsWith(")")) {
                    token = token.endsWith("(") ? "(" : ")";
                    offset.setStartLine(offset.getEndLine());
                    offset.setStartInLine(offset.getEndInLine());
                }

                ParserError error = ErrorMsg.getInstance().format(offset, token, Constant.Error.BRACKET_NOT_MATCH);
                throw new ParseCancelWithDetailException(error);
            }

            // 例如 token 为 a=b\cdefg，报错在第4个字符，应该用 charPositionInLine 来取
            if (token.length() > charPositionInLine) {
                // 需要判断的字符，下面才做处理。其它情况不需要这么提取了，例如不是完整的表达式，这么处理就出错
                if (token.contains("\\")) {
                    int position = charPositionInLine > 0 ? charPositionInLine - 1 : 0;
                    token = this.extractFromToken(token, charPositionInLine);

                    // 转义符错误
                    if ("\\".equals(token)) {
                        offset = new ConditionOffset(line, line, position, position, position, position);
                        ParserError error = ErrorMsg.getInstance().format(offset, "\\", Constant.Error.ESCAPE_ERROR);
                        throw new ParseCancelWithDetailException(error);
                    }
                }
            }
        }

        ParserError error = ErrorMsg.getInstance().format(offset, token, msg);
        throw new ParseCancelWithDetailException(error);
    }

    // 从 offendingSymbol 提取 offset
    private ConditionOffset extractOffset(Token offending) {
        int startLine = offending.getLine();
        int startInLine = offending.getCharPositionInLine();
        int start = offending.getStartIndex();

        if (offending.getType() == IntStream.EOF) {
            startInLine = startInLine > 0 ? startInLine - 1 : 0;
            start = start > 0 ? start - 1 : 0;
        }

        return new ConditionOffset(startLine, startLine, start, start, startInLine, startInLine);
    }

    // 从 offendingSymbol 提取 offset，结束位置正常获取。不是单字符异常的情况下使用
    private ConditionOffset extractOffsetWithEnd(Token offending) {
        ConditionOffset offset = this.extractOffset(offending);
        offset.setEnd(offending.getStopIndex());
        offset.setEndInLine(offending.getStopIndex());
        return offset;
    }

    // 重组错误信息。不是单字符异常的情况下使用
    private ParserError extractError(Token offending, String msg) {
        ConditionOffset offset = this.extractOffsetWithEnd(offending);
        return ErrorMsg.getInstance().format(offset, offending.getText(), msg);
    }

    // 重组错误信息
    private ParserError extractError(Token offending, String chars, String msg) {
        ConditionOffset offset = this.extractOffset(offending);
        return ErrorMsg.getInstance().format(offset, chars, msg);
    }

    // 如果是结束符，取前一个token
    private Token currentOrPreviousToken(Recognizer<?, ?> recognizer, Token offending) {
        Token token = null;

        if (offending.getType() == IntStream.EOF) {
            if (recognizer instanceof ExpressionParser) {
                TokenStream tokenStream = ((ExpressionParser) recognizer).getInputStream();
                try {
                    // ipc = 1 and 这样不完整的表达式，当前 token 的 index 是 0，所以 LT(-1) 不行
//                    token = tokenStream.LT(-1);
                    int index = offending.getTokenIndex();
                    if (index > 0) {
                        index = index - 1;
                    }
                    token = tokenStream.get(index);
                } catch (Exception e) {
                    token = offending;
                }
            }
        }

        return null == token ? offending : token;
    }

    // 取token中的最后一个字符，处理一些可能出现的错误
    private String extractFromToken(String token, int charPositionInLine) {
        // 表达式不完整的时候，可能会出现 charPositionInLine = -1
        if (charPositionInLine < 0) {
            charPositionInLine = 0;
        }
        if (charPositionInLine > token.length()) {
            charPositionInLine = token.length() - 1;
        }
        return String.valueOf(token.charAt(charPositionInLine));
    }

}
