package cn.gwssi.syntax.parser.converter.error;

import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.converter.util.Constant;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.Token;
import org.apache.commons.lang3.StringUtils;

public class ErrorMsg {

    private static ErrorMsg instance;

    private ErrorMsg() {
    }

    public static ErrorMsg getInstance() {
        if (null == instance) {
            instance = new ErrorMsg();
        }
        return instance;
    }

    /**
     * 格式化错误信息
     */
    public ParserError format(ParserRuleContext context, String chars, String reason) {
        if (StringUtils.isBlank(chars)) {
            chars = context.getText().trim();
        }

        ConditionOffset offset = this.extractOffset(context);
        return this.format(offset, chars, reason);
    }

    /**
     * 格式化错误信息，单独处理【括号不匹配】的错误
     */
    public ParserError format(ParserRuleContext context, boolean isStart) {
        String chars = isStart ? "(" : ")";
        Token token = isStart ? context.getStart() : context.getStop();
        // it will never happen
        if (null == token) {
            return this.format(context, chars, Constant.Error.BRACKET_NOT_MATCH);
        }

        // 提取offset
        int line = token.getLine();
        int positionInLine = token.getCharPositionInLine();
        int position = token.getStartIndex();
        ConditionOffset offset = new ConditionOffset(line, line, position, position, positionInLine, positionInLine);

        return this.format(offset, chars, Constant.Error.BRACKET_NOT_MATCH);
    }

    /**
     * 格式化错误信息
     */
    public ParserError format(ConditionOffset offset, String chars, String reason) {
        String message = this.format(offset.getStartLine(), offset.getStartInLine(), chars, reason);
        ParserError error = new ParserError(offset, reason, message);
        error.setChars(chars);

        return error;
    }

    /**
     * context 中提取错误位置
     */
    public ConditionOffset extractOffset(ParserRuleContext ctx) {
        ConditionOffset offset = new ConditionOffset();
        Token start = ctx.getStart();
        Token stop = ctx.getStop();

        if (null != start) {
            offset.setStartLine(start.getLine());
            offset.setStartInLine(start.getCharPositionInLine());
            offset.setStart(start.getStartIndex());
        }
        if (null != stop) {
            int endIndex = stop.getStopIndex();
//            // 这是到下一个token的位置，会把空格等字符计算进去，不能使用
//            int endInLine = stop.getTokenSource().getCharPositionInLine();
            int endInLine = stop.getCharPositionInLine() + endIndex - stop.getStartIndex();

            offset.setEndLine(stop.getLine());
            offset.setEndInLine(endInLine);
            offset.setEnd(endIndex);
        }

        // 设置内容，StatVisitor 中要用到
        offset.setChars(ctx.getText());

        return offset;
    }

    /**
     * 格式化错误信息
     */
    private String format(int line, int position, String chars, String reason) {
        StringBuffer message = new StringBuffer("检索表达式存在错误，位置：");
        message.append(line).append("行 ")
                .append(position + 1).append("列");

        if (StringUtils.isNotBlank(chars)) {
            message.append("，字符：").append(chars);
        }
        if (StringUtils.isNotBlank(reason)) {
            message.append("，可能原因：").append(reason);
        }

        return message.toString();
    }

}
