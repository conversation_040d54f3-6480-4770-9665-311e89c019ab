// Generated from src/main/java/cn/gwssi/syntax/parser/expression/Expression.g4 by ANTLR 4.13.2
package cn.gwssi.syntax.parser.expression;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.*;
import org.antlr.v4.runtime.tree.*;
import java.util.List;
import java.util.Iterator;
import java.util.ArrayList;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class ExpressionParser extends Parser {
	static { RuntimeMetaData.checkVersion("4.13.2", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		T__0=1, VALUEESCAPE=2, CONNECTOR_ERROR=3, CONNECTOR_AND=4, CONNECTOR_OR=5, 
		CONNECTOR_XOR=6, CONNECTOR_NOT=7, CONNECTOR_IN=8, LOCATION_PRE_L=9, LOCATION_AND_L=10, 
		LOCATION_AND_E=11, STRING_QUOTES=12, UNICODE_STRING_QUOTES=13, STRING_QUOTES_SINGLE=14, 
		UNICODE_STRING_QUOTES_SINGLE=15, BRACKETS_L=16, BRACKETS_R=17, POWER=18, 
		ESC=19, SPACE=20, EQ=21, EQA=22, NE=23, GT=24, GE=25, LT=26, LE=27, IN=28, 
		FREQ=29, PRE=30, EQU=31, SEN=32, SEG=33, AND_L=34, TRUECASE=35, BOOST=36, 
		INT=37, WORD=38, ANY=39, WS=40;
	public static final int
		RULE_expression = 0, RULE_statement = 1, RULE_stat = 2, RULE_statMulti = 3, 
		RULE_valueExpr = 4, RULE_field = 5, RULE_words = 6, RULE_valueFinal = 7, 
		RULE_valueIn = 8, RULE_wordsAll = 9, RULE_wordsBoost = 10, RULE_connector = 11, 
		RULE_comparator = 12, RULE_comparator_in = 13, RULE_wordsWithBracket = 14, 
		RULE_location = 15, RULE_location_pre = 16, RULE_location_pre_l = 17, 
		RULE_location_pre_sen = 18, RULE_location_pre_seg = 19, RULE_location_equ = 20, 
		RULE_location_and = 21, RULE_location_and_e = 22, RULE_location_and_l = 23, 
		RULE_location_and_sen = 24, RULE_location_and_seg = 25, RULE_parenth_l = 26, 
		RULE_parenth_r = 27, RULE_quotedWords = 28, RULE_quotedWordsSingle = 29;
	private static String[] makeRuleNames() {
		return new String[] {
			"expression", "statement", "stat", "statMulti", "valueExpr", "field", 
			"words", "valueFinal", "valueIn", "wordsAll", "wordsBoost", "connector", 
			"comparator", "comparator_in", "wordsWithBracket", "location", "location_pre", 
			"location_pre_l", "location_pre_sen", "location_pre_seg", "location_equ", 
			"location_and", "location_and_e", "location_and_l", "location_and_sen", 
			"location_and_seg", "parenth_l", "parenth_r", "quotedWords", "quotedWordsSingle"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'/'", null, null, null, null, null, null, null, null, null, null, 
			null, null, null, null, "'('", "')'", "'^'", "'\\'", null, "'='", "'+='", 
			"'!='", "'>'", "'>='", "'<'", "'<='"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, null, "VALUEESCAPE", "CONNECTOR_ERROR", "CONNECTOR_AND", "CONNECTOR_OR", 
			"CONNECTOR_XOR", "CONNECTOR_NOT", "CONNECTOR_IN", "LOCATION_PRE_L", "LOCATION_AND_L", 
			"LOCATION_AND_E", "STRING_QUOTES", "UNICODE_STRING_QUOTES", "STRING_QUOTES_SINGLE", 
			"UNICODE_STRING_QUOTES_SINGLE", "BRACKETS_L", "BRACKETS_R", "POWER", 
			"ESC", "SPACE", "EQ", "EQA", "NE", "GT", "GE", "LT", "LE", "IN", "FREQ", 
			"PRE", "EQU", "SEN", "SEG", "AND_L", "TRUECASE", "BOOST", "INT", "WORD", 
			"ANY", "WS"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}

	@Override
	public String getGrammarFileName() { return "Expression.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public ATN getATN() { return _ATN; }

	public ExpressionParser(TokenStream input) {
		super(input);
		_interp = new ParserATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ExpressionContext extends ParserRuleContext {
		public StatementContext statement() {
			return getRuleContext(StatementContext.class,0);
		}
		public TerminalNode EOF() { return getToken(ExpressionParser.EOF, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public ExpressionContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_expression; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterExpression(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitExpression(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitExpression(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ExpressionContext expression() throws RecognitionException {
		ExpressionContext _localctx = new ExpressionContext(_ctx, getState());
		enterRule(_localctx, 0, RULE_expression);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(60);
			statement();
			setState(64);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(61);
				match(SPACE);
				}
				}
				setState(66);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(67);
			match(EOF);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatementContext extends ParserRuleContext {
		public StatementContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_statement; }
	 
		public StatementContext() { }
		public void copyFrom(StatementContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatMultipleContext extends StatementContext {
		public StatMultiContext statMulti() {
			return getRuleContext(StatMultiContext.class,0);
		}
		public StatMultipleContext(StatementContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatMultiple(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatMultiple(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatMultiple(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatSingleContext extends StatementContext {
		public StatContext stat() {
			return getRuleContext(StatContext.class,0);
		}
		public StatSingleContext(StatementContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatSingle(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatementContext statement() throws RecognitionException {
		StatementContext _localctx = new StatementContext(_ctx, getState());
		enterRule(_localctx, 2, RULE_statement);
		try {
			setState(71);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,1,_ctx) ) {
			case 1:
				_localctx = new StatSingleContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(69);
				stat();
				}
				break;
			case 2:
				_localctx = new StatMultipleContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(70);
				statMulti();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatContext extends ParserRuleContext {
		public StatContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_stat; }
	 
		public StatContext() { }
		public void copyFrom(StatContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatFinalContext extends StatContext {
		public FieldContext field() {
			return getRuleContext(FieldContext.class,0);
		}
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public StatFinalContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatFinal(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatFinal(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatFinal(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatInContext extends StatContext {
		public FieldContext field() {
			return getRuleContext(FieldContext.class,0);
		}
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueInContext valueIn() {
			return getRuleContext(ValueInContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public StatInContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatIn(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatIn(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatIn(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatNestedContext extends StatContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public StatementContext statement() {
			return getRuleContext(StatementContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public StatNestedContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatNested(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatNested(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatNested(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatOnlyValueContext extends StatContext {
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public StatOnlyValueContext(StatContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatOnlyValue(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatOnlyValue(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatOnlyValue(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatContext stat() throws RecognitionException {
		StatContext _localctx = new StatContext(_ctx, getState());
		enterRule(_localctx, 4, RULE_stat);
		try {
			setState(88);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,2,_ctx) ) {
			case 1:
				_localctx = new StatNestedContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(73);
				parenth_l();
				setState(74);
				statement();
				setState(75);
				parenth_r();
				}
				break;
			case 2:
				_localctx = new StatFinalContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(77);
				field();
				setState(78);
				comparator();
				setState(79);
				valueExpr(0);
				}
				break;
			case 3:
				_localctx = new StatInContext(_localctx);
				enterOuterAlt(_localctx, 3);
				{
				setState(81);
				field();
				setState(82);
				comparator_in();
				setState(83);
				parenth_l();
				setState(84);
				valueIn();
				setState(85);
				parenth_r();
				}
				break;
			case 4:
				_localctx = new StatOnlyValueContext(_localctx);
				enterOuterAlt(_localctx, 4);
				{
				setState(87);
				valueExpr(0);
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StatMultiContext extends ParserRuleContext {
		public StatMultiContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_statMulti; }
	 
		public StatMultiContext() { }
		public void copyFrom(StatMultiContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class StatSerialContext extends StatMultiContext {
		public List<StatContext> stat() {
			return getRuleContexts(StatContext.class);
		}
		public StatContext stat(int i) {
			return getRuleContext(StatContext.class,i);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public StatSerialContext(StatMultiContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterStatSerial(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitStatSerial(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitStatSerial(this);
			else return visitor.visitChildren(this);
		}
	}

	public final StatMultiContext statMulti() throws RecognitionException {
		StatMultiContext _localctx = new StatMultiContext(_ctx, getState());
		enterRule(_localctx, 6, RULE_statMulti);
		int _la;
		try {
			_localctx = new StatSerialContext(_localctx);
			enterOuterAlt(_localctx, 1);
			{
			setState(90);
			stat();
			setState(94); 
			_errHandler.sync(this);
			_la = _input.LA(1);
			do {
				{
				{
				setState(91);
				connector();
				setState(92);
				stat();
				}
				}
				setState(96); 
				_errHandler.sync(this);
				_la = _input.LA(1);
			} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0) );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueExprContext extends ParserRuleContext {
		public ValueExprContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueExpr; }
	 
		public ValueExprContext() { }
		public void copyFrom(ValueExprContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNotContext extends ValueExprContext {
		public TerminalNode CONNECTOR_NOT() { return getToken(ExpressionParser.CONNECTOR_NOT, 0); }
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public ValueNotContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNot(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNot(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNot(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueTruecaseContext extends ValueExprContext {
		public TerminalNode TRUECASE() { return getToken(ExpressionParser.TRUECASE, 0); }
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public ValueTruecaseContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueTruecase(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueTruecase(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueTruecase(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNestedContext extends ValueExprContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public ValueExprContext valueExpr() {
			return getRuleContext(ValueExprContext.class,0);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public ValueNestedContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNested(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNested(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNested(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueSerialContext extends ValueExprContext {
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public List<ValueExprContext> valueExpr() {
			return getRuleContexts(ValueExprContext.class);
		}
		public ValueExprContext valueExpr(int i) {
			return getRuleContext(ValueExprContext.class,i);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public ValueSerialContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueSerial(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueSerial(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueSerial(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueFreqContext extends ValueExprContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public TerminalNode FREQ() { return getToken(ExpressionParser.FREQ, 0); }
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public ValueFreqContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueFreq(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueFreq(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueFreq(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueLocationContext extends ValueExprContext {
		public List<ValueExprContext> valueExpr() {
			return getRuleContexts(ValueExprContext.class);
		}
		public ValueExprContext valueExpr(int i) {
			return getRuleContext(ValueExprContext.class,i);
		}
		public LocationContext location() {
			return getRuleContext(LocationContext.class,0);
		}
		public ValueLocationContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueLocation(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueLocation(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueLocation(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueBoostContext extends ValueExprContext {
		public TerminalNode BOOST() { return getToken(ExpressionParser.BOOST, 0); }
		public Parenth_lContext parenth_l() {
			return getRuleContext(Parenth_lContext.class,0);
		}
		public List<WordsBoostContext> wordsBoost() {
			return getRuleContexts(WordsBoostContext.class);
		}
		public WordsBoostContext wordsBoost(int i) {
			return getRuleContext(WordsBoostContext.class,i);
		}
		public Parenth_rContext parenth_r() {
			return getRuleContext(Parenth_rContext.class,0);
		}
		public List<ConnectorContext> connector() {
			return getRuleContexts(ConnectorContext.class);
		}
		public ConnectorContext connector(int i) {
			return getRuleContext(ConnectorContext.class,i);
		}
		public ValueBoostContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueBoost(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueBoost(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueBoost(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueNormalContext extends ValueExprContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public ValueNormalContext(ValueExprContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueNormal(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueNormal(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueNormal(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueExprContext valueExpr() throws RecognitionException {
		return valueExpr(0);
	}

	private ValueExprContext valueExpr(int _p) throws RecognitionException {
		ParserRuleContext _parentctx = _ctx;
		int _parentState = getState();
		ValueExprContext _localctx = new ValueExprContext(_ctx, _parentState);
		ValueExprContext _prevctx = _localctx;
		int _startState = 8;
		enterRecursionRule(_localctx, 8, RULE_valueExpr, _p);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(140);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,6,_ctx) ) {
			case 1:
				{
				_localctx = new ValueNestedContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;

				setState(99);
				parenth_l();
				setState(100);
				valueExpr(0);
				setState(101);
				parenth_r();
				}
				break;
			case 2:
				{
				_localctx = new ValueNotContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(103);
				match(CONNECTOR_NOT);
				setState(104);
				valueExpr(7);
				}
				break;
			case 3:
				{
				_localctx = new ValueFreqContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(105);
				valueFinal();
				setState(106);
				match(FREQ);
				setState(107);
				comparator();
				setState(108);
				match(INT);
				}
				break;
			case 4:
				{
				_localctx = new ValueTruecaseContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(110);
				match(TRUECASE);
				setState(111);
				parenth_l();
				setState(112);
				valueFinal();
				setState(113);
				parenth_r();
				}
				break;
			case 5:
				{
				_localctx = new ValueBoostContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(115);
				match(BOOST);
				setState(116);
				parenth_l();
				setState(117);
				wordsBoost();
				setState(123);
				_errHandler.sync(this);
				_la = _input.LA(1);
				while ((((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0)) {
					{
					{
					setState(118);
					connector();
					setState(119);
					wordsBoost();
					}
					}
					setState(125);
					_errHandler.sync(this);
					_la = _input.LA(1);
				}
				setState(126);
				parenth_r();
				}
				break;
			case 6:
				{
				_localctx = new ValueSerialContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(128);
				parenth_l();
				setState(129);
				valueExpr(0);
				setState(133); 
				_errHandler.sync(this);
				_la = _input.LA(1);
				do {
					{
					{
					setState(130);
					connector();
					setState(131);
					valueExpr(0);
					}
					}
					setState(135); 
					_errHandler.sync(this);
					_la = _input.LA(1);
				} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & 248L) != 0) );
				setState(137);
				parenth_r();
				}
				break;
			case 7:
				{
				_localctx = new ValueNormalContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(139);
				valueFinal();
				}
				break;
			}
			_ctx.stop = _input.LT(-1);
			setState(148);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,7,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					if ( _parseListeners!=null ) triggerExitRuleEvent();
					_prevctx = _localctx;
					{
					{
					_localctx = new ValueLocationContext(new ValueExprContext(_parentctx, _parentState));
					pushNewRecursionContext(_localctx, _startState, RULE_valueExpr);
					setState(142);
					if (!(precpred(_ctx, 1))) throw new FailedPredicateException(this, "precpred(_ctx, 1)");
					{
					setState(143);
					location();
					setState(144);
					valueExpr(0);
					}
					}
					} 
				}
				setState(150);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,7,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			unrollRecursionContexts(_parentctx);
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class FieldContext extends ParserRuleContext {
		public List<WordsContext> words() {
			return getRuleContexts(WordsContext.class);
		}
		public WordsContext words(int i) {
			return getRuleContext(WordsContext.class,i);
		}
		public FieldContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_field; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterField(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitField(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitField(this);
			else return visitor.visitChildren(this);
		}
	}

	public final FieldContext field() throws RecognitionException {
		FieldContext _localctx = new FieldContext(_ctx, getState());
		enterRule(_localctx, 10, RULE_field);
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(152); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					setState(151);
					words();
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(154); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,8,_ctx);
			} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsContext extends ParserRuleContext {
		public TerminalNode WORD() { return getToken(ExpressionParser.WORD, 0); }
		public TerminalNode IN() { return getToken(ExpressionParser.IN, 0); }
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public TerminalNode SPACE() { return getToken(ExpressionParser.SPACE, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public TerminalNode VALUEESCAPE() { return getToken(ExpressionParser.VALUEESCAPE, 0); }
		public WordsWithBracketContext wordsWithBracket() {
			return getRuleContext(WordsWithBracketContext.class,0);
		}
		public WordsContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_words; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsContext words() throws RecognitionException {
		WordsContext _localctx = new WordsContext(_ctx, getState());
		enterRule(_localctx, 12, RULE_words);
		try {
			setState(164);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,9,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(156);
				match(WORD);
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(157);
				match(T__0);
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(158);
				match(IN);
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(159);
				comparator_in();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(160);
				match(SPACE);
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(161);
				match(INT);
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(162);
				match(VALUEESCAPE);
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(163);
				wordsWithBracket();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueFinalContext extends ParserRuleContext {
		public ValueFinalContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueFinal; }
	 
		public ValueFinalContext() { }
		public void copyFrom(ValueFinalContext ctx) {
			super.copyFrom(ctx);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueQuotedContext extends ValueFinalContext {
		public QuotedWordsContext quotedWords() {
			return getRuleContext(QuotedWordsContext.class,0);
		}
		public ValueQuotedContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueQuoted(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueQuoted(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueQuoted(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueQuotedSingleContext extends ValueFinalContext {
		public QuotedWordsSingleContext quotedWordsSingle() {
			return getRuleContext(QuotedWordsSingleContext.class,0);
		}
		public ValueQuotedSingleContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueQuotedSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueQuotedSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueQuotedSingle(this);
			else return visitor.visitChildren(this);
		}
	}
	@SuppressWarnings("CheckReturnValue")
	public static class ValueWordsContext extends ValueFinalContext {
		public List<WordsAllContext> wordsAll() {
			return getRuleContexts(WordsAllContext.class);
		}
		public WordsAllContext wordsAll(int i) {
			return getRuleContext(WordsAllContext.class,i);
		}
		public ValueWordsContext(ValueFinalContext ctx) { copyFrom(ctx); }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueFinalContext valueFinal() throws RecognitionException {
		ValueFinalContext _localctx = new ValueFinalContext(_ctx, getState());
		enterRule(_localctx, 14, RULE_valueFinal);
		try {
			int _alt;
			setState(173);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,11,_ctx) ) {
			case 1:
				_localctx = new ValueQuotedContext(_localctx);
				enterOuterAlt(_localctx, 1);
				{
				setState(166);
				quotedWords();
				}
				break;
			case 2:
				_localctx = new ValueQuotedSingleContext(_localctx);
				enterOuterAlt(_localctx, 2);
				{
				setState(167);
				quotedWordsSingle();
				}
				break;
			case 3:
				_localctx = new ValueWordsContext(_localctx);
				enterOuterAlt(_localctx, 3);
				{
				setState(169); 
				_errHandler.sync(this);
				_alt = 1;
				do {
					switch (_alt) {
					case 1:
						{
						{
						setState(168);
						wordsAll();
						}
						}
						break;
					default:
						throw new NoViableAltException(this);
					}
					setState(171); 
					_errHandler.sync(this);
					_alt = getInterpreter().adaptivePredict(_input,10,_ctx);
				} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ValueInContext extends ParserRuleContext {
		public List<WordsAllContext> wordsAll() {
			return getRuleContexts(WordsAllContext.class);
		}
		public WordsAllContext wordsAll(int i) {
			return getRuleContext(WordsAllContext.class,i);
		}
		public ValueInContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_valueIn; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterValueIn(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitValueIn(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitValueIn(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ValueInContext valueIn() throws RecognitionException {
		ValueInContext _localctx = new ValueInContext(_ctx, getState());
		enterRule(_localctx, 16, RULE_valueIn);
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(176); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					setState(175);
					wordsAll();
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(178); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,12,_ctx);
			} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsAllContext extends ParserRuleContext {
		public WordsContext words() {
			return getRuleContext(WordsContext.class,0);
		}
		public WordsWithBracketContext wordsWithBracket() {
			return getRuleContext(WordsWithBracketContext.class,0);
		}
		public ComparatorContext comparator() {
			return getRuleContext(ComparatorContext.class,0);
		}
		public Comparator_inContext comparator_in() {
			return getRuleContext(Comparator_inContext.class,0);
		}
		public TerminalNode VALUEESCAPE() { return getToken(ExpressionParser.VALUEESCAPE, 0); }
		public TerminalNode SPACE() { return getToken(ExpressionParser.SPACE, 0); }
		public TerminalNode ANY() { return getToken(ExpressionParser.ANY, 0); }
		public TerminalNode POWER() { return getToken(ExpressionParser.POWER, 0); }
		public TerminalNode BOOST() { return getToken(ExpressionParser.BOOST, 0); }
		public TerminalNode TRUECASE() { return getToken(ExpressionParser.TRUECASE, 0); }
		public TerminalNode ESC() { return getToken(ExpressionParser.ESC, 0); }
		public WordsAllContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsAll; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsAll(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsAll(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsAll(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsAllContext wordsAll() throws RecognitionException {
		WordsAllContext _localctx = new WordsAllContext(_ctx, getState());
		enterRule(_localctx, 18, RULE_wordsAll);
		try {
			setState(191);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,13,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(180);
				words();
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(181);
				wordsWithBracket();
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(182);
				comparator();
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(183);
				comparator_in();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(184);
				match(VALUEESCAPE);
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(185);
				match(SPACE);
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(186);
				match(ANY);
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(187);
				match(POWER);
				}
				break;
			case 9:
				enterOuterAlt(_localctx, 9);
				{
				setState(188);
				match(BOOST);
				}
				break;
			case 10:
				enterOuterAlt(_localctx, 10);
				{
				setState(189);
				match(TRUECASE);
				}
				break;
			case 11:
				enterOuterAlt(_localctx, 11);
				{
				setState(190);
				match(ESC);
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsBoostContext extends ParserRuleContext {
		public ValueFinalContext valueFinal() {
			return getRuleContext(ValueFinalContext.class,0);
		}
		public TerminalNode POWER() { return getToken(ExpressionParser.POWER, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public WordsBoostContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsBoost; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsBoost(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsBoost(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsBoost(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsBoostContext wordsBoost() throws RecognitionException {
		WordsBoostContext _localctx = new WordsBoostContext(_ctx, getState());
		enterRule(_localctx, 20, RULE_wordsBoost);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(193);
			valueFinal();
			setState(194);
			match(POWER);
			setState(195);
			match(INT);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ConnectorContext extends ParserRuleContext {
		public TerminalNode CONNECTOR_ERROR() { return getToken(ExpressionParser.CONNECTOR_ERROR, 0); }
		public TerminalNode CONNECTOR_AND() { return getToken(ExpressionParser.CONNECTOR_AND, 0); }
		public TerminalNode CONNECTOR_OR() { return getToken(ExpressionParser.CONNECTOR_OR, 0); }
		public TerminalNode CONNECTOR_XOR() { return getToken(ExpressionParser.CONNECTOR_XOR, 0); }
		public TerminalNode CONNECTOR_NOT() { return getToken(ExpressionParser.CONNECTOR_NOT, 0); }
		public ConnectorContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_connector; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterConnector(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitConnector(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitConnector(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ConnectorContext connector() throws RecognitionException {
		ConnectorContext _localctx = new ConnectorContext(_ctx, getState());
		enterRule(_localctx, 22, RULE_connector);
		try {
			setState(203);
			_errHandler.sync(this);
			switch (_input.LA(1)) {
			case CONNECTOR_ERROR:
				enterOuterAlt(_localctx, 1);
				{
				setState(197);
				match(CONNECTOR_ERROR);
				notifyErrorListeners(_ctx.getStart(), Constant.Error.CONNECTOR_SERIAL, null);
				}
				break;
			case CONNECTOR_AND:
				enterOuterAlt(_localctx, 2);
				{
				setState(199);
				match(CONNECTOR_AND);
				}
				break;
			case CONNECTOR_OR:
				enterOuterAlt(_localctx, 3);
				{
				setState(200);
				match(CONNECTOR_OR);
				}
				break;
			case CONNECTOR_XOR:
				enterOuterAlt(_localctx, 4);
				{
				setState(201);
				match(CONNECTOR_XOR);
				}
				break;
			case CONNECTOR_NOT:
				enterOuterAlt(_localctx, 5);
				{
				setState(202);
				match(CONNECTOR_NOT);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ComparatorContext extends ParserRuleContext {
		public TerminalNode EQ() { return getToken(ExpressionParser.EQ, 0); }
		public TerminalNode EQA() { return getToken(ExpressionParser.EQA, 0); }
		public TerminalNode NE() { return getToken(ExpressionParser.NE, 0); }
		public TerminalNode GT() { return getToken(ExpressionParser.GT, 0); }
		public TerminalNode GE() { return getToken(ExpressionParser.GE, 0); }
		public TerminalNode LT() { return getToken(ExpressionParser.LT, 0); }
		public TerminalNode LE() { return getToken(ExpressionParser.LE, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public ComparatorContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_comparator; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterComparator(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitComparator(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitComparator(this);
			else return visitor.visitChildren(this);
		}
	}

	public final ComparatorContext comparator() throws RecognitionException {
		ComparatorContext _localctx = new ComparatorContext(_ctx, getState());
		enterRule(_localctx, 24, RULE_comparator);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(208);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(205);
				match(SPACE);
				}
				}
				setState(210);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(211);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 266338304L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(215);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,16,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(212);
					match(SPACE);
					}
					} 
				}
				setState(217);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,16,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Comparator_inContext extends ParserRuleContext {
		public TerminalNode CONNECTOR_IN() { return getToken(ExpressionParser.CONNECTOR_IN, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Comparator_inContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_comparator_in; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterComparator_in(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitComparator_in(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitComparator_in(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Comparator_inContext comparator_in() throws RecognitionException {
		Comparator_inContext _localctx = new Comparator_inContext(_ctx, getState());
		enterRule(_localctx, 26, RULE_comparator_in);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(221);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(218);
				match(SPACE);
				}
				}
				setState(223);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(224);
			match(CONNECTOR_IN);
			setState(228);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,18,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(225);
					match(SPACE);
					}
					} 
				}
				setState(230);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,18,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class WordsWithBracketContext extends ParserRuleContext {
		public List<TerminalNode> WORD() { return getTokens(ExpressionParser.WORD); }
		public TerminalNode WORD(int i) {
			return getToken(ExpressionParser.WORD, i);
		}
		public List<TerminalNode> INT() { return getTokens(ExpressionParser.INT); }
		public TerminalNode INT(int i) {
			return getToken(ExpressionParser.INT, i);
		}
		public List<TerminalNode> ANY() { return getTokens(ExpressionParser.ANY); }
		public TerminalNode ANY(int i) {
			return getToken(ExpressionParser.ANY, i);
		}
		public List<Parenth_lContext> parenth_l() {
			return getRuleContexts(Parenth_lContext.class);
		}
		public Parenth_lContext parenth_l(int i) {
			return getRuleContext(Parenth_lContext.class,i);
		}
		public List<Parenth_rContext> parenth_r() {
			return getRuleContexts(Parenth_rContext.class);
		}
		public Parenth_rContext parenth_r(int i) {
			return getRuleContext(Parenth_rContext.class,i);
		}
		public WordsWithBracketContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_wordsWithBracket; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterWordsWithBracket(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitWordsWithBracket(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitWordsWithBracket(this);
			else return visitor.visitChildren(this);
		}
	}

	public final WordsWithBracketContext wordsWithBracket() throws RecognitionException {
		WordsWithBracketContext _localctx = new WordsWithBracketContext(_ctx, getState());
		enterRule(_localctx, 28, RULE_wordsWithBracket);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(231);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 962072674304L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(237); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					setState(237);
					_errHandler.sync(this);
					switch ( getInterpreter().adaptivePredict(_input,19,_ctx) ) {
					case 1:
						{
						setState(232);
						parenth_l();
						}
						break;
					case 2:
						{
						setState(233);
						parenth_r();
						}
						break;
					case 3:
						{
						setState(234);
						match(WORD);
						}
						break;
					case 4:
						{
						setState(235);
						match(INT);
						}
						break;
					case 5:
						{
						setState(236);
						match(ANY);
						}
						break;
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(239); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,20,_ctx);
			} while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER );
			setState(241);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 962072674304L) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class LocationContext extends ParserRuleContext {
		public Location_preContext location_pre() {
			return getRuleContext(Location_preContext.class,0);
		}
		public Location_pre_lContext location_pre_l() {
			return getRuleContext(Location_pre_lContext.class,0);
		}
		public Location_pre_senContext location_pre_sen() {
			return getRuleContext(Location_pre_senContext.class,0);
		}
		public Location_pre_segContext location_pre_seg() {
			return getRuleContext(Location_pre_segContext.class,0);
		}
		public Location_equContext location_equ() {
			return getRuleContext(Location_equContext.class,0);
		}
		public Location_andContext location_and() {
			return getRuleContext(Location_andContext.class,0);
		}
		public Location_and_eContext location_and_e() {
			return getRuleContext(Location_and_eContext.class,0);
		}
		public Location_and_lContext location_and_l() {
			return getRuleContext(Location_and_lContext.class,0);
		}
		public Location_and_senContext location_and_sen() {
			return getRuleContext(Location_and_senContext.class,0);
		}
		public Location_and_segContext location_and_seg() {
			return getRuleContext(Location_and_segContext.class,0);
		}
		public LocationContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation(this);
			else return visitor.visitChildren(this);
		}
	}

	public final LocationContext location() throws RecognitionException {
		LocationContext _localctx = new LocationContext(_ctx, getState());
		enterRule(_localctx, 30, RULE_location);
		try {
			setState(253);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,21,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				setState(243);
				location_pre();
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(244);
				location_pre_l();
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(245);
				location_pre_sen();
				}
				break;
			case 4:
				enterOuterAlt(_localctx, 4);
				{
				setState(246);
				location_pre_seg();
				}
				break;
			case 5:
				enterOuterAlt(_localctx, 5);
				{
				setState(247);
				location_equ();
				}
				break;
			case 6:
				enterOuterAlt(_localctx, 6);
				{
				setState(248);
				location_and();
				}
				break;
			case 7:
				enterOuterAlt(_localctx, 7);
				{
				setState(249);
				location_and_e();
				}
				break;
			case 8:
				enterOuterAlt(_localctx, 8);
				{
				setState(250);
				location_and_l();
				}
				break;
			case 9:
				enterOuterAlt(_localctx, 9);
				{
				setState(251);
				location_and_sen();
				}
				break;
			case 10:
				enterOuterAlt(_localctx, 10);
				{
				setState(252);
				location_and_seg();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_preContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_preContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_preContext location_pre() throws RecognitionException {
		Location_preContext _localctx = new Location_preContext(_ctx, getState());
		enterRule(_localctx, 32, RULE_location_pre);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(255);
			match(SPACE);
			setState(256);
			match(PRE);
			setState(257);
			match(INT);
			setState(258);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_lContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_PRE_L() { return getToken(ExpressionParser.LOCATION_PRE_L, 0); }
		public Location_pre_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_lContext location_pre_l() throws RecognitionException {
		Location_pre_lContext _localctx = new Location_pre_lContext(_ctx, getState());
		enterRule(_localctx, 34, RULE_location_pre_l);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(260);
			match(SPACE);
			setState(261);
			match(LOCATION_PRE_L);
			setState(262);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_senContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode SEN() { return getToken(ExpressionParser.SEN, 0); }
		public Location_pre_senContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_sen; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_sen(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_sen(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_sen(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_senContext location_pre_sen() throws RecognitionException {
		Location_pre_senContext _localctx = new Location_pre_senContext(_ctx, getState());
		enterRule(_localctx, 36, RULE_location_pre_sen);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(264);
			match(SPACE);
			setState(265);
			match(PRE);
			setState(266);
			match(SEN);
			setState(267);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_pre_segContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode PRE() { return getToken(ExpressionParser.PRE, 0); }
		public TerminalNode SEG() { return getToken(ExpressionParser.SEG, 0); }
		public Location_pre_segContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_pre_seg; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_pre_seg(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_pre_seg(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_pre_seg(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_pre_segContext location_pre_seg() throws RecognitionException {
		Location_pre_segContext _localctx = new Location_pre_segContext(_ctx, getState());
		enterRule(_localctx, 38, RULE_location_pre_seg);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(269);
			match(SPACE);
			setState(270);
			match(PRE);
			setState(271);
			match(SEG);
			setState(272);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_equContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode EQU() { return getToken(ExpressionParser.EQU, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_equContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_equ; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_equ(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_equ(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_equ(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_equContext location_equ() throws RecognitionException {
		Location_equContext _localctx = new Location_equContext(_ctx, getState());
		enterRule(_localctx, 40, RULE_location_equ);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(274);
			match(SPACE);
			setState(275);
			match(EQU);
			setState(276);
			match(INT);
			setState(277);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_andContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode INT() { return getToken(ExpressionParser.INT, 0); }
		public Location_andContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_andContext location_and() throws RecognitionException {
		Location_andContext _localctx = new Location_andContext(_ctx, getState());
		enterRule(_localctx, 42, RULE_location_and);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(279);
			match(SPACE);
			setState(280);
			match(AND_L);
			setState(281);
			match(INT);
			setState(282);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_eContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_AND_E() { return getToken(ExpressionParser.LOCATION_AND_E, 0); }
		public Location_and_eContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_e; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_e(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_e(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_e(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_eContext location_and_e() throws RecognitionException {
		Location_and_eContext _localctx = new Location_and_eContext(_ctx, getState());
		enterRule(_localctx, 44, RULE_location_and_e);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(284);
			match(SPACE);
			setState(285);
			match(LOCATION_AND_E);
			setState(286);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_lContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode LOCATION_AND_L() { return getToken(ExpressionParser.LOCATION_AND_L, 0); }
		public Location_and_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_lContext location_and_l() throws RecognitionException {
		Location_and_lContext _localctx = new Location_and_lContext(_ctx, getState());
		enterRule(_localctx, 46, RULE_location_and_l);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(288);
			match(SPACE);
			setState(289);
			match(LOCATION_AND_L);
			setState(290);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_senContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode SEN() { return getToken(ExpressionParser.SEN, 0); }
		public Location_and_senContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_sen; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_sen(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_sen(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_sen(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_senContext location_and_sen() throws RecognitionException {
		Location_and_senContext _localctx = new Location_and_senContext(_ctx, getState());
		enterRule(_localctx, 48, RULE_location_and_sen);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(292);
			match(SPACE);
			setState(293);
			match(AND_L);
			setState(294);
			match(SEN);
			setState(295);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Location_and_segContext extends ParserRuleContext {
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public TerminalNode AND_L() { return getToken(ExpressionParser.AND_L, 0); }
		public TerminalNode SEG() { return getToken(ExpressionParser.SEG, 0); }
		public Location_and_segContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_location_and_seg; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterLocation_and_seg(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitLocation_and_seg(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitLocation_and_seg(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Location_and_segContext location_and_seg() throws RecognitionException {
		Location_and_segContext _localctx = new Location_and_segContext(_ctx, getState());
		enterRule(_localctx, 50, RULE_location_and_seg);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(297);
			match(SPACE);
			setState(298);
			match(AND_L);
			setState(299);
			match(SEG);
			setState(300);
			match(SPACE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Parenth_lContext extends ParserRuleContext {
		public TerminalNode BRACKETS_L() { return getToken(ExpressionParser.BRACKETS_L, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Parenth_lContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_parenth_l; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterParenth_l(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitParenth_l(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitParenth_l(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Parenth_lContext parenth_l() throws RecognitionException {
		Parenth_lContext _localctx = new Parenth_lContext(_ctx, getState());
		enterRule(_localctx, 52, RULE_parenth_l);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(305);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(302);
				match(SPACE);
				}
				}
				setState(307);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(308);
			match(BRACKETS_L);
			setState(312);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,23,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(309);
					match(SPACE);
					}
					} 
				}
				setState(314);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,23,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class Parenth_rContext extends ParserRuleContext {
		public TerminalNode BRACKETS_R() { return getToken(ExpressionParser.BRACKETS_R, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public Parenth_rContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_parenth_r; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterParenth_r(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitParenth_r(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitParenth_r(this);
			else return visitor.visitChildren(this);
		}
	}

	public final Parenth_rContext parenth_r() throws RecognitionException {
		Parenth_rContext _localctx = new Parenth_rContext(_ctx, getState());
		enterRule(_localctx, 54, RULE_parenth_r);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(318);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(315);
				match(SPACE);
				}
				}
				setState(320);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(321);
			match(BRACKETS_R);
			setState(325);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,25,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(322);
					match(SPACE);
					}
					} 
				}
				setState(327);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,25,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class QuotedWordsContext extends ParserRuleContext {
		public TerminalNode STRING_QUOTES() { return getToken(ExpressionParser.STRING_QUOTES, 0); }
		public TerminalNode UNICODE_STRING_QUOTES() { return getToken(ExpressionParser.UNICODE_STRING_QUOTES, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public QuotedWordsContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_quotedWords; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterQuotedWords(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitQuotedWords(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitQuotedWords(this);
			else return visitor.visitChildren(this);
		}
	}

	public final QuotedWordsContext quotedWords() throws RecognitionException {
		QuotedWordsContext _localctx = new QuotedWordsContext(_ctx, getState());
		enterRule(_localctx, 56, RULE_quotedWords);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(331);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(328);
				match(SPACE);
				}
				}
				setState(333);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(334);
			_la = _input.LA(1);
			if ( !(_la==STRING_QUOTES || _la==UNICODE_STRING_QUOTES) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(338);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,27,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(335);
					match(SPACE);
					}
					} 
				}
				setState(340);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,27,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class QuotedWordsSingleContext extends ParserRuleContext {
		public TerminalNode STRING_QUOTES_SINGLE() { return getToken(ExpressionParser.STRING_QUOTES_SINGLE, 0); }
		public TerminalNode UNICODE_STRING_QUOTES_SINGLE() { return getToken(ExpressionParser.UNICODE_STRING_QUOTES_SINGLE, 0); }
		public List<TerminalNode> SPACE() { return getTokens(ExpressionParser.SPACE); }
		public TerminalNode SPACE(int i) {
			return getToken(ExpressionParser.SPACE, i);
		}
		public QuotedWordsSingleContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_quotedWordsSingle; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).enterQuotedWordsSingle(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof ExpressionListener ) ((ExpressionListener)listener).exitQuotedWordsSingle(this);
		}
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionVisitor ) return ((ExpressionVisitor<? extends T>)visitor).visitQuotedWordsSingle(this);
			else return visitor.visitChildren(this);
		}
	}

	public final QuotedWordsSingleContext quotedWordsSingle() throws RecognitionException {
		QuotedWordsSingleContext _localctx = new QuotedWordsSingleContext(_ctx, getState());
		enterRule(_localctx, 58, RULE_quotedWordsSingle);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(344);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==SPACE) {
				{
				{
				setState(341);
				match(SPACE);
				}
				}
				setState(346);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(347);
			_la = _input.LA(1);
			if ( !(_la==STRING_QUOTES_SINGLE || _la==UNICODE_STRING_QUOTES_SINGLE) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(351);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,29,_ctx);
			while ( _alt!=2 && _alt!=org.antlr.v4.runtime.atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(348);
					match(SPACE);
					}
					} 
				}
				setState(353);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,29,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	public boolean sempred(RuleContext _localctx, int ruleIndex, int predIndex) {
		switch (ruleIndex) {
		case 4:
			return valueExpr_sempred((ValueExprContext)_localctx, predIndex);
		}
		return true;
	}
	private boolean valueExpr_sempred(ValueExprContext _localctx, int predIndex) {
		switch (predIndex) {
		case 0:
			return precpred(_ctx, 1);
		}
		return true;
	}

	public static final String _serializedATN =
		"\u0004\u0001(\u0163\u0002\u0000\u0007\u0000\u0002\u0001\u0007\u0001\u0002"+
		"\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004\u0007\u0004\u0002"+
		"\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007\u0007\u0007\u0002"+
		"\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b\u0007\u000b\u0002"+
		"\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002\u000f\u0007\u000f"+
		"\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002\u0012\u0007\u0012"+
		"\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002\u0015\u0007\u0015"+
		"\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002\u0018\u0007\u0018"+
		"\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002\u001b\u0007\u001b"+
		"\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0001\u0000\u0001\u0000"+
		"\u0005\u0000?\b\u0000\n\u0000\f\u0000B\t\u0000\u0001\u0000\u0001\u0000"+
		"\u0001\u0001\u0001\u0001\u0003\u0001H\b\u0001\u0001\u0002\u0001\u0002"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002"+
		"\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002\u0001\u0002"+
		"\u0001\u0002\u0003\u0002Y\b\u0002\u0001\u0003\u0001\u0003\u0001\u0003"+
		"\u0001\u0003\u0004\u0003_\b\u0003\u000b\u0003\f\u0003`\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0005\u0004z\b\u0004\n\u0004"+
		"\f\u0004}\t\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0001"+
		"\u0004\u0001\u0004\u0001\u0004\u0004\u0004\u0086\b\u0004\u000b\u0004\f"+
		"\u0004\u0087\u0001\u0004\u0001\u0004\u0001\u0004\u0003\u0004\u008d\b\u0004"+
		"\u0001\u0004\u0001\u0004\u0001\u0004\u0001\u0004\u0005\u0004\u0093\b\u0004"+
		"\n\u0004\f\u0004\u0096\t\u0004\u0001\u0005\u0004\u0005\u0099\b\u0005\u000b"+
		"\u0005\f\u0005\u009a\u0001\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0001"+
		"\u0006\u0001\u0006\u0001\u0006\u0001\u0006\u0003\u0006\u00a5\b\u0006\u0001"+
		"\u0007\u0001\u0007\u0001\u0007\u0004\u0007\u00aa\b\u0007\u000b\u0007\f"+
		"\u0007\u00ab\u0003\u0007\u00ae\b\u0007\u0001\b\u0004\b\u00b1\b\b\u000b"+
		"\b\f\b\u00b2\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001\t\u0001"+
		"\t\u0001\t\u0001\t\u0001\t\u0003\t\u00c0\b\t\u0001\n\u0001\n\u0001\n\u0001"+
		"\n\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b"+
		"\u0003\u000b\u00cc\b\u000b\u0001\f\u0005\f\u00cf\b\f\n\f\f\f\u00d2\t\f"+
		"\u0001\f\u0001\f\u0005\f\u00d6\b\f\n\f\f\f\u00d9\t\f\u0001\r\u0005\r\u00dc"+
		"\b\r\n\r\f\r\u00df\t\r\u0001\r\u0001\r\u0005\r\u00e3\b\r\n\r\f\r\u00e6"+
		"\t\r\u0001\u000e\u0001\u000e\u0001\u000e\u0001\u000e\u0001\u000e\u0001"+
		"\u000e\u0004\u000e\u00ee\b\u000e\u000b\u000e\f\u000e\u00ef\u0001\u000e"+
		"\u0001\u000e\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f"+
		"\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u000f\u0003\u000f"+
		"\u00fe\b\u000f\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010"+
		"\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0012\u0001\u0012"+
		"\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013\u0001\u0013"+
		"\u0001\u0013\u0001\u0013\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014"+
		"\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0015"+
		"\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0017\u0001\u0017"+
		"\u0001\u0017\u0001\u0017\u0001\u0018\u0001\u0018\u0001\u0018\u0001\u0018"+
		"\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u0019\u0001\u0019\u0001\u0019"+
		"\u0001\u001a\u0005\u001a\u0130\b\u001a\n\u001a\f\u001a\u0133\t\u001a\u0001"+
		"\u001a\u0001\u001a\u0005\u001a\u0137\b\u001a\n\u001a\f\u001a\u013a\t\u001a"+
		"\u0001\u001b\u0005\u001b\u013d\b\u001b\n\u001b\f\u001b\u0140\t\u001b\u0001"+
		"\u001b\u0001\u001b\u0005\u001b\u0144\b\u001b\n\u001b\f\u001b\u0147\t\u001b"+
		"\u0001\u001c\u0005\u001c\u014a\b\u001c\n\u001c\f\u001c\u014d\t\u001c\u0001"+
		"\u001c\u0001\u001c\u0005\u001c\u0151\b\u001c\n\u001c\f\u001c\u0154\t\u001c"+
		"\u0001\u001d\u0005\u001d\u0157\b\u001d\n\u001d\f\u001d\u015a\t\u001d\u0001"+
		"\u001d\u0001\u001d\u0005\u001d\u015e\b\u001d\n\u001d\f\u001d\u0161\t\u001d"+
		"\u0001\u001d\u0000\u0001\b\u001e\u0000\u0002\u0004\u0006\b\n\f\u000e\u0010"+
		"\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.02468:\u0000\u0004"+
		"\u0001\u0000\u0015\u001b\u0001\u0000%\'\u0001\u0000\f\r\u0001\u0000\u000e"+
		"\u000f\u0187\u0000<\u0001\u0000\u0000\u0000\u0002G\u0001\u0000\u0000\u0000"+
		"\u0004X\u0001\u0000\u0000\u0000\u0006Z\u0001\u0000\u0000\u0000\b\u008c"+
		"\u0001\u0000\u0000\u0000\n\u0098\u0001\u0000\u0000\u0000\f\u00a4\u0001"+
		"\u0000\u0000\u0000\u000e\u00ad\u0001\u0000\u0000\u0000\u0010\u00b0\u0001"+
		"\u0000\u0000\u0000\u0012\u00bf\u0001\u0000\u0000\u0000\u0014\u00c1\u0001"+
		"\u0000\u0000\u0000\u0016\u00cb\u0001\u0000\u0000\u0000\u0018\u00d0\u0001"+
		"\u0000\u0000\u0000\u001a\u00dd\u0001\u0000\u0000\u0000\u001c\u00e7\u0001"+
		"\u0000\u0000\u0000\u001e\u00fd\u0001\u0000\u0000\u0000 \u00ff\u0001\u0000"+
		"\u0000\u0000\"\u0104\u0001\u0000\u0000\u0000$\u0108\u0001\u0000\u0000"+
		"\u0000&\u010d\u0001\u0000\u0000\u0000(\u0112\u0001\u0000\u0000\u0000*"+
		"\u0117\u0001\u0000\u0000\u0000,\u011c\u0001\u0000\u0000\u0000.\u0120\u0001"+
		"\u0000\u0000\u00000\u0124\u0001\u0000\u0000\u00002\u0129\u0001\u0000\u0000"+
		"\u00004\u0131\u0001\u0000\u0000\u00006\u013e\u0001\u0000\u0000\u00008"+
		"\u014b\u0001\u0000\u0000\u0000:\u0158\u0001\u0000\u0000\u0000<@\u0003"+
		"\u0002\u0001\u0000=?\u0005\u0014\u0000\u0000>=\u0001\u0000\u0000\u0000"+
		"?B\u0001\u0000\u0000\u0000@>\u0001\u0000\u0000\u0000@A\u0001\u0000\u0000"+
		"\u0000AC\u0001\u0000\u0000\u0000B@\u0001\u0000\u0000\u0000CD\u0005\u0000"+
		"\u0000\u0001D\u0001\u0001\u0000\u0000\u0000EH\u0003\u0004\u0002\u0000"+
		"FH\u0003\u0006\u0003\u0000GE\u0001\u0000\u0000\u0000GF\u0001\u0000\u0000"+
		"\u0000H\u0003\u0001\u0000\u0000\u0000IJ\u00034\u001a\u0000JK\u0003\u0002"+
		"\u0001\u0000KL\u00036\u001b\u0000LY\u0001\u0000\u0000\u0000MN\u0003\n"+
		"\u0005\u0000NO\u0003\u0018\f\u0000OP\u0003\b\u0004\u0000PY\u0001\u0000"+
		"\u0000\u0000QR\u0003\n\u0005\u0000RS\u0003\u001a\r\u0000ST\u00034\u001a"+
		"\u0000TU\u0003\u0010\b\u0000UV\u00036\u001b\u0000VY\u0001\u0000\u0000"+
		"\u0000WY\u0003\b\u0004\u0000XI\u0001\u0000\u0000\u0000XM\u0001\u0000\u0000"+
		"\u0000XQ\u0001\u0000\u0000\u0000XW\u0001\u0000\u0000\u0000Y\u0005\u0001"+
		"\u0000\u0000\u0000Z^\u0003\u0004\u0002\u0000[\\\u0003\u0016\u000b\u0000"+
		"\\]\u0003\u0004\u0002\u0000]_\u0001\u0000\u0000\u0000^[\u0001\u0000\u0000"+
		"\u0000_`\u0001\u0000\u0000\u0000`^\u0001\u0000\u0000\u0000`a\u0001\u0000"+
		"\u0000\u0000a\u0007\u0001\u0000\u0000\u0000bc\u0006\u0004\uffff\uffff"+
		"\u0000cd\u00034\u001a\u0000de\u0003\b\u0004\u0000ef\u00036\u001b\u0000"+
		"f\u008d\u0001\u0000\u0000\u0000gh\u0005\u0007\u0000\u0000h\u008d\u0003"+
		"\b\u0004\u0007ij\u0003\u000e\u0007\u0000jk\u0005\u001d\u0000\u0000kl\u0003"+
		"\u0018\f\u0000lm\u0005%\u0000\u0000m\u008d\u0001\u0000\u0000\u0000no\u0005"+
		"#\u0000\u0000op\u00034\u001a\u0000pq\u0003\u000e\u0007\u0000qr\u00036"+
		"\u001b\u0000r\u008d\u0001\u0000\u0000\u0000st\u0005$\u0000\u0000tu\u0003"+
		"4\u001a\u0000u{\u0003\u0014\n\u0000vw\u0003\u0016\u000b\u0000wx\u0003"+
		"\u0014\n\u0000xz\u0001\u0000\u0000\u0000yv\u0001\u0000\u0000\u0000z}\u0001"+
		"\u0000\u0000\u0000{y\u0001\u0000\u0000\u0000{|\u0001\u0000\u0000\u0000"+
		"|~\u0001\u0000\u0000\u0000}{\u0001\u0000\u0000\u0000~\u007f\u00036\u001b"+
		"\u0000\u007f\u008d\u0001\u0000\u0000\u0000\u0080\u0081\u00034\u001a\u0000"+
		"\u0081\u0085\u0003\b\u0004\u0000\u0082\u0083\u0003\u0016\u000b\u0000\u0083"+
		"\u0084\u0003\b\u0004\u0000\u0084\u0086\u0001\u0000\u0000\u0000\u0085\u0082"+
		"\u0001\u0000\u0000\u0000\u0086\u0087\u0001\u0000\u0000\u0000\u0087\u0085"+
		"\u0001\u0000\u0000\u0000\u0087\u0088\u0001\u0000\u0000\u0000\u0088\u0089"+
		"\u0001\u0000\u0000\u0000\u0089\u008a\u00036\u001b\u0000\u008a\u008d\u0001"+
		"\u0000\u0000\u0000\u008b\u008d\u0003\u000e\u0007\u0000\u008cb\u0001\u0000"+
		"\u0000\u0000\u008cg\u0001\u0000\u0000\u0000\u008ci\u0001\u0000\u0000\u0000"+
		"\u008cn\u0001\u0000\u0000\u0000\u008cs\u0001\u0000\u0000\u0000\u008c\u0080"+
		"\u0001\u0000\u0000\u0000\u008c\u008b\u0001\u0000\u0000\u0000\u008d\u0094"+
		"\u0001\u0000\u0000\u0000\u008e\u008f\n\u0001\u0000\u0000\u008f\u0090\u0003"+
		"\u001e\u000f\u0000\u0090\u0091\u0003\b\u0004\u0000\u0091\u0093\u0001\u0000"+
		"\u0000\u0000\u0092\u008e\u0001\u0000\u0000\u0000\u0093\u0096\u0001\u0000"+
		"\u0000\u0000\u0094\u0092\u0001\u0000\u0000\u0000\u0094\u0095\u0001\u0000"+
		"\u0000\u0000\u0095\t\u0001\u0000\u0000\u0000\u0096\u0094\u0001\u0000\u0000"+
		"\u0000\u0097\u0099\u0003\f\u0006\u0000\u0098\u0097\u0001\u0000\u0000\u0000"+
		"\u0099\u009a\u0001\u0000\u0000\u0000\u009a\u0098\u0001\u0000\u0000\u0000"+
		"\u009a\u009b\u0001\u0000\u0000\u0000\u009b\u000b\u0001\u0000\u0000\u0000"+
		"\u009c\u00a5\u0005&\u0000\u0000\u009d\u00a5\u0005\u0001\u0000\u0000\u009e"+
		"\u00a5\u0005\u001c\u0000\u0000\u009f\u00a5\u0003\u001a\r\u0000\u00a0\u00a5"+
		"\u0005\u0014\u0000\u0000\u00a1\u00a5\u0005%\u0000\u0000\u00a2\u00a5\u0005"+
		"\u0002\u0000\u0000\u00a3\u00a5\u0003\u001c\u000e\u0000\u00a4\u009c\u0001"+
		"\u0000\u0000\u0000\u00a4\u009d\u0001\u0000\u0000\u0000\u00a4\u009e\u0001"+
		"\u0000\u0000\u0000\u00a4\u009f\u0001\u0000\u0000\u0000\u00a4\u00a0\u0001"+
		"\u0000\u0000\u0000\u00a4\u00a1\u0001\u0000\u0000\u0000\u00a4\u00a2\u0001"+
		"\u0000\u0000\u0000\u00a4\u00a3\u0001\u0000\u0000\u0000\u00a5\r\u0001\u0000"+
		"\u0000\u0000\u00a6\u00ae\u00038\u001c\u0000\u00a7\u00ae\u0003:\u001d\u0000"+
		"\u00a8\u00aa\u0003\u0012\t\u0000\u00a9\u00a8\u0001\u0000\u0000\u0000\u00aa"+
		"\u00ab\u0001\u0000\u0000\u0000\u00ab\u00a9\u0001\u0000\u0000\u0000\u00ab"+
		"\u00ac\u0001\u0000\u0000\u0000\u00ac\u00ae\u0001\u0000\u0000\u0000\u00ad"+
		"\u00a6\u0001\u0000\u0000\u0000\u00ad\u00a7\u0001\u0000\u0000\u0000\u00ad"+
		"\u00a9\u0001\u0000\u0000\u0000\u00ae\u000f\u0001\u0000\u0000\u0000\u00af"+
		"\u00b1\u0003\u0012\t\u0000\u00b0\u00af\u0001\u0000\u0000\u0000\u00b1\u00b2"+
		"\u0001\u0000\u0000\u0000\u00b2\u00b0\u0001\u0000\u0000\u0000\u00b2\u00b3"+
		"\u0001\u0000\u0000\u0000\u00b3\u0011\u0001\u0000\u0000\u0000\u00b4\u00c0"+
		"\u0003\f\u0006\u0000\u00b5\u00c0\u0003\u001c\u000e\u0000\u00b6\u00c0\u0003"+
		"\u0018\f\u0000\u00b7\u00c0\u0003\u001a\r\u0000\u00b8\u00c0\u0005\u0002"+
		"\u0000\u0000\u00b9\u00c0\u0005\u0014\u0000\u0000\u00ba\u00c0\u0005\'\u0000"+
		"\u0000\u00bb\u00c0\u0005\u0012\u0000\u0000\u00bc\u00c0\u0005$\u0000\u0000"+
		"\u00bd\u00c0\u0005#\u0000\u0000\u00be\u00c0\u0005\u0013\u0000\u0000\u00bf"+
		"\u00b4\u0001\u0000\u0000\u0000\u00bf\u00b5\u0001\u0000\u0000\u0000\u00bf"+
		"\u00b6\u0001\u0000\u0000\u0000\u00bf\u00b7\u0001\u0000\u0000\u0000\u00bf"+
		"\u00b8\u0001\u0000\u0000\u0000\u00bf\u00b9\u0001\u0000\u0000\u0000\u00bf"+
		"\u00ba\u0001\u0000\u0000\u0000\u00bf\u00bb\u0001\u0000\u0000\u0000\u00bf"+
		"\u00bc\u0001\u0000\u0000\u0000\u00bf\u00bd\u0001\u0000\u0000\u0000\u00bf"+
		"\u00be\u0001\u0000\u0000\u0000\u00c0\u0013\u0001\u0000\u0000\u0000\u00c1"+
		"\u00c2\u0003\u000e\u0007\u0000\u00c2\u00c3\u0005\u0012\u0000\u0000\u00c3"+
		"\u00c4\u0005%\u0000\u0000\u00c4\u0015\u0001\u0000\u0000\u0000\u00c5\u00c6"+
		"\u0005\u0003\u0000\u0000\u00c6\u00cc\u0006\u000b\uffff\uffff\u0000\u00c7"+
		"\u00cc\u0005\u0004\u0000\u0000\u00c8\u00cc\u0005\u0005\u0000\u0000\u00c9"+
		"\u00cc\u0005\u0006\u0000\u0000\u00ca\u00cc\u0005\u0007\u0000\u0000\u00cb"+
		"\u00c5\u0001\u0000\u0000\u0000\u00cb\u00c7\u0001\u0000\u0000\u0000\u00cb"+
		"\u00c8\u0001\u0000\u0000\u0000\u00cb\u00c9\u0001\u0000\u0000\u0000\u00cb"+
		"\u00ca\u0001\u0000\u0000\u0000\u00cc\u0017\u0001\u0000\u0000\u0000\u00cd"+
		"\u00cf\u0005\u0014\u0000\u0000\u00ce\u00cd\u0001\u0000\u0000\u0000\u00cf"+
		"\u00d2\u0001\u0000\u0000\u0000\u00d0\u00ce\u0001\u0000\u0000\u0000\u00d0"+
		"\u00d1\u0001\u0000\u0000\u0000\u00d1\u00d3\u0001\u0000\u0000\u0000\u00d2"+
		"\u00d0\u0001\u0000\u0000\u0000\u00d3\u00d7\u0007\u0000\u0000\u0000\u00d4"+
		"\u00d6\u0005\u0014\u0000\u0000\u00d5\u00d4\u0001\u0000\u0000\u0000\u00d6"+
		"\u00d9\u0001\u0000\u0000\u0000\u00d7\u00d5\u0001\u0000\u0000\u0000\u00d7"+
		"\u00d8\u0001\u0000\u0000\u0000\u00d8\u0019\u0001\u0000\u0000\u0000\u00d9"+
		"\u00d7\u0001\u0000\u0000\u0000\u00da\u00dc\u0005\u0014\u0000\u0000\u00db"+
		"\u00da\u0001\u0000\u0000\u0000\u00dc\u00df\u0001\u0000\u0000\u0000\u00dd"+
		"\u00db\u0001\u0000\u0000\u0000\u00dd\u00de\u0001\u0000\u0000\u0000\u00de"+
		"\u00e0\u0001\u0000\u0000\u0000\u00df\u00dd\u0001\u0000\u0000\u0000\u00e0"+
		"\u00e4\u0005\b\u0000\u0000\u00e1\u00e3\u0005\u0014\u0000\u0000\u00e2\u00e1"+
		"\u0001\u0000\u0000\u0000\u00e3\u00e6\u0001\u0000\u0000\u0000\u00e4\u00e2"+
		"\u0001\u0000\u0000\u0000\u00e4\u00e5\u0001\u0000\u0000\u0000\u00e5\u001b"+
		"\u0001\u0000\u0000\u0000\u00e6\u00e4\u0001\u0000\u0000\u0000\u00e7\u00ed"+
		"\u0007\u0001\u0000\u0000\u00e8\u00ee\u00034\u001a\u0000\u00e9\u00ee\u0003"+
		"6\u001b\u0000\u00ea\u00ee\u0005&\u0000\u0000\u00eb\u00ee\u0005%\u0000"+
		"\u0000\u00ec\u00ee\u0005\'\u0000\u0000\u00ed\u00e8\u0001\u0000\u0000\u0000"+
		"\u00ed\u00e9\u0001\u0000\u0000\u0000\u00ed\u00ea\u0001\u0000\u0000\u0000"+
		"\u00ed\u00eb\u0001\u0000\u0000\u0000\u00ed\u00ec\u0001\u0000\u0000\u0000"+
		"\u00ee\u00ef\u0001\u0000\u0000\u0000\u00ef\u00ed\u0001\u0000\u0000\u0000"+
		"\u00ef\u00f0\u0001\u0000\u0000\u0000\u00f0\u00f1\u0001\u0000\u0000\u0000"+
		"\u00f1\u00f2\u0007\u0001\u0000\u0000\u00f2\u001d\u0001\u0000\u0000\u0000"+
		"\u00f3\u00fe\u0003 \u0010\u0000\u00f4\u00fe\u0003\"\u0011\u0000\u00f5"+
		"\u00fe\u0003$\u0012\u0000\u00f6\u00fe\u0003&\u0013\u0000\u00f7\u00fe\u0003"+
		"(\u0014\u0000\u00f8\u00fe\u0003*\u0015\u0000\u00f9\u00fe\u0003,\u0016"+
		"\u0000\u00fa\u00fe\u0003.\u0017\u0000\u00fb\u00fe\u00030\u0018\u0000\u00fc"+
		"\u00fe\u00032\u0019\u0000\u00fd\u00f3\u0001\u0000\u0000\u0000\u00fd\u00f4"+
		"\u0001\u0000\u0000\u0000\u00fd\u00f5\u0001\u0000\u0000\u0000\u00fd\u00f6"+
		"\u0001\u0000\u0000\u0000\u00fd\u00f7\u0001\u0000\u0000\u0000\u00fd\u00f8"+
		"\u0001\u0000\u0000\u0000\u00fd\u00f9\u0001\u0000\u0000\u0000\u00fd\u00fa"+
		"\u0001\u0000\u0000\u0000\u00fd\u00fb\u0001\u0000\u0000\u0000\u00fd\u00fc"+
		"\u0001\u0000\u0000\u0000\u00fe\u001f\u0001\u0000\u0000\u0000\u00ff\u0100"+
		"\u0005\u0014\u0000\u0000\u0100\u0101\u0005\u001e\u0000\u0000\u0101\u0102"+
		"\u0005%\u0000\u0000\u0102\u0103\u0005\u0014\u0000\u0000\u0103!\u0001\u0000"+
		"\u0000\u0000\u0104\u0105\u0005\u0014\u0000\u0000\u0105\u0106\u0005\t\u0000"+
		"\u0000\u0106\u0107\u0005\u0014\u0000\u0000\u0107#\u0001\u0000\u0000\u0000"+
		"\u0108\u0109\u0005\u0014\u0000\u0000\u0109\u010a\u0005\u001e\u0000\u0000"+
		"\u010a\u010b\u0005 \u0000\u0000\u010b\u010c\u0005\u0014\u0000\u0000\u010c"+
		"%\u0001\u0000\u0000\u0000\u010d\u010e\u0005\u0014\u0000\u0000\u010e\u010f"+
		"\u0005\u001e\u0000\u0000\u010f\u0110\u0005!\u0000\u0000\u0110\u0111\u0005"+
		"\u0014\u0000\u0000\u0111\'\u0001\u0000\u0000\u0000\u0112\u0113\u0005\u0014"+
		"\u0000\u0000\u0113\u0114\u0005\u001f\u0000\u0000\u0114\u0115\u0005%\u0000"+
		"\u0000\u0115\u0116\u0005\u0014\u0000\u0000\u0116)\u0001\u0000\u0000\u0000"+
		"\u0117\u0118\u0005\u0014\u0000\u0000\u0118\u0119\u0005\"\u0000\u0000\u0119"+
		"\u011a\u0005%\u0000\u0000\u011a\u011b\u0005\u0014\u0000\u0000\u011b+\u0001"+
		"\u0000\u0000\u0000\u011c\u011d\u0005\u0014\u0000\u0000\u011d\u011e\u0005"+
		"\u000b\u0000\u0000\u011e\u011f\u0005\u0014\u0000\u0000\u011f-\u0001\u0000"+
		"\u0000\u0000\u0120\u0121\u0005\u0014\u0000\u0000\u0121\u0122\u0005\n\u0000"+
		"\u0000\u0122\u0123\u0005\u0014\u0000\u0000\u0123/\u0001\u0000\u0000\u0000"+
		"\u0124\u0125\u0005\u0014\u0000\u0000\u0125\u0126\u0005\"\u0000\u0000\u0126"+
		"\u0127\u0005 \u0000\u0000\u0127\u0128\u0005\u0014\u0000\u0000\u01281\u0001"+
		"\u0000\u0000\u0000\u0129\u012a\u0005\u0014\u0000\u0000\u012a\u012b\u0005"+
		"\"\u0000\u0000\u012b\u012c\u0005!\u0000\u0000\u012c\u012d\u0005\u0014"+
		"\u0000\u0000\u012d3\u0001\u0000\u0000\u0000\u012e\u0130\u0005\u0014\u0000"+
		"\u0000\u012f\u012e\u0001\u0000\u0000\u0000\u0130\u0133\u0001\u0000\u0000"+
		"\u0000\u0131\u012f\u0001\u0000\u0000\u0000\u0131\u0132\u0001\u0000\u0000"+
		"\u0000\u0132\u0134\u0001\u0000\u0000\u0000\u0133\u0131\u0001\u0000\u0000"+
		"\u0000\u0134\u0138\u0005\u0010\u0000\u0000\u0135\u0137\u0005\u0014\u0000"+
		"\u0000\u0136\u0135\u0001\u0000\u0000\u0000\u0137\u013a\u0001\u0000\u0000"+
		"\u0000\u0138\u0136\u0001\u0000\u0000\u0000\u0138\u0139\u0001\u0000\u0000"+
		"\u0000\u01395\u0001\u0000\u0000\u0000\u013a\u0138\u0001\u0000\u0000\u0000"+
		"\u013b\u013d\u0005\u0014\u0000\u0000\u013c\u013b\u0001\u0000\u0000\u0000"+
		"\u013d\u0140\u0001\u0000\u0000\u0000\u013e\u013c\u0001\u0000\u0000\u0000"+
		"\u013e\u013f\u0001\u0000\u0000\u0000\u013f\u0141\u0001\u0000\u0000\u0000"+
		"\u0140\u013e\u0001\u0000\u0000\u0000\u0141\u0145\u0005\u0011\u0000\u0000"+
		"\u0142\u0144\u0005\u0014\u0000\u0000\u0143\u0142\u0001\u0000\u0000\u0000"+
		"\u0144\u0147\u0001\u0000\u0000\u0000\u0145\u0143\u0001\u0000\u0000\u0000"+
		"\u0145\u0146\u0001\u0000\u0000\u0000\u01467\u0001\u0000\u0000\u0000\u0147"+
		"\u0145\u0001\u0000\u0000\u0000\u0148\u014a\u0005\u0014\u0000\u0000\u0149"+
		"\u0148\u0001\u0000\u0000\u0000\u014a\u014d\u0001\u0000\u0000\u0000\u014b"+
		"\u0149\u0001\u0000\u0000\u0000\u014b\u014c\u0001\u0000\u0000\u0000\u014c"+
		"\u014e\u0001\u0000\u0000\u0000\u014d\u014b\u0001\u0000\u0000\u0000\u014e"+
		"\u0152\u0007\u0002\u0000\u0000\u014f\u0151\u0005\u0014\u0000\u0000\u0150"+
		"\u014f\u0001\u0000\u0000\u0000\u0151\u0154\u0001\u0000\u0000\u0000\u0152"+
		"\u0150\u0001\u0000\u0000\u0000\u0152\u0153\u0001\u0000\u0000\u0000\u0153"+
		"9\u0001\u0000\u0000\u0000\u0154\u0152\u0001\u0000\u0000\u0000\u0155\u0157"+
		"\u0005\u0014\u0000\u0000\u0156\u0155\u0001\u0000\u0000\u0000\u0157\u015a"+
		"\u0001\u0000\u0000\u0000\u0158\u0156\u0001\u0000\u0000\u0000\u0158\u0159"+
		"\u0001\u0000\u0000\u0000\u0159\u015b\u0001\u0000\u0000\u0000\u015a\u0158"+
		"\u0001\u0000\u0000\u0000\u015b\u015f\u0007\u0003\u0000\u0000\u015c\u015e"+
		"\u0005\u0014\u0000\u0000\u015d\u015c\u0001\u0000\u0000\u0000\u015e\u0161"+
		"\u0001\u0000\u0000\u0000\u015f\u015d\u0001\u0000\u0000\u0000\u015f\u0160"+
		"\u0001\u0000\u0000\u0000\u0160;\u0001\u0000\u0000\u0000\u0161\u015f\u0001"+
		"\u0000\u0000\u0000\u001e@GX`{\u0087\u008c\u0094\u009a\u00a4\u00ab\u00ad"+
		"\u00b2\u00bf\u00cb\u00d0\u00d7\u00dd\u00e4\u00ed\u00ef\u00fd\u0131\u0138"+
		"\u013e\u0145\u014b\u0152\u0158\u015f";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}