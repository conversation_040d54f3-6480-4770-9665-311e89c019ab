package cn.gwssi.syntax.parser;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.parser.ParserEscape;
import cn.gwssi.syntax.parser.converter.ExpressionConverter;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;

public class Expression {

    @Test
    public void convertSingle() throws IPException, IOException {
        String expression = "R=本发明公开了一种识别文本修正方法及系统，该方法包\\括：(实时接收^用户语音数据)；对所述语音数据进行语音识别，得到初始识别文本；对所述语音数据进行延时播放，并显示延时后的语音数据对应的初始识别文本，以使用户根据延时后的语音数据对所述初始识别文本进行修正，并将来不及修正的错误识别文本标记为待修正识别文本；根据修正后识别文本及其对应的语音数据对待修正识别文本进行修正。利用本发明，可以减少用户修正识别文本的工作量，提高识别文本修正的准确度及效率。";
        expression = ParserEscape.escapeAdd(expression);
        expression = FileUtils.readFileToString(new File("D:\\1.txt"));
        long start = System.currentTimeMillis();
        System.out.println(ExpressionConverter.getInstance().doExtract(expression));
        long end = System.currentTimeMillis();
        System.out.println(end - start);
        System.out.println(ExpressionConverter.getInstance().doCheck(expression, "tio"));
        IPCondition condition = ExpressionConverter.getInstance().doFilter(expression, "tio");
        System.out.println(condition);
    }

//    @Test
//    public void toStatement() throws Exception {
//        String queryString = "文字,问+=(java not c)";
//        IPCondition ipCondition = ScannerFilterFactory.getInstance().doFilter(queryString);
//
//        System.out.println(ipCondition.toString());
////        IPType ipType = IPDaoFactory.getInstance().getIPType(indexName);
////        IPCondition statementCon = FieldMappingFilterFactory.getInstance().doFilter(ipType, ipCondition);
//    }

}
