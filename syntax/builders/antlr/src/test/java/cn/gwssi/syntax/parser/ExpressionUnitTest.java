package cn.gwssi.syntax.parser;

import cn.gwssi.common.exception.IPErrorException;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.parser.converter.ExpressionConverter;
import cn.gwssi.syntax.parser.converter.error.ErrorHandler;
import cn.gwssi.syntax.parser.converter.visitor.StatVisitor;
import cn.gwssi.syntax.parser.expression.ExpressionLexer;
import cn.gwssi.syntax.parser.expression.ExpressionParser;
import org.antlr.v4.runtime.ANTLRInputStream;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

public class ExpressionUnitTest {

//这些表达式都要支持；
//    r=(华为(北京)分公司)
//    r=(华为(北京分(公)司)
//    r=(北a(x))(((zafeji(e中)eafejix(京分)x)
//    r=(华为((北京))分(公)司)
//    r=(qf()2)

    @Test
    public void assertAntlr() throws Exception {
        ClassLoader classLoader = this.getClass().getClassLoader();
        String expPath = classLoader.getResource("expressions").getPath();
        String resPath = classLoader.getResource("antlrs").getPath();
        List<String> expressions = FileUtils.readLines(new File(expPath), StandardCharsets.UTF_8.name());
        List<String> results = FileUtils.readLines(new File(resPath), StandardCharsets.UTF_8.name());

        expressions = expressions.stream().filter(content ->
                StringUtils.isNotEmpty(content) && !content.startsWith("//")
        ).collect(Collectors.toList());
        for (int i = 0; i < expressions.size(); i++) {
//            String result = this.convert(expressions.get(i));
//            Assertions.assertEquals(result.trim(), results.get(i).trim());
//            System.out.println("==========antlr解析，第" + (i + 1) + "行对比正确");

            ParserError error = this.check(expressions.get(i));
            if (null != error) {
                System.out.println(error);
            }
        }
    }

    @Test
    public void assertIPCondition() throws Exception {
        ClassLoader classLoader = this.getClass().getClassLoader();
        String expPath = classLoader.getResource("expressions").getPath();
        String resPath = classLoader.getResource("ipconditions").getPath();
        List<String> expressions = FileUtils.readLines(new File(expPath), StandardCharsets.UTF_8.name());
        List<String> results = FileUtils.readLines(new File(resPath), StandardCharsets.UTF_8.name());

        expressions = expressions.stream().filter(content ->
                StringUtils.isNotEmpty(content) && !content.startsWith("//")
        ).collect(Collectors.toList());
        for (int i = 0; i < expressions.size(); i++) {
            String result = this.convertIPCondition(expressions.get(i));
            Assertions.assertEquals(result.trim(), results.get(i).trim());
            System.out.println("==========转化为ipCondition，第" + (i + 1) + "行对比正确");
        }
    }

    private String convert(String expression) {
        ANTLRInputStream input = new ANTLRInputStream(expression);
        ExpressionLexer lexer = new ExpressionLexer(input);
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        ExpressionParser parser = new ExpressionParser(tokens);
        ParseTree tree = parser.expression();

        return tree.toStringTree(parser);
    }

    private String convertIPCondition(String expression) throws IPException {
        ANTLRInputStream input = new ANTLRInputStream(expression);
        ExpressionLexer lexer = new ExpressionLexer(input);
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        ExpressionParser parser = new ExpressionParser(tokens);
        parser.removeErrorListeners();
        parser.addErrorListener(new ErrorHandler());

        ParseTree tree;
        try {
            tree = parser.expression();
        } catch (ParseCancellationException e) {
            throw new IPErrorException("000000", e.getMessage());
        }

        StatVisitor converter = new StatVisitor();
        IPCondition query = converter.visit(tree);

        return query.toString().replace("\r\n", "  换行  ");
    }

    private ParserError check(String expression) {
        ExpressionConverter converter = ExpressionConverter.getInstance();
        return converter.doCheck(expression, "tio");
    }
}
