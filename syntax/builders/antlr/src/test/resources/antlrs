(expression (statement (statement ( (statement (field (words A)) (comparator =) (valueExpr (valueFinal (wordsAll (words B))))) )) (connector  OR ) (statement ( (statement (field (words C)) (comparator =) (valueExpr (valueFinal (wordsAll (words D))))) ))) <EOF>)
(expression (statement ( (statement (statement ( (statement (field (words A)) (comparator =) (valueExpr (valueFinal (wordsAll (words B))))) )) (connector  OR ) (statement ( (statement (field (words C)) (comparator =) (valueExpr (valueFinal (wordsAll (words D))))) ))) )) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words C)))) )) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr (valueFinal (wordsAll (words C)))))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) )))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words E)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words F)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words G)))) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) )) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) )) (connector  or ) (valueExpr (valueFinal (wordsAll (words E)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words F)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words G)))) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr ( (valueExpr (valueFinal (wordsAll (words D)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words E)))) )) (connector  or ) (valueExpr (valueFinal (wordsAll (words F)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words G)))) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words E)))) (connector  or ) (valueExpr ( (valueExpr (valueFinal (wordsAll (words F)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words G)))) )) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))  not  (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words E)))) (connector  or ) (valueExpr ( (valueExpr (valueFinal (wordsAll (words F)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words G)))) )) ))) )) ))) <EOF>)
(expression (statement (statement (field (words A1)) (comparator >=) (valueExpr (valueFinal (wordsAll (words A1))))) (connector  AND ) (statement (statement (field (words B1)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B11)))) (connector  OR ) (valueExpr (valueFinal (wordsAll (words B22)))) ))) (connector  AND ) (statement ( (statement (statement (field (words A2)) (comparator =) (valueExpr (valueFinal (wordsAll (words A2NOTA2))))) (connector  OR ) (statement ( (statement (field (words A3)) (comparator =) (valueExpr (valueFinal (wordsAll (words A33)))  NOT  (valueExpr (valueFinal (wordsAll (words A4)))))) ))) )))) <EOF>)
(expression (statement (statement (field (words A) (words  ) (words 1)) (comparator =) (valueExpr (valueFinal (wordsAll (words AB)) (wordsAll (comparator =)) (wordsAll (words 1))))) (connector  AND ) (statement ( (statement (statement (field (words C)) (comparator =) (valueExpr (valueFinal (wordsAll (words C2ANDASD)) (wordsAll (comparator =)) (wordsAll (words SA)) (wordsAll (words  )) (wordsAll (words AEWQWEWE)) (wordsAll (words  )) (wordsAll (words SADASDASDAS))))) (connector  OR ) (statement ( (statement (field (words CC) (words  ) (words SEQW)) (comparator >=) (valueExpr (valueFinal (wordsAll (words QWEQW23)) (wordsAll (words  )) (wordsAll (words 123))))) ))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))) (location (location_pre   pre/ 5  )) (valueExpr (valueFinal (wordsAll (words C)))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))) (connector  and ) (valueExpr (valueFinal (wordsAll (words C)))) )) (location (location_pre   pre/ 2  )) (valueExpr (valueFinal (wordsAll (words D)))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words C)))) )) (location (location_pre   pre/ 2  )) (valueExpr (valueFinal (wordsAll (words D)))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)) (wordsAll %))) (location (location_pre   pre/ 5  )) (valueExpr (valueFinal (wordsAll (words C)) (wordsAll %))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)) (wordsAll ?) (wordsAll (words C)) (wordsAll %))) (location (location_pre   pre/ 5  )) (valueExpr (valueFinal (wordsAll (words C)) (wordsAll ?) (wordsAll (words D)) (wordsAll %))) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)) (wordsAll %))) (connector  or ) (valueExpr (valueFinal (wordsAll (words C)) (wordsAll %))) )) (location (location_pre   pre/ 1  )) (valueExpr ( (valueExpr (valueFinal (wordsAll (words C)) (wordsAll %))) (connector  or ) (valueExpr (valueFinal (wordsAll (words D)) (wordsAll %))) )) ))) <EOF>)
(expression (statement (field (words A)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'B% C?D%'))) (location (location_pre   pre/ 2  )) (valueExpr (valueFinal (wordsAll (words E)) (wordsAll %))) ))) <EOF>)
(expression (statement (statement (field (words A) (words  )) (comparator =  ) (valueExpr ( (valueExpr (valueFinal (wordsAll (words B)))) (location (location_pre   PRE/ 1  )) (valueExpr (valueFinal (wordsAll (words C)) (wordsAll #) (wordsAll (words C)))) ))) (connector  AND ) (statement (statement (field (words D) (words  )) (comparator =  ) (valueExpr (valueFinal (wordsAll (words E)) (wordsAll %) (wordsAll (words F))))) (connector  AND ) (statement (statement (field (words G) (words  )) (comparator =  ) (valueExpr (valueFinal (wordsAll (words H))))) (connector  AND ) (statement (statement (field (words I) (words  )) (comparator =  ) (valueExpr (valueFinal (quotedWords 'J')))) (connector  AND ) (statement (statement (field (words K) (words  )) (comparator =  ) (valueExpr (valueFinal (wordsAll (words L))))) (connector  AND ) (statement (statement (field (words M)) (comparator =) (valueExpr TRUECASE ( (valueFinal (quotedWords 'N')) ))) (connector  AND ) (statement (statement (field (words MM)) (comparator =) (valueExpr (valueFinal (wordsAll (words 1)) (wordsAll (words  )) (wordsAll (words TO)) (wordsAll (words  )) (wordsAll (words 3))))) (connector  AND ) (statement (field (words NN)) (comparator =) (valueExpr (valueFinal (wordsAll (words OO)) (wordsAll /) (wordsAll (words FREQ)) (wordsAll (words  )) (wordsAll (comparator >  )) (wordsAll (words 1)))))))))))) <EOF>)
(expression (statement (statement ( (statement (statement (field (words 名称)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 日光温室)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 暖棚)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 卷帘机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 保温被)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 加温炉)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 加温机)))) ))) (connector  or ) (statement (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'A01G9/12%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/14%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/16%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/18%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/2%'))) ))) (connector  or ) (statement (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'E06B9/%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'F16H%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'B32B%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'E04%'))) ))) (connector  and ) (statement (field (words 摘要,主权项)) (comparator +=) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 日光温室)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 暖棚)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 卷帘机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 保温被)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 加温炉)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 加温机)))) )))))) )) (connector  or ) (statement (statement ( (statement (statement (field (words 名称)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 大棚)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 卷膜)))) ))) (connector  or ) (statement (statement (field (words 摘要,主权项)) (comparator +=) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 大棚)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 卷膜)))) ))) (connector  and ) (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'A01G9/%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'E04%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'B65H%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'C04%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'C08%'))) ))))) )) (connector  or ) (statement (statement ( (statement (statement (field (words 名称)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 连栋温室)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 开窗机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 拉幕机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 遮阳网)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 保温幕)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 排风机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 温帘)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 二氧化碳发生器)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 燃油热风炉)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 热水加温)))) ))) (connector  or ) (statement (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'A01G9/12%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/14%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/16%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/18%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G9/2%'))) ))) (connector  or ) (statement (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'E06B9/%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'F16H%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'B32B%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'E04%'))) ))) (connector  and ) (statement (statement (field (words 摘要,主权项)) (comparator +=) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 连栋温室)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 开窗机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 拉幕机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 遮阳网)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 保温幕)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 排风机)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 温帘)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 二氧化碳发生器)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 燃油热风炉)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 热水加温)))) ))) (connector  or ) (statement (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'A01G25/%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'B05B%'))) ))) (connector  and ) (statement (statement (field (words 名称,摘要,主权项)) (comparator +=) (valueExpr (valueFinal (wordsAll (words 温室))))) (connector  or ) (statement (field (words 名称,摘要,主权项)) (comparator +=) (valueExpr ( (valueExpr ( (valueExpr ( (valueExpr (valueFinal (wordsAll (words 水)))) (connector  and ) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 过滤)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 软化)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 增压)))) )) )) (connector  or ) (valueExpr (valueFinal (wordsAll (words 灌溉)))) )) (connector  and ) (valueExpr (valueFinal (wordsAll (words 温室)))) ))))))))) )) (connector  or ) (statement (statement ( (statement (statement (field (words 名称)) (comparator =) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 植物工厂)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 无土栽培)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 营养液栽培)))) (connector  or ) (valueExpr (valueFinal (wordsAll (words 水培)))) ))) (connector  or ) (statement (field (words 分类号)) (comparator =) (valueExpr ( (valueExpr (valueFinal (quotedWords 'A01G31/02%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G31/04%'))) (connector  or ) (valueExpr (valueFinal (quotedWords 'A01G31/06%'))) )))) )) (connector  and ) (statement ( (statement (statement (field (words 名称,摘要,主权项)) (comparator +=) (valueExpr ( (valueExpr (valueFinal (wordsAll (words 农)))) ))) (connector  OR ) (statement (field (words 分类号)) (comparator =) (valueExpr (valueFinal (wordsAll (words a01)))))) )))))) <EOF>)