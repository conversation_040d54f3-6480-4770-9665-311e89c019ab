// Generated from src/main/java/cn/gwssi/syntax/parser/expression/Expression.g4 by ANTLR 4.13.2
package cn.gwssi.syntax.parser.expression;
import org.antlr.v4.runtime.tree.ParseTreeVisitor;

/**
 * This interface defines a complete generic visitor for a parse tree produced
 * by {@link ExpressionParser}.
 *
 * @param <T> The return type of the visit operation. Use {@link Void} for
 * operations with no return type.
 */
public interface ExpressionVisitor<T> extends ParseTreeVisitor<T> {
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#expression}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitExpression(ExpressionParser.ExpressionContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statSingle}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatSingle(ExpressionParser.StatSingleContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statMultiple}
	 * labeled alternative in {@link ExpressionParser#statement}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatMultiple(ExpressionParser.StatMultipleContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statNested}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatNested(ExpressionParser.StatNestedContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statFinal}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatFinal(ExpressionParser.StatFinalContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statIn}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatIn(ExpressionParser.StatInContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statOnlyValue}
	 * labeled alternative in {@link ExpressionParser#stat}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatOnlyValue(ExpressionParser.StatOnlyValueContext ctx);
	/**
	 * Visit a parse tree produced by the {@code statSerial}
	 * labeled alternative in {@link ExpressionParser#statMulti}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitStatSerial(ExpressionParser.StatSerialContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueNot}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueNot(ExpressionParser.ValueNotContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueTruecase}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueTruecase(ExpressionParser.ValueTruecaseContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueNested}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueNested(ExpressionParser.ValueNestedContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueSerial}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueSerial(ExpressionParser.ValueSerialContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueFreq}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueFreq(ExpressionParser.ValueFreqContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueLocation}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueLocation(ExpressionParser.ValueLocationContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueBoost}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueBoost(ExpressionParser.ValueBoostContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueNormal}
	 * labeled alternative in {@link ExpressionParser#valueExpr}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueNormal(ExpressionParser.ValueNormalContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#field}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitField(ExpressionParser.FieldContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#words}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitWords(ExpressionParser.WordsContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueQuoted}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueQuoted(ExpressionParser.ValueQuotedContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueQuotedSingle}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueQuotedSingle(ExpressionParser.ValueQuotedSingleContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueWords}
	 * labeled alternative in {@link ExpressionParser#valueFinal}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueWords(ExpressionParser.ValueWordsContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#valueIn}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueIn(ExpressionParser.ValueInContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueInQuoted}
	 * labeled alternative in {@link ExpressionParser#valueInItem}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueInQuoted(ExpressionParser.ValueInQuotedContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueInQuotedSingle}
	 * labeled alternative in {@link ExpressionParser#valueInItem}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueInQuotedSingle(ExpressionParser.ValueInQuotedSingleContext ctx);
	/**
	 * Visit a parse tree produced by the {@code valueInWords}
	 * labeled alternative in {@link ExpressionParser#valueInItem}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitValueInWords(ExpressionParser.ValueInWordsContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#wordsAll}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitWordsAll(ExpressionParser.WordsAllContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#wordsBoost}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitWordsBoost(ExpressionParser.WordsBoostContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#connector}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitConnector(ExpressionParser.ConnectorContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#comparator}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitComparator(ExpressionParser.ComparatorContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#comparator_in}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitComparator_in(ExpressionParser.Comparator_inContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#wordsWithBracket}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitWordsWithBracket(ExpressionParser.WordsWithBracketContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation(ExpressionParser.LocationContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_pre}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_pre(ExpressionParser.Location_preContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_pre_l}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_pre_l(ExpressionParser.Location_pre_lContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_pre_sen}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_pre_sen(ExpressionParser.Location_pre_senContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_pre_seg}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_pre_seg(ExpressionParser.Location_pre_segContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_equ}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_equ(ExpressionParser.Location_equContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_and}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_and(ExpressionParser.Location_andContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_and_e}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_and_e(ExpressionParser.Location_and_eContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_and_l}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_and_l(ExpressionParser.Location_and_lContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_and_sen}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_and_sen(ExpressionParser.Location_and_senContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#location_and_seg}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitLocation_and_seg(ExpressionParser.Location_and_segContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#parenth_l}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitParenth_l(ExpressionParser.Parenth_lContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#parenth_r}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitParenth_r(ExpressionParser.Parenth_rContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#quotedWords}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitQuotedWords(ExpressionParser.QuotedWordsContext ctx);
	/**
	 * Visit a parse tree produced by {@link ExpressionParser#quotedWordsSingle}.
	 * @param ctx the parse tree
	 * @return the visitor result
	 */
	T visitQuotedWordsSingle(ExpressionParser.QuotedWordsSingleContext ctx);
}