package cn.gwssi;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.meta.IPType;
import cn.gwssi.mgenerator.MappingGenerator;
import cn.gwssi.xparser.IPXmlParser;

public class XMLParserTest {
    public static void main(String[] args) throws IPException {
        String xml ="<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<type name=\"system_monitor\" index=\"system_monitor\" number_of_shards=\"1\" number_of_replicas=\"1\">\n" +
                "    <columns>\n" +
                "        <column is_array=\"false\" name=\"taskId\" not_null=\"false\" type=\"item_no\" />\n" +
                "        <column is_array=\"false\" name=\"time\" not_null=\"false\" type=\"item_no\" />\n" +
                "        <column is_array=\"false\" name=\"ipAddr\" not_null=\"false\" type=\"item_no\" />\n" +
                "        <column is_array=\"false\" name=\"cpuUsage\" not_null=\"false\" type=\"double\" />\n" +
                "        <column is_array=\"false\" name=\"netSpeed\" not_null=\"false\" type=\"double\" />\n" +
                "        <column is_array=\"false\" name=\"memUsage\" not_null=\"false\" type=\"double\" />\n" +
                "        <column is_array=\"false\" name=\"timeasp\" not_null=\"false\" type=\"double\" />\n" +
                "    </columns>\n" +
                "    <fields>\n" +
                "        <field alias=\"time\" analyzer=\"null\" column_name=\"time\" name=\"time\" />\n" +
                "        <field alias=\"ipAddr\" analyzer=\"null\" column_name=\"ipAddr\" name=\"ipAddr\" />\n" +
                "        <field alias=\"cpuUsage\" analyzer=\"null\" column_name=\"cpuUsage\" name=\"cpuUsage\" />\n" +
                "        <field alias=\"taskId\" analyzer=\"null\" column_name=\"taskId\" name=\"taskId\" />\n" +
                "        <field alias=\"memUsage\" analyzer=\"null\" column_name=\"memUsage\" name=\"memUsage\" />\n" +
                "        <field alias=\"netSpeed\" analyzer=\"null\" column_name=\"netSpeed\" name=\"netSpeed\" />\n" +
                "        <field alias=\"timeasp\" analyzer=\"null\" column_name=\"timeasp\" name=\"timeasp\" />\n" +
                "    </fields>\n" +
                "</type>";

        IPType ipType = IPXmlParser.getInstance().parseXml(xml);

        System.out.println(ipType);
        String mapping = MappingGenerator.getInstance().generateMapping(2,ipType);
        System.out.println(mapping);

    }
}
