package cn.gwssi;

import cn.gwssi.mgenerator.MappingGenerator;

public class MappingTest {

    public static void main(String[] args){
        String config = "{\"mappings\": {\n" +
                "        \"config\": {\n" +
                "            \"properties\": {\n" +
                "                \"typeName\": {\n" +
                "                    \"type\": \"keyword\"\n" +
                "                },\n" +
                "                \"virtualFields\": {\n" +
                "                    \"type\": \"keyword\",\n" +
                "                    \"ignore_above\":256\n" +
                "                },\n" +
                "                \"state\": {\n" +
                "                    \"type\": \"keyword\"\n" +
                "                },\n" +
                "                \"shardNum\": {\n" +
                "                    \"type\": \"integer\"\n" +
                "                },\n" +
                "                \"confXml\": {\n" +
                "                    \"type\": \"keyword\",\n" +
                "                    \"ignore_above\":256\n" +
                "                },\n" +
                "                \"indexName\": {\n" +
                "                    \"type\": \"keyword\"\n" +
                "                },\n" +
                "                \"replicasNum\": {\n" +
                "                    \"type\": \"integer\"\n" +
                "                },\n" +
                "                \"nestedFields\": {\n" +
                "                    \"type\": \"keyword\",\n" +
                "                    \"ignore_above\":256\n" +
                "                },\n" +
                "                \"fields\": {\n" +
                "                    \"type\": \"keyword\",\n" +
                "                    \"ignore_above\":256\n" +
                "                },\n" +
                "                \"confJson\": {\n" +
                "                    \"type\": \"keyword\",\n" +
                "                    \"ignore_above\":256\n" +
                "                },\n" +
                "                \"createTime\": {\n" +
                "                    \"type\": \"date\",\n" +
                "                    \"format\": \"yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis\"\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    }}";
        String json = MappingGenerator.getInstance().extractMappingFromConfig(7,config);
        System.out.println(json);
    }
}
