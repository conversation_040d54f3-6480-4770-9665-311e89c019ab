package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPObject implements Serializable{
	
	/**
	 * 所属type
	 */
	private IPType ipType = null;
	
	/**
	 * 配置文件名 
	 */
	private String configFileName = null;

	/**
	 * 
	 * @throws IPException
	 */
	protected IPObject() throws IPException {
	}
	
	/**
	 * 
	 * @param ipType
	 */
	public IPObject(IPType ipType) throws IPException {
		this.ipType = ipType;
		this.configFileName =  ipType.getConfigFileName();
	}

	/**
	 * @return the ipType
	 */
	public IPType getIpType() {
		return ipType;
	}

	/**
	 * @param ipType the ipType to set
	 */
	public void setIpType(IPType ipType) {
		this.ipType = ipType;
		this.configFileName = ipType.getConfigFileName();
	}

	/**
	 * @return the configFileName
	 */
	public String getConfigFileName() {
		return configFileName;
	}
}
