package cn.gwssi.condition;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.condition.filter.HeaderFilter;
import cn.gwssi.condition.filter.TailFilter;
import cn.gwssi.condition.filter.VirtualFieldFilter;
import cn.gwssi.meta.IPType;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.xparser.IPDaoFactory;

import java.util.ArrayList;
import java.util.Collection;

/**
 * 
 * <AUTHOR>
 *
 */
public class FieldMappingFilterFactory {
	private static FieldMappingFilterFactory _instance = new FieldMappingFilterFactory();

	/**
	 *  过滤器集合
	 */
	private ArrayList<ConditionFilter> filters = new ArrayList<ConditionFilter>();

	/**
	 * 构造函数，私有化
	 */
	private FieldMappingFilterFactory() {

		//转为statementItemCondition
		ConditionFilter filter = new HeaderFilter();
		filters.add(filter);

		//虚拟字段处理
		filter = new VirtualFieldFilter();
		filters.add(filter);
		
		//长短文本处理
		filter = new TailFilter();
		filters.add(filter);
	}
	
	public static FieldMappingFilterFactory getInstance() {
		return _instance;
	}
	
	/**
	 * 
	 * @param indexTypes
	 * @param ipCondition
	 * @return
	 * @throws IPException
	 */
	public IPCondition doFilter(Collection<String> indexTypes, IPCondition ipCondition) throws IPException {
		String indexType = indexTypes.iterator().next();
		IPType ipType = IPDaoFactory.getInstance().getIPType(indexType);
		return this.doFilter(ipType, ipCondition);
	}
	
	/**
	 * 
	 * @param ipCondition
	 * @return
	 * @throws IPException
	 */
	public IPCondition doFilter(IPType ipType, IPCondition ipCondition) throws IPException {
		IPCondition _condition = ipCondition;
		for(ConditionFilter filter : filters) {
			_condition = filter.doFilter(ipType, _condition);
		}
		
		return _condition;
	}
}
