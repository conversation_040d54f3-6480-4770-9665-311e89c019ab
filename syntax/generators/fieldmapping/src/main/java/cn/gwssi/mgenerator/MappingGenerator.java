package cn.gwssi.mgenerator;

import cn.gwssi.meta.*;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class MappingGenerator {

    private static final String TYPE_STRING_LOW_VERSION = "{\"type\":\"string\",\"index\":\"not_analyzed\"}";
    private static final String TYPE_STRING_HIGH_VERSION = "{\"type\":\"keyword\"}";
    private static final long DEFAULT_DIM = 4096;
    private static final long DEFAULT_HASH = 500;
    private static final long DEFAULT_BUNDLE = 4;
    private static final long DEFAULT_SEMANTIC_DIM = 768;

    private MappingGenerator() {
    }

    private static MappingGenerator instance = null;

    public static MappingGenerator getInstance() {
        if (instance == null) {
            synchronized (MappingGenerator.class) {
                if (instance == null) {
                    instance = new MappingGenerator();
                }
            }
        }

        return instance;
    }

    /**
     * 把IPType转换成mapping
     *
     * @param version version
     * @param ipType  ipType
     */
    public String generateMapping(int version, IPType ipType) {
        Map<String, DateField> dateFieldMap = ipType.getDateFieldMap();
        Map<String, IPField> ipTypeMap = ipType.getIPFieldMap();
        Map<String, IPField> ipFieldMap = new HashMap<>();
        Map<String, IPField> multiFieldMap = new HashMap<>();
        List<NestedType> nestedList = ipType.getNestedType();

        for (Map.Entry<String, IPField> entry : ipTypeMap.entrySet()) {
            String key = entry.getKey();
            IPField ipField = entry.getValue();
            String key_ = ipField.getIpColumn().getName();

            // FIXED 2019-06-17 用 value 去重
            if (!ipFieldMap.containsValue(ipField) && !multiFieldMap.containsValue(ipField)) {
//            if (!hasChineseByReg(key)) {//如果key不包含中文 也就是不为别名时，才转换
                if (key.endsWith("_raw")) {
                    multiFieldMap.put(key, ipField);
                } else {
                    ipFieldMap.put(key_, ipField);
                }
            }
        }

        StringBuffer stringBuffer = new StringBuffer();
        //mapping开头部分
        if (version < 7) {
            mappingHeadLT7(ipType, stringBuffer);
        } else {
            mappingHeadGET7(ipType, stringBuffer);
        }
        if (ipFieldMap != null && !ipFieldMap.isEmpty()) {
            field2Mapping(version, stringBuffer, ipFieldMap, multiFieldMap, dateFieldMap);
        }
        if (ipFieldMap != null && !ipFieldMap.isEmpty() && nestedList != null && !nestedList.isEmpty()) {
            stringBuffer.append(",");
        }
        nestedField2Mapping(version, stringBuffer, filterNestedMap(nestedList), dateFieldMap);
        if (version < 7) {
            mappingEndLT7(stringBuffer);
        } else {
            mappingEndGTE7(stringBuffer);
        }

        return prettyJson(stringBuffer.toString());
    }

    /**
     * 从生成的json中，抽取 mappings 部分
     *
     * @param config config
     * @return mapping
     */
    public String extractSettingFromConfig(String config) {
        return ((JSONObject) JSONPath.extract(config, "$.settings")).toJSONString();
    }

    /**
     * 从生成的json中，抽取 mappings 部分
     *
     * @param config config
     * @return mapping
     */
    public String extractMappingFromConfig(int version, String config) {
        if (version < 7) {
            return ((JSONArray) JSONPath.extract(config, "$.mappings.*")).getJSONObject(0).toJSONString();
        } else {
            return ((JSONObject) JSONPath.extract(config, "$.mappings")).toJSONString();
        }
    }

    /**
     * mapping头文件
     *
     * @param ipType       ipType
     * @param stringBuffer stringBuffer
     */
    private void mappingHeadLT7(IPType ipType, StringBuffer stringBuffer) {
        //mapping开头部分
//        stringBuffer.append("put").append("  "+type);//索引名称没有这部分可以不拼接
        mappingSettins(ipType, stringBuffer);
        stringBuffer.append("\"mappings\":")
                .append("{")
                .append("\"").append(ipType.getTypeName()).append("\":")
                .append("{")
                .append("\"properties\":")
                .append("{");
    }

    private void mappingHeadGET7(IPType ipType, StringBuffer stringBuffer) {
        //mapping开头部分
//        stringBuffer.append("put").append("  "+type);//索引名称没有这部分可以不拼接
        mappingSettins(ipType, stringBuffer);
        stringBuffer.append("\"mappings\":")
                .append("{")
                .append("\"properties\":")
                .append("{");
    }

    /**
     * 设置mapping的分片数量
     *
     * @param ipType       ipType
     * @param stringBuffer stringBuffer
     */
    private void mappingSettins(IPType ipType, StringBuffer stringBuffer) {
        String number_of_shards = ipType.getNumberOfShards();
        String number_of_replicas = ipType.getNumberOfReplicas();
        if (StringUtils.isEmpty(number_of_shards)) {
            number_of_shards = "5";
        }
        if (StringUtils.isEmpty(number_of_replicas)) {
            number_of_replicas = "1";
        }
        stringBuffer.append("{")
                .append("\"settings\":")
                .append("{")
                .append("\"index\":")
                .append("{")
                .append("\"number_of_shards\":")
                .append(number_of_shards)
                .append(",")
                .append("\"number_of_replicas\":")
                .append(number_of_replicas).append(",")
                .append("\"analysis\":")
                .append("{\"filter\":{\"english_stemmer\":{\"type\":\"stemmer\",\"language\":\"english\"}," +
                        "\"my_stopwords\":{\"type\":\"stop\",\"stopwords\":[\"\\n\",\"\\r\\n\"]}," +
                        "\"english_possessive_stemmer\":{\"type\":\"stemmer\",\"language\":\"possessive_english\"}," +
                        "\"english_stop\":{\"type\":\"stop\",\"stopwords\":\"_english_\"}}," +
                        "\"analyzer\":{\"lowercase_analyzer\":{\"filter\":[\"lowercase\"],\"type\":\"custom\",\"tokenizer\":\"keyword\"}," +
                        "\"english_without_html_tag\":{\"filter\":[\"english_possessive_stemmer\",\"lowercase\",\"english_stop\",\"english_stemmer\"]," +
                        "\"char_filter\":[\"html_strip\",\"n_char_filter\",\"rn_char_filter\"],\"type\":\"custom\",\"tokenizer\":\"standard\"}," +
                        "\"ik_without_html_tag\":{\"filter\":[\"lowercase\"],\"char_filter\":[\"html_strip\",\"n_char_filter\",\"rn_char_filter\"]," +
                        "\"type\":\"custom\",\"tokenizer\":\"ik_max_word\"},\"2gram_analyzer\" : {\"tokenizer\" : \"2gram_tokenizer\"}},\"tokenizer\" : {\"2gram_tokenizer\" : {\"type\" : \"nGram\",\"min_gram\" : \"1\",\"max_gram\" : \"2\",\"token_chars\": [ ]}}," +
                        "\"char_filter\":{\"n_char_filter\":{\"pattern\":\"\\n\",\"type\":\"pattern_replace\"," +
                        "\"replacement\":\"\"},\"rn_char_filter\":{\"pattern\":\"\\r\\n\",\"type\":\"pattern_replace\",\"replacement\":\"\"}}}")
                .append("}")
                .append("}").append(",");
    }


    /**
     * mapping 结束部分
     *
     * @param stringBuffer stringBuffer
     */
    private void mappingEndLT7(StringBuffer stringBuffer) {
        stringBuffer.append("}")
                .append("}")
                .append("}")
                .append("}");
    }

    private void mappingEndGTE7(StringBuffer stringBuffer) {
        stringBuffer.append("}")
                .append("}")
                .append("}");
    }


    /**
     * 把xml中的field节点转换为mapping
     *
     * @param version       version
     * @param stringBuffer  stringBuffer
     * @param ipFieldMap    ipFieldMap
     * @param multiFieldMap multiFieldMap
     */
    private void field2Mapping(int version, StringBuffer stringBuffer, Map<String, IPField> ipFieldMap,
                               Map<String, IPField> multiFieldMap, Map<String, DateField> dateFieldMap) {

        int i = 1;
        for (Map.Entry<String, IPField> entry : ipFieldMap.entrySet()) {
            String key = entry.getKey();
            int size = ipFieldMap.size();
            IPField ipField = entry.getValue();
            //存入ES时，统一改为小写字母。
            // FIXED 20190701 去掉 toLowerCase
            String name = ipField.getName();//字段名称
            IPAnalyzerType analyzer = ipField.getAnalyzer();//分词器
            String column_type = ipField.getIpColumn().getColumnType().name();//字段类型
            IPField multiField = multiFieldMap.get(key + "_raw");

            if (column_type.equalsIgnoreCase(IPColumnType.COLUMN_TYPE_IMG.name())) {
                if (version == 5) {
                    stringBuffer.append("\"").append(name).append("\":{")
                            .append("\"type\":\"nested\",\"properties\":{\"img_url\":").append(TYPE_STRING_HIGH_VERSION)
                            .append(",\"image_vec\":{\"type\":\"gw_vector\",\"dim\":").append(DEFAULT_DIM)
                            .append(",\"hash\":").append(DEFAULT_HASH)
                            .append(",\"bundle\":").append(DEFAULT_BUNDLE)
                            .append("}}}");
                } else if (version > 5) {
                    stringBuffer.append("\"").append(name).append("\":{")
                            .append("\"type\":\"nested\",\"properties\":{\"img_url\":")
                            .append(TYPE_STRING_HIGH_VERSION)
                            .append(",\"image_vec\":{\"type\":\"gw_knn_ready\",\"dim\":4096,")
                            .append("\"lsh_params\": {\"n_hashes\": 64,\"band\": 1,\"model_path\": \"\"},\"pq_params\": {\"n_bits\": 8,\"n_centroids\": 512,\"model_path\": \"/data/model.dat\"}")
                            .append("}}}");
                }
            } else if (column_type.equalsIgnoreCase(IPColumnType.COLUMN_TYPE_SEMANTIC.name())) {
                if (version == 5) {
                    stringBuffer.append("\"").append(name).append("\":{")
                            .append("\"type\":\"nested\",\"properties\":{\"semantic\":").append(TYPE_STRING_HIGH_VERSION)
                            .append(",\"semantic_vec\":{\"type\":\"gw_vector\",\"dim\":").append(DEFAULT_SEMANTIC_DIM)
                            .append(",\"hash\":").append(DEFAULT_HASH)
                            .append(",\"bundle\":").append(DEFAULT_BUNDLE)
                            .append("}}}");
                } else if (version > 5) {
                    stringBuffer.append("\"").append(name).append("\":{")
                            .append("\"type\":\"nested\",\"properties\":{\"semantic\":")
                            .append(TYPE_STRING_HIGH_VERSION)
                            .append(",\"semantic_vec\":{\"type\":\"gw_knn_ready\",\"dim\":4096,")
                            .append("\"lsh_params\": {\"n_hashes\": 64,\"band\": 1,\"model_path\": \"\"},\"pq_params\": {\"n_bits\": 8,\"n_centroids\": 512,\"model_path\": \"/data/model.dat\"}")
                            .append("}}}");
                }
            } else if (column_type.equalsIgnoreCase(IPColumnType.COLUMN_TYPE_DATE.name())) {
                String formatDate = dateFieldMap.get(name).getFormat();
                dateField(stringBuffer, name, column_type, formatDate);
            } else if (column_type.equalsIgnoreCase(IPColumnType.COLUMN_TYPE_GEO_POINT.name())) {
                stringBuffer.append("\"").append(name).append("\":{\"type\":\"geo_point\"}");
            } else {
                normalField(version, stringBuffer, name, column_type, analyzer);
                //multi节点转换为mapping
                if (null != multiField) {
                    String multi_type = multiField.getIpColumn().getColumnType().name();
                    IPAnalyzerType multi_analyzer = multiField.getAnalyzer();
                    multiField2Mapping(version, stringBuffer, multi_type, multi_analyzer);
                }
                analyzer2Mapping(version, stringBuffer, analyzer);//设置分词器
                stringBuffer.append("}");
            }
            i++;
            if (i <= size) {
                stringBuffer.append(",");
            }
        }
    }

    /**
     * 把multi转换为mapping
     *
     * @param stringBuffer   stringBuffer
     * @param multi_type     multi_type
     * @param multi_analyzer multi_analyzer
     */
    private void multiField2Mapping(int version, StringBuffer stringBuffer,
                                    String multi_type,
                                    IPAnalyzerType multi_analyzer) {
        String type = getColumnType(multi_type);
        if ("string".equals(type) && version >= 5) {
            type = "keyword";
        }
        stringBuffer.append(",")
                .append("\"fields\":")
                .append("{")
                .append("\"raw\":")
                .append("{")
                .append("\"type\":")
                .append("\"").append(type).append("\"");
        // FIXED 2022-10-10 版本7及以上，keyword类型，不能有分词器
        if (version < 7) {
            analyzer2Mapping(version, stringBuffer, multi_analyzer);
        }
        stringBuffer.append("}").append("}");
    }

    /**
     * 转换嵌套节点为mapping
     *
     * @param version      version
     * @param stringBuffer stringBuffer
     * @param nestedMap    nestedMap
     * @param dateFieldMap dateFieldMap
     */
    private void nestedField2Mapping(int version, StringBuffer stringBuffer,
                                     Map<String, Map<String, Map<String, IPField>>> nestedMap, Map<String, DateField> dateFieldMap) {
        int i = 1;
        for (Map.Entry<String, Map<String, Map<String, IPField>>> entry : nestedMap.entrySet()) {
            stringBuffer.append("\"").append(entry.getKey()).append("\":")
                    .append("{")
                    .append("\"type\":")
                    .append("\"nested\"")
                    .append(",")
                    .append("\"properties\":")
                    .append("{");
            Map<String, IPField> fieldMap = entry.getValue().get("nested_field");
            Map<String, IPField> multiFieldMap = entry.getValue().get("nested_raw");
            int j = 1;
            for (Map.Entry<String, IPField> fieldEntry : fieldMap.entrySet()) {
                String column_type = fieldEntry.getValue().getIpColumn().getColumnType().name();
                String fieldName = fieldEntry.getValue().getName();
                IPAnalyzerType analyzer = fieldEntry.getValue().getAnalyzer();
                IPField multliIPField = multiFieldMap.get(fieldName + "_raw");
                if (column_type.equalsIgnoreCase(IPColumnType.COLUMN_TYPE_DATE.name())) {
                    String format = dateFieldMap.get(fieldName).getFormat();
                    dateField(stringBuffer, fieldName, column_type, format);
                } else {
                    normalField(version, stringBuffer, fieldName, column_type, analyzer);
                    if (null != multliIPField) {
                        String columnType = multliIPField.getIpColumn().getColumnType().name();
                        IPAnalyzerType multli_analyzer = multliIPField.getAnalyzer();
                        multiField2Mapping(version, stringBuffer, columnType, multli_analyzer);
                    }
                    analyzer2Mapping(version, stringBuffer, analyzer);
                    stringBuffer.append("}");
                }
                j++;
                if (j <= fieldMap.size()) {
                    stringBuffer.append(",");
                }
            }
            i++;
            stringBuffer.append("}").append("}");
            if (i <= nestedMap.size()) {
                stringBuffer.append(",");
            }
        }
    }

    /**
     * 返回mapping 类型
     *
     * @param column_type column_type
     * @return string
     */
    private String getColumnType(String column_type) {
        String type = "string";
        if (column_type.equals("COLUMN_TYPE_ITEM_NO")
                || column_type.equals("COLUMN_TYPE_LONG_TEXT")
                || column_type.equals("COLUMN_TYPE_TINY_TEXT")) {
            type = "string";
        } else if (column_type.equals("COLUMN_TYPE_DATE")) {
            type = "date";
        } else if (column_type.equals("COLUMN_TYPE_INTEGER")) {
            type = "integer";
        } else if ((column_type.equals("COLUMN_TYPE_DOUBLE"))) {
            type = "double";
        } else if ((column_type.equals("COLUMN_TYPE_LONG"))) {
            return type = "long";
        } else if ((column_type.equals("COLUMN_TYPE_SHORT"))) {
            return type = "short";
        } else if ((column_type.equals("COLUMN_TYPE_BYTE"))) {
            return type = "byte";
        } else if ((column_type.equals("COLUMN_TYPE_FLOAT"))) {
            type = "float";
        }
        return type;
    }

    /**
     * 把分词器转换为mapping
     *
     * @param version      version
     * @param stringBuffer stringBuffer
     * @param analyzer     analyzer
     */
    private void analyzer2Mapping(int version, StringBuffer stringBuffer, IPAnalyzerType analyzer) {
        if (analyzer != IPAnalyzerType.ANALYZER_TYPE_NONE) {
            stringBuffer.append(",\"analyzer\":")
                    .append("\"").append(analyzer.getAnalyzer()).append("\"");
        } else if (version < 5) {
            stringBuffer.append(",\"index\":")
                    .append("\"").append(analyzer.getAnalyzer()).append("\"");
        }

    }

//    /**
//     * 得到分词器
//     *
//     * @param analyzer analyzer
//     * @return string
//     */
//    private String getAnalyzer(String analyzer) {
//        String analyzer_type;
//        if (analyzer.equals("ANALYZER_TYPE_IK_MAX_WORD")) {
//            analyzer_type = "ik_without_html_tag";
//        } else if (analyzer.equals("ANALYZER_TYPE_ENGLISH")) {
//            analyzer_type = "english_without_html_tag";
//        } else if (analyzer.equals("ANALYZER_TYPE_LOWERCASE")) {
//            analyzer_type = "lowercase_analyzer";
//        } else if (analyzer.equals("ANALYZER_TYPE_STANDARD")) {
//            analyzer_type = "standard";
//        } else if (analyzer.equals("ANALYZER_IK_MAX_WORD")) {
//            analyzer_type = "ik_max_word";
//        } else if (analyzer.equals("ANALYZER_TYPE_NGRAM")) {
//            analyzer_type = "2gram_analyzer";
//        } else if (analyzer.equals("ANALYZER_SMART")) {
//            analyzer_type = "smart_analyzer";
//        } else {
//            analyzer_type = "not_analyzed";
//        }
//        return analyzer_type;
//    }

    /**
     * 处理nested节点，把多值与单值分开
     *
     * @param nestedList nestedList
     * @return map
     */
    private Map<String, Map<String, Map<String, IPField>>> filterNestedMap(List<NestedType> nestedList) {
        Map<String, Map<String, Map<String, IPField>>> nestedMap = new HashMap<>();
        if (nestedList != null && nestedList.size() > 0) {
            for (NestedType nestedType : nestedList) {
                String name = nestedType.getName();
                Map<String, IPField> nestedTypeIPFieldMap = nestedType.getIPFieldMap();
                Map<String, IPColumn> nestedTypeColumnMap = nestedType.getColumnMap();
                Map<String, Map<String, IPField>> fieldMap = new HashMap<>();
                Map<String, IPField> ipFieldMap = new HashMap<>();
                Map<String, IPField> multiFieldMap = new HashMap<>();
                for (Map.Entry<String, IPColumn> entry : nestedTypeColumnMap.entrySet()) {
                    String key = entry.getKey();
                    IPField ipField = nestedTypeIPFieldMap.get(key + "_raw");
                    if (null != ipField) {
                        multiFieldMap.put(key + "_raw", nestedTypeIPFieldMap.get(key + "_raw"));
                    }
                    ipFieldMap.put(key, nestedTypeIPFieldMap.get(key));
                }
                fieldMap.put("nested_field", ipFieldMap);
                fieldMap.put("nested_raw", multiFieldMap);
                nestedMap.put(name, fieldMap);
            }
        }
        return nestedMap;
    }

    /**
     * 判断是否包含中文
     *
     * @param str str
     * @return boolean
     */
    private boolean hasChineseByReg(String str) {
        if (str == null) {
            return false;
        }
        Pattern pattern = Pattern.compile("[\\u4E00-\\u9FBF]+");
        return pattern.matcher(str).find();
    }


    /**
     * Json 美化
     *
     * @param jsonStr jsonStr
     * @return string
     */
    private String prettyJson(String jsonStr) {
        if (null == jsonStr || "".equals(jsonStr)) return "";
        StringBuilder sb = new StringBuilder();
        char last;
        char current = '\0';
        int indent = 0;
        for (int i = 0; i < jsonStr.length(); i++) {
            last = current;
            current = jsonStr.charAt(i);
            switch (current) {
                case '{':
                case '[':
                    sb.append(current);
                    sb.append('\n');
                    indent++;
                    addIndentBlank(sb, indent);
                    break;
                case '}':
                case ']':
                    sb.append('\n');
                    indent--;
                    addIndentBlank(sb, indent);
                    sb.append(current);
                    break;
                case ',':
                    sb.append(current);
                    if (last != '\\') {
                        sb.append('\n');
                        addIndentBlank(sb, indent);
                    }
                    break;
                default:
                    sb.append(current);
            }
        }

        return sb.toString();
    }

    /**
     * 添加space
     *
     * @param stringBuilder stringBuilder
     * @param indent        indent
     */
    private void addIndentBlank(StringBuilder stringBuilder, int indent) {
        for (int i = 0; i < indent; i++) {
            stringBuilder.append('\t');
        }
    }

    private void dateField(StringBuffer stringBuffer, String name, String column_type, String formatDate) {
        stringBuffer.append("\"").append(name).append("\":")
                .append("{")
                .append("\"type\":")
                .append("\"").append(getColumnType(column_type)).append("\",")
                .append("\"format\":").append("\"" + formatDate + "\"").append("}");
    }

//    private void normalField(StringBuffer stringBuffer, String name, String column_type){
//        stringBuffer.append("\"").append(name).append("\":")
//                .append("{")
//                .append("\"type\":")
//                .append("\"").append(getColumnType(column_type)).append("\"");
//    }

    private void normalField(int version, StringBuffer stringBuffer, String name, String column_type, IPAnalyzerType analyzer) {
        column_type = getColumnType(column_type);

        if ("string".equals(column_type) && version >= 5) {
            column_type = analyzer == IPAnalyzerType.ANALYZER_TYPE_NONE ? "keyword" : "text";
        }

        stringBuffer.append("\"").append(name).append("\":")
                .append("{")
                .append("\"type\":")
                .append("\"").append(column_type).append("\"");
    }
}
