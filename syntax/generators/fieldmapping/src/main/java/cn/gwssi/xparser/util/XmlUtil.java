package cn.gwssi.xparser.util;

import java.io.IOException;

/**
 * Created by ZLQ on 2018/3/26.
 */
public class XmlUtil {

    public static final String COLUMNS = "columns";
    public static final String COLUMN = "column";
    public static final String DESCRIPTION = "description";
    public static final String ISARRAY = "is_array";
    public static final String NAME = "name";
    public static final String NOTNULL = "not_null";
    public static final String TYPE = "type";

    public static final String FIELDS = "fields";
    public static final String FIELD = "field";
    public static final String ALIAS = "alias";
    public static final String ANALYZER = "analyzer";
    public static final String COLUMNNAME = "column_name";

    public static final String MULTI_FIELD = "multi_field";
    public static final String NESTED = "nested";
    public static final String VIRTUAL_FIELDS = "virtual_fields";
    public static final String VIRTUAL_FIELD = "virtual_field";

    public static final String DATE_FIELDS = "date_fields";
    public static final String DATE_FIELD = "date_field";
    public static final String D_FIELD = "d_field";
    public static final String M_FIELD = "m_field";
    public static final String Y_FIELD = "y_field";
    public static final String YM_FIELD = "ym_field";
    public static final String DATE_FORMAT = "format";

    public static final String CHARSETNAME = "UTF-8";

    /**
     * 文件的头
     *
     * @param result 数据缓冲区
     * @param charsetName 字符集
     */
    public static void prepareXmlHead(StringBuilder result, String charsetName) {
        if (charsetName == null || charsetName.length() == 0) {
            charsetName = XmlUtil.CHARSETNAME;
        }

        result.append("<?xml version=\"1.0\" encoding=\"");
        result.append(charsetName);
        result.append("\"?>\n");
    }

    /**
     * 增加属性
     *
     * @param buffer 数据缓冲区
     * @param name 字段名称
     * @param value 字段值
     */
    public static void addProperty(StringBuilder buffer, String name, String value) {
        if (value != null) {
            buffer.append(" ");
            buffer.append(name);
            buffer.append("=\"");
            filter(buffer, value);
            buffer.append("\"");
        }
    }

    /**
     * 添加元素开始位置
     * @param buffer
     * @param name
     */
    public static void addElementPre(StringBuilder buffer, String name){
        buffer.append("<");
        buffer.append(name);
        buffer.append(">\n");
    }

    /**
     * 添加元素结束位置
     * @param buffer
     * @param name
     */
    public static void addElementAfter(StringBuilder buffer, String name){
        buffer.append("</");
        buffer.append(name);
        buffer.append(">\n");
    }

    /**
     * 添加column字段
     * @param buffer
     * @param description
     * @param isArray
     * @param name
     * @param notNull
     * @param type
     */
    public static void addColumn(StringBuilder buffer, String description, String isArray, String name, String notNull, String type){
        buffer.append("<").append(COLUMN);
        //添加描述
        XmlUtil.addProperty(buffer, DESCRIPTION, description);
        //添加数组
        XmlUtil.addProperty(buffer, ISARRAY, isArray);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
        //添加空判断
        XmlUtil.addProperty(buffer, NOTNULL, notNull);
        //添加字段类型
        XmlUtil.addProperty(buffer, TYPE, type);
        buffer.append("/>\n");

    }

    /**
     * 添加field字段
     * @param buffer
     * @param alias
     * @param analyzer
     * @param columnName
     * @param description
     * @param name
     */
    public static void addField(StringBuilder buffer, String alias, String analyzer, String columnName, String description, String name){
        buffer.append("<").append(FIELD);
        //添加别名
        XmlUtil.addProperty(buffer, ALIAS, alias);
        //添加分词器
        XmlUtil.addProperty(buffer, ANALYZER, analyzer);
        //添加column名称
        XmlUtil.addProperty(buffer, COLUMNNAME, columnName);
        //添加描述
        XmlUtil.addProperty(buffer, DESCRIPTION, description);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
    }

    /**
     * 添加mutil_field字段
     * @param buffer
     * @param analyzer
     * @param columnName
     * @param description
     * @param name
     */
    public static void addMutilField(StringBuilder buffer, String analyzer, String columnName, String description, String name){
        buffer.append("<");
        buffer.append(MULTI_FIELD);
        //添加分词器
        XmlUtil.addProperty(buffer, ANALYZER, analyzer);
        //添加colum名称
        XmlUtil.addProperty(buffer, COLUMNNAME, columnName);
        //添加描述
        XmlUtil.addProperty(buffer, DESCRIPTION, description);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
        buffer.append("/>\n");
    }

    /**
     * 添加虚拟规则字段
     * @param buffer
     * @param name
     * @param alias
     * @param fields
     */
    public static void addVirtualFields(StringBuilder buffer, String name, String alias, String fields){
        buffer.append("<");
        buffer.append(VIRTUAL_FIELD);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
        //添加别名
        XmlUtil.addProperty(buffer, ALIAS, alias);
        //添加虚拟字段
        XmlUtil.addProperty(buffer, FIELDS, fields);
        buffer.append("/>\n");
    }

    public static void addDateFieldItSelf(StringBuilder buffer, String name, String format){
        buffer.append("<");
        buffer.append(DATE_FIELD);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
        XmlUtil.addProperty(buffer, DATE_FORMAT, format);
        buffer.append("/>\n");
    }

    public static void addDateFields(StringBuilder buffer, String name, String dField, String mField, String yField, String ymField){
        buffer.append("<");
        buffer.append(DATE_FIELD);
        //添加名称
        XmlUtil.addProperty(buffer, NAME, name);
        XmlUtil.addProperty(buffer, D_FIELD, dField);
        XmlUtil.addProperty(buffer, M_FIELD, mField);
        XmlUtil.addProperty(buffer, Y_FIELD, yField);
        XmlUtil.addProperty(buffer, YM_FIELD, ymField);
        buffer.append("/>\n");
    }

    /**
     * 格式化内容，替换字符串中的[<>&"']等字符
     *
     * @param value 输入字符串
     * @return 转换后的字符串
     */
    public static String filter(String value) {
        if (value == null || value.length() == 0) {
            return value;
        }
        StringBuilder result = new StringBuilder(value.length() + 50);
        filter(result, value);
        return result.toString();
    }

    /**
     * 格式化内容，替换字符串中的[<>&"']等字符
     *
     * @param result 数据缓冲区
     * @param value 输入字符串
     */
    public static void filter(Appendable result, String value) {
        if (value != null && value.length() != 0) {
            int len = value.length();
            char content[] = new char[len];
            value.getChars(0, len, content, 0);

            for (char c : content) {
                try {
                    switch (c) {
                        case '<':
                            result.append("&lt;");
                            break;
                        case '>':
                            result.append("&gt;");
                            break;
                        case '&':
                            result.append("&amp;");
                            break;
                        case '"':
                            result.append("&quot;");
                            break;
                        case '\'':
                            result.append("&#39;");
                            break;
                        case '\\':
                            result.append("&#92;");
                            break;
                        default:
                            result.append(c);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e.getMessage());
                }
            }
        }
    }
}
