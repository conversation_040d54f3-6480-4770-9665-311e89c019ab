package cn.gwssi.condition.filter.util;

import cn.gwssi.common.common.IPTypeErrCodes;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.StringUtil;
import cn.gwssi.meta.*;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 10:25
 */
public class IPTypeUtil {

    public static String getMuiltField(IPType ipType, String fieldName) throws IPException {
        String multiFieldName = fieldName;
        IPField ipField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName));
        Map<String, IPField> fieldMap = ipField.getIPFieldMap();
        if (null != fieldMap && fieldMap.size() > 0) {

            for (Map.Entry<String, IPField> mutiFieldEntry : fieldMap.entrySet()) {
                IPMultiField mutiField = (IPMultiField) mutiFieldEntry.getValue();
                IPAnalyzerType analyzerType = mutiField.getAnalyzer();
                if (IPAnalyzerType.ANALYZER_TYPE_NONE == analyzerType) {
//			找到分词器为null的mti-field，更新columnName
                    String multiColumnName = mutiField.getMultiColumnName();
                    String columnName = ipField.getIpColumn().getName();
//					statementItemCondition.setFieldName(columnName + "." + multiColumnName);
                    multiFieldName = columnName + "_" + multiColumnName;

                }
            }
        }

        return multiFieldName;
    }

    public static String getSourceColumnName(IPType ipType, String fieldName) throws IPException {
        StringBuilder buf = new StringBuilder();
        if (StringUtils.isBlank(fieldName) || StringUtil.equals(fieldName, "_id")) {
            return fieldName;
        }

        IPField ipField = ipType.getIPField(fieldName);
        if (null == ipField) {
//            字段在es中不存在，就不会返回，也不会报错，所有直接返回fieldName吧，避免多项目多环境字段不一致问题
//            return fieldName;

            throw IPTypeErrCodes.NOT_EXIST_FIELD.exception(fieldName);
        }

        // FIXED 2022-11-08 不能使用虚拟字段
        if (ipField instanceof VirtualField) {
            throw IPTypeErrCodes.SELECT_FIELD_NOT_VIRTUAL.exception(fieldName);
        }

        if (ipField.isMultiField()) {
            ipField = (IPField) ipField.getIpFieldContainer();
        }

        if (ipField.isNestedField()) {
            NestedType nestedType = (NestedType) ipField.getIpFieldContainer();
            buf.append(nestedType.getName()).append(".");
        }

        String columnName = ipField.getIpColumn().getName();
        buf.append(columnName);
        return buf.toString();
    }

    public static String getFullColumnName(IPType ipType, String fieldName) throws IPException {
        StringBuilder buf = new StringBuilder();
        IPField ipField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName));
        ipField = ipField.getIPMultiField(ipField);
        if (ipField.isMultiField()) {
            IPMultiField ipMultiField = (IPMultiField) ipField;
            String multiColumnName = ipMultiField.getMultiColumnName();
            IPField parentIPField = (IPField) ipField.getIpFieldContainer();
            String columnName = parentIPField.getIpColumn().getName();

            if (parentIPField.isNestedField()) {
                NestedType nestedType = (NestedType) parentIPField.getIpFieldContainer();
                buf.append(nestedType.getName()).append(".");
            }

            buf.append(columnName).append(".");
            buf.append(multiColumnName);
            return buf.toString();
        }

        if (ipField.isNestedField()) {
            NestedType nestedType = (NestedType) ipField.getIpFieldContainer();
            buf.append(nestedType.getName()).append(".");
        }
        String columnName = ipField.getIpColumn().getName();
        buf.append(columnName);
        return buf.toString();
    }

}
