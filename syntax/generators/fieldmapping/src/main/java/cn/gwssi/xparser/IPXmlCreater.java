package cn.gwssi.xparser;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.meta.*;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;
import cn.gwssi.xparser.util.XmlUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 15:23
 */
public class IPXmlCreater {

    private final static Logger logger = LoggerFactory.getLogger(IPXmlParser.class);

    private static IPXmlCreater _instance = null;

    public static IPXmlCreater getInstance() throws IPException {
        if (_instance == null) {
            synchronized(IPXmlCreater.class){
                if(_instance == null){
                    _instance = new IPXmlCreater();
                }
            }
        }

        return _instance;
    }

    public String createXml(IPType ipType) throws ParserConfigurationException
    {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.newDocument();

        StringBuilder buffer = new StringBuilder(1024);
        //生成xml文件头
        XmlUtil.prepareXmlHead(buffer, XmlUtil.CHARSETNAME);
        buffer.append("<type ");
        XmlUtil.addProperty(buffer, "name", ipType.getTypeName());
        XmlUtil.addProperty(buffer, "index", ipType.getIndexName());
        XmlUtil.addProperty(buffer, "number_of_shards", ipType.getNumberOfShards());
        XmlUtil.addProperty(buffer, "number_of_replicas", ipType.getNumberOfReplicas());
        buffer.append(">\n");

        XmlUtil.addElementPre(buffer, XmlUtil.COLUMNS);
        // 创建属性名、赋值
        Element root = document.createElement("type");
        root.setAttribute("name", ipType.getTypeName());
        root.setAttribute("number_of_shards", ipType.getNumberOfShards());
        root.setAttribute("number_of_replicas", ipType.getNumberOfReplicas());

        //添加column字段
        Map<String, IPColumn> columnMap = ipType.getColumnMap();
        Set<String> columnNameSet = columnMap.keySet();
        for(String clumnName : columnNameSet){
            IPColumn ipColumn = columnMap.get(clumnName);
            IPColumnType columnType = ipColumn.getColumnType();
            String type = (null == columnType) ? "" : ipColumn.getColumnType().getColumnValue();
            //添加column
            XmlUtil.addColumn(buffer, ipColumn.getDescription(), String.valueOf(ipColumn.isArray()), ipColumn.getName(), String.valueOf(ipColumn.isNotNull()), type);
        }
        XmlUtil.addElementAfter(buffer, XmlUtil.COLUMNS);

        //添加field字段
        Map<String, IPField> fieldMap = ipType.getIPFieldMap();
        Set<String> fieldNameSet = fieldMap.keySet();
        if(null != fieldNameSet && fieldNameSet.size() > 0){

            XmlUtil.addElementPre(buffer, XmlUtil.FIELDS );

            Map<String, String> fieldNameMap = new HashMap<>();
            for(String fieldName : fieldNameSet){

                IPField ipField = fieldMap.get(fieldName);
                String name1 = StringUtils.trimToEmpty(ipField.getName());

                if(name1.endsWith(IPDaoConstants.MULTI_FIELD_NAME_ENDWITH) || fieldNameMap.get(name1) != null){
                    continue;
                }
                fieldNameMap.put(name1, "1");

                XmlUtil.addField(buffer, ipField.getAlias(), ipField.getAnalyzer()== IPAnalyzerType.ANALYZER_TYPE_NONE?"null":ipField.getAnalyzer().getAnalyzer(), ipField.getIpColumn().getName(), ipField.getDescription(), name1);

                //添加multi_field字段
                Map<String, IPField> multiFieldMap = ipField.getIPFieldMap();
                Set<String> fieldKeySet = multiFieldMap.keySet();
                if(null != fieldKeySet && fieldKeySet.size() > 0){
                    buffer.append(">\n");
                    for(String fieldKey : fieldKeySet){

                        IPField multiField = multiFieldMap.get(fieldKey);
                        IPAnalyzerType analyzer = multiField.getAnalyzer();
                        XmlUtil.addMutilField(buffer, analyzer == null ? "null" : analyzer.getAnalyzer(), IPDaoConstants.MULTI_FIELD_COLUMNNAME, multiField.getDescription(), multiField.getName());
                    }
                    buffer.append("</").append(XmlUtil.FIELD).append(">\n");
                }else {
                    buffer.append("/>\n");
                }
            }
            XmlUtil.addElementAfter(buffer, XmlUtil.FIELDS );
        }

        //生成nested部分
        List<NestedType> nestedTypeList = ipType.getNestedType();
        if(nestedTypeList != null && nestedTypeList.size() > 0){
            for(NestedType nestedType : nestedTypeList){

                String description = nestedType.getDescription();
                String name1 = nestedType.getName();

                buffer.append("<");
                buffer.append(XmlUtil.NESTED);
                XmlUtil.addProperty(buffer, XmlUtil.DESCRIPTION, description);
                XmlUtil.addProperty(buffer, XmlUtil.NAME, name1);
                buffer.append(">\n");

                //生成columns
                Map<String, IPColumn> nestedColumns = nestedType.getColumnMap();
                Set<String> columnKeySet = nestedColumns.keySet();
                if(columnKeySet != null && columnKeySet.size() > 0){
                    XmlUtil.addElementPre(buffer, XmlUtil.COLUMNS);
                    for(String columnKey : columnKeySet){
                        IPColumn ipColumn = nestedColumns.get(columnKey);
                        IPColumnType columnType = ipColumn.getColumnType();
                        String type = (null == columnType) ? "" : ipColumn.getColumnType().getColumnValue();
                        XmlUtil.addColumn(buffer, ipColumn.getDescription(), String.valueOf(ipColumn.isArray()), ipColumn.getName(), String.valueOf(ipColumn.isNotNull()), type);
                    }
                    XmlUtil.addElementAfter(buffer, XmlUtil.COLUMNS);
                }

                //生成field
                Map<String, IPField> ipFieldMap = nestedType.getIPFieldMap();
                Set<String> ipFieldKeySet = ipFieldMap.keySet();
                if(ipFieldKeySet != null && ipFieldKeySet.size() > 0){

                    Map<String, String> nestedNameMap = new HashMap<>();
                    for(String ipFieldKey : ipFieldKeySet){
                        IPField ipField = ipFieldMap.get(ipFieldKey);
                        String name2 = ipField.getName();
                        if(name2.endsWith(IPDaoConstants.MULTI_FIELD_NAME_ENDWITH) || nestedNameMap.get(name2) != null){
                            continue;
                        }
                        nestedNameMap.put(name2, "1");

                        XmlUtil.addField(buffer, ipField.getAlias(), ipField.getAnalyzer().getAnalyzer(), ipField.getIpColumn().getName(), ipField.getDescription(), name2);
                        //添加multi_field字段
                        Map<String, IPField> multiFieldMap = ipField.getIPFieldMap();
                        Set<String> fieldKeySet = multiFieldMap.keySet();
                        if(null != fieldKeySet && fieldKeySet.size() > 0){
                            buffer.append(">\n");
                            for (String fieldKey : fieldKeySet){
                                IPField multiField = multiFieldMap.get(fieldKey);
                                IPAnalyzerType analyzer = multiField.getAnalyzer();
                                XmlUtil.addMutilField(buffer, analyzer == null ? "null" : analyzer.getAnalyzer(), IPDaoConstants.MULTI_FIELD_COLUMNNAME, multiField.getDescription(), multiField.getName());
                            }
                            XmlUtil.addElementAfter(buffer, XmlUtil.FIELD);
                        }else{
                            buffer.append("/>\n");
                        }
                    }
                }
                XmlUtil.addElementAfter(buffer, XmlUtil.NESTED);
            }
        }
        //生成虚拟字段规则
        Map<String, VirtualField> virtualFieldMap = ipType.getVirtualFieldMap();
        Set<String> virtualFieldKeySet = virtualFieldMap.keySet();
        if(virtualFieldKeySet != null && virtualFieldKeySet.size() > 0){
            XmlUtil.addElementPre(buffer, XmlUtil.VIRTUAL_FIELDS);
            Map<String, String> virNameMap = new HashMap<>();
            for (String virtualFieldKey : virtualFieldKeySet) {
                VirtualField virtualField = virtualFieldMap.get(virtualFieldKey);
                String virtualName = virtualField.getName();
                if (null == virNameMap.get(virtualName)) {
                    virNameMap.put(virtualName, "1");
                    List<IPField> realFields = virtualField.getRealFields();
                    StringBuffer buf = new StringBuffer();
                    if (realFields != null && realFields.size() > 0) {
                        int index = 0;
                        buf.append(realFields.get(index++).getName());
                        for (; index < realFields.size(); index++) {
                            buf.append(", ").append(realFields.get(index).getName());
                        }
                    }
                    XmlUtil.addVirtualFields(buffer, virtualField.getName(), virtualField.getAlias(), buf.toString());
                }
            }
            XmlUtil.addElementAfter(buffer, XmlUtil.VIRTUAL_FIELDS);
        }
        //添加日期型字段
        Map<String, DateField> dateFieldMap = ipType.getDateFieldMap();
        Set<String> dateFieldKeySet = dateFieldMap.keySet();
        if(dateFieldKeySet != null && dateFieldKeySet.size() > 0){
            XmlUtil.addElementPre(buffer, XmlUtil.DATE_FIELDS);
            for(String dateFieldKey : dateFieldKeySet){
                DateField dateField = dateFieldMap.get(dateFieldKey);

//                XmlUtil.addDateFields(buffer, dateField.getName(), dateField.getDFieldName(), dateField.getMFieldName(), dateField.getYFieldName(), dateField.getYMFieldName());
                XmlUtil.addDateFieldItSelf(buffer, dateField.getName(), dateField.getFormat());
            }
            XmlUtil.addElementAfter(buffer, XmlUtil.DATE_FIELDS);
        }
        XmlUtil.addElementAfter(buffer, "type");

        return buffer.toString();
    }
}
