package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 虚字段
 * 
 * <AUTHOR>
 *
 */
public class VirtualField  extends IPField{
	/**
	 * 实字段列表
	 */
	private List<IPField> realFields = new ArrayList<IPField>();


	public VirtualField()throws IPException{}
	/**
	 * 
	 * @param ipType
	 * @param name
	 * @throws IPException
	 */
	public VirtualField(IPType ipType, String name) throws IPException {
		super(ipType, name, null);
	}
	
	/**
	 * 
	 * @param ipType
	 * @param name
	 * @param alias
	 * @throws IPException
	 */
	public VirtualField(IPType ipType, String name, String alias)  throws IPException {
		super(ipType, name, alias);
	}

	public List<IPField> getRealFields() {
		return realFields;
	}
	public IPField getRealFields(String name) {
		for(IPField realField : realFields) {
			if(StringUtils.equals(name, realField.getName())) {
				return realField;
			}
		}
		return null;
	}
	public void addRealFields(IPField field) {
		this.realFields.add(field);
	}
}
