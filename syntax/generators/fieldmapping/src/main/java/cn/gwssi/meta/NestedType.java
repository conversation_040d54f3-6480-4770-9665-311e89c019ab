package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.common.util.StringUtil;

/**
 * 嵌套对象描述
 * 
 * <AUTHOR>
 *
 */
public class NestedType extends IPFieldContainer{
	/**
	 * 嵌套对象名称
	 */
	private String name = null;
	
	/**
	 * 嵌套对象描述
	 */
	private String description = null;

	public NestedType()throws IPException{}
	
	/**
	 * 
	 * @param ipType
	 * @param name
	 * @throws IPException
	 */
	public NestedType(IPType ipType, String name) throws IPException {
		super(ipType);
		if(StringUtil.isNullOrEmpty(name)) {
			throw IPDaoErrCodes.NESTED_NAME_ERROR.exception(this.getConfigFileName());
		}
		this.name = name;
		this.setContainerType(IPFieldContainerType.NESTED_TYPE_CONTAINER);
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}
}
