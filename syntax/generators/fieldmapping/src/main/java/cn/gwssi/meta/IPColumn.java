package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.syntax.meta.IPColumnType;
import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPColumn extends IPObject {
	/**
	 * IPSearch中字段名称
	 * 
	 */
	private String name = null;
	
	/**
	 * 字段类型
	 */
	private IPColumnType columnType = IPColumnType.COLUMN_TYPE_INTEGER;
	
	/**
	 * 是否数组
	 */
	private boolean isArray = false;
	
	/**
	 * 值是否允许为空
	 * true 不允许为空; false 允许为空
	 */
	private boolean notNull = false;
	
	/**
	 * 字段描述
	 */
	private String description = null;

	public IPColumn()throws IPException{}
	
	/**
	 * @param ipType
	 * @param name
	 */
	public IPColumn(IPType ipType, String name) throws IPException {
		super(ipType);
		if(StringUtils.isEmpty(name)) {
			throw IPDaoErrCodes.FIELD_NAME_ERROR.exception(this.getConfigFileName());
		}
		this.name = name;
	}
	
	public IPColumn(IPType ipType, String name, boolean isArray, boolean notNull, String type, String description) throws IPException {
		super(ipType);
		
		this.name = name;
		this.isArray = isArray;
		this.notNull = notNull;
		this.columnType = IPColumnType.getColumnType(type);
		this.description = description;
	}

	public String getName() {
		return name;
	}
	
	public IPColumnType getColumnType() {
		return columnType;
	}

	public void setColumnType(IPColumnType columnType) {
		this.columnType = columnType;
	}

	public boolean isArray() {
		return isArray;
	}

	public void setArray(boolean isArray) {
		this.isArray = isArray;
	}

	public boolean isNotNull() {
		return notNull;
	}

	public void setNotNull(boolean notNull) {
		this.notNull = notNull;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
}
