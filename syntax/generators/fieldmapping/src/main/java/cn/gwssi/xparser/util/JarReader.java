package cn.gwssi.xparser.util;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 10:33
 */
public class JarReader {

    /**
     * 从jar包内提取某个目录下的所有文件的路径
     * @param dirPath 目录
     * @param fileSuffix 文件后缀
     * @return list
     * @throws IOException IPException
     */
    public static List<String> extractFilePathByDirPath(String dirPath, String fileSuffix) throws IOException {
        List<String> pathList = new ArrayList<>();
        String dir = dirPath.substring(1);

        // 读取jar
        String jarPath = JarReader.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        JarFile jarFile = new JarFile(jarPath);
        Enumeration<JarEntry> entries = jarFile.entries();

        while (entries.hasMoreElements()) {
            JarEntry jarEntry = entries.nextElement();
            String innerPath = jarEntry.getName();

            // 只处理本目录，只处理文件
            if (!innerPath.startsWith(dir) || jarEntry.isDirectory() || !innerPath.endsWith(fileSuffix)) {
                continue;
            }

            String innerDirPath = "";
            if (innerPath.contains(File.separator)) {
                innerDirPath = innerPath.substring(0, innerPath.lastIndexOf(File.separator));
            }

            // 只处理本目录
            if (innerDirPath.equals(dir)) {
                pathList.add(File.separator + innerPath);
            }
        }

        return pathList;
    }
}
