package cn.gwssi.xparser;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.StringUtil;
import cn.gwssi.meta.IPType;
import cn.gwssi.xparser.util.JarReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 17:38
 */
public class IPConfigReader {

    protected final static Logger logger = LoggerFactory.getLogger(IPConfigReader.class);

    private final String XML_FILE_SUFFIX = ".xml";

    /**
     * 初始化ipType
     *
     * @param filePath 目录或文件绝对路径
     * @param isEnv 是否外部目录，true 为 jar 包外目录，false 为 resources 下目录
     * @throws IOException IOException
     * @throws IPException IPException
     */
    public IPConfigReader(String filePath, boolean isEnv) throws IOException, IPException {
        if (isEnv) {
            this.readConfigFromEnv(filePath);
        } else {
            this.readConfigFromResource(filePath);
        }
    }

    /**
     * 初始化ipType
     *
     * @param indexName 索引名称
     * @param typeName  类型名称
     * @param confXml   配置文件
     * @throws Exception Exception
     */
    public IPConfigReader(String indexName, String typeName, String confXml) throws Exception {
        this.ipTypeFromHit(indexName, typeName, confXml);
    }

    /**
     * 读取es中xml
     *
     * @param indexName 索引名称
     * @param typeName  类型名称
     * @param confXml   配置文件
     * @throws Exception exception
     */
    private void ipTypeFromHit(String indexName, String typeName, String confXml) throws Exception {
        String[] typeNames = typeName.split(",");
        if (typeNames.length == 1) {//一个索引多个type的情况先跳过
            IPType ipType = IPXmlParser.getInstance().parseXml(confXml);
            ipType.setIndexName(indexName);
            ipType.setTypeName(typeName);
            IPDaoFactory.getInstance().putIPType(ipType);
        }
    }

    /**
     * 读取 resources 下目录
     *
     * @param path 目录
     * @throws IPException IPException
     * @throws IOException IOException
     */
    private void readConfigFromResource(String path) throws IOException, IPException {
        ClassLoader loader = this.getClass().getClassLoader();
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(loader);
        Resource[] resources = resolver.getResources("classpath*:/" + path + "/*.xml");
        if (resources.length <= 0) {
            logger.error("解析xml文件时错误，" + path + " 目录不存在");
            return;
        }

        for (Resource resource : resources) {
            // jar 包内的文件不能用 resource.getFile() 获取，只能取 inputStream
            InputStream stream = resource.getInputStream();
            IPType ipType = IPXmlParser.getInstance().parseXml(stream);
            IPDaoFactory.getInstance().putIPType(ipType);
        }
    }

    /**
     * 读取本地目录或文件
     *
     * @param path 目录或文件路径
     * @throws IPException IPException
     * @throws IOException IOException
     */
    private void readConfigFromEnv(String path) throws IPException, IOException {
        String resource = IPConfigReader.class.getResource("\\" + path).getPath();

        // 读取jar包内资源
        if (resource.contains(".jar!" + File.separator)) {
            if (resource.endsWith(XML_FILE_SUFFIX)) {
                readConfigFromJarFile(path);
            } else {
                readConfigFromJarDir(path);
            }
        } else { // 直接读取
            File file = new File(resource);

            if (file.isDirectory()) {
                readConfigFromDir(file);
            } else if (resource.endsWith(XML_FILE_SUFFIX)) {
                readConfigFromFile(file);
            }
        }
    }

    /**
     * 读取jar包内，目录下所有资源文件
     *
     * @param dirPath 目录
     * @throws IOException IOException
     * @throws IPException IPException
     */
    private void readConfigFromJarDir(String dirPath) throws IOException, IPException {
        List<String> fileList = JarReader.extractFilePathByDirPath(dirPath, XML_FILE_SUFFIX);
        if (null != fileList && fileList.size() > 0) {

            for (String filePath : fileList) {
                readConfigFromJarFile(filePath);
            }
        }
    }

    /**
     * 读取jar包内资源文件
     *
     * @param filePath 文件路径
     * @throws IPException IPException
     */
    private void readConfigFromJarFile(String filePath) throws IPException {
        InputStream inputStream = IPConfigReader.class.getResourceAsStream(filePath);
        IPType ipType = IPXmlParser.getInstance().parseXml(inputStream);
        IPDaoFactory.getInstance().putIPType(ipType);
    }

    /**
     * 读取资源目录下文件
     *
     * @param dir 目录
     * @throws IPException IPException
     * @throws IOException IOException
     */
    private void readConfigFromDir(File dir) throws IPException, IOException {
        // 过滤文件
        FilenameFilter filter = (dir1, name) -> {
            if (StringUtil.isNullOrEmpty(name)) {
                return false;
            }

            return name.endsWith(XML_FILE_SUFFIX);
        };

        File[] fileList = dir.listFiles(filter);
        if (null != fileList && fileList.length > 0) {

            for (File file : fileList) {
                readConfigFromFile(file);
            }
        }
    }

    /**
     * 读取资源文件
     *
     * @param file 文件
     * @throws IPException IPException
     * @throws IOException IOException
     */
    private void readConfigFromFile(File file) throws IPException, IOException {
        IPType ipType = IPXmlParser.getInstance().parseXml(file);
        IPDaoFactory.getInstance().putIPType(ipType);
    }
}
