package cn.gwssi.meta;


import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.common.util.StringUtil;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.meta.IPAnalyzerType;

/**
 * 字段描述对象
 * 
 * <AUTHOR>
 *
 */
public class IPField extends IPFieldContainer{
	/**
	 * 检索表达式中用
	 * 
	 * 名称，可以为空，为空时表示该字段不能出现在检索表达式
	 */
	private String name = null;
	
	/**
	 * 检索表达式式中用
	 * 
	 * 英文名称，可以为空，为空时表示该字段不能通过别名进行检索
	 * 
	 */
	private String alias = null;
	
	/**
	 *该字段 包含的description 
	 */
	
	private String description = null;
	
	/**
	 * 分析器，默认为空，表示不分词
	 */
	private IPAnalyzerType analyzer = null;
	
	/**
	 * 对应IPSearch中的字段
	 */
	private IPColumn ipColumn = null;

	/**
	 * IPField容器
	 */
	private IPFieldContainer ipFieldContainer = null;

	public IPField()throws IPException {}

	/**
	 * 
	 * @param name
	 * @param alias
	 * @throws IPException
	 */
	public IPField(IPType ipType, String name, String alias) throws IPException {
		super(ipType);
		if(StringUtil.isNullOrEmpty(name)) {
			throw IPDaoErrCodes.FIELD_NAME_ERROR.exception(this.getConfigFileName());
		}		
		this.name = name;
		this.alias = alias;
		this.setContainerType(IPFieldContainerType.IP_FIELD_CONTAINER);
	}
	
	/**
	 * 
	 * @param name
	 * @throws IPException
	 */
	public IPField(IPType ipType, String name, String alia, String analyzer, IPColumn ipColumn) throws IPException {
		super(ipType);
		if(StringUtil.isNullOrEmpty(name)) {
			throw IPDaoErrCodes.FIELD_NAME_ERROR.exception(this.getConfigFileName());
		}		
		this.name = name;
		this.alias = alia;
		this.setContainerType(IPFieldContainerType.IP_FIELD_CONTAINER);
		this.analyzer = IPAnalyzerType.getAnalyzerType(analyzer);
		this.description = alia;
		this.ipColumn = ipColumn;
	}

	public IPField(IPType ipType, String name, String alias, String analyzer, String description, IPColumn ipColumn ) throws IPException {
		super(ipType);
		this.name = name;
		this.alias = alias;
		this.setContainerType(IPFieldContainerType.IP_FIELD_CONTAINER);
		this.analyzer = IPAnalyzerType.getAnalyzerType(analyzer);
		this.description = description;
		this.ipColumn = ipColumn;

	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @return the alias
	 */
	public String getAlias() {
		return alias;
	}
	
	public IPColumn getIpColumn() {
		return ipColumn;
	}

	public void setIpColumn(IPColumn ipColumn) {
		this.ipColumn = ipColumn;
	}

	public IPAnalyzerType getAnalyzer() {
		return analyzer;
	}

	public void setAnalyzer(IPAnalyzerType analyzer) {
		this.analyzer = analyzer;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}	
	
	public IPFieldContainer getIpFieldContainer() {
		return ipFieldContainer;
	}

	public void setIpFieldContainer(IPFieldContainer ipFieldContainer) {
		this.ipFieldContainer = ipFieldContainer;
	}
	
	/**
	 * 是否嵌套字段
	 * 
	 * @return
	 */
	public boolean isNestedField() {
		if(ipFieldContainer == null) {
			return false;
		}
		
		return ipFieldContainer.getContainerType().equals(IPFieldContainerType.NESTED_TYPE_CONTAINER);
	}

	/**
	 * 是否多值字段中的字段
	 * @return
	 */
	public boolean isMultiField() {
		if(ipFieldContainer == null) {
			return false;
		}
		
		return ipFieldContainer.getContainerType().equals(IPFieldContainerType.IP_FIELD_CONTAINER);
	}


	private boolean hasMultField(IPField ipField){
		return ipField.getIPFieldMap() != null && ipField.getIPFieldMap().size() > 0;
	}


	public IPField getIPMultiField(IPField ipField){
		if (hasMultField(ipField)){
			return ipField.getIPFieldMap().get(ipField.getIpColumn().getName() + IPConditionConstants.MULTI_FIELD_NAME_ENDWITH);//固定的raw不可变
		}else {
			return  ipField;
		}
	}



}
