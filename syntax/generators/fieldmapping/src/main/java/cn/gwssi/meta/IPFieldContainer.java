package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.StringUtil;
import org.apache.commons.collections4.map.CaseInsensitiveMap;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPFieldContainer extends IPObject{
	/**
	 * 在Type定义的字段
	 */
	public static final String IP_TYPE_CONTAINER = "ip_type";
	
	/**
	 * 在嵌入对象中定义的字段
	 */
	public static final String NESTED_TYPE_CONTAINER = "nested_type";
	
	/**
	 * multi_field 中的字段
	 */
	public static final String IP_FIELD_CONTAINER = "ip_field";
	
	/**
	 * 容器类型
	 */
	private IPFieldContainerType containerType = null;
	
	/**
	 * key name
	 */
	protected Map<String, IPColumn> columnMap = new CaseInsensitiveMap<>();

	/**
	 * key name
	 */
	protected Map<String, IPField> fieldMap = new CaseInsensitiveMap<String, IPField>();

	protected IPFieldContainer() throws IPException {
	}
	
	public IPFieldContainer(IPType ipType) throws IPException {
		super(ipType);
	}

	public Map<String, IPColumn> getColumnMap() {
		return columnMap;
	}
	public IPColumn getIPColumn(String key) {
		return columnMap.get(key);
	}
	public void putIPColumn(String key, IPColumn ipColumn) {
		columnMap.put(key, ipColumn);
	}
	
	/**
	 * 
	 * @return
	 */
	public Map<String, IPField> getIPFieldMap() {
		return fieldMap;
	}
	
	/**
	 * 
	 * @param key
	 * @return
	 */	
	public IPField getIPField(String key) {
		// FIXED 20190701 去掉 toLowerCase
		return fieldMap.get(key);
	}
	
	public void putIPField(String key, IPField ipField) throws IPException {
		// FIXED 20190701 去掉 toLowerCase
		fieldMap.put(key, ipField);
	}
	
	/** add ipField to the fieldList
	 * @param ipField
	 */
	public void addIPField(IPField ipField) throws IPException {
		String name = ipField.getName();
		// FIXED 20190701 去掉 toLowerCase
		fieldMap.put(name, ipField);
		
		String alias = ipField.getAlias();
		if(!StringUtil.isNullOrEmpty(alias)) {
			// FIXED 20190701 去掉 toLowerCase
			fieldMap.put(alias, ipField);
		}
		
		ipField.setIpFieldContainer(this);
		IPType ipType = this.getIpType();
		if(ipType != null) {
			ipField.setIpType(ipType);
		}
		else if (this instanceof IPType){
			ipField.setIpType((IPType)this);
		}
	}
	
	public IPFieldContainerType getContainerType() {
		return containerType;
	}
	public void setContainerType(IPFieldContainerType containerType) {
		this.containerType = containerType;
	}

	public enum IPFieldContainerType {
		IP_TYPE_CONTAINER(IPFieldContainer.IP_TYPE_CONTAINER),
		NESTED_TYPE_CONTAINER(IPFieldContainer.NESTED_TYPE_CONTAINER),
		IP_FIELD_CONTAINER(IPFieldContainer.IP_FIELD_CONTAINER);
		
		String containerType = null;
		IPFieldContainerType(String containerType){
			this.containerType = containerType;
		}
		
		public boolean equals(IPFieldContainerType _containerType) {
			return StringUtil.equals(containerType, _containerType.containerType);
		}
	}
}
