package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.common.util.StringUtil;
import org.apache.commons.collections4.map.CaseInsensitiveMap;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPType extends IPFieldContainer implements Serializable{
	
	/**
	 * 分片数量
	 */
	private String numberOfShards;
	
	/**
	 * 副本数量
	 */
	private String numberOfReplicas;

	/**
	 * 配置文件名
	 */
	private String configFileName = null;
	
	/**
	 * 是否是默认配置
	 * 
	 */
	private String isDefault = null;
	
	/**
	 * 索引名称
	 */
	private String indexName = null;

	/**
	 * 类型名称
	 */
	private String typeName = null;

	/**
	 * 主键字段（_ID字段）
	 */
	private String primaryKey = null;

	/**
	 * 默认检索字段
	 */
	private String defaultField = null;

	/**
	 * 嵌套类型
	 */
	private List<NestedType> nestedTypeList = null;

	/**
	 * 虚字段列表
	 */
	private Map<String, VirtualField> virtualFieldMap = null;

	/**
	 * 日期字段列表
	 */
	private Map<String, DateField> dateFieldMap = null;

	public IPType()throws IPException{
		super();
	}

	/**
	 * 构造函数
	 */
	public IPType(String configFileName) throws IPException {
		this.configFileName = configFileName;
		setIpType(this);
		this.setContainerType(IPFieldContainerType.IP_TYPE_CONTAINER);

		virtualFieldMap = new CaseInsensitiveMap<>();
		dateFieldMap = new CaseInsensitiveMap<>();
	}

	public String getIsDefault() {
		return isDefault;
	}

	public boolean isDefault() {

        return isDefault.equals("true");

    }

	public void setIsDefault(String isDefault) {
		this.isDefault = isDefault;
	}

	/**
	 * @return the configFileName
	 */
	public String getConfigFileName() {
		return configFileName;
	}

	/**
	 * @return the name
	 */
	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) throws IPException {
		if (StringUtil.isNullOrEmpty(typeName)) {
			throw IPDaoErrCodes.IPTYPE_NAME_ERROR.exception(configFileName);
		}
		this.typeName = typeName;
	}
	
	/**
	 * 索引名称
	 * @return
	 */
	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	/**
	 * @return the primaryKey
	 */
	public String getPrimaryKey() {
		return primaryKey;
	}

	public void setPrimaryKey(String primaryKey) {
		this.primaryKey = primaryKey;
	}

	@Override
	public IPField getIPField(String key) {
		// FIXED 20190701 去掉 toLowerCase
		String key1 = key;
		IPField ipField = super.getIPField(key1);
		if (ipField != null) {
			return ipField;
		}

		if (nestedTypeList != null) {
			for (NestedType nestedType : nestedTypeList) {
				ipField = nestedType.getIPField(key1);
				if (ipField != null) {
					return ipField;
				}
			}
		}

		if (virtualFieldMap != null) {
			VirtualField virtualField = virtualFieldMap.get(key1);
			if (virtualField != null) {
				return virtualField;
			}
		}
		return null;
	}

	public IPField getIPFieldAndThrow(String key, IPException exception) throws IPException {
	    IPField ipField = this.getIPField(key);
	    if (null == ipField) {
	        throw exception;
        }

	    return ipField;
    }

	/**
	 * nested字段列表
	 * 
	 * @param nestedType
	 */
	public void addNestedType(NestedType nestedType) {
		if (nestedTypeList == null) {
			nestedTypeList = new ArrayList<NestedType>();
		}
		this.nestedTypeList.add(nestedType);
	}
	public List<NestedType> getNestedType(){
		return this.nestedTypeList;
	}
	public NestedType getNestedType(String name){
		if(nestedTypeList != null){
			
			for(NestedType type : this.nestedTypeList){
				if(type.getName().equals(name)){
					return type;
				}
			}
		}
		
		return null;
	}

	/**
	 * 虚字段列表
	 * 
	 * @return
	 */
	public Map<String, VirtualField> getVirtualFieldMap() {
		return virtualFieldMap;
	}

	public VirtualField getVirtualField(String key) {
		// FIXED 20190701 去掉 toLowerCase
		return virtualFieldMap.get(key);
	}

	public void addVirtualField(VirtualField virtualField) {
		String name = virtualField.getName();
		// FIXED 20190701 去掉 toLowerCase
		virtualFieldMap.put(name, virtualField);

		String alias = virtualField.getAlias();
		if (!StringUtil.isNullOrEmpty(alias)) {
			// FIXED 20190701 去掉 toLowerCase
			virtualFieldMap.put(alias, virtualField);
		}

		virtualField.setIpType(this);
		virtualField.setIpFieldContainer(this);
	}

	/**
	 * 日期字段列表
	 * 
	 * @return
	 */
	public Map<String, DateField> getDateFieldMap() {
		return dateFieldMap;
	}

	public DateField getDateField(String key) {
		// FIXED 20190701 去掉 toLowerCase
		DateField dateField = dateFieldMap.get(key);
		if (dateField == null) {
			IPField ipField = this.getIPField(key);
			if (ipField != null) {
				dateField = dateFieldMap.get(ipField.getAlias());
				if (dateField == null) {
					dateField = dateFieldMap.get(ipField.getName());
				}
			}
		}

		return dateField;
	}

	public void addDateField(DateField dateField) {
		String name = dateField.getName();
		// FIXED 20190701 去掉 toLowerCase
		dateFieldMap.put(name, dateField);
		dateField.setIpType(this);
	}

	public String getDefaultField() {
		return defaultField;
	}

	public void setDefaultField(String defaultField) {
		this.defaultField = defaultField;
	}

	public String getNumberOfShards() {
		return numberOfShards;
	}

	public void setNumberOfShards(String numberOfShards) {
		this.numberOfShards = numberOfShards;
	}

	public String getNumberOfReplicas() {
		return numberOfReplicas;
	}

	public void setNumberOfReplicas(String numberOfReplicas) {
		this.numberOfReplicas = numberOfReplicas;
	}

	public void clearFields()
	{
		this.fieldMap=new HashMap<>();
		this.columnMap=new HashMap<>();

		this.nestedTypeList=new ArrayList<>();
		this.virtualFieldMap=new HashMap<>();
		this.dateFieldMap=new HashMap<>();
	}
}
