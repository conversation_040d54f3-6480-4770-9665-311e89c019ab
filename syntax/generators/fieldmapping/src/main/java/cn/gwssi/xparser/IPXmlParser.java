package cn.gwssi.xparser;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.common.util.StringUtil;
import cn.gwssi.meta.*;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class IPXmlParser {

    private final static Logger logger = LoggerFactory.getLogger(IPXmlParser.class);

    private static IPXmlParser _instance = null;

    public static IPXmlParser getInstance() throws IPException {
        if (_instance == null) {
            synchronized (IPXmlParser.class) {
                if (_instance == null) {
                    _instance = new IPXmlParser();
                }
            }
        }

        return _instance;
    }

    public IPType parseXml(File file) throws IOException, IPException {
        return parseXml(new FileInputStream(file), file.getName());
    }

    public IPType parseXml(InputStream inputStream) throws IPException {
        return parseXml(inputStream, "");
    }

    public IPType parseXml(String content) throws IPException {
        return parseXml(new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8)), "");
    }

    private IPType parseXml(InputStream inputStream, String daoFileName) throws IPException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = null;
        Document document = null;

        try {
            db = factory.newDocumentBuilder();
            document = db.parse(inputStream);
        } catch (ParserConfigurationException | SAXException | IOException e) {
            throw IPDaoErrCodes.IPTYPE_CONFIG_ERROR.exception(daoFileName, e);
        }

        return parseXml(document, daoFileName);
    }

    private IPType parseXml(Document document, String daoFileName) throws IPException {
        IPType ipType = new IPType(daoFileName);
        Element element = document.getDocumentElement();

        // XML文件名称
        String typeName = element.getAttribute("name");

        // 主键字段
        String primaryKey = element.getAttribute("primary_key");

        String indexName = element.getAttribute("index");

        // 是否是默认配置
        String isDefault = StringUtils.trimToEmpty(element.getAttribute("is_default"));

        // 默认检索字段
        String defaultField = element.getAttribute("defaultField");

        //分片数
        String numberOfShards = "";
        if (element.hasAttribute("number_of_shards")) {
            numberOfShards = element.getAttribute("number_of_shards");
        }

        //副本数
        String numberOfReplicas = "";
        if (element.hasAttribute("number_of_replicas")) {
            numberOfReplicas = element.getAttribute("number_of_replicas");
        }

        ipType.setTypeName(typeName);
        ipType.setIndexName(indexName);
        ipType.setPrimaryKey(primaryKey);
        ipType.setDefaultField(defaultField);
        ipType.setIsDefault(isDefault);
        ipType.setNumberOfReplicas(numberOfReplicas);
        ipType.setNumberOfShards(numberOfShards);

        // 加载column里面的内容
        document.normalize();
        NodeList columns = document.getElementsByTagName("columns");
        if (null != columns && columns.getLength() > 0) {
            NodeList columnList = columns.item(0).getChildNodes();
            for (int i = 0; i < columnList.getLength(); i++) {
                Node columnNode = columnList.item(i);
                if (StringUtil.equals("column", columnNode.getNodeName())) {
                    IPColumn column = newIPColumn(ipType, (Element) columnNode);
                    ipType.putIPColumn(column.getName(), column);
                }
            }
        }

        // 加载fields里面的内容
        NodeList fields = document.getElementsByTagName("fields");
        if (null != fields && fields.getLength() > 0) {
            NodeList fieldList = fields.item(0).getChildNodes();
            for (int i = 0; i < fieldList.getLength(); i++) {
                Node fieldNode = fieldList.item(i);
                if (StringUtil.equals("field", fieldNode.getNodeName())) {
                    Element fieldElement = (Element) fieldList.item(i);
                    IPField field = newIPField(ipType, ipType.getColumnMap(), fieldElement);
                    ipType.addIPField(field);

                    Map<String, IPField> fieldMap = field.getIPFieldMap();
                    if (!fieldMap.isEmpty()) {
                        Set<String> keySet = fieldMap.keySet();
                        for (String key : keySet) {
                            IPMultiField multiField = (IPMultiField) fieldMap.get(key);
                            ipType.putIPField(key, multiField);
                        }
                    }
                }
            }
        }

        // 加载nested里面的内容
        NodeList nestedList = document.getElementsByTagName("nested");
        if (null != nestedList && nestedList.getLength() > 0) {
            for (int i = 0; i < nestedList.getLength(); i++) {
                Node nestedNode = nestedList.item(i);
                if (StringUtil.equals("nested", nestedNode.getNodeName())) {
                    Element nestedElement = (Element) nestedNode;
                    String nestedName = nestedElement.getAttribute("name");
                    String nestedDescription = nestedElement.getAttribute("description");
                    NestedType nestedType = new NestedType(ipType, nestedName);
                    nestedType.setDescription(nestedDescription);
                    ipType.addNestedType(nestedType);

                    // 加载nested里面的columns的值
                    NodeList nestedChildren = nestedElement.getChildNodes();
                    for (int j = 0; j < nestedChildren.getLength(); j++) {
                        Node node = nestedChildren.item(j);
                        if (StringUtil.equals("columns", node.getNodeName())) {
                            Element _element = (Element) node;
                            NodeList columnList = _element.getChildNodes();
                            for (int k = 1; k < columnList.getLength(); k++) {
                                Node _columnNode = columnList.item(k);
                                if (StringUtil.equals("column", _columnNode.getNodeName())) {
                                    Element _columnElement = (Element) _columnNode;
                                    IPColumn ipColumn = newIPColumn(ipType, _columnElement);
                                    nestedType.putIPColumn(ipColumn.getName(), ipColumn);
                                }
                            }
                        }
                    }
                    for (int j = 0; j < nestedChildren.getLength(); j++) {
                        Node _fieldNode = nestedChildren.item(j);
                        if (StringUtil.equals("field", _fieldNode.getNodeName())) {
                            Element _fieldElement = (Element) _fieldNode;
                            IPField ipField = newIPField(ipType, nestedType.getColumnMap(), _fieldElement);
                            nestedType.addIPField(ipField);

                            Map<String, IPField> fieldMap = ipField.getIPFieldMap();
                            if (!fieldMap.isEmpty()) {
                                Set<String> keySet = fieldMap.keySet();
                                for (String key : keySet) {
                                    IPMultiField multiField = (IPMultiField) fieldMap.get(key);
                                    nestedType.putIPField(key, multiField);
                                }
                            }
                        }
                    }
                }
            }
        }

        // 加载virtual_fields里面的内容
        NodeList virtualList = document.getElementsByTagName("virtual_fields");
        if (null != virtualList && virtualList.getLength() > 0) {
            Node virtualPatentNode = virtualList.item(0);
            if (null != virtualPatentNode) {
                NodeList virtualFields = virtualPatentNode.getChildNodes();
                for (int i = 0; i < virtualFields.getLength(); i++) {
                    Node virtualFieldNode = virtualFields.item(i);
                    if (StringUtil.equals("virtual_field", virtualFieldNode.getNodeName())) {
                        Element virtualFieldElement = (Element) virtualFieldNode;
                        VirtualField virtualField = newVirtualField(ipType, virtualFieldElement);
                        ipType.addVirtualField(virtualField);
                    }

                }
            }
        }

        // 加载date_fields里面的内容
        NodeList dateList = element.getElementsByTagName("date_fields");
        if (null != dateList && dateList.getLength() > 0) {
            Node dateNode = dateList.item(0);
            if (null != dateNode) {
                NodeList dateFields = dateNode.getChildNodes();
                for (int i = 0; i < dateFields.getLength(); i++) {
                    Node dateFieldNode = dateFields.item(i);
                    if (StringUtil.equals("date_field", dateFieldNode.getNodeName())) {
                        Element dateFieldElement = (Element) dateFieldNode;
//						DateField dateField = extendDateFiled(ipType, dateFieldElement);//不扩展其它日期字段
                        DateField dateField = itselfDateFiled(ipType, dateFieldElement);
                        ipType.addDateField(dateField);
                    }
                }
            }
        }

        return ipType;
    }

//	public void addDateField(IPType ipType, String dateFieldName) throws IPException {
//
//		if(!dateFieldName.endsWith(IPDaoConstants.DATEFIELD_ENDWITH)){
//			throw IPDaoErrCodes.IP_DATE_FIELD_END_RRROR.exception(dateFieldName);
//		}
//
//		String subName = dateFieldName.substring(0, dateFieldName.length() - 3);
//
//		String y_field = subName + "y";
//		String m_field = subName + "m";
//		String d_field = subName + "d";
//		String ym_field = subName  + "ym";
//
//		DateField dateField = new DateField(ipType, dateFieldName, y_field, m_field, d_field, ym_field);
//
//		ipType.addDateField(dateField);
//	}

    /**
     * @param ipType
     * @param columnNode
     * @return
     * @throws IPException
     */
    private IPColumn newIPColumn(IPType ipType, Element columnNode) throws IPException {
        String columnName = columnNode.getAttribute("name");
        if (StringUtil.isNullOrEmpty(columnName)) {
            throw IPDaoErrCodes.COLUMN_NAME_ERROR.exception(ipType.getConfigFileName());
        }
        IPColumn ipColumn = new IPColumn(ipType, columnName);

        String type = columnNode.getAttribute("type");
        if (!StringUtil.isNullOrEmpty(type)) {
            IPColumnType columnType = IPColumnType.getColumnType(type);
            ipColumn.setColumnType(columnType);
        }

        String is_array = columnNode.getAttribute("is_array");
        if (!StringUtil.isNullOrEmpty(is_array)) {
            ipColumn.setArray(Boolean.parseBoolean(is_array));

        }

        String not_null = columnNode.getAttribute("not_null");
        if (!StringUtil.isNullOrEmpty(not_null)) {
            ipColumn.setNotNull(Boolean.getBoolean(not_null));
        }

        String columnDescription = columnNode.getAttribute("description");
        ipColumn.setDescription(columnDescription);
        return ipColumn;
    }

    /**
     * IPField
     *
     * @param ipType
     * @param columnMap
     * @param fieldElement
     * @return
     * @throws IPException
     */
    private IPField newIPField(IPType ipType, Map<String, IPColumn> columnMap, Element fieldElement)
            throws IPException {
        String ipFieldName = fieldElement.getAttribute("name");
        String ipFieldAlias = fieldElement.getAttribute("alias");
        IPField field = new IPField(ipType, ipFieldName, ipFieldAlias);
        String ipFieldColumnName = fieldElement.getAttribute("column_name");
        if (StringUtil.isNullOrEmpty(ipFieldColumnName)) {
            ipFieldColumnName = ipFieldName;
        }
        IPColumn ipColumn = columnMap.get(ipFieldColumnName);
        if (ipColumn == null) {
            throw IPDaoErrCodes.COLUMN_NOT_EXIST_ERROR.exception(ipType.getConfigFileName(), ipFieldName,
                    ipFieldColumnName);
        }
        field.setIpColumn(ipColumn);
        String ipFieldAnalyzer = fieldElement.getAttribute("analyzer");
        field.setAnalyzer(IPAnalyzerType.getAnalyzerType(ipFieldAnalyzer));

        String ipFieldDescription = fieldElement.getAttribute("description");
        field.setDescription(ipFieldDescription);

        IPMultiField ipMultField = null;
        NodeList multi_field = fieldElement.getChildNodes();
        if (null != multi_field && multi_field.getLength() > 1) {
            for (int i = 0; i < multi_field.getLength(); i++) {
                Node multiFieldNode = multi_field.item(i);
                if (StringUtil.equals("multi_field", multiFieldNode.getNodeName())) {
                    Element multiFieldElement = (Element) multiFieldNode;
                    String name = multiFieldElement.getAttribute("name");
                    ipMultField = new IPMultiField(ipType, name);
                    ipMultField.setIpColumn(ipColumn);
                    String analyzer = multiFieldElement.getAttribute("analyzer");
                    ipMultField.setAnalyzer(IPAnalyzerType.getAnalyzerType(analyzer));
                    String columnName = multiFieldElement.getAttribute("column_name");
                    ipMultField.setMultiColumnName(columnName);
                    String description = multiFieldElement.getAttribute("description");
                    field.addIPField(ipMultField);
                    ipMultField.setDescription(description);
                }
            }
        }
        return field;
    }

    /**
     * 虚字段
     *
     * @param ipType
     * @param fieldNode
     * @return
     * @throws IPException
     */
    private VirtualField newVirtualField(IPType ipType, Element fieldNode) throws IPException {

        String name = fieldNode.getAttribute("name");
        String alias = fieldNode.getAttribute("alias");
        String fields = fieldNode.getAttribute("fields");

        VirtualField virtualField = new VirtualField(ipType, name, alias);
        if (!StringUtil.isNullOrEmpty(fields)) {
            String[] fieldList = fields.split(",");
            for (String field : fieldList) {
                IPField ipField = ipType.getIPField(field.trim());
                if (ipField == null) {
                    throw IPDaoErrCodes.VIRTUAL_FIELD_ERROR3.exception(ipType.getConfigFileName(), name, field);
                }
                virtualField.addRealFields(ipField);
            }
        }

        return virtualField;
    }

    /**
     * 日期字段
     * 把原有的日期字段扩展为年、月、日、
     *
     * @param ipType
     * @param fieldNode
     * @return
     * @throws IPException
     */
    private DateField extendDateFiled(IPType ipType, Element fieldNode) throws IPException {
        String name = fieldNode.getAttribute("name");
        String y_field = fieldNode.getAttribute("y_field");
        String m_field = fieldNode.getAttribute("m_field");
        String d_field = fieldNode.getAttribute("d_field");
        String ym_field = fieldNode.getAttribute("ym_field");
        DateField dateField = new DateField(ipType, name, y_field, m_field, d_field, ym_field);
        return dateField;
    }


    /**
     * 处理日期字段，只解析它本身，不扩展其它日期字段
     *
     * @param ipType
     * @param fieldNode
     * @return
     * @throws IPException
     */
    private DateField itselfDateFiled(IPType ipType, Element fieldNode) throws IPException {
        String name = fieldNode.getAttribute("name");
        String format = fieldNode.getAttribute("format");
        if (StringUtil.isNullOrEmpty(format)){
            return new DateField(ipType, name);
        }else {
            return new DateField(ipType,name, format);
        }
    }
}
