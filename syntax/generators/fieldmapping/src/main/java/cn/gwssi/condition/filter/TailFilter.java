package cn.gwssi.condition.filter;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.condition.ConditionFilter;
import cn.gwssi.condition.filter.util.IPTypeUtil;
import cn.gwssi.meta.IPField;
import cn.gwssi.meta.IPMultiField;
import cn.gwssi.meta.IPType;
import cn.gwssi.meta.NestedType;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.IPConditionErrCodes;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.offset.ConditionOffset;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TailFilter implements ConditionFilter {
    protected static Logger logger = Logger.getLogger(TailFilter.class);

    @Override
    public IPCondition doFilter(IPType ipType, IPCondition ipCondition)
            throws IPException {
        ipCondition = this.doFilterDepth(ipType, ipCondition);

        return ipCondition;
    }

    private IPCondition doFilterDepth(IPType ipType, IPCondition ipCondition) throws IPException {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0; i < subConditions.size(); i++) {
            IPCondition subCondition = subConditions.get(i);
            this.mergeOffset(ipCondition, subCondition);
            if (!subCondition.isLeafCondition()) {
                this.doFilterDepth(ipType, subCondition);
            } else {
                StatementItemCondition statementItemCondition = (StatementItemCondition) subCondition;
                String fieldName = statementItemCondition.getFieldName();
                ConditionOffset offset = this.extractOffset(statementItemCondition, fieldName);

                IPField ipField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName, offset));
                IPColumnType columnType = ipField.getIpColumn().getColumnType();
                statementItemCondition.setIpColumnType(columnType);

//                // 判断字段类型和操作符是否匹配
//                ItemOperator itemOperator = statementItemCondition.getOperator();
//                checkFieldAndOperator(columnType, itemOperator, fieldName);

                // 号单字段
                if (IPColumnType.COLUMN_TYPE_ITEM_NO.equals(columnType) || IPColumnType.COLUMN_TYPE_TINY_TEXT.equals(columnType)) {
                    IPAnalyzerType analyzerType = ipField.getAnalyzer();
                    statementItemCondition.setIpAnalyzerType(analyzerType);
                }
                // 短文本类型(如名称、标题等)，支持单双引号、双引号、通配符
                if (IPColumnType.COLUMN_TYPE_TINY_TEXT.equals(columnType)) {
                    String multiFieldName = IPTypeUtil.getMuiltField(ipType, fieldName);
                    statementItemCondition.setMultiFieldName(multiFieldName);
                }

                // 设置 fieldName 和 nestedFieldName
                setCondition(ipType, statementItemCondition);

                // 设置 fullFieldName
                String fullFieldName = IPTypeUtil.getFullColumnName(ipType, fieldName);
                statementItemCondition.setFullFieldName(fullFieldName);
            }
        }

        return ipCondition;
    }

    /**
     * 检查字段类型和操作符是否匹配
     *
     * @param columnType   columnType
     * @param itemOperator itemOperator
     */
    private void checkFieldAndOperator(IPColumnType columnType, ItemOperator itemOperator, String fieldName) throws IPException {
        // tiny_text 和 long_text 只支持 EQ 和 NE 逻辑运算符，其余类型支持所有逻辑运算符
        if (columnType.equals(IPColumnType.COLUMN_TYPE_TINY_TEXT) || columnType.equals(IPColumnType.COLUMN_TYPE_LONG_TEXT)) {
            if (!itemOperator.equals(ItemOperator.ITEM_OPERATOR_EQ) && !itemOperator.equals(ItemOperator.ITEM_OPERATOR_NE)) {
                throw IPConditionErrCodes.IP_CONNECTOR_UNMATCH.exception(fieldName, itemOperator.getName());
            }
        }
    }

    /**
     * 设置 fieldName 和 nestedFieldName
     *
     * @param ipType      ipType
     * @param ipCondition ipCondition
     */
    private void setCondition(IPType ipType, StatementItemCondition ipCondition) throws IPException {
        String fieldName = ipCondition.getFieldName();
        ConditionOffset offset = this.extractOffset(ipCondition, fieldName);
        IPField ipField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName, offset));

        if (!ipField.isMultiField() && !ipField.isNestedField()) {
            ipCondition.setFieldName(ipField.getIpColumn().getName());
        }

        if (ipField.isMultiField()) {
            IPMultiField ipMultiField = (IPMultiField) ipField;
            String multiColumnName = ipMultiField.getMultiColumnName();
            IPField parentIPField = (IPField) ipField.getIpFieldContainer();
            String columnName = parentIPField.getIpColumn().getName();

            ipCondition.setFieldName(columnName + "." + multiColumnName);
            ipField = parentIPField;
        }
        if (ipField.isNestedField()) {
            NestedType nestedType = (NestedType) ipField.getIpFieldContainer();

            //没有考虑非muti-field的普通嵌套对象，且字段名为中文的情况，此时的ipCondition.getFieldName()还是中文，zyq-20170516
//			String columnName = nestedType.getName() + "." + ipCondition.getFieldName();

            String columnName = nestedType.getName() + "." + ipField.getIpColumn().getName(); //默认取ipColumn的字段名
            //如果已经在mutifield判断中更新过fieldName，则当前ipField已经更新为parentIPField，应该用原IPField
            IPField oriIpField = ipType.getIPFieldAndThrow(fieldName, IPConditionErrCodes.EXPRESSION_NOT_EXIST_FIELD.exception(fieldName, offset));
            if (oriIpField.isMultiField()) {
                columnName = nestedType.getName() + "." + ipCondition.getFieldName();//如果已经在mutifield判断中更新过fieldName，直接用
            }

            ipCondition.setFieldName(columnName);
            ipCondition.setNestedField(true);
            ipCondition.setNestedFieldName(nestedType.getName());
        }
    }

    // 提取offset
    // 有些子表达式只有value，缺少field和comparator，使用默认字段，这时候取value的位置
    private ConditionOffset extractOffset(StatementItemCondition condition, String fieldName) {
        ConditionOffset offset = condition.getOffsetOf(IPConditionConstants.ConditionType.FIELD);
        if (null == offset) {
            offset = condition.getOffsetOf(IPConditionConstants.ConditionType.COMPARATOR);
            if (null == offset) {
                offset = condition.getOffsetOf(IPConditionConstants.ConditionType.VALUE);
            }
        }
        if (null != offset && StringUtils.isBlank(offset.getChars())) {
            offset.setChars(fieldName);
        }

        return offset;
    }

    // 把上一级的 offset，合并到本级
    // 这以后只使用字段做判断，操作符、值 就先不做合并
    private void mergeOffset(IPCondition condition, IPCondition subCondition) {
        ConditionOffset offset = subCondition.getOffsetOf(IPConditionConstants.ConditionType.FIELD);
        if (null == offset) {
            offset = condition.getOffsetOf(IPConditionConstants.ConditionType.FIELD);
            subCondition.setOffsetOf(IPConditionConstants.ConditionType.FIELD, offset);
        }
    }
}
