package cn.gwssi.condition.filter;

import cn.gwssi.condition.ConditionFilter;
import cn.gwssi.meta.IPType;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.ScannerItemCondition;
import cn.gwssi.syntax.condition.StatementItemCondition;
import org.apache.log4j.Logger;

import java.util.List;

public class HeaderFilter implements ConditionFilter {
    protected static Logger logger = Logger.getLogger(HeaderFilter.class);

    @Override
    public IPCondition doFilter(IPType ipType, IPCondition ipCondition) {
        return ipCondition.isLeafCondition() ? this.toStatementItemCondition((ScannerItemCondition) ipCondition) : this.doFilterDepth(ipCondition);
    }

    private IPCondition doFilterDepth(IPCondition ipCondition) {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0; i < subConditions.size(); i++) {
            IPCondition subCondition = subConditions.get(i);
            if (subCondition.isLeafCondition()) {
                subCondition = toStatementItemCondition((ScannerItemCondition) subCondition);
                subConditions.set(i, subCondition);
            } else {
                this.doFilterDepth(subCondition);
            }
        }

        return ipCondition;
    }

    private StatementItemCondition toStatementItemCondition(ScannerItemCondition scannerItemCondition) {
        StatementItemCondition statementItemCondition = new StatementItemCondition(scannerItemCondition.getFieldName(), scannerItemCondition.getItemValue());
        statementItemCondition.setOperator(scannerItemCondition.getOperator());
        statementItemCondition.setReverse(scannerItemCondition.isReverse());
        statementItemCondition.setOffsets(scannerItemCondition.getOffsets());
        return statementItemCondition;
    }
}
