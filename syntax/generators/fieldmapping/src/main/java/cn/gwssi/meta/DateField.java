package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.common.IPDaoErrCodes;
import org.apache.commons.lang3.StringUtils;

/**
 * 日期的特殊需求
 * 
 * <AUTHOR>
 *
 */
public class DateField  extends IPObject{
	/**
	 * type为IPFieldType.FIELD_TYPE_DATE的字段的名称（年月日）
	 */
	private String name = null;

	/**
	 * 年字段名称（field_name）
	 */
	private String yFieldName = null;

	/**
	 * 月字段名称（field_name）
	 */
	private String mFieldName = null;

	/**
	 * 日字段名称（field_name）
	 */
	private String dFieldName = null;

	/**
	 * 年月字段名称（field_name）
	 */
	private String ymFieldName = null;

	/**
	 * 日期格式化
	 */
	private String format = "yyyMMdd";

	public DateField()throws IPException{}
	/**
	 * 
	 * @param ipType
	 * @param name
	 * @param yFieldName
	 * @param mFieldName
	 * @param dFieldName
	 * @param ymFieldName
	 * @throws IPException
	 */
	public DateField(IPType ipType, String name, String yFieldName,
                     String mFieldName, String dFieldName, String ymFieldName)
			throws IPException {
		super(ipType);
		if (StringUtils.isEmpty(name)) {
			throw IPDaoErrCodes.DATE_NAME_ERROR.exception(this.getConfigFileName());
		}

		if (StringUtils.isEmpty(yFieldName)
				|| StringUtils.isEmpty(mFieldName)
				|| StringUtils.isEmpty(dFieldName)
				|| StringUtils.isEmpty(ymFieldName)) {
			throw IPDaoErrCodes.DATE_ATTRIBUTE_ERROR.exception(this.getConfigFileName(), name);
		}
		
		this.name = name;
		this.yFieldName = yFieldName;
		this.mFieldName = mFieldName;
		this.dFieldName = dFieldName;
		this.ymFieldName = ymFieldName;
	}

	public DateField(IPType ipType, String name) throws IPException {
		super(ipType);
		this.name = name;
	}

	public DateField(IPType ipType, String name, String format) throws IPException {
		this(ipType,name);
		this.format = format;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @return the yFieldName
	 */
	public String getYFieldName() {
		return yFieldName;
	}

	/**
	 * @return the mFieldName
	 */
	public String getMFieldName() {
		return mFieldName;
	}

	/**
	 * @return the dFieldName
	 */
	public String getDFieldName() {
		return dFieldName;
	}

	/**
	 * @return the ymFieldName
	 */
	public String getYMFieldName() {
		return ymFieldName;
	}

	public String getFormat() {
		return format;
	}
}
