package cn.gwssi.xparser;

import cn.gwssi.common.common.IPDaoErrCodes;
import cn.gwssi.common.common.IPTypeErrCodes;
import cn.gwssi.common.common.pojo.ColumnAndField;
import cn.gwssi.common.common.pojo.IndexSettings;
import cn.gwssi.common.common.pojo.NestedField;
import cn.gwssi.common.common.pojo.VirtualField1;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.meta.*;
import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.meta.IPColumnType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 */
public class IPDaoFactory {

    protected final static Logger logger = LoggerFactory.getLogger(IPDaoFactory.class);

    private Map<String, IPType> ipTypes = new HashMap<>();

    private static IPDaoFactory _instance = null;

    public static IPDaoFactory getInstance() {
        if (_instance == null) {
            synchronized (IPDaoFactory.class) {
                if (_instance == null) {
                    _instance = new IPDaoFactory();
                }
            }
        }

        return _instance;
    }

    /**
     * 通过索引名称获取IPType
     *
     * @param indexName 索引名称
     * @return ipType
     */
    public IPType getIPType(String indexName) throws IPException {
        IPType ipType = this.ipTypes.get(indexName);

        if (null == ipType) {
            throw IPTypeErrCodes.IP_INDEX_IS_NOT_EXIST_ERROR.exception(indexName);
        }

        return ipType;
    }

    /**
     * 更新 ipType
     *
     * @param ipType ipType
     */
    public void putIPType(IPType ipType) {
        this.ipTypes.put(ipType.getIndexName(), ipType);
    }

    /**
     * 获取索引 type
     * @param indexName 索引名称
     * @return type
     * @throws IPException IPException
     */
    public String getTypeName(String indexName) throws IPException {
        return this.getIPType(indexName).getTypeName();
    }

    /**
     * 创建 ipType，包含字段
     *
     * @param indexSettings indexSettings
     * @return IPType
     * @throws IPException IPException
     */
    public IPType createIPType(IndexSettings indexSettings) throws IPException {
        String indexName = indexSettings.getIndexName();
        this.createIPType(indexName, indexSettings.getTypeName(), indexSettings.getNumberOfShards(), indexSettings.getNumberOfReplicas());
        this.updateIPType(indexSettings);
        return this.getIPType(indexName);
    }

    /**
     * 创建 ipType，不包含字段，只有索引名称、副本、分片数
     *
     * @param indexName        索引名称
     * @param typeName        类型名称
     * @param numberOfShards   分片数
     * @param numberOfReplicas 副本数
     * @throws IPException IPException
     */
    public void createIPType(String indexName, String typeName, String numberOfShards, String numberOfReplicas) throws IPException {
        IPType ipType = this.ipTypes.get(indexName);
        if (null == ipType) {
            ipType = new IPType(indexName);
        }

        ipType.setIndexName(indexName);
        ipType.setTypeName(typeName);
        ipType.setNumberOfReplicas(numberOfReplicas);
        ipType.setNumberOfShards(numberOfShards);

        this.putIPType(ipType);
    }

    /**
     * 更新 ipType
     *
     * @param settings settings
     * @throws IPException IPException
     */
    public void updateIPType(IndexSettings settings) throws IPException {
        if (null != settings) {
            IPType ipType = this.getIPType(settings.getIndexName());

            ipType.clearFields();
            addFields(ipType, settings.getFields());
            addNestedFields(ipType, settings.getNestedFields());
            addVirtualFields(ipType, settings.getVirtualFields());

            this.putIPType(ipType);
        }
    }

    /**
     * 获取普通字段
     *
     * @param indexName 索引名称
     * @return list
     */
    public List<ColumnAndField> getField(String indexName) {
        IPType ipType = this.ipTypes.get(indexName);
        if (ipType == null) {
            return null;
        }

        Map<String, IPField> ipFieldMap = ipType.getIPFieldMap();
        Set<String> keys = ipFieldMap.keySet();
        List<ColumnAndField> list = new ArrayList<ColumnAndField>();

        if (null != keys && keys.size() > 0) {
            Map<String, String> fieldNameMap = new HashMap<String, String>();
            for (String key : keys) {
                IPField ipField = ipFieldMap.get(key);
                IPColumn ipColumn = ipField.getIpColumn();

                String name1 = ipField.getName();
                if (name1.endsWith(IPConditionConstants.MULTI_FIELD_NAME_ENDWITH) || fieldNameMap.get(name1) != null) {
                    continue;
                }
                fieldNameMap.put(name1, "1");
                ColumnAndField vo = new ColumnAndField();
                vo.setAnalyzer(ipField.getAnalyzer().getAnalyzer());
                vo.setIsArray(ipColumn.isArray());
                vo.setDescription(ipField.getDescription());
                vo.setName(name1);
                vo.setNotNull(ipColumn.isNotNull());
                vo.setType(ipColumn.getColumnType() == null ? IPColumnType.COLUMN_TYPE_ITEM_NO.getColumnValue()
                        : ipColumn.getColumnType().getColumnValue());
                vo.setAlias(ipField.getAlias());
                list.add(vo);
            }
        }
        return list;
    }

    /**
     * 获取嵌套字段
     *
     * @param indexName 索引名称
     * @return list
     */
    public List<NestedField> getNestedField(String indexName) {
        IPType ipType = this.ipTypes.get(indexName);
        if (ipType == null) {
            return null;
        }

        List<NestedField> list = new ArrayList<NestedField>();
        List<NestedType> nestedTypes = ipType.getNestedType();

        if (nestedTypes != null && nestedTypes.size() > 0) {
            for (int index = 0; index < nestedTypes.size(); index++) {
                NestedType nestedType = nestedTypes.get(index);
                Map<String, IPField> ipFieldMap = nestedType.getIPFieldMap();
                Set<String> keys = ipFieldMap.keySet();
                Map<String, String> nestedNameMap = new HashMap<String, String>();
                for (String key : keys) {
                    IPField ipField = ipFieldMap.get(key);
                    IPColumn ipColumn = ipField.getIpColumn();

                    String name2 = ipField.getName();
                    if (name2.endsWith(IPConditionConstants.MULTI_FIELD_NAME_ENDWITH) || nestedNameMap.get(name2) != null) {
                        continue;
                    }
                    nestedNameMap.put(name2, "1");
                    NestedField nestedField = new NestedField();
                    nestedField.setAnalyzer(ipField.getAnalyzer().getAnalyzer());
                    nestedField.setArray(ipColumn.isArray());
                    nestedField.setDescription(ipField.getDescription());
                    nestedField.setName(ipField.getName());
                    nestedField.setNestedDesc(nestedType.getDescription());
                    nestedField.setNestedName(nestedType.getName());
                    nestedField.setNotNull(ipColumn.isNotNull());
                    nestedField.setType(ipColumn.getColumnType().getColumnValue());
                    nestedField.setAlias(ipField.getAlias());

                    list.add(nestedField);
                }

            }
        }
        return list;
    }

    /**
     * 获取虚拟字段
     *
     * @param indexName 索引名称
     * @return list
     */
    public List<VirtualField1> getVirtualField(String indexName) {
        IPType ipType = this.ipTypes.get(indexName);
        if (null == ipType) {
            return null;
        }

        List<VirtualField1> list = new ArrayList<VirtualField1>();
        Map<String, VirtualField> virtualMap = ipType.getVirtualFieldMap();
        Set<String> virtualKeys = virtualMap.keySet();

        if (virtualKeys != null && virtualKeys.size() > 0) {
            Map<String, String> virNameMap = new HashMap<String, String>();
            for (String virtaulKey : virtualKeys) {
                VirtualField virtualField = virtualMap.get(virtaulKey);
                String name = virtualField.getName();
                if (virNameMap.get(name) == null) {
                    virNameMap.put(name, "1");
                    VirtualField1 virtualField1 = new VirtualField1();
                    virtualField1.setAlias(virtualField.getAlias());
                    List<IPField> realFields = virtualField.getRealFields();
                    StringBuffer buf = new StringBuffer();
                    for (IPField ipField : realFields) {
                        buf.append(ipField.getName()).append(",");
                    }
                    virtualField1.setFields(buf.toString());
                    virtualField1.setName(virtualField.getName());
                    virtualField1.setAlias(virtualField.getAlias());

                    list.add(virtualField1);
                }
            }
        }

        return list;
    }

    /**
     * 添加普通字段到IPType
     *
     * @param ipType ipType
     * @param fields fields
     * @throws IPException IPException
     */
    private void addFields(IPType ipType, List<ColumnAndField> fields) throws IPException {
        if (fields == null)
            return;

        for (ColumnAndField field : fields) {
            if (ipType.getIPColumn(field.getName()) != null) {
                throw IPTypeErrCodes.IP_FIELD_IS_EXIST_ERROR.exception(field.getName());
            }

            IPColumn ipColumn = new IPColumn(ipType, field.getName(),
                    field.getIsArray(), field.isNotNull(), field.getType(), field.getDescription());
            ipType.putIPColumn(ipColumn.getName(), ipColumn);

            IPField ipField = fieldHandle(field.getType(), field.getName(), field.getAlias(), field.getDescription(),
                    field.getAnalyzer(), field.getIsArray(), field.getFormat(), field.isNotNull(), ipType, ipColumn);
            ipType.putIPField(ipField.getName(), ipField);

            if ("date".equals(field.getType())) {
                addDateField(ipType, field.getName(), field.getFormat());
            }
        }
    }

    /**
     * 添加虚拟字段到IPType
     *
     * @param ipType         ipType
     * @param virtualField1s virtualField1s
     * @throws IPException IPException
     */
    private void addVirtualFields(IPType ipType, List<VirtualField1> virtualField1s) throws IPException {
        if (virtualField1s == null)
            return;

        for (VirtualField1 virtualField1 : virtualField1s) {
            if (ipType.getVirtualField(virtualField1.getName()) != null) {
                throw IPTypeErrCodes.IP_VIRTUAL_FIELD_IS_EXIST.exception(virtualField1.getName());
            }

            VirtualField virtualField = new VirtualField(ipType, virtualField1.getName(), virtualField1.getAlias());
            ipType.addVirtualField(virtualField);

            if (!StringUtils.isEmpty(virtualField1.getFields())) {
                String[] fieldList = virtualField1.getFields().split(",");
                for (String field : fieldList) {
                    IPField ipField = ipType.getIPField(field.trim());
                    if (ipField == null) {
                        throw IPDaoErrCodes.VIRTUAL_FIELD_ERROR3.exception(ipType.getConfigFileName(),
                                virtualField1.getName(), field);
                    }
                    virtualField.addRealFields(ipField);
                }
            }
        }
    }

    /**
     * 添加嵌套字段到IPType
     *
     * @param ipType       ipType
     * @param nestedFields nestedFields
     * @throws IPException IPException
     */
    private void addNestedFields(IPType ipType, List<NestedField> nestedFields) throws IPException {
        if (nestedFields == null)
            return;

        for (NestedField nestedField : nestedFields) {
            NestedType nestedType = ipType.getNestedType(nestedField.getNestedName());
            if (nestedType == null) {
                nestedType = new NestedType(ipType, nestedField.getNestedName());
                nestedType.setDescription(nestedField.getDescription());
                ipType.addNestedType(nestedType);
            }
            if (nestedType.getIPColumn(nestedField.getName()) != null) {
                throw IPTypeErrCodes.IP_NESTED_FIELD_IS_EXIST.exception(nestedField.getNestedName(), nestedField.getName());
            }

            IPColumn ipColumn = new IPColumn(ipType, nestedField.getName(), nestedField.isArray(),
                    nestedField.isNotNull(), nestedField.getType(), nestedField.getDescription());
            IPField ipField = fieldHandle(nestedField.getType(), nestedField.getName(), nestedField.getAlias(),
                    nestedField.getDescription(), nestedField.getAnalyzer(),
                    nestedField.isArray(), nestedField.getFormat(), nestedField.isNotNull(), ipType, ipColumn);

            nestedType.putIPColumn(nestedField.getName(), ipColumn);
            nestedType.putIPField(nestedField.getName(), ipField);
        }
    }

    /**
     * 添加日期字段到IPType
     *
     * @param ipType        ipType
     * @param dateFieldName dateFieldName
     * @throws IPException IPException
     */
    private void addDateField(IPType ipType, String dateFieldName, String format) throws IPException {
        DateField dateField = new DateField(ipType, dateFieldName, format);
        ipType.addDateField(dateField);
    }

    /**
     * 字啊处理
     *
     * @param type        类型
     * @param name        字段名称
     * @param alias       别名
     * @param description 别名
     * @param analyzer    分词器
     * @param isArray     是否数组值
     * @param format     日期格式
     * @param notNull     是否空值
     * @param ipType      ipType
     * @param ipColumn    ipColumn
     * @return ipField
     * @throws IPException IPException
     */
    private IPField fieldHandle(String type, String name, String alias, String description, String analyzer, boolean isArray, String format,
                                boolean notNull, IPType ipType, IPColumn ipColumn) throws IPException {
        IPField ipField = new IPField(ipType, name, alias, analyzer, description, ipColumn);

        if ("tiny_text".equals(type) || "long_text".equals(type)) {
            IPMultiField ipMultField = new IPMultiField(ipType, name + IPConditionConstants.MULTI_FIELD_NAME_ENDWITH);
            ipMultField.setDescription(description);
            ipMultField.setIpColumn(ipColumn);
            ipMultField.setMultiColumnName(IPConditionConstants.MULTI_FIELD_COLUMNNAME);
            ipField.addIPField(ipMultField);
        }
        if ("date".equals(type)) {
            addDateField(ipType, name, format);
        }

        return ipField;
    }
}
