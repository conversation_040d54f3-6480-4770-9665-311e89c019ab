package cn.gwssi.meta;

import cn.gwssi.common.exception.IPException;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPMultiField extends IPField {
	/**
	 * 对应的IPSearch的字段名
	 */
	private String multiColumnName = null;

	public IPMultiField()throws IPException{}

	/**
	 * 
	 * @param ipType
	 * @param name
	 * @throws IPException
	 */
	public IPMultiField(IPType ipType, String name)
			throws IPException {
		super(ipType, name, null);
	}

	public String getMultiColumnName() {
		return multiColumnName;
	}

	public void setMultiColumnName(String multiColumnName) {
		this.multiColumnName = multiColumnName;
	}
}
