package cn.gwssi.condition.filter;

import cn.gwssi.condition.ConditionFilter;
import cn.gwssi.meta.IPField;
import cn.gwssi.meta.IPType;
import cn.gwssi.meta.VirtualField;
import cn.gwssi.syntax.condition.IPCondition;
import cn.gwssi.syntax.condition.StatementItemCondition;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.List;

/**
 * 虚拟字段 条件过滤器
 */
public class VirtualFieldFilter implements ConditionFilter {
    protected static Logger logger = Logger.getLogger(VirtualFieldFilter.class);

    @Override
    public IPCondition doFilter(IPType ipType, IPCondition ipCondition) {
        return this.doFilterDepth(ipType, ipCondition);
    }

    private IPCondition doFilterDepth(IPType ipType, IPCondition ipCondition) {
        List<IPCondition> subConditions = ipCondition.getSubConditions();
        for (int i = 0; i < subConditions.size(); i++) {
            IPCondition subCondition = subConditions.get(i);
            if (!subCondition.isLeafCondition()) {

                // 虚拟字段的连续值
                String serialField = subCondition.getSerialField();
                if (StringUtils.isNotBlank(serialField)) {
                    VirtualField virtualField = ipType.getVirtualField(serialField);
                    if (null != virtualField) {
                        List<IPField> realFields = virtualField.getRealFields();
                        IPCondition serialCondition = doFilterDepthWithSerial(subCondition, realFields);
                        subConditions.set(i, serialCondition);
                        continue;
                    }
                }

                // 非虚拟字段，非连续值处理
                doFilterDepth(ipType, subCondition);
            } else {
                StatementItemCondition itemCondition = (StatementItemCondition) subCondition;
                String itemName = itemCondition.getFieldName();
                Value itemValue = itemCondition.getValue();

                if (null != itemName) {
                    VirtualField virtualField = ipType.getVirtualField(itemName);

                    // 判断该检索字段是否是虚拟字段，如果是则将其通过虚拟字段规则展开
                    if (virtualField != null) {
                        IPCondition condition = new IPCondition();
                        List<IPField> realFields = virtualField.getRealFields();
                        int length = realFields.size();

                        // 转化字段
                        for (int j = 0; j < length; j++) {
                            IPField realField = realFields.get(j);
                            StatementItemCondition siCondition = new StatementItemCondition(realField.getIpColumn().getName(), itemValue);
                            siCondition.setOperator(itemCondition.getOperator());
                            siCondition.setReverse(itemCondition.isReverse());
                            siCondition.setOffsets(itemCondition.getOffsets());
                            condition.addSubCondition(siCondition);
                        }

                        // 添加连接符
                        for (int j = 0; j < length - 1; j++) {
                            if (ItemOperator.ITEM_OPERATOR_NE == itemCondition.getOperator()) {
                                condition.addConnector(ItemConnector.ITEM_CONNECTOR_AND);
                            } else {
                                condition.addConnector(ItemConnector.ITEM_CONNECTOR_OR);
                            }
                        }

                        subConditions.set(i, condition);
                    }
                }
            }
        }

        return ipCondition;
    }

    /**
     * 虚拟字段，连续值处理
     * 1、替换字段
     * 2、用 or 或 and 连接
     * 例如，ti=(a or b) 处理成 ticn=(a or b) or tien=(a or b)
     */
    private IPCondition doFilterDepthWithSerial(IPCondition condition, List<IPField> fields) {
        IPCondition newCondition = new IPCondition();
        boolean equalOrNot = condition.isEqualOrNot();

        for (IPField ipField : fields) {
            IPCondition converted = this.doFilterDepthWithSerial(condition, ipField.getIpColumn().getName());
            newCondition.addSubCondition(converted);
        }

        for (int i = 0, length = fields.size(); i < length - 1; i++) {
            newCondition.addConnector(equalOrNot ? ItemConnector.ITEM_CONNECTOR_OR : ItemConnector.ITEM_CONNECTOR_AND);
        }

        return newCondition;
    }

    /**
     * 虚拟字段，连续值，替换字段
     * 例如，ti=(a or b) 处理成 ticn=(a or b)
     */
    private IPCondition doFilterDepthWithSerial(IPCondition condition, String field) {
        IPCondition newCondition = new IPCondition();
        List<IPCondition> subConditions = condition.getSubConditions();
        for (IPCondition subCondition : subConditions) {

            // 替换字段
            IPCondition newSubCondition;
            if (!subCondition.isLeafCondition()) {
                newSubCondition = this.doFilterDepthWithSerial(subCondition, field);
            } else {
                StatementItemCondition itemCondition = (StatementItemCondition) subCondition;
                newSubCondition = new StatementItemCondition(field, itemCondition.getValue());
                ((StatementItemCondition) newSubCondition).setOperator(itemCondition.getOperator());
                newSubCondition.setOffsets(itemCondition.getOffsets());
                ((StatementItemCondition) newSubCondition).setReverse(itemCondition.isReverse());
            }

            newCondition.addSubCondition(newSubCondition);
        }

        // 复制 connectors 和 offsets
        newCondition.setConnectors(condition.getConnectors());
        newCondition.setOffsets(condition.getOffsets());
        return newCondition;
    }
}
