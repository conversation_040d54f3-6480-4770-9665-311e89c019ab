package cn.gwssi.xparser;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPDaoConstants {
	/**
	 * 嵌套关系连接符
	 */
	public static final String NESTED_TYPE_JOINER = ",";
	
	/**
	 * 多值字段连接符
	 */
	public static final String MULTI_FIELD_JOINER = ",";

	/**
	 * 日期型字段必须要以此结束
	 */
	public static final String DATEFIELD_ENDWITH = "ymd";

	/**
	 * multi_field的columnName
	 */
	public static final String MULTI_FIELD_COLUMNNAME = "raw";

	/**
	 * multi_field的name要以此结尾
	 */
	public static final String MULTI_FIELD_NAME_ENDWITH = "_raw";
}
