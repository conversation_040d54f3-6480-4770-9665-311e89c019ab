package cn.gwssi.syntax.condition.scannerValue.tool;

import cn.gwssi.syntax.condition.constants.IPConditionConstants;

import java.util.regex.Pattern;

public class ValueTool {

    private static ValueTool handler;
    private final static Pattern PATTERN_ZH = Pattern.compile(IPConditionConstants.CONTAIN_ZH, Pattern.CASE_INSENSITIVE);
    private final static Pattern PATTERN_EN = Pattern.compile(IPConditionConstants.CONTAIN_EN, Pattern.CASE_INSENSITIVE);
    private final static Pattern PATTERN_JP = Pattern.compile(IPConditionConstants.CONTAIN_JP, Pattern.CASE_INSENSITIVE);
    private final static Pattern PATTERN_KR = Pattern.compile(IPConditionConstants.CONTAIN_KR, Pattern.CASE_INSENSITIVE);

    public static ValueTool getInstance() {
        if (null == handler) {
            handler = new ValueTool();
        }

        return handler;
    }

    // 是否包含通配符
    public boolean hasWildCard(String value) {
        return value.contains(IPConditionConstants.KEYWORDS_POUND)
                || value.contains(IPConditionConstants.KEYWORDS_QUESTION_MARK)
                || value.contains(IPConditionConstants.KEYWORDS_ASTERISK)
                || value.contains(IPConditionConstants.KEYWORDS_PERCENT);
    }

    // 是否双引号
    public boolean isQuotation(String value) {
        return value.startsWith(IPConditionConstants.KEYWORDS_QUOTES_DOUBLE)
                && value.endsWith(IPConditionConstants.KEYWORDS_QUOTES_DOUBLE);
    }

    // 是否单引号
    public boolean isQuotationSingle(String value) {
        return value.startsWith(IPConditionConstants.KEYWORDS_QUOTES_SINGLE)
                && value.endsWith(IPConditionConstants.KEYWORDS_QUOTES_SINGLE);
    }

    // FIXED 2023-03-29 value.matches 方法调用的是正则的 matcher 方法
    // matcher 需要全部匹配，find 只需要部分匹配
    // 是否包含中文
    public boolean hasZh(String value) {
        return PATTERN_ZH.matcher(value).find();
    }

    public boolean hasJPOrKR(String value) {
        return PATTERN_JP.matcher(value).find() || PATTERN_KR.matcher(value).find();
    }

    // 是否包含英文
    public boolean hasEn(String value) {
        return PATTERN_EN.matcher(value).find();
    }
}
