package cn.gwssi.syntax.condition.parser;

import cn.gwssi.syntax.condition.offset.ConditionOffset;

public class ParserError extends ConditionOffset {

    private boolean hasError = false;
    private boolean hasVector = false;

    private String errCode = "exp000";
    private String reason;
    private String wholeMsg;

    public ParserError() {
        super();
        this.reason = "";
        this.wholeMsg = "无法解析";
    }

    public ParserError(String wholeMsg) {
        this();
        this.wholeMsg = wholeMsg;
    }

    public ParserError(ConditionOffset offset, String reason, String wholeMsg) {
        if (null != offset) {
            this.setStartLine(offset.getStartLine());
            this.setEndLine(offset.getEndLine());
            this.setStart(offset.getStart());
            this.setEnd(offset.getEnd());
            this.setStartInLine(offset.getStartInLine());
            this.setEndInLine(offset.getEndInLine());
            this.setChars(offset.getChars());
        }

        this.reason = reason;
        this.wholeMsg = wholeMsg;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getWholeMsg() {
        return wholeMsg;
    }

    public void setWholeMsg(String wholeMsg) {
        this.wholeMsg = wholeMsg;
    }

    public boolean isHasError() {
        return hasError;
    }

    public void setHasError(boolean hasError) {
        this.hasError = hasError;
    }

    public boolean isHasVector() {
        return hasVector;
    }

    public void setHasVector(boolean hasVector) {
        this.hasVector = hasVector;
    }
}
