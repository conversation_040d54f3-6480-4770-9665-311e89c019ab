package cn.gwssi.syntax.condition.constants;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPConditionConstants {
	/**
	 * 单引号
	 */
	public static final char KEYWORDS_CHAR_QUOTES_SINGLE = '\'';

	/**
	 * 单引号
	 */
	public static final String KEYWORDS_QUOTES_SINGLE = "'";

	/**
	 * 双引号
	 */
	public static final char KEYWORDS_CHAR_QUOTES_DOUBLE = '"';

	/**
	 * 双引号
	 */
	public static final String KEYWORDS_QUOTES_DOUBLE = "\"";

	/**
	 * 井号
	 */
	public static final String KEYWORDS_POUND = "#";
	
	/**
	 * 井号
	 */
	public static final char KEYWORDS_CHAR_POUND = '#';
	
	/**
	 * 问号
	 */
	public static final String KEYWORDS_QUESTION_MARK = "?";
	
	/**
	 * 问号
	 */
	public static final char KEYWORDS_CHAR_QUESTION_MARK = '?';
	
	/**
	 * 百分号
	 */
	public static final char KEYWORDS_CHAR_PERCENT = '%';
	
	/**
	 * 百分号
	 */
	public static final String KEYWORDS_PERCENT = "%";

	/**
	 * 星号
	 */
	public static final String KEYWORDS_ASTERISK = "*";
	
	/**
	 * 2015
	 */
	public static final String REG_DATE_Y = "[12]\\d{3}";

	/**
	 * 201501
	 */
	public static final String REG_DATE_YM1 = "[12]\\d{3}[01]\\d";

	/**
	 * 2015/01 == 2015.01 == 2015/1 == 2015.1
	 */
	public static final String REG_DATE_YM2 = "[12]\\d{3}[/.-]\\d{1,2}";

    /**
     * 20151
     */
    public static final String REG_DATE_YM3 = "[12]\\d{3}\\d";

	/**
	 * 2015..01 == 2015..1
	 */
	public static final String REG_DATE_YD1 = "[12]\\d{3}[.]{2}\\d{1,2}";

	/**
	 * 20150112
	 */
	public static final String REG_DATE_YMD1 = "[12]\\d{3}[01]\\d[0123]\\d";

	/**
	 * 2015/01/12 == 2015.01.12 == 2015/1/12 == 2015.1.12
	 */
	public static final String REG_DATE_YMD2 = "[12]\\d{3}([/.-]\\d{1,2}){2}";
	
	/**
	 * 通配符检索，去除通配符时访问的索引
	 */
	public static final String WILDCARDINDEX = "wildcard";
	
	/**
	 * 通配符检索，去除通配符时访问的类型
	 */
	public static final String WILDCARDTYPE = "english";
	
	/**
	 * 通配符检索，检索结果返回条数
	 */
	public static final int WILDCARDNUM = 100000;
	
	/**
	 * 日期型字段必须要以此结束
	 */
	public static final String DATEFIELD_ENDWITH = "ymd";
	
	/**
	 * multi_field的columnName
	 */
	public static final String MULTI_FIELD_COLUMNNAME = "raw";
	
	/**
	 * multi_field的name要以此结尾
	 */
	public static final String MULTI_FIELD_NAME_ENDWITH = "_raw";

    /**
     * 匹配to，仅仅为了匹配
     */
    public static final String KEYWORDS_TO = "(.?) to (.?)";

    /**
     * 匹配to，为了提取内容
     */
    public static final String KEYWORDS_TO_SUB = "(.*) to (.*)";

    /**
     * 中文
     */
    public static final String CONTAIN_ZH = "[\\u4e00-\\u9fa5]";

    /**
     * 日文
     * FIXED 2024-03-22 正则表达式里的 unicode，\\u之后只能有4位，超过4位的需要用以下方式转为两个字符集合的连接
     * Character.toChars(0x1B000)
     * 这样转化后，原来的 \\u1B000-\\u1B0FF 要写成下面的形式
     */
    public static final String CONTAIN_JP = "[\\u3040-\\u309F|\\u30A0-\\u30FF|\uD82C\uDC00-\uD82C\uDCFF]";

    /**
     * 韩文
     */
    public static final String CONTAIN_KR = "[\\uAC00-\\uD7AF|\\u1100-\\u11FF|\\u3130-\\u318F]";

    /**
     * 英文
     */
    public static final String CONTAIN_EN = "[a-zA-Z]";

    /**
     * 同句分隔符
     */
    public static final String SEPARATOR_SEN = "$_sen_%$";

    /**
     * 同段分隔符
     */
    public static final String SEPARATOR_SEG = "$_seg_$";

    /**
     * 地理位置，默认距离
     */
    public static final double GEO_DISTANCE = 5;

    /**
     * 图像字段
     */
    public static final String DEFAULT_IMAGE_FIELD = ".image_vec";

    /**
     * 语义字段
     */
    public static final String DEFAULT_SEMANTIC_FIELD = ".semantic_vec";

    /**
     * 图片base64格式
     */
    public static final String IMAGE_START = "^data:image/(.+);base64,(.+)";

    /**
     * 号码格式，申请号/公开公共号等
     */
    public static final String AN_PN_START = "^(?=.{6,20}$)[a-zA-Z]+(.*?)\\d{4,}(.*?)$";

    /**
     * 向量检索的rescore参数
     */
    public static final int DEFAULT_WINDOW_SIZE = 1000;

    /**
     * 向量检索的rescore参数
     */
    public static final int RESCORE_QUERY_WEIGHT = 0;

    public enum ConditionType implements Serializable {
        FIELD("field"),
        COMPARATOR("comparator"),
        VALUE("value");

        private String type;

        ConditionType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

}
