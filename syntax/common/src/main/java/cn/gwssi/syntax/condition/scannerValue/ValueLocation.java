package cn.gwssi.syntax.condition.scannerValue;

import cn.gwssi.syntax.condition.constants.ItemOperator;

import java.io.Serializable;

public class ValueLocation implements Serializable {

    private int count;
    private ItemOperator comparator;
    private SenSeg senSeg;
    private boolean inOrder;

    public enum SenSeg {
        SEN,
        SEG;
    }

    public ValueLocation(ItemOperator comparator, boolean inOrder, int count) {
        this.comparator = comparator;
        this.inOrder = inOrder;
        this.count = count;
        this.senSeg = null;
    }

    public ValueLocation(ItemOperator comparator, boolean inOrder, int count, SenSeg senSeg) {
        this(comparator, inOrder, count);
        this.senSeg = senSeg;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public ItemOperator getComparator() {
        return comparator;
    }

    public void setComparator(ItemOperator comparator) {
        this.comparator = comparator;
    }

    public SenSeg getSenSeg() {
        return senSeg;
    }

    public void setSenSeg(SenSeg senSeg) {
        this.senSeg = senSeg;
    }

    public boolean isInOrder() {
        return inOrder;
    }

    public void setInOrder(boolean inOrder) {
        this.inOrder = inOrder;
    }
}
