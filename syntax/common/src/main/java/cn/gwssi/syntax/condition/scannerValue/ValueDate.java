package cn.gwssi.syntax.condition.scannerValue;

import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.DateValidator;

import java.io.Serializable;
import java.util.Locale;

public class ValueDate implements Serializable {

    private String date;            // 原始日期
    private String format;          // 格式
    private String dateFormatted;   // 处理后的日期
    private String type;            // 类型--年/月/日
    private boolean isDate;         // 是否日期类型

    public ValueDate(String date) {
        this.date = date.trim();
        this.toDate(this.date);
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getDateFormatted() {
        return dateFormatted;
    }

    public void setDateFormatted(String dateFormatted) {
        this.dateFormatted = dateFormatted;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isDate() {
        return isDate;
    }

    public void setDate(boolean date) {
        isDate = date;
    }

    private void toDate(String date) {
        String dateFormatted = date;
        String format = "yyyy";
        String type = "date";
        boolean isDate = true;
        int length = date.length();

        if (StringUtils.isNumeric(date) && length == 4) {
            dateFormatted = date + "||/y";
            type = "year";
            isDate = false;
        } else if (length == 5 || length == 6 || length == 7) {
            String separator = (StringUtils.isNumeric(date)) ? "" : String.valueOf(date.charAt(4));

            // FIXED 2023-12-18 月份，补齐两位
            if (length == 5) {
                date = date.substring(0, 4) + "0" + date.substring(4);
            } else if (length == 6 && StringUtils.isNotBlank(separator)) {
                date = date.replace(separator, separator + "0");
            }

            format = "yyyy" + separator + "MM";
            dateFormatted = date + "||/M";
            type = "month";
            isDate = false;
        } else if (length == 8 || length == 9 || length == 10) {
            String separator = (StringUtils.isNumeric(date)) ? "" : String.valueOf(date.charAt(4));
            format = "yyyy" + separator + "MM" + separator + "dd";

            // FIXED 2023-12-18 日期类型，月份和日期补齐两位
            if (length < 10 && StringUtils.isNotBlank(separator)) {
                String[] values = dateFormatted.split("/|-|\\.");
                if (values[1].length() == 1) {
                    values[1] = "0" + values[1];
                }
                if (values[2].length() == 1) {
                    values[2] = "0" + values[2];
                }
                dateFormatted = StringUtils.join(values, separator);
            }
        }

        this.dateFormatted = dateFormatted;
        this.format = format;
        this.type = type;
        this.isDate = isDate;
    }

    public static boolean isDateLegal(String date) {
        return (StringUtils.isBlank(date) || date.matches(IPConditionConstants.REG_DATE_Y) ||
                date.matches(IPConditionConstants.REG_DATE_YM1) || date.matches(IPConditionConstants.REG_DATE_YM2) ||
                date.matches(IPConditionConstants.REG_DATE_YM3) ||
                date.matches(IPConditionConstants.REG_DATE_YMD1) || date.matches(IPConditionConstants.REG_DATE_YMD2));
    }

    public static boolean isDateLegal(ValueDate date) {
        if (null == date) {
            return true;
        }

        String value = date.getDate();
        String format = date.getFormat();
        return (StringUtils.isBlank(value) || value.matches(IPConditionConstants.REG_DATE_Y) ||
                value.matches(IPConditionConstants.REG_DATE_YM1) || value.matches(IPConditionConstants.REG_DATE_YM2) ||
                value.matches(IPConditionConstants.REG_DATE_YM3) ||
                value.matches(IPConditionConstants.REG_DATE_YMD1) || value.matches(IPConditionConstants.REG_DATE_YMD2)) &&
                DateValidator.getInstance().isValid(value, format, Locale.CHINA);
    }

}
