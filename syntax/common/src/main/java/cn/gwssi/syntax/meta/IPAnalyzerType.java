package cn.gwssi.syntax.meta;

import cn.gwssi.common.util.StringUtil;

/**
 * 
 * <AUTHOR>
 *
 */
public enum IPAnalyzerType {
	ANALYZER_TYPE_NONE("none"),	//
	ANALYZER_TYPE_IK_MAX_WORD("ik_without_html_tag"),	//
	ANALYZER_TYPE_STANDARD("standard"),	//
	ANALYZER_TYPE_LOWERCASE("lowercase_analyzer"),	//
	ANALYZER_TYPE_ENGLISH("english_without_html_tag"),//
	ANALYZER_TYPE_NGRAM("2gram_analyzer"),//
	ANALYZER_IK_MAX_WORD("ik_max_word"),
    ANALYZER_SMART("smart_analyzer");	//

	String analyzer = null;
	IPAnalyzerType(String analyzer) {
		this.analyzer = analyzer;
	}
	public String getAnalyzer() {
		return analyzer;
	}
	public void setAnalyzer(String analyzer) {
		this.analyzer = analyzer;
	}
	/**
	 * 
	 * @param type
	 * @return
	 */
	public static IPAnalyzerType getAnalyzerType(String type) {
		if(StringUtil.isNullOrEmpty(type)) {
			return ANALYZER_TYPE_NONE;
		}
		
		IPAnalyzerType[] analyzerTypes = IPAnalyzerType.values();
		for(IPAnalyzerType analyzerType : analyzerTypes) {
			String analyzer = analyzerType.analyzer;
			if(StringUtil.equals(analyzer, type)) {
					return analyzerType;
			}
		}
		
		return ANALYZER_TYPE_NONE;
	}	
}
