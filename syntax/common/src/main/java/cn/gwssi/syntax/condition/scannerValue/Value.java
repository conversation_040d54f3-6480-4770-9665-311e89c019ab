package cn.gwssi.syntax.condition.scannerValue;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Value implements Serializable {

    private String value;
    private boolean hasQuotation;
    private boolean hasWildCard;
    private boolean hasZh;
    private boolean isLocation;
    private boolean isFreq;
    private boolean isTrueCase;
    private boolean isRange;
    private boolean isIn;
    private float boost = -1;
    private float[] vector;
    private String[] multiValues;
    private List<Value> values = new ArrayList<>();
    private List<ValueLocation> locations = new ArrayList<>();
    private ValueFreq freq;
    private ValueRange range;

    public Value() {}

    public Value(List<Value> values) {
        this.values = values;
    }

    public Value(String[] multiValues) {
        this.multiValues = multiValues;
    }

    public Value(String value) {
        this.value = value;
        this.hasQuotation = false;
        this.hasWildCard = false;
        this.hasZh = false;
        this.isLocation = false;
        this.isFreq = false;
        this.isTrueCase = false;
        this.isRange = false;
        this.isIn = false;
        this.boost = -1;
        this.vector = null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean hasQuotation() {
        return hasQuotation;
    }

    public void setHasQuotation(boolean hasQuotation) {
        this.hasQuotation = hasQuotation;
    }

    public boolean hasWildCard() {
        return hasWildCard;
    }

    public void setHasWildCard(boolean hasWildCard) {
        this.hasWildCard = hasWildCard;
    }

    public boolean hasZh() {
        return hasZh;
    }

    public void setHasZh(boolean hasZh) {
        this.hasZh = hasZh;
    }

    public boolean isLocation() {
        return isLocation;
    }

    public void setLocation(boolean location) {
        isLocation = location;
    }

    public boolean isFreq() {
        return isFreq;
    }

    public void setFreq(boolean freq) {
        isFreq = freq;
    }

    public boolean isTrueCase() {
        return isTrueCase;
    }

    public void setTrueCase(boolean trueCase) {
        isTrueCase = trueCase;
    }

    public boolean isRange() {
        return isRange;
    }

    public void setRange(boolean range) {
        isRange = range;
    }

    public boolean isIn() {
        return isIn;
    }

    public void setIn(boolean in) {
        isIn = in;
    }

    public float getBoost() {
        return boost;
    }

    public void setBoost(float boost) {
        this.boost = boost;
    }

    public float[] getVector() {
        return vector;
    }

    public void setVector(float[] vector) {
        this.vector = vector;
    }

    public List<ValueLocation> getLocations() {
        return locations;
    }

    public void setLocations(List<ValueLocation> locations) {
        this.locations = locations;
    }

    public void addLocations(List<ValueLocation> locations) {
        this.locations.addAll(locations);
    }

    public void addLocation(ValueLocation location) {
        this.locations.add(location);
    }

    public List<Value> getValues() {
        return values;
    }

    public void setValues(List<Value> values) {
        this.values = values;
    }

    public void addValues(List<Value> values) {
        this.values.addAll(values);
    }

    public void addValue(Value value) {
        this.values.add(value);
    }

    public String[] getMultiValues() {
        return multiValues;
    }

    public void setMultiValues(String[] multiValues) {
        this.multiValues = multiValues;
    }

    public ValueFreq getFreq() {
        return freq;
    }

    public void setFreq(ValueFreq freq) {
        this.freq = freq;
    }

    public ValueRange getRange() {
        return range;
    }

    public void setRange(ValueRange range) {
        this.range = range;
    }
}
