package cn.gwssi.syntax.condition;

import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;
import cn.gwssi.syntax.meta.IPAnalyzerType;
import cn.gwssi.syntax.meta.IPColumnType;

/**
 * <AUTHOR>
 */
public class StatementItemCondition extends IPCondition {
    /**
     * 是否取反（not修饰）
     */
    private boolean isReverse = false;

    // 是否是嵌套字段
    private boolean isNestedField = false;

    // 字段名
    private String fieldName;

    // 多值字段
    private String multiFieldName;

    // 嵌套字段
    private String nestedFieldName;

    // 嵌套/多值 字段
    private String fullFieldName;

    // 值
    private Value value;

    // 操作符
    private ItemOperator operator;

    private IPAnalyzerType ipAnalyzerType;

    private IPColumnType ipColumnType;

    public StatementItemCondition() {
    }

    public StatementItemCondition(String fieldName, Value value) {
        this.fieldName = fieldName;
        this.value = value;
    }

    public boolean isReverse() {
        return isReverse;
    }

    public void setReverse(boolean isReverse) {
        this.isReverse = isReverse;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getMultiFieldName() {
        return multiFieldName;
    }

    public void setMultiFieldName(String multiFieldName) {
        this.multiFieldName = multiFieldName;
    }

    public Value getValue() {
        return value;
    }

    public void setValue(Value value) {
        this.value = value;
    }

    public ItemOperator getOperator() {
        return operator;
    }

    public void setOperator(ItemOperator operator) {
        this.operator = operator;
    }

    public IPAnalyzerType getIpAnalyzerType() {
        return ipAnalyzerType;
    }

    public void setIpAnalyzerType(IPAnalyzerType ipAnalyzerType) {
        this.ipAnalyzerType = ipAnalyzerType;
    }

    public boolean isNestedField() {
        return isNestedField;
    }

    public void setNestedField(boolean nestedField) {
        isNestedField = nestedField;
    }

    public String getNestedFieldName() {
        return nestedFieldName;
    }

    public void setNestedFieldName(String nestedFieldName) {
        this.nestedFieldName = nestedFieldName;
    }

    public String getFullFieldName() {
        return fullFieldName;
    }

    public void setFullFieldName(String fullFieldName) {
        this.fullFieldName = fullFieldName;
    }

    public IPColumnType getIpColumnType() {
        return ipColumnType;
    }

    public void setIpColumnType(IPColumnType ipColumnType) {
        this.ipColumnType = ipColumnType;
    }
}
