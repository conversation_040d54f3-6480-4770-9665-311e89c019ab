package cn.gwssi.syntax.condition.constants;

import cn.gwssi.common.util.StringUtil;

/**
 * <AUTHOR>
 */
public enum ItemOperator {
    ITEM_OPERATOR_EQ("="),
    ITEM_OPERATOR_GE(">="),
    ITEM_OPERATOR_GT(">"),
    ITEM_OPERATOR_LE("<="),
    ITEM_OPERATOR_LT("<"),
    ITEM_OPERATOR_NE("!="),
    ITEM_OPERATOR_OR("+="),
    ITEM_OPERATOR_IN("in");

    private String name;

    ItemOperator(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public boolean equals(ItemOperator c) {
        if (c == null) {
            return false;
        }

        return StringUtil.equals(this.name, c.name);
    }

    public static ItemOperator getOperator(String name) {
        if (!StringUtil.isNullOrEmpty(name)) {

            for (ItemOperator operator : ItemOperator.values()) {
                if (operator.getName().equals(name)) {
                    return operator;
                }
            }
        }

        return null;
    }
}
