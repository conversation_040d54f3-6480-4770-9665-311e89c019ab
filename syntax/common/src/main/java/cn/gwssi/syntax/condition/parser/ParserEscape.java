package cn.gwssi.syntax.condition.parser;

public class ParserEscape {

    /**
     * 转义特殊字符，规则为：
     * \ 转为 \\
     * ( 转为 \(
     * ) 转为 \)
     */
    public static String escapeAdd(String text) {
        return text.replaceAll("\\\\", "\\\\\\\\")
                .replaceAll("\\(", "\\\\(")
                .replaceAll("\\)", "\\\\)");
    }

    /**
     * 移除转义，规则为：
     * \( 转为 \
     * \) 转为 )
     * \\ 转为 \
     * \" 转为 "
     * \' 转为 '
     */
    public static String escapeRemove(String text) {
        return text.replaceAll("\\\\\\(", "(")
                .replaceAll("\\\\\\)", ")")
                .replaceAll("\\\\\\\\", "\\\\")
                .replaceAll("\\\\\"", "\"")
                .replaceAll("\\\\'", "\\'");
    }

    /**
     * 处理双引号中的内容
     * 移除转义，规则为：
     * \" 转为 "
     * \\ 转为 \
     */
    public static String escapeRemoveQuotes(String text) {
        return text.replaceAll("\\\\\\\"", "\"")
                .replaceAll("\\\\\\\\", "\\\\");
    }

    /**
     * 处理单引号中的内容
     * 移除转义，规则为：
     * \' 转为 '
     */
    public static String escapeRemoveQuotesSingle(String text) {
        return text.replaceAll("\\\\\\'", "\\'");
    }
}
