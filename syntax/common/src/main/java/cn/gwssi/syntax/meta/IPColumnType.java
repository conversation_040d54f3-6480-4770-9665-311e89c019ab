package cn.gwssi.syntax.meta;

import org.apache.commons.lang3.StringUtils;

/**
 * 字段类型
 * 
 * <AUTHOR>
 *
 */
public enum IPColumnType {
	COLUMN_TYPE_ITEM_NO("item_no", "keyword"),	// 号单，编号
	COLUMN_TYPE_TINY_TEXT("tiny_text"),	// 短文本类型(如名称、标题等)
	COLUMN_TYPE_LONG_TEXT("long_text", "text"),	// 长文本类型（如全文）
	COLUMN_TYPE_BOOLEAN("boolean", "bool"),	// 
//	COLUMN_TYPE_NUMBER("long", "integer", "short", "byte"),	// 数字类型
	COLUMN_TYPE_SHORT("short"),	// 数字类型
	COLUMN_TYPE_BYTE("byte"),	// 数字类型
	COLUMN_TYPE_INTEGER("integer"),	// 数字类型
	COLUMN_TYPE_LONG("long"),	// 数字类型
	COLUMN_TYPE_DOUBLE("double"),	// 数字类型
	COLUMN_TYPE_FLOAT("float"),	// 数字类型
	COLUMN_TYPE_DATE("date"),	// 日期类型，注和ES的date类型不一样，这个是为了2015..01检索需求
	COLUMN_TYPE_BINARY("binary"),
	COLUMN_TYPE_RAW_DATE("raw_date"),//ES原生日期类型
	COLUMN_TYPE_IMG("image"),
//    COLUMN_TYPE_VECTOR("gw_vector"),// 图像的特征值，向量数据
	COLUMN_TYPE_SEMANTIC("semantic"),// 语义类型
    COLUMN_TYPE_GEO_POINT("geo_point");// 地理位置类型

	String[] types = null;
	IPColumnType(String...types) {
		this.types = types;
	}

	/**
	 * 
	 * @param type
	 * @return
	 */
	public static IPColumnType getColumnType(String type) {
		if(StringUtils.isEmpty(type)) {
			return null;
		}

		IPColumnType[] columnTypes = IPColumnType.values();
		for(IPColumnType columnType : columnTypes) {
			String[] types = columnType.types;
			for(String _type : types) {
				if(_type.compareTo(type) == 0) {
					return columnType;
				}
			}
		}
		
		return null;
	}
	
	public String getColumnValue(){
		if(this.types != null  && this.types.length > 0){

			return types[0];
		}
		return "item_no";
	}
}
