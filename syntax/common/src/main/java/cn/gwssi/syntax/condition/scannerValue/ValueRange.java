package cn.gwssi.syntax.condition.scannerValue;

import java.io.Serializable;

public class ValueRange implements Serializable {

    private String frontValue;
    private String endValue;

    public ValueRange() {}

    public ValueRange(String frontValue, String endValue) {
        this.frontValue = frontValue;
        this.endValue = endValue;
    }

    public String getFrontValue() {
        return frontValue;
    }

    public void setFrontValue(String frontValue) {
        this.frontValue = frontValue;
    }

    public String getEndValue() {
        return endValue;
    }

    public void setEndValue(String endValue) {
        this.endValue = endValue;
    }
}
