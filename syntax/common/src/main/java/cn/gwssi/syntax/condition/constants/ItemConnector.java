package cn.gwssi.syntax.condition.constants;

import cn.gwssi.common.util.StringUtil;

/**
 * <AUTHOR>
 */
public enum ItemConnector {
    ITEM_CONNECTOR_AND("AND"),
    ITEM_CONNECTOR_OR("OR"),
    ITEM_CONNECTOR_XOR("XOR"),

    //最后这个not需要做转换，他需要将它后面的完整的表达式用摩根定理处理
    ITEM_CONNECTOR_NOT("NOT");

    private String name;

    ItemConnector(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public boolean equals(ItemConnector c) {
        if (c == null) {
            return false;
        }

        return StringUtil.equals(this.name, c.name);
    }

    public static ItemConnector getConnector(String name) {
        if (!StringUtil.isNullOrEmpty(name)) {

            for (ItemConnector connector : ItemConnector.values()) {
                if (connector.getName().equals(name)) {
                    return connector;
                }
            }
        }

        return null;
    }
}
