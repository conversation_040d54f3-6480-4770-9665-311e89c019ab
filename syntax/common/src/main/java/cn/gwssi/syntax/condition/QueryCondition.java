package cn.gwssi.syntax.condition;

import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.GsonObject;
import cn.gwssi.common.common.pojo.IPSearchResult;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;

import java.util.List;
import java.util.Map;

public class QueryCondition<T extends GsonObject> {

    // 索引名称
    private String indexName;

    // 查询语句
    private String query;

    // 默认字段
    private String defaultField;

    // 图片检索条件，rescore 时需要设置
    private int windowSize;

    // 高亮检索条件
    private HighLightCondition highLightCondition;

    // distinct检索条件
    private CollapseCondition collapseCondition;

    // 排序字段
    private SortFieldList sortFields;

    private AggregationDim[] aggregationDims;

    private FieldList fieldList;

    private int size;

    private int from;

    private String scroll;

    private String scrollId;

    private int scrollSliceId;

    private int scrollSliceMax;

    private Map<String, List<String>> groups;

    private boolean isVectorSort;

    private boolean isUnique;

    /**
     * 其它参数，目前包含：
     * 1、语义相关，是否为号码
     * 2、语义相关，是否忽略背景技术
     * 3、语义相关，强化技术要素
     * 4、强制打分，track_score
     */
    private Map<String, Object> others;

    private ScrollConsumer<T> scrollConsumer;

    private Class<T> gsonClass;

    public QueryCondition(String indexName, Class<T> gsonClass) {
        this();
        this.indexName = indexName;
        this.gsonClass = gsonClass;
    }

    public QueryCondition() {
        this.indexName = "";
        this.query = "";
        this.defaultField = null;
        this.windowSize = -1;
        this.highLightCondition = null;
        this.collapseCondition = null;
        this.sortFields = null;
        this.aggregationDims = null;
        this.fieldList = null;
        this.size = 10;
        this.from = 0;
        this.scroll = null;
        this.scrollId = null;
        this.scrollSliceId = -1;
        this.scrollSliceMax = -1;
        this.groups = null;
        this.isVectorSort = false;
        this.isUnique = false;
        this.others = null;
        this.scrollConsumer = null;
        this.gsonClass = null;
    }

    @FunctionalInterface
    public interface ScrollConsumer<T extends GsonObject> {
        abstract boolean consume(IPSearchResult<T> result);
    }

    public String getIndexName() {
        return indexName;
    }

    public QueryCondition<T> setIndexName(String indexName) {
        this.indexName = indexName;
        return this;
    }

    public String getQuery() {
        return query;
    }

    public QueryCondition<T> setQuery(String query) {
        this.query = query;
        return this;
    }

    public String getDefaultField() {
        return defaultField;
    }

    public QueryCondition<T> setDefaultField(String defaultField) {
        this.defaultField = defaultField;
        return this;
    }

    public int getWindowSize() {
        return windowSize;
    }

    public QueryCondition<T> setWindowSize(int windowSize) {
        this.windowSize = windowSize;
        return this;
    }

    public HighLightCondition getHighLightCondition() {
        return highLightCondition;
    }

    public QueryCondition<T> setHighLightCondition(HighLightCondition highLightCondition) {
        this.highLightCondition = highLightCondition;
        return this;
    }

    public CollapseCondition getCollapseCondition() {
        return collapseCondition;
    }

    public QueryCondition<T> setCollapseCondition(CollapseCondition collapseCondition) {
        this.collapseCondition = collapseCondition;
        return this;
    }

    public SortFieldList getSortFields() {
        return sortFields;
    }

    public QueryCondition<T> setSortFields(SortFieldList sortFields) {
        this.sortFields = sortFields;
        return this;
    }

    public AggregationDim[] getAggregationDims() {
        return aggregationDims;
    }

    public QueryCondition<T> setAggregationDims(AggregationDim[] aggregationDims) {
        this.aggregationDims = aggregationDims;
        return this;
    }

    public FieldList getFieldList() {
        return fieldList;
    }

    public QueryCondition<T> setFieldList(FieldList fieldList) {
        this.fieldList = fieldList;
        return this;
    }

    public int getSize() {
        return size;
    }

    public QueryCondition<T> setSize(int size) {
        this.size = size;
        return this;
    }

    public int getFrom() {
        return from;
    }

    public QueryCondition<T> setFrom(int from) {
        this.from = from;
        return this;
    }

    public String getScroll() {
        return scroll;
    }

    public QueryCondition<T> setScroll(String scroll) {
        this.scroll = scroll;
        return this;
    }

    public String getScrollId() {
        return scrollId;
    }

    public QueryCondition<T> setScrollId(String scrollId) {
        this.scrollId = scrollId;
        return this;
    }

    public int getScrollSliceId() {
        return scrollSliceId;
    }

    public QueryCondition<T> setScrollSliceId(int scrollSliceId) {
        this.scrollSliceId = scrollSliceId;
        return this;
    }

    public int getScrollSliceMax() {
        return scrollSliceMax;
    }

    public QueryCondition<T> setScrollSliceMax(int scrollSliceMax) {
        this.scrollSliceMax = scrollSliceMax;
        return this;
    }

    public Map<String, List<String>> getGroups() {
        return groups;
    }

    public QueryCondition<T> setGroups(Map<String, List<String>> groups) {
        this.groups = groups;
        return this;
    }

    public Class<T> getGsonClass() {
        return gsonClass;
    }

    public QueryCondition<T> setGsonClass(Class<T> gsonClass) {
        this.gsonClass = gsonClass;
        return this;
    }

    public boolean isVectorSort() {
        return isVectorSort;
    }

    public QueryCondition<T> setVectorSort(boolean vectorSort) {
        this.isVectorSort = vectorSort;
        if (vectorSort) {
            this.windowSize = 0;
        }
        return this;
    }

    public boolean isUnique() {
        return isUnique;
    }

    public QueryCondition<T> setUnique(boolean unique) {
        isUnique = unique;
        return this;
    }

    public Map<String, Object> getOthers() {
        return others;
    }

    public QueryCondition<T> setOthers(Map<String, Object> others) {
        this.others = others;
        return this;
    }

    public ScrollConsumer<T> getScrollConsumer() {
        return scrollConsumer;
    }

    public QueryCondition<T> setScrollConsumer(ScrollConsumer<T> scrollConsumer) {
        this.scrollConsumer = scrollConsumer;
        return this;
    }
}
