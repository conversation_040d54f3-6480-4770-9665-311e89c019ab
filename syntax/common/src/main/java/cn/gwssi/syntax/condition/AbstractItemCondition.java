package cn.gwssi.syntax.condition;

import cn.gwssi.syntax.condition.constants.ItemOperator;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractItemCondition<T> extends IPCondition {
	/**
	 * 是否取反（not修饰）
	 */
	private boolean isReverse = false;

	private boolean isNestedField = false;

	private String nestedName;

	// 字段名
	private String fieldName;
	
	// 值
	private List<T> fieldValue;

	// 操作符
	private ItemOperator operator;

	/**
	 * 
	 * @param fieldName
	 */
	protected AbstractItemCondition(String fieldName) {
		this.fieldName = fieldName;
	}
	
	/**
	 * 
	 * @param fieldName
	 * @param fieldValue
	 */
	public AbstractItemCondition(String fieldName, T fieldValue) {
		this.fieldName = fieldName;
		List<T> list = new ArrayList<T>();
		list.add(fieldValue);
		this.fieldValue = list;
	}

    /**
     *
     * @param fieldName
     * @param fieldValue
     */
    public AbstractItemCondition(String fieldName, T fieldValue, boolean isReverse) {
        this(fieldName, fieldValue);
        this.isReverse = isReverse;
    }
	
	/**
	 * 
	 * @param fieldName
	 * @param fieldValue
	 */
	public AbstractItemCondition(String fieldName, List<T> fieldValue) {
		this.fieldName = fieldName;
		this.fieldValue = fieldValue;
	}
	
	public boolean isReverse() {
		return isReverse;
	}

	public void setReverse(boolean isReverse) {
		this.isReverse = isReverse;
	}

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public List<T> getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(List<T> fieldValue) {
		this.fieldValue = fieldValue;
	}

	public ItemOperator getOperator() {
		return operator;
	}

	public void setOperator(ItemOperator operator) {
		this.operator = operator;
	}

	public boolean isNestedField() {
		return isNestedField;
	}

	public void setNestedField(boolean nestedField) {
		isNestedField = nestedField;
	}

	public String getNestedName() {
		return nestedName;
	}

	public void setNestedName(String nestedName) {
		this.nestedName = nestedName;
	}
}
