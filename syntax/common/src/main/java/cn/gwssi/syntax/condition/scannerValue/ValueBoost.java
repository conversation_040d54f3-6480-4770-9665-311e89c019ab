package cn.gwssi.syntax.condition.scannerValue;

import java.io.Serializable;

public class ValueBoost implements Serializable {

    private String value;
    private int count;

    public ValueBoost() {}

    public ValueBoost(String value, int count) {
        this.count = count;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
