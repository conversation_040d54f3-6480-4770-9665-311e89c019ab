package cn.gwssi.syntax.condition;

import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;
import org.nustaq.serialization.annotations.Version;

import java.io.Serializable;
import java.util.List;

/**
 * 这个 @Version 是编码、解码的时候起作用的，用来检验 client 和插件调用的 Condition 是否是同一套
 * 如果两边对应不上，解码会报错 NullPointerException
 * @Version 中的数字，不能删除，不能减小，只能增大
 * 每次修改 Condition 或其引用的属性类，记得修改这个值
 */
public class Condition implements Serializable {
    private static final long serialVersionUID = 6135393326504382826L;

    // 布尔检索条件
    @Version(1)
    private IPCondition ipCondition;

    // 语义检索条件
    @Version(1)
    private SemanticCondition semanticCondition;

    // 图片检索条件
    @Version(1)
    private List<PictureCondition> pictureCondition;

    // 图片检索条件，rescore 时需要设置
    @Version(1)
    private int pictureWindowSize;

    // 高亮检索条件
    @Version(1)
    private HighLightCondition highLightCondition;

    // distinct检索条件
    @Version(1)
    private CollapseCondition collapseCondition;

    // 检索条件的类型、检索条件连接符
    @Version(1)
    private List<QueryRelation> queryRelations;

    // 排序字段
    @Version(1)
    private SortFieldList sortFields;

    @Version(1)
    private List<AggregationDim> aggregationDims;

    @Version(1)
    private FieldList fieldList;

    @Version(1)
    private int size;

    @Version(1)
    private int from;

    @Version(1)
    private String phraseSearch;

    @Version(1)
    private String scroll;

    @Version(1)
    private int scrollSliceId;

    @Version(1)
    private int scrollSliceMax;

    @Version(1)
    private boolean hasVector;

    // 布尔、图像、语义，三种检索之间的关系
    public static class QueryRelation implements Serializable {
        private static final long serialVersionUID = 4405876149005087683L;
        private QueryType queryType;
        private QueryConnector connector;
        private float boost;

        public QueryRelation() {}

        public QueryRelation(QueryType queryType, QueryConnector connector) {
            this(queryType, connector, -1);
        }

        public QueryRelation(QueryType queryType, QueryConnector connector, float boost) {
            this.queryType = queryType;
            this.connector = connector;
            this.boost = boost;
        }

        public QueryType getQueryType() {
            return queryType;
        }

        public void setQueryType(QueryType queryType) {
            this.queryType = queryType;
        }

        public QueryConnector getConnector() {
            return connector;
        }

        public void setConnector(QueryConnector connector) {
            this.connector = connector;
        }

        public float getBoost() {
            return boost;
        }

        public void setBoost(float boost) {
            this.boost = boost;
        }
    }

    // 检索条件的类型
    public enum QueryType implements Serializable {
        EXPRESS("express"),
        IMAGE("image"),
        SEMANTIC("semantic");

        private String type;

        QueryType(String type) {
            this.type = type;
        }

        public String getValue() {
            return type;
        }
    }

    // 检索条件连接符
    public enum QueryConnector implements Serializable {
        AND("and"),
        OR("or"),
        NOT("not");

        private String connector;

        QueryConnector(String connector) {
            this.connector = connector;
        }

        public String getValue() {
            return connector;
        }
    }

    public IPCondition getIpCondition() {
        return ipCondition;
    }

    public void setIpCondition(IPCondition ipCondition) {
        this.ipCondition = ipCondition;
    }

    public SemanticCondition getSemanticCondition() {
        return semanticCondition;
    }

    public void setSemanticCondition(SemanticCondition semanticCondition) {
        this.semanticCondition = semanticCondition;
    }

    public List<PictureCondition> getPictureCondition() {
        return pictureCondition;
    }

    public void setPictureCondition(List<PictureCondition> pictureCondition) {
        this.pictureCondition = pictureCondition;
    }

    public int getPictureWindowSize() {
        return pictureWindowSize;
    }

    public void setPictureWindowSize(int pictureWindowSize) {
        this.pictureWindowSize = pictureWindowSize;
    }

    public SortFieldList getSortFields() {
        return sortFields;
    }

    public void setSortFields(SortFieldList sortFields) {
        this.sortFields = sortFields;
    }

    public List<AggregationDim> getAggregationDims() {
        return aggregationDims;
    }

    public void setAggregationDims(List<AggregationDim> aggregationDims) {
        this.aggregationDims = aggregationDims;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getFrom() {
        return from;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public FieldList getFieldList() {
        return fieldList;
    }

    public void setFieldList(FieldList fieldList) {
        this.fieldList = fieldList;
    }

    public void addToFieldList(String field) {
        if (null == this.fieldList) {
            this.fieldList = new FieldList();
        }

        this.fieldList.add(field);
    }

    public List<QueryRelation> getQueryRelations() {
        return queryRelations;
    }

    public void setQueryRelations(List<QueryRelation> queryRelations) {
        this.queryRelations = queryRelations;
    }

    public String getPhraseSearch() {
        return phraseSearch;
    }

    public void setPhraseSearch(String phraseSearch) {
        this.phraseSearch = phraseSearch;
    }

    public HighLightCondition getHighLightCondition() {
        return highLightCondition;
    }

    public void setHighLightCondition(HighLightCondition highLightCondition) {
        this.highLightCondition = highLightCondition;
    }

    public CollapseCondition getCollapseCondition() {
        return collapseCondition;
    }

    public void setCollapseCondition(CollapseCondition collapseCondition) {
        this.collapseCondition = collapseCondition;
    }

    public String getScroll() {
        return scroll;
    }

    public void setScroll(String scroll) {
        this.scroll = scroll;
    }

    public int getScrollSliceId() {
        return scrollSliceId;
    }

    public void setScrollSliceId(int scrollSliceId) {
        this.scrollSliceId = scrollSliceId;
    }

    public int getScrollSliceMax() {
        return scrollSliceMax;
    }

    public void setScrollSliceMax(int scrollSliceMax) {
        this.scrollSliceMax = scrollSliceMax;
    }

    public boolean hasVector() {
        return hasVector;
    }

    public void setHasVector(boolean hasVector) {
        this.hasVector = hasVector;
    }
}
