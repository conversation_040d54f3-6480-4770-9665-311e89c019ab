package cn.gwssi.syntax.condition;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @time 17:44
 */
public class PictureCondition extends Object implements Serializable {

    // 图片字段名称
    private String imageColumnName;

    // 图片，多个
    private List<float[]> imageColumnFea;

    public PictureCondition() {}

    public PictureCondition(String imageColumnName, List<float[]> imageColumnFea) {
        this.imageColumnName = imageColumnName;
        this.imageColumnFea = imageColumnFea;
    }

    public String getImageColumnName() {
        return imageColumnName;
    }

    public void setImageColumnName(String imageColumnName) {
        this.imageColumnName = imageColumnName;
    }

    public List<float[]> getImageColumnFea() {
        return imageColumnFea;
    }

    public void setImageColumnFea(List<float[]> imageColumnFea) {
        this.imageColumnFea = imageColumnFea;
    }
}
