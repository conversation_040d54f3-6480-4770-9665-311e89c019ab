package cn.gwssi.syntax.condition.offset;

import java.io.Serializable;

public class ConditionOffset implements Serializable {

    // 开始行
    private int startLine;
    
    // 结束行
    private int endLine;

    // 开始字符的位置，忽略换行
    private int start;

    // 结束字符的位置，忽略换行
    private int end;

    // 开始字符的位置，行内的第几个
    private int startInLine;

    // 结束字符的位置，行内的第几个
    private int endInLine;

    // 字符长度，跨行暂时无法计算
    private int length;

    // 字符
    private String chars;

    public ConditionOffset() {
        this.startLine = -1;
        this.endLine = -1;
        this.start = -1;
        this.end = -1;
        this.startInLine = -1;
        this.endInLine = -1;
        this.length = -1;
    }

    public ConditionOffset(int startLine, int endLine, int start, int end, int startInLine, int endInLine) {
        this.startLine = startLine;
        this.endLine = endLine;
        this.start = start;
        this.end = end;
        this.startInLine = startInLine;
        this.endInLine = endInLine;
        this.length = -1;
    }

    public int getStartLine() {
        return startLine;
    }

    public void setStartLine(int startLine) {
        this.startLine = startLine;
    }

    public int getEndLine() {
        return endLine == -1 ? startLine : endLine;
    }

    public void setEndLine(int endLine) {
        this.endLine = endLine;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getEnd() {
        return end == -1 ? start : end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public int getStartInLine() {
        return startInLine;
    }

    public void setStartInLine(int startInLine) {
        this.startInLine = startInLine;
    }

    public int getEndInLine() {
        return endInLine == -1 ? startInLine : endInLine;
    }

    public void setEndInLine(int endInLine) {
        this.endInLine = endInLine;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getChars() {
        return chars;
    }

    public ConditionOffset setChars(String chars) {
        this.chars = chars;
        return this;
    }
}
