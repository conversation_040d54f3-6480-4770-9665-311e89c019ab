package cn.gwssi.syntax.condition;

import cn.gwssi.syntax.condition.constants.ItemOperator;
import cn.gwssi.syntax.condition.scannerValue.Value;

/**
 * <AUTHOR>
 */
public class ScannerItemCondition extends IPCondition {
    /**
     * 是否取反（not修饰）
     */
    private boolean isReverse = false;

    // 字段名
    private String fieldName;

    // 值
    private Value itemValue;

    // 操作符
    private ItemOperator operator;

    public ScannerItemCondition() {}

    public boolean isReverse() {
        return isReverse;
    }

    public void setReverse(boolean isReverse) {
        this.isReverse = isReverse;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public Value getItemValue() {
        return itemValue;
    }

    public void setItemValue(Value itemValue) {
        this.itemValue = itemValue;
    }

    public ItemOperator getOperator() {
        return operator;
    }

    public void setOperator(ItemOperator operator) {
        this.operator = operator;
    }
}
