package cn.gwssi.syntax.condition.constants;

import cn.gwssi.common.exception.code.ErrorCode0;
import cn.gwssi.common.exception.code.ErrorCode1;
import cn.gwssi.common.exception.code.ErrorCode2;
import cn.gwssi.common.exception.code.ErrorCode3;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPConditionErrCodes {

	/**
	 * 解析表达式，少[)]错误
	 */
	public static final ErrorCode3 IP_SCANNER_DEFECT1 = new ErrorCode3("exp001", "检索表达式中{0}存在错误,{1}不匹配 位置:{2}");

	/**
	 * 解析表达式时，操作符前面少表达式检索字段
	 */
	public static final ErrorCode2 IP_SCANNER_DEFECT2 = new ErrorCode2("exp002", "检索表达式 {0} 存在错误, 位置: {1} 前面缺少检索字段");
	
	/**
	 * 解析表达式时，“+=”运算符前面的检索字段不是用','分割
	 */
	public static final ErrorCode2 IP_SCANNER_DEFECT3 = new ErrorCode2("exp003", "检索表达式{0}存在错误, 位置:{1}前面的检索字段存在错误");

	/**
	 * 解析表达式时，表达式中缺少逻辑运算符
	 */
	public static final ErrorCode2 IP_CONNECTOR_DEFECT1 = new ErrorCode2("exp004", "检索表达式中{0}后面存在错误,缺少逻辑运算符位置:{1}");

	/**
	 * 表达式中，字段与运算符不匹配
	 */
	public static final ErrorCode2 IP_CONNECTOR_UNMATCH = new ErrorCode2("exp005", "检索表达式错误, 字段 {0} 不支持逻辑运算符 {1}");
	
	/**
	 * 表达式中，字段的值为空
	 */
	public static final ErrorCode1 EXPRESSION_VALUE_NULL = new ErrorCode1("exp101", "检索表达式错误, 字段{0}的值为空！");

	/**
	 * 表达式中，日期字段的格式不支持
	 */
	public static final ErrorCode1 EXPRESSION_DATE_FORMAT_ERROR = new ErrorCode1("exp110", "检索表达式错误, 字段 {0} 为不支持的日期格式！");

    /**
     * 表达式中，日期字段的格式不支持
     */
    public static final ErrorCode1 EXPRESSION_DATE_OPERATOR_ERROR = new ErrorCode1("exp1101", "检索表达式错误, to 的操作符只支持=！");

    /**
     * 表达式中，数字字段的格式不支持
     */
    public static final ErrorCode1 EXPRESSION_NUMBER_FORMAT_ERROR = new ErrorCode1("exp111", "检索表达式错误, 字段 {0} 为不支持的数字！");

	/**
	 * 表达式中，日期字段的两个值不匹配
	 */
	public static final ErrorCode1 EXPRESSION_DATE_UNMATCH = new ErrorCode1("exp112", "检索表达式错误, 字段 {0} 为日期字段，前后精确度必须相同！");

    /**
     * 表达式中，数字和操作符不匹配
     */
    public static final ErrorCode1 EXPRESSION_NUMBER_UNMATCH = new ErrorCode1("exp113", "检索表达式错误, 字段 {0} 为不支持的数字格式！");

	/**
	 * 表达式中，日期字段的内容不合法
	 */
	public static final ErrorCode2 EXPRESSION_DATE_INVALID = new ErrorCode2("exp110", "检索表达式错误, 字段{0} 为日期字段，值{1}不符合规则！");

	/**
	 * 表达式中，小文本字段有不匹配的双引号
	 */
	public static final ErrorCode2 EXPRESSION_TINY_TEXT_INVALID1 = new ErrorCode2("exp121", "检索表达式错误, 字段{0}为小文本字段，值{1}中双引号不匹配！");

	/**
	 * 表达式中，小文本字段有不匹配的单引号
	 */
	public static final ErrorCode2 EXPRESSION_TINY_TEXT_INVALID2 = new ErrorCode2("exp122", "检索表达式错误, 字段{0}为小文本字段，值{1}中单引号不匹配！");

	/**
	 * 表达式中，长文本字段有不匹配的单引号
	 */
	public static final ErrorCode2 EXPRESSION_LONG_TEXT_INVALID2 = new ErrorCode2("exp132", "检索表达式错误, 字段{0}为长文本字段，值{1}中单引号不匹配！");

    /**
     * 表达式中，长文本字段不支持in/terms
     */
    public static final ErrorCode1 EXPRESSION_LONG_TEXT_INVALID3 = new ErrorCode1("exp133", "检索表达式错误, 字段 {0} 为文本字段，不能使用算符 in！");

	/**
	 * 表达式中使用不了不存在的字段
	 */
	public static final ErrorCode1 EXPRESSION_NOT_EXIST_FIELD = new ErrorCode1("exp200", "检索表达式错误, 使用了不存在的字段 {0}！");
	
	public static final ErrorCode1 EXPRESSION_SPANNEAR_ERROR = new ErrorCode1("exp201", "检索表达式错误, {0}中位置检索使用不合法！");

    /**
     * 表达式中，操作符不合法
     */
    public static final ErrorCode1 EXPRESSION_OPERATOR_ERROR = new ErrorCode1("exp301", "检索表达式错误, 字段 {0} 的操作符只支持= ");

    /**
     * 表达式中，地理位置的值不合法
     */
    public static final ErrorCode1 EXPRESSION_GEO_NUMBER_LENGTH_ERROR = new ErrorCode1("exp3020", "检索表达式错误, 字段 {0} 的地理位置必须是2-3个数字！");

    /**
     * 表达式中，地理位置的值不合法
     */
    public static final ErrorCode1 EXPRESSION_GEO_NUMBER_ERROR = new ErrorCode1("exp302", "检索表达式错误, 字段 {0} 的地理位置必须是数字！");

    /**
     * 表达式中，图片base64不合法
     */
    public static final ErrorCode0 EXPRESSION_IMAGE_BASE64_ERROR = new ErrorCode0("exp303", "检索表达式错误, 图片base64值不合法！");
}
