package cn.gwssi.syntax.condition;

import cn.gwssi.syntax.condition.constants.IPConditionConstants;
import cn.gwssi.syntax.condition.constants.ItemConnector;
import cn.gwssi.syntax.condition.offset.ConditionOffset;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IPCondition implements Serializable {
    /**
     * 子条件
     */
    private List<IPCondition> subConditions = new ArrayList<>();

    /**
     * 子条件的连接符
     */
    private List<ItemConnector> connectors = new ArrayList<ItemConnector>();

    // 连续值，用来处理虚拟字段。连续值 condition 节点下的所有子节点，使用同一个字段
    private String serialField;
    // 连续值，用来处理虚拟字段。连续值 condition 的操作符是不是 !=
    private boolean equalOrNot;

    // 字段、操作符、值的行、列信息
    private Map<IPConditionConstants.ConditionType, ConditionOffset> offsets = new HashMap<>();

    public void addSubCondition(IPCondition subCondition) {
        subConditions.add(subCondition);
    }

    public void addConnector(ItemConnector connector) {
        connectors.add(connector);
    }

    public ConditionOffset getOffsetOf(IPConditionConstants.ConditionType type) {
        return this.offsets.get(type);
    }

    public void setOffsetOf(IPConditionConstants.ConditionType type, ConditionOffset offset) {
        this.offsets.put(type, offset);
    }

    /**
     * 判断该节点是否是叶子节点
     */
    public boolean isLeafCondition() {
        return this instanceof StatementItemCondition || this instanceof AbstractItemCondition
                || this instanceof ScannerItemCondition;
    }

    @Override
    public String toString() {
        StringBuilder buf = new StringBuilder(this.getClass().getSimpleName()).append("\r\n");
        int seq = 1;
        String indentation = "";
        if (!subConditions.isEmpty()) {
            int i;
            for (i = 0; i < subConditions.size() - 1; i++) {
                IPCondition child = subConditions.get(i);
                this.appendChild(buf, indentation, seq * 10 + i, child);

                ItemConnector connector = connectors.get(i);
                buf.append(indentation).append(connector.getName()).append("\r\n");
            }
            IPCondition child = subConditions.get(i);
            this.appendChild(buf, indentation, seq * 10 + i, child);
        }

        return buf.toString();
    }

    protected void appendChild(StringBuilder buf, String indentation, int seq, IPCondition ipCondition) {
        String inIndentation = indentation + "    ";
        buf.append(indentation).append("X").append(seq).append("\r\n");
        if (!ipCondition.isLeafCondition()) {
            List<IPCondition> insubConditions = ipCondition.getSubConditions();
            List<ItemConnector> inConnectors = ipCondition.getConnectors();
            if (!insubConditions.isEmpty()) {
                int i = 0;
                IPCondition child = null;
                for (i = 0; i < insubConditions.size() - 1; i++) {
                    child = insubConditions.get(i);
                    this.appendChild(buf, inIndentation, seq * 10 + i, child);

                    ItemConnector connector = inConnectors.get(i);
                    buf.append(inIndentation).append(connector.getName()).append("\r\n");
                }
                child = insubConditions.get(i);
                this.appendChild(buf, inIndentation, seq * 10 + i, child);
            }
        } else {
            String clazzName = ipCondition.getClass().getSimpleName();
            buf.append(inIndentation).append("clazz=[").append(clazzName).append("];");
            if (ipCondition instanceof StatementItemCondition) {
                StatementItemCondition itemCondition = (StatementItemCondition) ipCondition;
                buf.append("itemName=[").append(itemCondition.getFieldName()).append("];itemValue=[")
                        .append(itemCondition.getValue().getValue()).append("]\r\n");
            } else if (ipCondition instanceof AbstractItemCondition) {
                AbstractItemCondition itemCondition = (AbstractItemCondition) ipCondition;
                List fieldValues = itemCondition.getFieldValue();
                buf.append("itemName=[").append(itemCondition.getFieldName()).append("];itemValue=[");
                if (null != fieldValues) {
                    for (Object o : fieldValues) {
                        buf.append(o);
                        buf.append(",");
                    }
                }
                buf.append("]\r\n");
            } else {
                ScannerItemCondition itemCondition = (ScannerItemCondition) ipCondition;
                buf.append("itemName=[").append(itemCondition.getFieldName()).append("];itemValue=[")
                        .append(itemCondition.getItemValue().toString()).append("]\r\n");
            }

        }
    }

    public List<IPCondition> getSubConditions() {
        return subConditions;
    }

    public List<ItemConnector> getConnectors() {
        return connectors;
    }

    public void setConnectors(List<ItemConnector> connectors) {
        this.connectors = connectors;
    }

    public Map<IPConditionConstants.ConditionType, ConditionOffset> getOffsets() {
        return offsets;
    }

    public void setOffsets(Map<IPConditionConstants.ConditionType, ConditionOffset> offsets) {
        this.offsets = offsets;
    }

    public String getSerialField() {
        return serialField;
    }

    public void setSerialField(String serialField) {
        this.serialField = serialField;
    }

    public boolean isEqualOrNot() {
        return equalOrNot;
    }

    public void setEqualOrNot(boolean equalOrNot) {
        this.equalOrNot = equalOrNot;
    }
}
