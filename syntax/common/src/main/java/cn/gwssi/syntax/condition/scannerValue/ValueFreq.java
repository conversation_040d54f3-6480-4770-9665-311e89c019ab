package cn.gwssi.syntax.condition.scannerValue;

import cn.gwssi.syntax.condition.constants.ItemOperator;

import java.io.Serializable;

public class ValueFreq implements Serializable {

    private String value;
    private ItemOperator operator;
    private int count;
    private boolean hasWildCard;
    private boolean hasZh;

    public ValueFreq() {
    }

    public ValueFreq(String value, ItemOperator operator, int count, boolean hasWildCard, boolean hasZh) {
        this.value = value;
        this.operator = operator;
        this.count = count;
        this.hasWildCard = hasWildCard;
        this.hasZh = hasZh;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public ItemOperator getOperator() {
        return operator;
    }

    public void setOperator(ItemOperator operator) {
        this.operator = operator;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public boolean hasWildCard() {
        return hasWildCard;
    }

    public void setHasWildCard(boolean hasWildCard) {
        this.hasWildCard = hasWildCard;
    }

    public boolean hasZh() {
        return hasZh;
    }

    public void setHasZh(boolean hasZh) {
        this.hasZh = hasZh;
    }
}
