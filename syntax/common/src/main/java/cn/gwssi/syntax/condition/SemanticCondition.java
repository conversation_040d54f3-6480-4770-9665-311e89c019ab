package cn.gwssi.syntax.condition;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class SemanticCondition extends Object implements Serializable {

    // 语义字段名称
    private List<String> semanticFields;

    // 语义特征值
    private float[] semanticFea;

    public SemanticCondition() {}

    public SemanticCondition(List<String> semanticFields, float[] semanticFea) {
        this.semanticFields = semanticFields;
        this.semanticFea = semanticFea;
    }

    public List<String> getSemanticFields() {
        return semanticFields;
    }

    public void setSemanticFields(List<String> semanticFields) {
        this.semanticFields = semanticFields;
    }

    public float[] getSemanticFea() {
        return semanticFea;
    }

    public void setSemanticFea(float[] semanticFea) {
        this.semanticFea = semanticFea;
    }
}
