package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;
import cn.gwssi.common.util.NumberUtil;

/**
 * 二维分析结果
 * 
 * <AUTHOR>
 *
 */
public class TwoDimCountResult extends DimCountResult {
	/**
	 * 组名
	 */
	private String groupName = null;
	
	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}	
	
	public TwoDimCountResult setCountOther(String groupName, long count) {
	    this.groupName = groupName;
	    this.setName(IPConstants.AGG_OTHER);
	    this.setCount(count);
	    return this;
    }
	
	@Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}
}
