package cn.gwssi.common.common.pojo.relationship;


import cn.gwssi.common.common.array.SortArrayBase;

/**
 * 
 * <AUTHOR>
 *
 */
public class RelationshipSortByNum extends SortArrayBase<RelationshipPojo> {
	private static final long serialVersionUID = -2214321093251241063L;

	@Override
	protected int compare(RelationshipPojo e1, RelationshipPojo e2) {
		return e2.getNumber() - e1.getNumber();
	}

	@Override
	protected int compare(RelationshipPojo e1, String number) {
		return Integer.parseInt(number) - e1.getNumber();
	}

	@Override
	protected RelationshipPojo[] makeArray(int len) {
		return new RelationshipPojo[len];
	}
}
