package cn.gwssi.common.common.pojo;


import cn.gwssi.common.util.StringUtil;

import java.util.ArrayList;

/**
 * 
 * <AUTHOR>
 *
 */
public class FieldList extends ArrayList<String> {
	private static final long serialVersionUID = 2105006050054808811L;
	
	/**
	 * 
	 */
	public FieldList() {
	}
	
	/**
	 * 
	 * @param fields
	 */
	public FieldList(String[] fields) {
		if(fields == null || fields.length == 0) {
			return ;
		}
		for(String field : fields) {
			if(StringUtil.isNullOrEmpty(field)) {
				continue;
			}
			this.add(field);
		}
	}
}
