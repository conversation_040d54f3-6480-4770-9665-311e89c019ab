package cn.gwssi.common.common.pojo.aggregation;


import cn.gwssi.common.common.IPErrCodes;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.util.StringUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AggregationDim implements Serializable {
    private static final long serialVersionUID = -1537132965595744216L;
    /**
     * 字段名
     */
    private String fieldName = "";

    /**
     * 前*个结果, 缺省表示全部
     */
    private int size = 1000;
    private int shardSize = 0;

    /**
     * 是否包含其他
     */
    private boolean hasOther = false;

    /**
     * 最少数量
     */
    private long minDocCount = 1;

    /**
     * distinct 字段
     */
    private String distinctField;

    private String nestedFieldName = "";

    /**
     * 过滤值
     */
    private List<String> filterValues = null;

    private AggregationDim aggregationDim;

    private List<AggregationDim> aggregationDims;

    /**
     * 排序方式
     */
    private AggrOrder order = AggrOrder.ORDER_COUNT_DESC;

    public AggregationDim() {}

    public AggregationDim(String fieldName) {
        this.fieldName = fieldName;
    }

    public AggregationDim(String fieldName, int size) {
        this.fieldName = fieldName;
        this.size = size;
    }

    public AggregationDim(String fieldName, int size, AggrOrder order) {
        this.fieldName = fieldName;
        this.size = size;
        this.order = order;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String setFieldName(String fieldName) {
        return this.fieldName = fieldName;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getShardSize() {
        return shardSize;
    }

    public void setShardSize(int shardSize) {
        this.shardSize = shardSize;
    }

    public boolean isHasOther() {
        return hasOther;
    }

    public void setHasOther(boolean hasOther) {
        this.hasOther = hasOther;
    }

    public List<String> getFilterValue() {
        return filterValues;
    }

    public void addFilterValue(String filterValue) {
        if (filterValues == null) {
            filterValues = new ArrayList<>();
        }
        this.filterValues.add(filterValue);
    }

    public void setFilterValues(String filterValues) throws IPException {
        if (StringUtil.isNullOrEmpty(filterValues)) {
            return;
        }
        this.filterValues = new ArrayList<>();
        String[] filterArray = filterValues.split(",");
        for (String filter : filterArray) {
            filter = filter.trim();
            if (filter.compareTo("") != 0) {
                this.filterValues.add(filter);
            }
        }
        if (size != 0 && size < this.filterValues.size()) {
            throw IPErrCodes.IP_AGGREGATION_DIM_ERROR2.exception(filterValues, String.valueOf(this.size));
        }
    }

    public void setFilterValues(List<String> filterValues) {
        this.filterValues = filterValues;
    }

    public AggrOrder getAggrOrder() {
        return order;
    }

    public void setAggrOrder(AggrOrder order) {
        this.order = order;
    }

    public AggregationDim getAggregationDim() {
        return aggregationDim;
    }

    public void setAggregationDim(AggregationDim aggregationDim) {
        this.aggregationDim = aggregationDim;
        this.addAggregationDim(aggregationDim);
    }

    public List<AggregationDim> getAggregationDims() {
        return aggregationDims;
    }

    public void setAggregationDims(List<AggregationDim> aggregationDims) {
        this.aggregationDims = aggregationDims;
    }

    public void addAggregationDim(AggregationDim aggregationDim) {
        if (null == this.aggregationDims) {
            this.aggregationDims = new ArrayList<>();
        }

        this.aggregationDims.add(aggregationDim);
    }

    public String getDistinctField() {
        return distinctField;
    }

    public void setDistinctField(String distinctField) {
        this.distinctField = distinctField;
        this.addAggregationDim(new AggeragationCardinalityDim(distinctField));
    }

    public String getNestedFieldName() {
        return nestedFieldName;
    }

    public void setNestedFieldName(String nestedFieldName) {
        this.nestedFieldName = nestedFieldName;
    }

    public String path() {
        return this.nestedFieldName;
    }

    public AggregationDim child() {
        return this.aggregationDim;
    }

    public long getMinDocCount() {
        return minDocCount;
    }

    public void setMinDocCount(long minDocCount) {
        this.minDocCount = minDocCount;
    }
}

