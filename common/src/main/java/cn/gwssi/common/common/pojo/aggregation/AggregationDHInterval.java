package cn.gwssi.common.common.pojo.aggregation;

import java.io.Serializable;
import java.util.Objects;

public class AggregationDHInterval implements Serializable {
    private static final long serialVersionUID = -6786228145684462103L;

    public static final AggregationDHInterval SECOND = new AggregationDHInterval("1s");
    public static final AggregationDHInterval MINUTE = new AggregationDHInterval("1m");
    public static final AggregationDHInterval HOUR = new AggregationDHInterval("1h");
    public static final AggregationDHInterval DAY = new AggregationDHInterval("1d");
    public static final AggregationDHInterval WEEK = new AggregationDHInterval("1w");
    public static final AggregationDHInterval MONTH = new AggregationDHInterval("1M");
    public static final AggregationDHInterval QUARTER = new AggregationDHInterval("1q");
    public static final AggregationDHInterval YEAR = new AggregationDHInterval("1y");
    private final String expression;

    public static AggregationDHInterval seconds(int sec) {
        return new AggregationDHInterval(sec + "s");
    }

    public static AggregationDHInterval minutes(int min) {
        return new AggregationDHInterval(min + "m");
    }

    public static AggregationDHInterval hours(int hours) {
        return new AggregationDHInterval(hours + "h");
    }

    public static AggregationDHInterval days(int days) {
        return new AggregationDHInterval(days + "d");
    }

    public static AggregationDHInterval weeks(int weeks) {
        return new AggregationDHInterval(weeks + "w");
    }

    public AggregationDHInterval(String expression) {
        this.expression = expression;
    }

    @Override
    public String toString() {
        return this.expression;
    }

    @Override
    public int hashCode() {
        return Objects.hash(new Object[]{this.expression});
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        } else if (this.getClass() != obj.getClass()) {
            return false;
        } else {
            AggregationDHInterval other = (AggregationDHInterval)obj;
            return Objects.equals(this.expression, other.expression);
        }
    }
}
