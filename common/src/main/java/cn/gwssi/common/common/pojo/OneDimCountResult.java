package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;

/**
 * 一维分析结果
 * 
 * <AUTHOR>
 *
 */
public class OneDimCountResult extends DimCountResult {

	public OneDimCountResult setCountOther(long count) {
	    this.setName(IPConstants.AGG_OTHER);
	    this.setCount(count);
	    return this;
    }

    @Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}		
	
}
