package cn.gwssi.common.common.pojo;


import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class Sort<PERSON>ield implements Serializable {
    private String fieldName;
    private IPSorting order = IPSorting.DESC;
    private Object missing;
    private String customOrder;

    public SortField(String fieldName) {
        this.fieldName = fieldName;
    }

    public SortField(String fieldName, IPSorting order) {
        this.fieldName = fieldName;
        this.order = order;
    }

    public SortField(String fieldName, String customOrder) {
        this.fieldName = fieldName;
        this.customOrder = customOrder;

        if (IPSorting.DESC.name.equals(customOrder)) {
            this.order = IPSorting.DESC;
        } else if (IPSorting.ASC.name.equals(customOrder)) {
            this.order = IPSorting.ASC;
        } else {
            this.order = null;
        }
    }

    public String getFieldName() {
        return fieldName;
    }

    public IPSorting getOrder() {
        return order;
    }

    public String getCustomOrder() {
        return customOrder;
    }

    public Object getMissing() {
        return missing;
    }

    /**
     * @param m should be a Missing object (LAST or FIRST) or a custom value
     *          (String, Integer, Double, ...) that will be used for missing docs as the sort value
     */
    public void setMissing(Object m) {
        this.missing = m;
    }

    public enum IPSorting {
        ASC("asc"),
        DESC("desc");

        private final String name;

        private IPSorting(String s) {
            name = s;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum IPMissing {
        LAST("_last"),
        FIRST("_first");

        private final String name;

        private IPMissing(String s) {
            name = s;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    @Override
    public SortField clone() {
        SortField sort = new SortField(this.fieldName);
        sort.setMissing(this.missing);
        sort.customOrder = this.getCustomOrder();
        sort.order = this.getOrder();
        return sort;
    }
}
