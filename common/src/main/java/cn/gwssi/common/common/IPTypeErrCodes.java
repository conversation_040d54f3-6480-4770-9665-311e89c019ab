package cn.gwssi.common.common;

import cn.gwssi.common.exception.code.ErrorCode0;
import cn.gwssi.common.exception.code.ErrorCode1;
import cn.gwssi.common.exception.code.ErrorCode2;
import cn.gwssi.common.exception.code.ErrorCode3;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPTypeErrCodes {
	public static final String IP_INDEX_NAME = "索引名称";
	public static final String IP_TYPYE_NAME = "类型名称";
	public static final String IP_INDEX_ID = "主键";

	/**
	 * 配置文件错误
	 */
	public static final ErrorCode1 IP_CFG_ERROR0 = new ErrorCode1("cfg000", "读取配置文件{0}时出现错误");
	public static final ErrorCode2 IP_CFG_ERROR1 = new ErrorCode2("cfg001", "解析配置文件{0}时出现错误， 错误信息{1}");

	/**
	 * 索引数据错误
	 */
	public static final ErrorCode0 IP_INDEX_ERROR0 = new ErrorCode0("idx000", "索引数据错误");
	public static final ErrorCode1 IP_INDEX_ERROR1 = new ErrorCode1("idx001", "索引数据错误，错误信息{0}");


	/**
	 * 更新数据错误
	 */
	public static final ErrorCode0 IP_UPDATE_ERROR0 = new ErrorCode0("upd000", "更新数据错误");
	public static final ErrorCode1 IP_UPDATE_ERROR1 = new ErrorCode1("upd001", "更新数据错误，错误信息{0}");


	/**
	 * 检索数据错误
	 */
	public static final ErrorCode1 IP_QUERY_ERROR1 = new ErrorCode1("qry001", "检索数据时错误，错误信息{0}");

	/**
	 * 检索数据时索引名称为空
	 */
	public static final ErrorCode0 IP_QUERY_INDEX_NAME_NULL = new ErrorCode0("qry011", "检索数据时错误，索引名为空");

	/**
	 * 检索数据时类型名称为空
	 */
	public static final ErrorCode0 IP_QUERY_TYPE_NAME_NULL = new ErrorCode0("qry012", "检索数据时错误，类型名称为空");


	/**
	 * Count数据错误
	 */
	public static final ErrorCode1 IP_COUNT_ERROR1 = new ErrorCode1("cnt001", "统计数据时错误，错误信息{0}");

	/**
	 * Count数据时索引名称为空
	 */
	public static final ErrorCode0 IP_COUNT_INDEX_NAME_NULL = new ErrorCode0("cnt011", "统计数据时错误，索引名为空");

	/**
	 * Count数据时类型名称为空
	 */
	public static final ErrorCode0 IP_COUNT_TYPE_NAME_NULL = new ErrorCode0("cnt012", "统计数据时错误，类型名称为空");
	
	/**
	 * 根据表达式批量更新数据时， 每次更新的最大记录数
	 */
	public static final ErrorCode3 IP_BATCH_UPDATE_TOO_MANY = new ErrorCode3("upd001", "表达式[{0}]批量更新数据时，需要更新的记录数{1}, 超过最大限制数量{2}");
	
	/**
	 * 字段不存在
	 */
	public static final ErrorCode1 NOT_EXIST_FIELD = new ErrorCode1("fld001", "使用了不存在的字段{0}！");

    /**
     * 返回字段不能使用虚拟字段
     */
    public static final ErrorCode1 SELECT_FIELD_NOT_VIRTUAL = new ErrorCode1("fld002", "返回字段不能使用虚拟字段{0}！");
	
	/**
	 * 检索结果转json异常
	 */
	public static final ErrorCode0 IP_SEARCHRESULT_TO_JSON_ERROR = new ErrorCode0("result001", "检索结果转json异常");
	
	/**
	 * 索引已經存在
	 */
	public static final ErrorCode1 IP_INDEX_IS_EXIST_ERROR = new ErrorCode1("createError001", "数据库{0}已经存在");
	
	/**
	 * 索引不存在
	 */
	public static final ErrorCode1 IP_INDEX_IS_NOT_EXIST_ERROR = new ErrorCode1("createError002", "数据库{0}不存在");
	
	/**
	 * 添加的字段已经存在
	 */
	public static final ErrorCode1 IP_FIELD_IS_EXIST_ERROR = new ErrorCode1("addField001", "字段{0}已经存在");
	
	/**
	 * 添加集合字段已经存在
	 */
	public static final ErrorCode2 IP_NESTED_FIELD_IS_EXIST = new ErrorCode2("addField002", "集合{0}中字段{1}已经存在");
	
	/**
	 * 添加虚拟字段已经存在
	 */
	public static final ErrorCode1 IP_VIRTUAL_FIELD_IS_EXIST = new ErrorCode1("addField003", "虚拟字段{0}已经存在");
	
	/**
	 * 添加日期型字段已经存在
	 */
	public static final ErrorCode1 IP_DATE_FIELD_IS_EXIST = new ErrorCode1("addField004", "日期型字段{0}已经存在");
	
	/**
	 * 添加日期型字段已经存在
	 */
	public static final ErrorCode1 IP_DATE_FIELD_END_RRROR = new ErrorCode1("addField005", "日期型字段{0}必须以YMD结尾");
	
	/**
	 * 创建数据库时失败
	 */
	public static final ErrorCode2 IP_CREATEDATABASE_ERROR = new ErrorCode2("createIndex001", "创建index[{0}]==>type[{1}]失败");
	
	/**
	 * 添加字段失败
	 */
	public static final ErrorCode1 IP_ADD_FIELD_ERROR = new ErrorCode1("addField006", "添加字段{0}失败");
	
	/**
	 * 生成DAO配置文件失败
	 */
	public static final ErrorCode1 IP_IPTYPE_TO_XML_ERROR = new ErrorCode1("ipType2xml001", "{0}生成DAO配置文件失败");


	public static final String ECODE_SUCCESS = "000000";			// 成功的返回代码
	public static final String ECODE_FILE_NOTFOUND = "FILE01";	// 文件不存在

	public static final ErrorCode0 JNDI_LOOKUP_ERROR 		= new ErrorCode0("100001", "取JNDI的上下文时错误");
	public static final ErrorCode2 TXN_LOAD_PARAM_ERROR2	= new ErrorCode2("100003", "从{0}的方法{1}生成参数时错误");
	public static final ErrorCode1 TASK_UNKNOW_CMD1 		= new ErrorCode1("100006", "未知的流程处理命令:{0}");
	public static final ErrorCode2 TXN_EXEC_ERROR2 			= new ErrorCode2("100010", "执行命令{0}时错误:{1}");
	public static final ErrorCode1 TXN_EXEC_ERROR1 			= new ErrorCode1("100010", "执行命令{0}时错误");
	public static final ErrorCode1 TXN_USERNAME_ERROR		= new ErrorCode1("100011", "错误的用户名,{0}");
	public static final ErrorCode0 TXN_USERNAME_ERROR1		= new ErrorCode0("100011", "错误的用户名,可能没有签到");

	/**
	 * 处理文件时发生的异常
	 */
	public static final ErrorCode1 FILE_NOTEXIST1 			= new ErrorCode1("FILE01", "文件[{0}]不存在");
	public static final ErrorCode2 FILE_NOTEXIST2 			= new ErrorCode2("FILE01", "{0},文件[{1}]不存在");

	// 打开文件错误
	public static final ErrorCode1 FILE_OPEN_ERROR1 		= new ErrorCode1("FILE02", "打开文件[{0}]时错误");
	public static final ErrorCode1 FILE_OPEN_ERROR11 		= new ErrorCode1("FILE02", "打开文件错误:{0}");

	// 读文件错误
	public static final ErrorCode0 FILE_READERROR 			= new ErrorCode0("FILE03", "读取文件时错误");
	public static final ErrorCode1 FILE_READERROR1 			= new ErrorCode1("FILE03", "读取文件[{0}]错误");

	// 写文件错误
	public static final ErrorCode0 FILE_WRITEERROR 		 	= new ErrorCode0("FILE04", "写文件时错误");
	public static final ErrorCode1 FILE_WRITEERROR1 		= new ErrorCode1("FILE04", "写文件[{0}]错误");
	public static final ErrorCode1 FILE_WRITEERROR11 		= new ErrorCode1("FILE04", "写文件时错误:{0}");
	public static final ErrorCode2 FILE_WRITEERROR21 		= new ErrorCode2("FILE04", "写文件[{0}]时,编码集{1}错误");

	// 复制文件错误
	public static final ErrorCode1 FILE_COPYERROR1 			= new ErrorCode1("FILE13", "复制文件错误, {0}");
	public static final ErrorCode2 FILE_COPYERROR2 			= new ErrorCode2("FILE13", "复制文件错误, 源文件{0}, 目标文件{1}");


	public static final ErrorCode0 FILE_NAME_ISNULL			= new ErrorCode0("FILE05", "文件名称为空");

	public static final ErrorCode0 FILE_DOWNLOAD_ERROR 		= new ErrorCode0("FILE06", "下载文件错误");
	public static final ErrorCode1 FILE_DOWNLOAD_ERROR1 	= new ErrorCode1("FILE06", "下载文件[{0}]错误");
	public static final ErrorCode1 FILE_DOWNLOAD_ERROR11 	= new ErrorCode1("FILE06", "下载文件错误:{0}");
	public static final ErrorCode0 FILE_UPLOAD_ERROR 		= new ErrorCode0("FILE07", "取上传文件错误");
	public static final ErrorCode1 FILE_DELETE_ERROR1 		= new ErrorCode1("FILE08", "删除文件[{0}]时错误");
	public static final ErrorCode1 FILE_CREATE_ERROR1 		= new ErrorCode1("FILE09", "创建文件[{0}]时错误");
	public static final ErrorCode1 FILE_CLOSE_ERROR 		= new ErrorCode1("FILE10", "关闭文件[{0}]错误");
	public static final ErrorCode1 FILE_ALREADY_EXIST1 		= new ErrorCode1("FILE11", "文件[{0}]已经存在");
	public static final ErrorCode1 FILE_CREATEPATH_ERROR 	= new ErrorCode1("FILE12", "创建目录[{0}]时错误");
	public static final ErrorCode1 FILE_PATH_NOTFOUND		= new ErrorCode1("FILE13", "目录[{0}]不存在");
	public static final ErrorCode2 FILE_RENAME_ERROR 		= new ErrorCode2("FILE14", "修改文件{0}->{1}名称时错误");
	public static final ErrorCode0 FILE_CREATE_EXCEL_ERROR 	= new ErrorCode0("FILE15", "生成EXCEL文档时错误");
	public static final ErrorCode1 FILE_CREATE_ZIP_ERROR 	= new ErrorCode1("FILE16", "创建压缩文件[{0}]时错误");
	public static final ErrorCode1 FILE_CALC_CRC32_ERROR 	= new ErrorCode1("FILE17", "计算文件{0}的CRC时错误");
	public static final ErrorCode0 FILE_NOT_DIRECTORY 		= new ErrorCode0("FILE18", "指定的名称应该是目录，结果为文件");
	public static final ErrorCode0 FILE_UPLOAD_READERROR	= new ErrorCode0("FILE19", "读取上传文件时错误");
	public static final ErrorCode1 FILE_ENGINE_NOTFOUND		= new ErrorCode1("FILE66", "没有找到文件{0}的生成方式");
	public static final ErrorCode1 FILE_DOWNLOAD_LIMIT 		= new ErrorCode1("FILE20", "没有下载文件[{0}]的权限");
	public static final ErrorCode1 FILE_NAME_VALID			= new ErrorCode1("FILE21", "文件名{0}不合法");

	public static final ErrorCode1 LOAD_CONFIGERROR 		= new ErrorCode1("100050", "加载系统配置参数{0}时错误");
	public static final ErrorCode1 LOAD_DBCONF_ERROR 		= new ErrorCode1("100051", "加载数据表配置文件时错误:{0}");
	public static final ErrorCode2 ACTION_DUPLICATE 		= new ErrorCode2("100055", "交易码{0}在类{1}已经存在");
	public static final ErrorCode1 TXN_CONFIG_NOTFOUND		= new ErrorCode1("100056", "交易{0}的配置信息不存在");
	public static final ErrorCode2 ACTION_DISABLED			= new ErrorCode2("100058", "交易{0}已经被禁止:{1}");
	public static final ErrorCode2 ACTION_FORWARD_ERROR 	= new ErrorCode2("100059", "交易{0}的导航页面{1}不存在");
	public static final ErrorCode0 DAO_CONFIG_DUPLICATE 	= new ErrorCode0("100061", "DAO配置信息重复");
	public static final ErrorCode1 DAO_CONFIG_NOTFOUND		= new ErrorCode1("100062", "数据表{0}的配置信息不存在");
	public static final ErrorCode1 DAO_SEQUENCE_ERROR		= new ErrorCode1("100063", "获取Sequence[{0}]时错误");
	public static final ErrorCode1 DAO_AUTO_COLUMN_ERROR	= new ErrorCode1("100064", "服务端生成的变量[{0}]没有定义转换函数");

	public static final ErrorCode1 LIST_LOADFILE_ERROR		= new ErrorCode1("100070", "加载代码文件{0}时错误");
	public static final ErrorCode0 LIST_LOADMODULE_ERROR	= new ErrorCode0("100071", "从模块文件加载代码时错误");

	public static final ErrorCode0 DATABUS_NOT_INIT 		= new ErrorCode0("100100", "数据总线还没有初始化");
	public static final ErrorCode0 DATABUS_NAME_ISNULL 		= new ErrorCode0("100101", "数据项名称为空");
	public static final ErrorCode0 DATABUS_VALUE_ISNULL 	= new ErrorCode0("100102", "数据项内容为空");
	public static final ErrorCode1 DATABUS_DATA_NOTFOUND1 	= new ErrorCode1("100103", "数据项[{0}]不存在");
	public static final ErrorCode1 DATABUS_DATA_NOTFOUND11 	= new ErrorCode1("100103", "数据项不存在, {0}");
	public static final ErrorCode1 DATABUS_FORMAT_ERROR 	= new ErrorCode1("100105", "数据格式错误:{0}");
	public static final ErrorCode0 DATABUS_INVALID_DATATYPE = new ErrorCode0("100107", "不支持的数据类型");
	public static final ErrorCode1 DATABUS_INVALID_DATATYPE11 = new ErrorCode1("100107", "不支持的数据类型,{0}");
	public static final ErrorCode1 DATABUS_NOT_DETAIL 		= new ErrorCode1("100108", "节点{0}没有明细数据");
	public static final ErrorCode0 DATABUS_OUTOFBOUNDS 		= new ErrorCode0("100109", "记录越界");
	public static final ErrorCode0 PROPERTY_NOTEXIST 		= new ErrorCode0("100111", "属性不存在");
	public static final ErrorCode1 PROPERTY_NOTEXIST1 		= new ErrorCode1("100111", "属性[{0}]不存在");
	public static final ErrorCode0 DATABUS_DATATYPE_ERR 	= new ErrorCode0("100112", "数据节点的类型不一致");
	public static final ErrorCode1 DATABUS_TOO_MANY_ERR1	= new ErrorCode1("100113", "节点上DATABUS数量超过[{0}]条，请更换数据处理方式");
	public static final ErrorCode0 DATABUS_RECORD_NOTFOUND 	= new ErrorCode0("100104", "记录集中没有找到记录");
	public static final ErrorCode2 DATABUS_RECORD_NOTFOUND1 = new ErrorCode2("100104", "没有找到记录[{0},{1}]");
	public static final ErrorCode0 RECORDSET_KEY_ISNULL 	= new ErrorCode0("100105", "关键字内容为空");
	public static final ErrorCode1 RECORDSET_KEY_ISNULL1 	= new ErrorCode1("100105", "关键字内容为空，{0}");
	public static final ErrorCode2 RECORDSET_OUTBOUND		= new ErrorCode2("100106", "记录数量[{0}],定位记录[{1}]");
	public static final ErrorCode0 RECORDSET_ISNULL 		= new ErrorCode0("100107", "记录集为空");
	public static final ErrorCode0 RECORDSET_NOTFOUNT_MAX	= new ErrorCode0("100108", "没有取到最大值");
	public static final ErrorCode1 DATABUS_SET_DETAIL_ERR	= new ErrorCode1("100121", "不能在主节点[{0}]上保存明细数据");

	public static final ErrorCode0 DATABUS_MODIFIABLE_ERROR = new ErrorCode0("106001", "UnmodifiableDataBus中数据不能修改");
	public static final ErrorCode0 RECORDSET_MODIFIABLE_ERROR = new ErrorCode0("106002", "UnmodifiableRecordset中数据不能修改");
	public static final ErrorCode0 DATABUS_CONSTRUCTOR_ERROR  = new ErrorCode0("106003", "Unmodifiable类型的数据不能使用DataBus的缺省构造函数");

	public static final ErrorCode1 ARRAY_OUTBOUND1		= new ErrorCode1("100108", "记录越界，{0}");
	public static final ErrorCode2 ARRAY_OUTBOUND2		= new ErrorCode2("100108", "记录数量[{0}],定位记录[{1}]");

	public static final ErrorCode1 CHARSET_ERROR1 		= new ErrorCode1("100201", "不支持编码集:{0}");
	public static final ErrorCode0 KEY_DATA_ERROR 		= new ErrorCode0("100211", "密钥数据错误");
	public static final ErrorCode0 KEY_FILE_ERROR 		= new ErrorCode0("100211", "密钥文件错误");
	public static final ErrorCode0 KEY_PADDING_ERROR 	= new ErrorCode0("100212", "数据块长度或填充信息错误");
	public static final ErrorCode0 NO_DES_ALGORITHM 	= new ErrorCode0("100213", "不支持DES算法");
	public static final ErrorCode0 NO_MD5_ALGORITHM 	= new ErrorCode0("100214", "不支持MD5算法");
	public static final ErrorCode0 ENCRYPT_KEY_ERROR 	= new ErrorCode0("100216", "加密错误:获取密钥错误");
	public static final ErrorCode0 ENCRYPT_DATA_ERROR 	= new ErrorCode0("100216", "加密错误:加密数据错误");
	public static final ErrorCode0 DECRYPT_KEY_ERROR 	= new ErrorCode0("100217", "解密错误:获取密钥错误");
	public static final ErrorCode0 DECRYPT_DATA_ERROR 	= new ErrorCode0("100217", "解密错误:解密数据错误");
	public static final ErrorCode0 GET_SIGN_ERROR 		= new ErrorCode0("100218", "生成签名时错误");
	public static final ErrorCode0 CHECK_SIGN_ERROR 	= new ErrorCode0("100219", "检查签名数据错误:数据不一致");
	public static final ErrorCode1 CHECK_SIGN_ERROR11 	= new ErrorCode1("100219", "检查签名数据错误:{0}");
	public static final ErrorCode0 GET_MD5_ERROR 		= new ErrorCode0("100220", "计算消息摘要时错误");
	public static final ErrorCode0 KEY_GEN_ERROR 		= new ErrorCode0("100221", "生成密钥错误");
	public static final ErrorCode0 KEY_LOAD_ERROR 		= new ErrorCode0("100222", "获取密钥数据错误");

	public static final ErrorCode1 VALID_VALUE_ISNULL 	= new ErrorCode1("100401", "数据项{0}为空");
	public static final ErrorCode3 VALID_VALUE_TOLONG 	= new ErrorCode3("100402", "数据项{0}的长度{1}超过最大长度{2}");
	public static final ErrorCode1 VALID_VALUE_TOLONG1 	= new ErrorCode1("100402", "数据项超过最大长度,{0}");
	public static final ErrorCode3 VALID_VALUE_LENGTH 	= new ErrorCode3("100402", "数据项{0}的长度{1}必须是{2}");
	public static final ErrorCode1 VALID_FORMAT_ERROR 	= new ErrorCode1("100403", "数据项{0}格式错误");
	public static final ErrorCode0 VALID_NAME_ISNULL 	= new ErrorCode0("100404", "数据的键值为空");
	public static final ErrorCode1 VALID_NAME_ISNULL1 	= new ErrorCode1("100404", "数据的键值为空,{0}");
	public static final ErrorCode1 VALID_TYPE_ERROR11 	= new ErrorCode1("100405", "数据类型错误，{0}");
	public static final ErrorCode0 DATE_MONTH_ERROR		= new ErrorCode0("100411", "日期的格式错误，月只能是[1~12]" );
	public static final ErrorCode0 DATE_DAY_ERROR		= new ErrorCode0("100412", "日期的格式错误，日只能是[1~31]" );
	public static final ErrorCode0 FORMAT_MONEY_ERROR	= new ErrorCode0("100413", "金额的格式错误，只能精确到仟亿，小数点只能两位" );
	public static final ErrorCode1 FORMAT_CODE_ERROR	= new ErrorCode1("100414", "数据字典[{0}]的格式错误" );

	public static final ErrorCode0 CONF_NOT_FOUND 		= new ErrorCode0("100501", "配置信息没有找到");
	public static final ErrorCode0 MENU_NOT_INIT 		= new ErrorCode0("100502", "菜单文件没有初始化");
	public static final ErrorCode1 MENU_NODE_NOT_FOUND 	= new ErrorCode1("100503", "菜单代码{0}没有找到");
	public static final ErrorCode0 MENU_CONF_ERROR		= new ErrorCode0("100504", "菜单节点没有配置名称或ID");
	public static final ErrorCode1 MENU_INCLUDE_ERROR	= new ErrorCode1("100505", "菜单节点{0}的部分include中没有file-name属性");

	public static final ErrorCode1 TXN_LOAD_PROXY_ERROR = new ErrorCode1("101000", "加载交易代理类{0}时错误");
	public static final ErrorCode0 EJB_NOTFOUND 		= new ErrorCode0("101001", "找不到EJB容器");
	public static final ErrorCode1 EJB_CONNECT_ERROR 	= new ErrorCode1("101002", "连接EJB容器{0}时错误");
	public static final ErrorCode2 EJB_CALL_ERROR 		= new ErrorCode2("101003", "调用EJB服务{0}:{1}时错误");
	public static final ErrorCode0 EJB_REGISTER_ERROR 	= new ErrorCode0("101004", "注册EJB服务时错误");
	public static final ErrorCode1 BEAN_NOTFOUND 		= new ErrorCode1("101011", "业务组件{0}不存在");
	public static final ErrorCode0 BEAN_CALL_ERROR 		= new ErrorCode0("101013", "调用业务组件时错误");
	public static final ErrorCode1 BEAN_LOAD_ERROR 		= new ErrorCode1("101014", "加载业务组件{0}时错误");
	public static final ErrorCode1 BEAN_TYPE_ERROR1 	= new ErrorCode1("101015", "业务组件类型{0}错误");
	public static final ErrorCode1 BEAN_TYPE_ERROR11 	= new ErrorCode1("101015", "业务组件类型错误, {0}");
	public static final ErrorCode1 CLASS_TYPE_ERROR 	= new ErrorCode1("101016", "服务类{0}的类型错误");
	public static final ErrorCode1 DAO_CLASS_TYPE_ERROR = new ErrorCode1("101016", "DAO类{0}的类型错误");
	public static final ErrorCode0 LOG_MONITOR_ERROR 	= new ErrorCode0("101017", "注册监控信息时错误");
	public static final ErrorCode0 LOG_MONITOR_IS_OVER	= new ErrorCode0("101018", "监控队列最多支持8个并发用户");

	public static final ErrorCode2 BEAN_METHOD_NOTFOUND 	= new ErrorCode2("101021", "业务组件{0}的方法{1}不存在");
	public static final ErrorCode1 BEAN_LOADMODULE_ERROR 	= new ErrorCode1("101048", "加载服务模块{0}时错误");
	public static final ErrorCode0 BEAN_LOADCLASS_ERROR 	= new ErrorCode0("101049", "加载模块中的类时错误");
	public static final ErrorCode1 BEAN_LOADCLASS_ERROR1 	= new ErrorCode1("101049", "加载类{0}时错误");
	public static final ErrorCode1 BEAN_LOADCLASS_ERROR11 	= new ErrorCode1("101049", "加载模块中的类时错误, {0}");
	public static final ErrorCode1 TXN_GEN_METHOD_ERROR 	= new ErrorCode1("101050", "生成交易的接口类{0}时错误");
	public static final ErrorCode0 TXN_NO_STEP 				= new ErrorCode0("101051", "交易没有处理流程");
	public static final ErrorCode2 TXN_ENTRY_NOTFOUND		= new ErrorCode2("101052", "没有找到交易[{0}:{1}]的处理函数" );


	public static final ErrorCode2 JAVA_METHOD_NOTFOUND2 	= new ErrorCode2("103101", "类{0}中没有找到方法{1}");
	public static final ErrorCode1 JAVA_CLASS_NOTFOUND1 	= new ErrorCode1("103102", "类{0}没有定义");
	public static final ErrorCode1 JAVA_CLASS_NOTFOUND11 	= new ErrorCode1("103102", "类没有定义,{0}");
	public static final ErrorCode1 JAVA_CLASS_NOTINSTANCE 	= new ErrorCode1("103103", "类{0}不能被实例化");
	public static final ErrorCode1 JAVA_ACCESS_LIMIT 		= new ErrorCode1("103104", "类{0}没有存取权限");
	public static final ErrorCode1 JAVA_ACCESS_LIMIT11 		= new ErrorCode1("103104", "Java存取权限错误， {0}");
	public static final ErrorCode2 JAVA_METHOD_ACCESS_LIMIT2= new ErrorCode2("103104", "类{0}中的方法{1}没有存取权限");
	public static final ErrorCode2 JAVA_METHOD_LIMIT 		= new ErrorCode2("103104", "没有权限访问类{0}的方法{1}");
	public static final ErrorCode2 JAVA_INVOCATE_EXCEPTION2 = new ErrorCode2("103105", "调用{0}的方法{1}异常");
	public static final ErrorCode1 JAVA_METHOD_NOTIMPL1  	= new ErrorCode1("103106", "类{0}中没有实现方法");
	public static final ErrorCode2 JAVA_METHOD_NOTIMPL2  	= new ErrorCode2("103106", "类{0}中没有实现方法,{1}");
	public static final ErrorCode1 JAVA_CLASS_LOAD_ERROR 	= new ErrorCode1("103107", "加载类{0}时错误");
	public static final ErrorCode2 JAVA_CLASS_DEFINE_DUPLICATION	= new ErrorCode2("103108", "{0}文件中定义的类{1}已经定义过");
	public static final ErrorCode1 JAVA_INSTANCE_ERROR  	= new ErrorCode1("103109", "类{0}实例化时错误");
	public static final ErrorCode0 JAVA_THREAD_INTERRUPTED 	= new ErrorCode0("103110", "JAVA线程被中断");
	public static final ErrorCode1 JAVA_OTHER_ERROR 		= new ErrorCode1("103111", "调用JAVA服务时异常错误:{0}");
	public static final ErrorCode0 JAVA_COMPILE_EXCEPTION 	= new ErrorCode0("103201", "编译组件时错误");
	public static final ErrorCode1 JAVA_COMPILE_ERROR		= new ErrorCode1("103201", "编译文件{0}时错误");
	public static final ErrorCode0 JAVA_PARSER_ERROR 		= new ErrorCode0("103202", "解析JAVA源文件时错误");
	public static final ErrorCode0 JAVA_OUT_MEMORY 			= new ErrorCode0("103203", "内存溢出");
	public static final ErrorCode2 JAVA_OUT_MEMORY1 		= new ErrorCode2("103203", "执行{0}方法{1}时内存溢出");
	public static final ErrorCode1 JAVA_PARAMETER_ERROR11 	= new ErrorCode1("103204", "执行方法时输入参数时错误，{0}");
	public static final ErrorCode2 JAVA_PARAMETER_ERROR2 	= new ErrorCode2("103204", "执行{0}方法{1}的输入参数时错误");
	public static final ErrorCode0 JAVA_FORMAT_ERROR		= new ErrorCode0("103205", "格式化类文件时错误");
	public static final ErrorCode1 INSTANCE_WRAP_ERROR 		= new ErrorCode1("103206","Wrap实例类{0}出现错误");

	public static final ErrorCode1 SERIAL_BEAN_NOTFOUND 	= new ErrorCode1("104101", "没有找到序列号{0}的函数");
	public static final ErrorCode0 SERIAL_KEYCOLUMN_ISNULL 	= new ErrorCode0("104102", "没有指定分类字段名称");
	public static final ErrorCode0 ECODE_CONFIG_ERROR		= new ErrorCode0("104111", "错误代码格式错误");

	public static final ErrorCode1 XML_PARSER_ERROR 		= new ErrorCode1("105001", "解析XML文档{0}时错误");
	public static final ErrorCode1 XML_PARSER_ERROR2 	= new ErrorCode1("105001", "解析XML文档时错误,{0}");
	public static final ErrorCode1 XML_CREATE_ERROR1 	= new ErrorCode1("105002", "生成XML文档{0}时错误");
	public static final ErrorCode1 XML_CREATE_ERROR11 	= new ErrorCode1("105002", "生成XML文档时错误:{0}");
	public static final ErrorCode1 DTD_PARSER_ERROR1 	= new ErrorCode1("105003", "解析DTD文档{0}时错误");
	public static final ErrorCode1 XML_ELEMENT_NOFOUND 	= new ErrorCode1("105101", "XML文件中节点{0}不存在");
	public static final ErrorCode1 XML_KEYNAME_NOFOUND 	= new ErrorCode1("105102", "查找节点记录时，没有指定{0}的关键字字段");
	public static final ErrorCode0 XML_OUTPUT_NOFOUND 	= new ErrorCode0("105103", "没有指定输出数据的节点");
	public static final ErrorCode0 XML_ADD_PROPERTY_ERROR = new ErrorCode0("105103", "不在节点内，不能增加属性" );
	public static final ErrorCode0 HTML_PARSER_ERROR1 	= new ErrorCode0("105201", "解析HTML内容时错误");

	public static final ErrorCode0 JTA_CONTEXT_ERROR 	= new ErrorCode0("102111", "生成JTA的上下文时错误");
	public static final ErrorCode1 JTA_JNDI_NOTFOUND 	= new ErrorCode1("102112", "取JTA事务的JNDI{0}时错误");
	public static final ErrorCode0 JTA_BEGIN_ERROR		= new ErrorCode0("102113", "启动JTA事务时错误");
	public static final ErrorCode0 JTA_COMMIT_ERROR		= new ErrorCode0("102114", "处理JTA事务时错误");

	public static final ErrorCode1 EBD_OPEN_ERROR		= new ErrorCode1("103114", "打开内嵌数据库{0}时错误");

	public static final ErrorCode1 INST_ADD_ERROR		= new ErrorCode1("TASK81", "增加指令时错误:{0}");
	public static final ErrorCode0 INST_ADD_INTERRUPT	= new ErrorCode0("TASK81", "增加指令时发生了中断");
	public static final ErrorCode1 INST_SAVE_ERROR		= new ErrorCode1("TASK82", "保存指令到文件{0}时错误");
	public static final ErrorCode1 INST_RESTORE_ERROR	= new ErrorCode1("TASK82", "从文件{0}恢复指令时错误");

	public static final ErrorCode1 CACHE_FILE_NOTFOUND	= new ErrorCode1("CACH01", "缓存文件{0}不存在");
	public static final ErrorCode1 CACHE_SAVE_ERROR		= new ErrorCode1("CACH02", "保存缓存数据{0}错误");
	public static final ErrorCode1 CACHE_READ_ERROR		= new ErrorCode1("CACH03", "读取缓存数据{0}错误");

	public static final ErrorCode1 ARG_NULL_ERROR1		= new ErrorCode1("ARG001", "参数{0}为空");
	public static final ErrorCode1 ARG_NULL_ERROR11		= new ErrorCode1("ARG001", "参数错误,{0}");
	public static final ErrorCode2 ARG_FMT_ERROR		= new ErrorCode2("ARG001", "参数{0}为{1}，格式错误");

	public static final ErrorCode1 JMS_JNDI_ERROR 		= new ErrorCode1("JMS001", "查找JMS的JNDI[{0}]时错误");
	public static final ErrorCode1 JMS_INIT_ERROR 		= new ErrorCode1("JMS002", "初始化JMS[{0}]时错误");
	public static final ErrorCode1 JMS_SEND_ERROR 		= new ErrorCode1("JMS003", "发送消息[{0}]时错误");
	public static final ErrorCode1 JMS_RECV_ERROR 		= new ErrorCode1("JMS004", "接收消息[{0}]时错误");
	public static final ErrorCode1 JMS_NO_CONFIG 		= new ErrorCode1("JMS005", "没有配置JMS[{0}]");
	public static final ErrorCode0 JMS_START_ERROR 		= new ErrorCode0("JMS006", "启动JMS服务线程时错误");
	public static final ErrorCode0 JMS_PROCESS_ERR 		= new ErrorCode0("JMS007", "处理异步消息时异常错误");


	// SMTP
	public static final ErrorCode0 MAIL_SEND_ADDR_ERROR = new ErrorCode0( "MAIL01", "发件人地址格式错误" );
	public static final ErrorCode0 MAIL_TO_ADDR_ERROR 	= new ErrorCode0( "MAIL02", "TO[收件人]邮件地址错误" );
	public static final ErrorCode0 MAIL_CC_ADDR_ERROR 	= new ErrorCode0( "MAIL03", "CC[抄送人]人邮件地址错误" );
	public static final ErrorCode0 MAIL_BCC_ADDR_ERROR 	= new ErrorCode0( "MAIL04", "BCC[暗送人]人邮件地址错误" );
	public static final ErrorCode0 SET_SENDTIME_ERROR 	= new ErrorCode0( "MAIL05", "设置发送时间时错误" );
	public static final ErrorCode0 SET_SUBJECT_ERROR 	= new ErrorCode0( "MAIL06", "设置邮件的主题错误" );
	public static final ErrorCode0 SET_MAILBODY_ERROR 	= new ErrorCode0( "MAIL07", "设置邮件内容时错误" );
	public static final ErrorCode0 SET_ATTACH_ERROR 	= new ErrorCode0( "MAIL08", "设置邮件的附件时错误" );
	public static final ErrorCode0 SEND_MAIL_ERROR 		= new ErrorCode0( "MAIL09", "发送邮件时错误" );

	// POP3
	public static final ErrorCode1 OPEN_POP3_ERROR 		= new ErrorCode1( "MAIL11", "连接邮件服务器[{0}]错误" );
	public static final ErrorCode0 RECV_MAIL_ERROR 		= new ErrorCode0( "MAIL12", "接收邮件时错误" );
	public static final ErrorCode0 DEL_MAIL_ERROR 		= new ErrorCode0( "MAIL13", "删除邮件时错误" );
	public static final ErrorCode0 GET_SUBJECT_ERROR	= new ErrorCode0( "MAIL14", "取邮件的标题错误" );
	public static final ErrorCode0 GET_MAILBODY_ERROR	= new ErrorCode0( "MAIL15", "取邮件的正文时错误" );
	public static final ErrorCode0 MAIL_FORMAT_ERROR 	= new ErrorCode0( "MAIL16", "未知的邮件正文格式" );
	public static final ErrorCode1 MAIL_FORMAT_ERROR1 	= new ErrorCode1( "MAIL16", "未知的邮件正文格式:{0}" );

	// 邮件内容
	public static final ErrorCode0 CREATE_MAILBODY_ERROR 	= new ErrorCode0( "MAIL30", "生成邮件的主体时错误" );
	public static final ErrorCode0 PARSER_MAILBODY_ERROR 	= new ErrorCode0( "MAIL31", "解析邮件的主体时错误" );
	public static final ErrorCode1 ATTACHMENT_NOEXIST_ERROR = new ErrorCode1( "MAIL32", "邮件的的附件[{0}]不存在" );

	// 身份证
	public static final ErrorCode0 CHK_IDCARD_LENGTH 	= new ErrorCode0("card01", "身份证长度错误");
	public static final ErrorCode0 CHK_IDCARD_YEAR 		= new ErrorCode0("card02", "身份证年份错误");
	public static final ErrorCode0 CHK_IDCARD_MONTH 	= new ErrorCode0("card03", "身份证月份错误");
	public static final ErrorCode0 CHK_IDCARD_DAY 		= new ErrorCode0("card04", "身份证日错误");
	public static final ErrorCode0 CHK_IDCARD_CHECK 	= new ErrorCode0("card05", "身份证校验码错误");
	public static final ErrorCode0 CHK_IDCARD_NUMBER18 	= new ErrorCode0("card06", "身份证前17位必须是数字");
	public static final ErrorCode0 CHK_IDCARD_NUMBER15 	= new ErrorCode0("card07", "身份证必须全是数字");

	// 其他错误
	public static final ErrorCode0 ONLY_DEVELOPMENT		= new ErrorCode0("999901", "只有开发环境才能执行");
	public static final ErrorCode0 ONLY_RUNTIME			= new ErrorCode0("999902", "只有运行环境才能执行");
	public static final ErrorCode1 CALL_SERVICE_ERROR	= new ErrorCode1("999903", "调用服务{0}时产生未知错误");
	public static final ErrorCode2 CALL_METHOD_ERROR	= new ErrorCode2("999904", "调用{0}的方法{1}时产生未知错误");
	public static final ErrorCode0 EXEC_OTHER_ERROR		= new ErrorCode0("999905", "执行程序时产生未知错误");
	public static final ErrorCode0 TXN_OTHER_ERROR		= new ErrorCode0("999999", "未知的错误");
	public static final ErrorCode1 TXN_OTHER_ERROR1		= new ErrorCode1("999999", "调用交易{0}发生未知错误");
	public static final ErrorCode1 TXN_OTHER_ERROR11	= new ErrorCode1("999999", "调用交易发生未知错误，{0}");

	public static final ErrorCode0 MONITOR_DAO_ERROR	= new ErrorCode0("moni01", "监控没有启动就开始监控数据库操作");


}
