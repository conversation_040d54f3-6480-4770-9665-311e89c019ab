package cn.gwssi.common.common.pojo.collapse;

import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;

import java.io.Serializable;

public class InnerHitCondition implements Serializable {

    private static final long serialVersionUID = 7945310309708873738L;

    public InnerHitCondition(String name) {
        this.name = name;
    }

    private String name;
    private SortFieldList sortFieldList;
    private FieldList fieldList;
    private int from;
    private int size;

    public String getName() {
        return name;
    }

    public InnerHitCondition setName(String name) {
        this.name = name;
        return this;
    }

    public SortFieldList getSortFieldList() {
        return sortFieldList;
    }

    public InnerHitCondition setSortFieldList(SortFieldList sortFieldList) {
        this.sortFieldList = sortFieldList;
        return this;
    }

    public InnerHitCondition addSortField(SortField sortField) {
        if (null == sortFieldList) {
            sortFieldList = new SortFieldList();
        }

        this.sortFieldList.add(sortField);
        return this;
    }

    public FieldList getFieldList() {
        return fieldList;
    }

    public InnerHitCondition setFieldList(FieldList fieldList) {
        this.fieldList = fieldList;
        return this;
    }

    public InnerHitCondition addField(String field) {
        if (null == fieldList) {
            fieldList = new FieldList();
        }

        this.fieldList.add(field);
        return this;
    }

    public int getFrom() {
        return from;
    }

    public InnerHitCondition setFrom(int from) {
        this.from = from;
        return this;
    }

    public int getSize() {
        return size;
    }

    public InnerHitCondition setSize(int size) {
        this.size = size;
        return this;
    }

    @Override
    public InnerHitCondition clone() {
        InnerHitCondition condition = new InnerHitCondition(this.name);
        condition.setFrom(this.from);
        condition.setSize(this.size);

        // clone sort
        SortFieldList sortsSource = this.sortFieldList;
        SortFieldList sortsTarget = null;
        if (null != sortsSource && sortsSource.size() > 0) {
            sortsTarget = new SortFieldList();
            for (SortField source : sortsSource) {
                sortsTarget.add(source.clone());
            }
        }

        // clone field
        FieldList fieldSource = this.fieldList;
        FieldList fieldTarget = null;
        if (null != fieldSource && fieldSource.size() > 0) {
            fieldTarget = new FieldList();
            fieldTarget.addAll(fieldSource);
        }

        condition.setSortFieldList(sortsTarget);
        condition.setFieldList(fieldTarget);
        return condition;
    }
}
