package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;

/**
 * 多维分析结果
 * 
 * <AUTHOR>
 *
 */
public class MultiDimCountResult extends DimCountResult {
	/**
	 * ID
	 */
	private String uid = null;
	
	/**
	 * 上级节点ID
	 */
	private String pid = null;
	
	/**
	 * pname
	 */
	private String pname =  null;
	
	/**
	 * Key
	 */
	private String key =  null;
	
	public String getId() {
		return uid;
	}

	public void setId(String uid) {
		this.uid = uid;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getPName() {
		return pname;
	}

	public void setPName(String pname) {
		this.pname = pname;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public MultiDimCountResult setCountOther(String uid, String pid, String pname, long count) {
	    this.uid = uid;
	    this.pid = pid;
	    this.pname = pname;
	    this.setCount(count);
	    this.key = IPConstants.AGG_OTHER;
	    return this;
    }
	
	@Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}		
		
}
