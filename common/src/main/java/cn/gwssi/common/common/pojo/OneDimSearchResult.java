package cn.gwssi.common.common.pojo;

import java.util.List;

public class OneDimSearchResult<T extends GsonObject> {

    private IPSearchResult<T> searchResult;

    private List<OneDimCountResult> aggResult;

    public IPSearchResult<T> getSearchResult() {
        return searchResult;
    }

    public void setSearchResult(IPSearchResult<T> searchResult) {
        this.searchResult = searchResult;
    }

    public List<OneDimCountResult> getAggResult() {
        return aggResult;
    }

    public void setAggResult(List<OneDimCountResult> aggResult) {
        this.aggResult = aggResult;
    }
}
