package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class ValueArrayObject  extends ValueResult{
	private List<String> value = new ArrayList<String>();

	public List<String> getValue() {
		return value;
	}

	public void setValue(List<String> value) {
		this.value = value;
	}
	
	@Override
	public Set<String> getValueSet(){
		Set<String> set = new HashSet<String>();
		if(value != null && !value.isEmpty()) {
			for(String d : value) {
				set.add(d);
			}
		}
		return set;
	}	

	@Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}	
}
