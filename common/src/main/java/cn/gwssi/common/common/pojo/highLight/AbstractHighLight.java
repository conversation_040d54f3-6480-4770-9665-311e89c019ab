package cn.gwssi.common.common.pojo.highLight;

import cn.gwssi.common.common.pojo.highLight.HighLightCondition.BoundaryScannerType;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition.Order;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

public abstract class AbstractHighLight<HB extends AbstractHighLight<?>> implements Serializable {
    protected String[] preTags;

    protected String[] postTags;

    protected Integer fragmentSize;

    protected Integer numOfFragments;

    protected String highlighterType;

    protected String fragmenter;

    protected Order order;

    protected Boolean highlightFilter;

    protected Boolean forceSource;

    protected BoundaryScannerType boundaryScannerType;

    protected Integer boundaryMaxScan;

    protected char[] boundaryChars;

    protected Locale boundaryScannerLocale;

    protected Integer noMatchSize;

    protected Integer phraseLimit;

    protected Map<String, Object> options;

    protected Boolean requireFieldMatch;

    public AbstractHighLight() {
    }

    /**
     * Read from a stream.
     */
    protected AbstractHighLight(String[] preTags, String[] postTags, Integer fragmentSize, Integer numOfFragments,
                                String highlighterType, String fragmenter, String order, Boolean highlightFilter,
                                Boolean forceSource, BoundaryScannerType boundaryScannerType, Integer boundaryMaxScan,
                                char[] boundaryChars, String boundaryScannerLocale, Integer noMatchSize,
                                Integer phraseLimit, Map<String, Object> options, Boolean requireFieldMatch) {
        preTags(preTags);
        postTags(postTags);
        fragmentSize(fragmentSize);
        numOfFragments(numOfFragments);
        highlighterType(highlighterType);
        fragmenter(fragmenter);
        order(order);
        highlightFilter(highlightFilter);
        forceSource(forceSource);
        // 必须大于等于 ES_5_4_0 LUCENE_6_5_0，才支持这个参数
        boundaryScannerType(boundaryScannerType);
        boundaryMaxScan(boundaryMaxScan);
        boundaryChars(boundaryChars);
        // 必须大于等于 ES_5_4_0 LUCENE_6_5_0，才支持这个参数
        boundaryScannerLocale(boundaryScannerLocale);
        noMatchSize(noMatchSize);
        phraseLimit(phraseLimit);
        options(options);
        requireFieldMatch(requireFieldMatch);
    }

    /**
     * Set the pre tags that will be used for highlighting.
     */
    @SuppressWarnings("unchecked")
    public HB preTags(String... preTags) {
        this.preTags = preTags;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #preTags(String...)}
     */
    public String[] preTags() {
        return this.preTags;
    }

    /**
     * Set the post tags that will be used for highlighting.
     */
    @SuppressWarnings("unchecked")
    public HB postTags(String... postTags) {
        this.postTags = postTags;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #postTags(String...)}
     */
    public String[] postTags() {
        return this.postTags;
    }

    /**
     * Set the fragment size in characters, defaults to {@link HighlightCondition#DEFAULT_FRAGMENT_CHAR_SIZE}
     */
    @SuppressWarnings("unchecked")
    public HB fragmentSize(Integer fragmentSize) {
        this.fragmentSize = fragmentSize;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #fragmentSize(Integer)}
     */
    public Integer fragmentSize() {
        return this.fragmentSize;
    }

    /**
     * Set the number of fragments, defaults to {@link HighlightCondition#DEFAULT_NUMBER_OF_FRAGMENTS}
     */
    @SuppressWarnings("unchecked")
    public HB numOfFragments(Integer numOfFragments) {
        this.numOfFragments = numOfFragments;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #numOfFragments(Integer)}
     */
    public Integer numOfFragments() {
        return this.numOfFragments;
    }

    /**
     * Set type of highlighter to use. Out of the box supported types
     * are <tt>plain</tt>, <tt>fvh</tt> and <tt>postings</tt>.
     * The default option selected is dependent on the mappings defined for your index.
     * Details of the different highlighter types are covered in the reference guide.
     */
    @SuppressWarnings("unchecked")
    public HB highlighterType(String highlighterType) {
        this.highlighterType = highlighterType;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #highlighterType(String)}
     */
    public String highlighterType() {
        return this.highlighterType;
    }

    /**
     * Sets what fragmenter to use to break up text that is eligible for highlighting.
     * This option is only applicable when using the plain highlighterType <tt>highlighter</tt>.
     * Permitted values are "simple" or "span" relating to {@link SimpleFragmenter} and
     * {@link SimpleSpanFragmenter} implementations respectively with the default being "span"
     */
    @SuppressWarnings("unchecked")
    public HB fragmenter(String fragmenter) {
        this.fragmenter = fragmenter;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #fragmenter(String)}
     */
    public String fragmenter() {
        return this.fragmenter;
    }

    /**
     * The order of fragments per field. By default, ordered by the order in the
     * highlighted text. Can be <tt>score</tt>, which then it will be ordered
     * by score of the fragments, or <tt>none</TT>.
     */
    public HB order(String order) {
        return order(Order.fromString(order));
    }

    /**
     * By default, fragments of a field are ordered by the order in the highlighted text.
     * If set to {@link Order#SCORE}, this changes order to score of the fragments.
     */
    @SuppressWarnings("unchecked")
    public HB order(Order scoreOrdered) {
        this.order = scoreOrdered;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #order(Order)}
     */
    public Order order() {
        return this.order;
    }

    /**
     * Set this to true when using the highlighterType <tt>fvh</tt>
     * and you want to provide highlighting on filter clauses in your
     * query. Default is <tt>false</tt>.
     */
    @SuppressWarnings("unchecked")
    public HB highlightFilter(Boolean highlightFilter) {
        this.highlightFilter = highlightFilter;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #highlightFilter(Boolean)}
     */
    public Boolean highlightFilter() {
        return this.highlightFilter;
    }

    /**
     * When using the highlighterType <tt>fvh</tt> this setting
     * controls which scanner to use for fragment boundaries, and defaults to "simple".
     */
    @SuppressWarnings("unchecked")
    public HB boundaryScannerType(String boundaryScannerType) {
        this.boundaryScannerType = BoundaryScannerType.fromString(boundaryScannerType);
        return (HB) this;
    }

    /**
     * When using the highlighterType <tt>fvh</tt> this setting
     * controls which scanner to use for fragment boundaries, and defaults to "simple".
     */
    @SuppressWarnings("unchecked")
    public HB boundaryScannerType(BoundaryScannerType boundaryScannerType) {
        this.boundaryScannerType = boundaryScannerType;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #boundaryScannerType(String)}
     */
    public BoundaryScannerType boundaryScannerType() {
        return this.boundaryScannerType;
    }

    /**
     * When using the highlighterType <tt>fvh</tt> this setting
     * controls how far to look for boundary characters, and defaults to 20.
     */
    @SuppressWarnings("unchecked")
    public HB boundaryMaxScan(Integer boundaryMaxScan) {
        this.boundaryMaxScan = boundaryMaxScan;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #boundaryMaxScan(Integer)}
     */
    public Integer boundaryMaxScan() {
        return this.boundaryMaxScan;
    }

    /**
     * When using the highlighterType <tt>fvh</tt> this setting
     * defines what constitutes a boundary for highlighting. It’s a single string with
     * each boundary character defined in it. It defaults to .,!? \t\n
     */
    @SuppressWarnings("unchecked")
    public HB boundaryChars(char[] boundaryChars) {
        this.boundaryChars = boundaryChars;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #boundaryChars(char[])}
     */
    public char[] boundaryChars() {
        return this.boundaryChars;
    }

    /**
     * When using the highlighterType <tt>fvh</tt> and boundaryScannerType <tt>break_iterator</tt>, this setting
     * controls the locale to use by the BreakIterator, defaults to "root".
     */
    @SuppressWarnings("unchecked")
    public HB boundaryScannerLocale(String boundaryScannerLocale) {
        if (boundaryScannerLocale != null) {
            this.boundaryScannerLocale = Locale.forLanguageTag(boundaryScannerLocale);
        }
        return (HB) this;
    }

    /**
     * @return the value set by {@link #boundaryScannerLocale(String)}
     */
    public Locale boundaryScannerLocale() {
        return this.boundaryScannerLocale;
    }

    /**
     * Allows to set custom options for custom highlighters.
     */
    @SuppressWarnings("unchecked")
    public HB options(Map<String, Object> options) {
        this.options = options;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #options(Map)}
     */
    public Map<String, Object> options() {
        return this.options;
    }

    /**
     * Set to true to cause a field to be highlighted only if a query matches that field.
     * Default is false meaning that terms are highlighted on all requested fields regardless
     * if the query matches specifically on them.
     */
    @SuppressWarnings("unchecked")
    public HB requireFieldMatch(Boolean requireFieldMatch) {
        this.requireFieldMatch = requireFieldMatch;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #requireFieldMatch(Boolean)}
     */
    public Boolean requireFieldMatch() {
        return this.requireFieldMatch;
    }

    /**
     * Sets the size of the fragment to return from the beginning of the field if there are no matches to
     * highlight and the field doesn't also define noMatchSize.
     *
     * @param noMatchSize integer to set or null to leave out of request.  default is null.
     * @return this for chaining
     */
    @SuppressWarnings("unchecked")
    public HB noMatchSize(Integer noMatchSize) {
        this.noMatchSize = noMatchSize;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #noMatchSize(Integer)}
     */
    public Integer noMatchSize() {
        return this.noMatchSize;
    }

    /**
     * Sets the maximum number of phrases the fvh will consider if the field doesn't also define phraseLimit.
     *
     * @param phraseLimit maximum number of phrases the fvh will consider
     * @return this for chaining
     */
    @SuppressWarnings("unchecked")
    public HB phraseLimit(Integer phraseLimit) {
        this.phraseLimit = phraseLimit;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #phraseLimit(Integer)}
     */
    public Integer phraseLimit() {
        return this.phraseLimit;
    }

    /**
     * Forces the highlighting to highlight fields based on the source even if fields are stored separately.
     */
    @SuppressWarnings("unchecked")
    public HB forceSource(Boolean forceSource) {
        this.forceSource = forceSource;
        return (HB) this;
    }

    /**
     * @return the value set by {@link #forceSource(Boolean)}
     */
    public Boolean forceSource() {
        return this.forceSource;
    }

    @Override
    public final int hashCode() {
        return Objects.hash(getClass(), Arrays.hashCode(preTags), Arrays.hashCode(postTags), fragmentSize,
                numOfFragments, highlighterType, fragmenter, order, highlightFilter,
                forceSource, boundaryScannerType, boundaryMaxScan, Arrays.hashCode(boundaryChars), boundaryScannerLocale,
                noMatchSize, phraseLimit, options, requireFieldMatch, doHashCode());
    }

    /**
     * fields only present in subclass should contribute to hashCode in the implementation
     */
    protected abstract int doHashCode();

    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        @SuppressWarnings("unchecked")
        HB other = (HB) obj;
        return Arrays.equals(preTags, other.preTags) &&
                Arrays.equals(postTags, other.postTags) &&
                Objects.equals(fragmentSize, other.fragmentSize) &&
                Objects.equals(numOfFragments, other.numOfFragments) &&
                Objects.equals(highlighterType, other.highlighterType) &&
                Objects.equals(fragmenter, other.fragmenter) &&
                Objects.equals(order, other.order) &&
                Objects.equals(highlightFilter, other.highlightFilter) &&
                Objects.equals(forceSource, other.forceSource) &&
                Objects.equals(boundaryScannerType, other.boundaryScannerType) &&
                Objects.equals(boundaryMaxScan, other.boundaryMaxScan) &&
                Arrays.equals(boundaryChars, other.boundaryChars) &&
                Objects.equals(boundaryScannerLocale, other.boundaryScannerLocale) &&
                Objects.equals(noMatchSize, other.noMatchSize) &&
                Objects.equals(phraseLimit, other.phraseLimit) &&
                Objects.equals(options, other.options) &&
                Objects.equals(requireFieldMatch, other.requireFieldMatch) &&
                doEquals(other);
    }

    /**
     * fields only present in subclass should be checked for equality in the implementation
     */
    protected abstract boolean doEquals(HB other);
}
