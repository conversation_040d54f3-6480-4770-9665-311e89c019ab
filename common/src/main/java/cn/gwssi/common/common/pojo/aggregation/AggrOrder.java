package cn.gwssi.common.common.pojo.aggregation;


import cn.gwssi.common.util.StringUtil;

public enum AggrOrder {
	ORDER_TERM_ASC("_term", "asc"), 
	ORDER_TERM_DESC("_term", "desc"), 
	ORDER_COUNT_ASC("_count", "asc"), 
	ORDER_COUNT_DESC("_count", "desc");

	String orderKey = "_count";
	String orderType = "desc";

	AggrOrder(String orderKey, String orderType) {
		this.orderKey = orderKey;
		this.orderType = orderType;
	}

	public boolean equals(AggrOrder _order) {
		if (_order == null) {
			return false;
		}

		return StringUtil.equals(this.orderKey, _order.orderKey)
				&& StringUtil.equals(this.orderType, _order.orderType);
	}
}
