package cn.gwssi.common.common.pojo;

import java.util.List;
import java.util.Map;

/**
 * 聚类参数类
 *
 * <AUTHOR>
 * @create 2016-05-17-14:41
 */
public class ClusterParam extends GsonObject{
	
	/**
	 * 索引(index)名称
	 */
	private String indexName;

	/**
	 * 类型(type)名称
	 */
	private String typeName;
    
	/**
     * 聚类表达式
     */
    private String searchExpress;

    /**
     * 排序字段
     */
    private String sortExpress;
	
	/**
	 * 设置检索数据库表的主键
	 */
	private String rowId;
	
    /**
     * 主题词/关键词个数
     */
    private int keywordNum;

    /**
     * 聚类结果的族数
     */
    private int classNum;

    /**
     * 迭代的次数
     * 该参数在运用kmeans算法时控制迭代的最多次数
     */
    private int iterMaxNum;
    
    /**
     * 屏蔽词
     * 用户不太关注的那些词，需要在聚类分析中将其的权重减少，甚至设置为0
     */
    private List<String> shieldingWord;
    
    /**
     * 强化词
     * 用户自定义的比较关注的那些词以及每个词增加的倍数
     */
    private Map<String, Integer> intensiveWord;
    
    /**
     * 权利要求关键词权重
     */
    private Double clkwr;
    
    /**
     * 摘要关键词权重
     */
    private Double abkwr;
    
    /**
     * 全文关键词权重
     */
    private Double ftkwor;

    /**
     * 参加聚类的最大数量
     */
    private int recordNum;
    
    public ClusterParam(){
    	
    }
    
    /**
     * 
     * @param clusterName    集群名称
     * @param indexName      索引名称
     * @param typeName		  类型名称
     * @param keywordNum     主题词/关键词个数
     * @param classNum  	  聚类结果的族数
     * @param iterMaxNum  	  聚类表达式
     * @param searchExpress  聚类表达式
     * @param sortExpress  	  排序字段
     * @param shieldingWord  屏蔽词
     * @param intensiveWord  强化词及强化倍数
     * @param clkwr   		  权利要求关键词权重
     * @param abkwr   		  全文关键词权重
     * @param ftkwor  		  参加聚类的最大数量
     */
    public ClusterParam(String indexName, String typeName, int keywordNum, int classNum, int iterMaxNum, String searchExpress, String sortExpress, List<String> shieldingWord, Map<String, Integer> intensiveWord, Double clkwr, Double abkwr, Double ftkwor) {
        
    	this.indexName = indexName;
    	this.typeName = typeName;
    	this.keywordNum = keywordNum;
        this.classNum = classNum;
        this.searchExpress = searchExpress;
        this.sortExpress = sortExpress;
        this.iterMaxNum = iterMaxNum;
        this.shieldingWord = shieldingWord;
        this.intensiveWord = intensiveWord;
        this.clkwr = clkwr;
        this.abkwr = abkwr;
        this.ftkwor = ftkwor;
    }

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

    public Double getClkwr() {
		return clkwr;
	}

	public void setClkwr(Double clkwr) {
		this.clkwr = clkwr;
	}

	public Double getAbkwr() {
		return abkwr;
	}

	public void setAbkwr(Double abkwr) {
		this.abkwr = abkwr;
	}

	public Double getFtkwor() {
		return ftkwor;
	}

	public void setFtkwor(Double ftkwor) {
		this.ftkwor = ftkwor;
	}

    public int getRecordNum() {
        return recordNum;
    }

    public void setRecordNum(int recordNum) {
        this.recordNum = recordNum;
    }

    public String getSearchExpress() {
        return searchExpress;
    }

    public void setSearchExpress(String searchExpress) {
        this.searchExpress = searchExpress;
    }

    public String getSortExpress() {
        return sortExpress;
    }

    public void setSortExpress(String sortExpress) {
        this.sortExpress = sortExpress;
    }

    public int getKeywordNum() {
        return keywordNum;
    }

    public void setKeywordNum(int keywordNum) {
        this.keywordNum = keywordNum;
    }

    public int getClassNum() {
        return classNum;
    }

    public void setClassNum(int classNum) {
        this.classNum = classNum;
    }


    public int getIterMaxNum() {
        return iterMaxNum;
    }

    public void setIterMaxNum(int iterMaxNum) {
        this.iterMaxNum = iterMaxNum;
    }

	public List<String> getShieldingWord() {
		return shieldingWord;
	}

	public void setShieldingWord(List<String> shieldingWord) {
		this.shieldingWord = shieldingWord;
	}

	public Map<String, Integer> getIntensiveWord() {
		return intensiveWord;
	}

	public void setIntensiveWord(Map<String, Integer> intensiveWord) {
		this.intensiveWord = intensiveWord;
	}

	public String getRowId() {
		return rowId;
	}

	public void setRowId(String rowId) {
		this.rowId = rowId;
	}
}
