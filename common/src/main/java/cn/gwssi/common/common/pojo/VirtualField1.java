package cn.gwssi.common.common.pojo;

/**
 * 虚拟字段
 * <AUTHOR>
 *
 */
public class VirtualField1 {
	
	//索引名称
	private String indexName;
	
	//字段名称
	private String name;
	
	//别名
	private String alias;
	
	//虚拟字段规则
	private String fields;

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getFields() {
		return fields;
	}

	public void setFields(String fields) {
		this.fields = fields;
	}
	

}
