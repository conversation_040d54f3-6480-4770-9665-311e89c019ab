package cn.gwssi.common.common.array;

import org.apache.log4j.Logger;

import java.lang.reflect.Array;

/**
 * 可排序的数组，参见：Arrays、ArrayList
 *
 */
public abstract class SortArrayBase<T> extends ArrayBase<T>
{
	private static final long serialVersionUID = 200901080101009007L;
	private static Logger log = Logger.getLogger(SortArrayBase.class);
	
    /**
     * Tuning parameter: list size at or below which insertion sort will be
     * used in preference to mergesort or quicksort.
     */
    private static final int INSERTIONSORT_THRESHOLD = 7;
    
    /**
     * 排序
     */
    @SuppressWarnings("unchecked")
	public void sort( )
    {
    	// 复制数组
        T aux[] = (T[]) Array.newInstance(elements.getClass().getComponentType(), size);
        System.arraycopy( elements, 0, aux, 0, size );
        
        // 排序
        mergeSort( aux, elements, 0, size, 0 );
    }
    
    /**
     * Src is the source array that starts at index 0
     * Dest is the (possibly larger) array destination with a possible offset
     * low is the index in dest to start sorting
     * high is the end index in dest to end sorting
     * off is the offset to generate corresponding low, high in src
     */
    private void mergeSort( T src[], T dest[], int low, int high, int off )
    {
    	int length = high - low;
    	
    	// Insertion sort on smallest arrays
    	if (length < INSERTIONSORT_THRESHOLD) {
            for (int i=low; i<high; i++){
            	int j=i;
            	
            	try{
	                for( ; j>low && compare(dest[j-1],dest[j])>0; j-- ){
	                    swap(dest, j, j-1);
	                }
            	}
            	catch( RuntimeException e ){
            		log.error( "排序错误：j=" + j + ";i=" + i );
            		throw e;
            	}
            }
            
            return;
        }
    	
        // Recursively sort halves of dest into src
        int destLow  = low;
        int destHigh = high;
        low  += off;
        high += off;
        int mid = (low + high) >>> 1;
        mergeSort(dest, src, low, mid, -off);
        mergeSort(dest, src, mid, high, -off);

        // If list is already sorted, just copy from src to dest.  This is an
        // optimization that results in faster sorts for nearly ordered lists.
        if( compare(src[mid-1],src[mid]) <= 0) {
            System.arraycopy(src, low, dest, destLow, length);
            return;
        }

        // Merge sorted halves (now in src) into dest
        for(int i = destLow, p = low, q = mid; i < destHigh; i++) {
            if (q >= high || p < mid && compare(src[p],src[q])<=0){
                dest[i] = src[p++];
            }
            else{
                dest[i] = src[q++];
            }
        }
    }

    /**
     * Swaps x[a] with x[b].
     */
    private void swap( T x[], int a, int b )
    {
    	T t = x[a];
    	x[a] = x[b];
    	x[b] = t;
    }


    /**
     * 二分查找
     * @param key
     * @return
     */
    public int binarySearch( T key )
    {
    	int low = 0;
    	int high = size-1;
    	
    	while (low <= high) {
    		int mid = (low + high) >>> 1;
    		int cmp = compare(elements[mid], key);
    		
    		if (cmp < 0){
    			low = mid + 1;
    		}
    		else if (cmp > 0){
    			high = mid - 1;
    		}
    		else{
    			return mid; // key found
    		}
    	}
    	
    	return -1;  // key not found.
    }
    
    /**
     * 比较
     * @param key
     * @return
     */
    public int binarySearch( String key )
    {
    	int low = 0;
    	int high = size-1;
    	
    	while (low <= high) {
    		int mid = (low + high) >>> 1;
    		int cmp = compare(elements[mid], key);
    		
    		if (cmp < 0){
    			low = mid + 1;
    		}
    		else if (cmp > 0){
    			high = mid - 1;
    		}
    		else{
    			return mid; // key found
    		}
    	}
    	
    	return -1;  // key not found.
    }
    
    /**
     * 比较两个对象
     * @param e1
     * @param e2
     * @return
     */
    protected abstract int compare( T e1, T e2 );
    
    /**
     * 比较对象和键字
     * @param e1
     * @param key
     * @return
     */
    protected abstract int compare( T e1, String key );
}
