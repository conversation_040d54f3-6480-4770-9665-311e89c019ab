package cn.gwssi.common.common.pojo.relationship;


import cn.gwssi.common.common.array.SortArrayBase;

/**
 * 
 * <AUTHOR>
 *
 */
public class RelationshipSortByKey extends SortArrayBase<RelationshipSortByRelation> {
	private static final long serialVersionUID = -2214321093251241063L;

	@Override
	protected int compare(RelationshipSortByRelation e1, RelationshipSortByRelation e2) {
		return e1.get(0).getKey().compareTo(e2.get(0).getKey());
	}

	@Override
	protected int compare(RelationshipSortByRelation e1, String key) {
		return e1.get(0).getKey().compareTo(key);
	}

	@Override
	protected RelationshipSortByRelation[] makeArray(int len) {
		return new RelationshipSortByRelation[len];
	}
}
