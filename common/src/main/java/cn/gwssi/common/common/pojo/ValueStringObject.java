package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;

import java.util.HashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class ValueStringObject extends ValueResult{
	private String value = null;
	
	public ValueStringObject(String value) {
		this.value = value;
	}
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	@Override
	public Set<String> getValueSet(){
		Set<String> set = new HashSet<String>();
		set.add(value);
		return set;
	}

	@Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}		
}
