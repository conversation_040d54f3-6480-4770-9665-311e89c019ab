package cn.gwssi.common.common.pojo;

import cn.gwssi.common.IPConstants;
import cn.gwssi.common.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;

public class DimCountResult {

    /**
     * 维度名称（字段名称）
     */
    private String name = null;

    /**
     * 数量
     */
    private long count = -1;

    /**
     * 比率，百分比
     */
    private float ratio;

    /**
     * metric 聚合结果
     */
    private long unique;
    private long max;
    private long min;
    private long avg;

    private List<DimCountResult> children = new ArrayList<>();
    private boolean hasChildren;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public float getRatio() {
        return ratio;
    }

    public void setRatio(float ratio) {
        this.ratio = ratio;
    }

    public void setRatio(long sum) {
        this.setRatio(NumberUtil.calPercent(this.count, sum));
    }

    public long getUnique() {
        return unique;
    }

    public void setUnique(long unique) {
        this.unique = unique;
    }

    public long getMax() {
        return max;
    }

    public void setMax(long max) {
        this.max = max;
    }

    public long getMin() {
        return min;
    }

    public void setMin(long min) {
        this.min = min;
    }

    public long getAvg() {
        return avg;
    }

    public void setAvg(long avg) {
        this.avg = avg;
    }

    public List<DimCountResult> getChildren() {
        return children;
    }

    public void setChildren(List<DimCountResult> children) {
        this.children = children;
        this.hasChildren = this.children.size() > 0;
    }

    public boolean hasChildren() {
        return this.hasChildren;
    }

    public void setHasChildren(boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    public DimCountResult setCountOther(long count) {
        this.setName(IPConstants.AGG_OTHER);
        this.setCount(count);
        return this;
    }
}
