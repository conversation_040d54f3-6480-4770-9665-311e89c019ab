package cn.gwssi.common.common.pojo.aggregation;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class AggregationRangeDim extends AggregationDim implements Serializable {
    private static final long serialVersionUID = 5407979005938776624L;

    private List<Range> ranges = new ArrayList<>();

    public AggregationRangeDim() {}
    public AggregationRangeDim(String fieldName) {
        super(fieldName);
    }

    public static class Range implements Serializable {
        private static final long serialVersionUID = 442603085138151174L;

        private String key;
        private Double from;
        private Double to;

        public Range(String key, Double from, Double to) {
            this.key = key;
            this.from = from;
            this.to = to;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Double getFrom() {
            return from;
        }

        public void setFrom(Double from) {
            this.from = from;
        }

        public Double getTo() {
            return to;
        }

        public void setTo(Double to) {
            this.to = to;
        }
    }

    public List<Range> getRanges() {
        return ranges;
    }

    public void setRanges(List<Range> ranges) {
        this.ranges = ranges;
    }

    public AggregationRangeDim addRange(Range range) {
        this.ranges.add(range);
        return this;
    }

    public AggregationRangeDim addRange(String key, Double from, Double to) {
        this.ranges.add(new Range(key, from, to));
        return this;
    }
}

