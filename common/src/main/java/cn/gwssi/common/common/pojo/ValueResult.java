package cn.gwssi.common.common.pojo;

import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class ValueResult {
	private String _id = null;
	
	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}


	/**
	 * @zyq 因为关联关系分析需要返回pid 跟DI/CKM保持一致 均需要返回pid
	 *
	 * */

	private String pid = null;

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
		set_id(pid);

	}

	/**
	 * 获取值
	 * 
	 * @return
	 */
	public abstract Set<String> getValueSet();
}
