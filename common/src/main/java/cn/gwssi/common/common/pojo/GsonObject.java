package cn.gwssi.common.common.pojo;

import com.google.gson.annotations.Expose;
import org.apache.log4j.Logger;

import java.util.List;


/**
 * <AUTHOR>
 */
public class GsonObject implements Cloneable {
    private static Logger logger = Logger.getLogger(GsonObject.class);
    /**
     * 索引
     */
    private String index = null;

    /**
     * 类型
     */
    private String type = null;

    /**
     * 评分
     */
    private Double score = 0.0;

    /**
     * 路由
     */
    private String routing = null;

    /**
     * 主键
     */
    @Expose
    private String id = null;

    private List<? extends GsonObject> children = null;

    public String getID() {
        return id;
    }

    public void setID(String id) {
        this.id = id;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getRouting() {
        return routing;
    }

    public void setRouting(String routing) {
        this.routing = routing;
    }

    public List<? extends GsonObject> getChildren() {
        return children;
    }

    public void setChildren(List<? extends GsonObject> children) {
        this.children = children;
    }

    @Override
    public Object clone() {
        try {
            GsonObject v = (GsonObject) super.clone();
            return v;
        } catch (CloneNotSupportedException e) {
            // this shouldn't happen, since we are Cloneable
            logger.info(e);
            throw new InternalError();
        }
    }
}
