package cn.gwssi.common.common.pojo;

/**
 * 普通字段
 * <AUTHOR>
 *
 */
public class ColumnAndField {
	
	//索引名称
	private String indexName;
	
	//名称
	private String name;
	
	//是否是数组
	private boolean isArray;
	
	//是否可以为空
	private boolean notNull;
	
	//字段类型
	private String type;
	
	//字段描述
	private String description;
	
	//分词器名称
	private String analyzer;

	//别名
	private String alias;

	//日期格式
	private String format;

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	// 不能用自动生成的get、set，转化json的时候会丢掉 is
	public boolean getIsArray() {
		return isArray;
	}

	// 不能用自动生成的get、set，转化json的时候会丢掉 is
	public void setIsArray(boolean isArray) {
		this.isArray = isArray;
	}

	public boolean isNotNull() {
		return notNull;
	}

	public void setNotNull(boolean notNull) {
		this.notNull = notNull;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getAnalyzer() {
		return analyzer;
	}

	public void setAnalyzer(String analyzer) {
		this.analyzer = analyzer;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}
}
