package cn.gwssi.common.common.pojo.aggregation;


import cn.gwssi.common.IPConstants;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AggregationFiltersDim extends AggregationDim implements Serializable {
    private static final long serialVersionUID = -8453767376659312380L;

    private static Map<String, String[]> groupFields = new HashMap<>();
    private List<Group> groups = new ArrayList<>();

    public AggregationFiltersDim() {}
    public AggregationFiltersDim(String fieldName) {
        super(fieldName);
    }
    public AggregationFiltersDim(String fieldName, Map<String, List<String>> groupMaps) {
        super(fieldName);
        for (String key : groupMaps.keySet()) {
            this.addGroup(key, groupMaps.get(key));
        }
    }

    // 组和组成员的映射。在这里定义，如果有变动，插件就不用修改了
    static {
        groupFields.put(IPConstants.FIELD_PACG, IPConstants.FIELD_PACG_ELEMENTS);
    }

    public static class Group implements Serializable {
        private static final long serialVersionUID = -6060754690829848572L;

        private String key;
        private List<String> values;

        public Group(String key, List<String> values) {
            this.key = key;
            this.values = values;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public List<String> getValues() {
            return values;
        }

        public void setValues(List<String> values) {
            this.values = values;
        }
    }

    public List<Group> getGroups() {
        return groups;
    }

    public void setGroups(List<Group> groups) {
        this.groups = groups;
    }

    public AggregationFiltersDim addGroup(AggregationFiltersDim.Group group) {
        this.groups.add(group);
        return this;
    }

    public AggregationFiltersDim addGroup(String key, List<String> values) {
        this.groups.add(new AggregationFiltersDim.Group(key, values));
        return this;
    }

    public static Map<String, String[]> getGroupFields() {
        return groupFields;
    }
}
