package cn.gwssi.common.common.pojo;


import cn.gwssi.common.IPConstants;

/**
 * 二维分析结果
 * 
 * <AUTHOR>
 *
 */
public class TwoDimSumResult {
	/**
	 * 组名
	 */
	private String groupName = null;
	
	/**
	 * 名称
	 */
	private String name = null;
	
	/**
	 * 求和值
	 */
	private double sum;

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}	
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public double getSum() {
		return sum;
	}

	public void setSum(double sum) {
		this.sum = sum;
	}
	
	@Override
	public String toString() {
		return IPConstants.GSON.toJson(this);
	}
}
