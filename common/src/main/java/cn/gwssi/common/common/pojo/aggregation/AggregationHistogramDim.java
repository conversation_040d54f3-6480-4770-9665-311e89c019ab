package cn.gwssi.common.common.pojo.aggregation;

import java.io.Serializable;

public class AggregationHistogramDim extends AggregationDim implements Serializable {

    private static final long serialVersionUID = 5407979005938776624L;

    private double interval;
    private double offset = 0;
    private double minBound = Double.MIN_VALUE;
    private double maxBound = Double.MAX_VALUE;
    private boolean keyed = false;

    public AggregationHistogramDim(String field) {
        this.setFieldName(field);
    }

    public AggregationHistogramDim(String field, double interval, double offset, double minBound, double maxBound, boolean keyed) {
        this(field);
        this.interval = interval;
        this.offset = offset;
        this.minBound = minBound;
        this.maxBound = maxBound;
        this.keyed = keyed;
    }

    public double getInterval() {
        return interval;
    }

    public void setInterval(double interval) {
        this.interval = interval;
    }

    public double getOffset() {
        return offset;
    }

    public void setOffset(double offset) {
        this.offset = offset;
    }

    public double getMinBound() {
        return minBound;
    }

    public void setMinBound(double minBound) {
        this.minBound = minBound;
    }

    public double getMaxBound() {
        return maxBound;
    }

    public void setMaxBound(double maxBound) {
        this.maxBound = maxBound;
    }

    public boolean isKeyed() {
        return keyed;
    }

    public void setKeyed(boolean keyed) {
        this.keyed = keyed;
    }
}
