package cn.gwssi.common.common.pojo;

/**
 * <AUTHOR>
 *
 */
public class ExtractedWord {
	/**
	 * 词语
	 */
	private String word = null;
	
	/**
	 * 词语类型（需要调整）
	 */
	private String wordType = null;
	
	/**
	 * 词语权重
	 */
	private float wordBoost = -1.0f;

	/**
	 * 词语在输入短语中出现的频率
	 */
	private int wordFrequency = -1;
	
	/**
	 * 词语在输入短语中首次出现的偏移量
	 */
	private int firstOffset = -1;

	public String getWord() {
		return word;
	}

	public void setWord(String word) {
		this.word = word;
	}

	public String getWordType() {
		return wordType;
	}

	public void setWordType(String wordType) {
		this.wordType = wordType;
	}

	public float getWordBoost() {
		return wordBoost;
	}

	public void setWordBoost(float wordBoost) {
		this.wordBoost = wordBoost;
	}

	public int getWordFrequency() {
		return wordFrequency;
	}

	public void setWordFrequency(int wordFrequency) {
		this.wordFrequency = wordFrequency;
	}

	public int getFirstOffset() {
		return firstOffset;
	}

	public void setFirstOffset(int firstOffset) {
		this.firstOffset = firstOffset;
	}
}
