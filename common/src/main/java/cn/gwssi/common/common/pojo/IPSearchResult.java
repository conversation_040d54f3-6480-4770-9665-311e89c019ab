package cn.gwssi.common.common.pojo;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPSearchResult<T extends GsonObject> {
	// 总数
	private long total = -1;

	// 耗时
	private long took = -1;

	// 滚动查询id
	private String scrollId;

	// 是否包含向量检索
    private boolean hasVector = false;
	
	/**
	 * 记录
	 */
	private List<T> records = new ArrayList<T>();

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}

    public long getTook() {
        return took;
    }

    public void setTook(long took) {
        this.took = took;
    }

    public List<T> getRecords() {
		return records;
	}

	public void setRecords(List<T> records) {
		this.records = records;
	}

    public String getScrollId() {
        return scrollId;
    }

    public void setScrollId(String scrollId) {
        this.scrollId = scrollId;
    }

    public boolean isHasVector() {
        return hasVector;
    }

    public void setHasVector(boolean hasVector) {
        this.hasVector = hasVector;
    }

    @Override
	public String toString() {
		StringBuilder buf = new StringBuilder();
		buf.append("total : [").append(total).append("]");
		if(records != null && !records.isEmpty()) {
			for(T record : records) {
				buf.append(record.toString());
				buf.append("\r\n");
			}
		}
		
		String result = buf.toString();
		return result;
	}
}
