package cn.gwssi.common.common.pojo.aggregation;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class AggregationDateRangeDim extends AggregationDim implements Serializable {
    private static final long serialVersionUID = 5532098289655916216L;

    private List<Range> ranges = new ArrayList<>();
    private String format;

    public AggregationDateRangeDim() {}
    public AggregationDateRangeDim(String fieldName) {
        super(fieldName);
    }

    public static class Range implements Serializable {
        private static final long serialVersionUID = -6164860932044545909L;

        private String key;
        private String from;
        private String to;

        public Range(String key, String from, String to) {
            this.key = key;
            this.from = from;
            this.to = to;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }
    }

    public List<Range> getRanges() {
        return ranges;
    }

    public void setRanges(List<Range> ranges) {
        this.ranges = ranges;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public AggregationDateRangeDim addRange(Range range) {
        this.ranges.add(range);
        return this;
    }

    public AggregationDateRangeDim addRange(String key, String from, String to) {
        this.ranges.add(new Range(key, from, to));
        return this;
    }
}

