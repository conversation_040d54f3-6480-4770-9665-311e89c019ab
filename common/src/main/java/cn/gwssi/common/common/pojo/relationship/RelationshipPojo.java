package cn.gwssi.common.common.pojo.relationship;

import java.util.HashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class RelationshipPojo {
	/**
	 * 键
	 */
	private String key = null;
	
	/**
	 * 关联方
	 */
	private String relation = null;
	
	/**
	 * 关联次数
	 */
	private int number = 0;
	
	/**
	 * 主键列表
	 */
	private Set<String> mainIDs = new HashSet<String>();
	
	/**
	 * 
	 * @param key
	 * @param relation
	 */
	public RelationshipPojo(String key, String relation) {
		this.key = key;
		this.relation = relation;
		this.number = 1;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}
	
	public void incNumber() {
		this.number += 1;
	}

	public Set<String> getMainIDs() {
		return mainIDs;
	}

	public void addMainID(String mainID) {
		this.mainIDs.add(mainID);
	}

	public void setMainIDs(Set<String> mainIDs) {
		this.mainIDs = mainIDs;
	}
}
