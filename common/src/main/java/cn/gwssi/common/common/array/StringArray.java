package cn.gwssi.common.common.array;


public class StringArray extends ArrayBase<String>
{
	private static final long serialVersionUID = 200511110101002002L;

	/**
	 * 生成数组
	 * @param len
	 * @return
	 */
	@Override
	protected String[] makeArray(int len )
	{
		return new String[len];
	}
	
	/**
	 * 增加数组
	 * @param set
	 * @return
	 */
	public boolean add( StringArray set )
	{
		int num = set.size;
		ensureCapacity( size + num );
		System.arraycopy(set.elements, 0, elements, size, num);
        size += num;
        return num != 0;
	}
	
    /**
     * 分拆字符串
     * @param src
     * @param regex
     */
    public void split(String src, String regex )
    {
    	if( src == null || src.length() == 0 ){
    		return;
    	}
    	
    	String list[] = src.split( regex );
		
		// 截去空格
		for( int i=0; i<list.length; i++ ){
			list[i] = list[i].trim();
		}
		
		// 增加到数组中
		add( list );
    }
    
    /**
     * 分拆字符串
     * @param value
     * @param splitChar
     */
    public void split(String value, char splitChar )
    {
        int len = value.length();
        char content[] = new char[len];
        value.getChars(0, len, content, 0);
        
        int i;
        int start = 0;
        for (i = 0; i < len; i++) {
     	   if( content[i] == splitChar ){
     		   String v = String.valueOf(content, start, i-start);
     		   start = i + 1;
     		   add( v );
     	   }
        }
        
        if( i > start ){
     	   String v = String.valueOf(content, start, i-start);
     	   add( v );
        }
    }
    
    /**
     * 生成字符串
     * @param delim 分割符
     * @return
     */
    public String meger(String delim )
    {
    	if( size == 0 ){
    		return null;
    	}
    	
    	StringBuilder result = new StringBuilder();
    	
    	// 第一个字符串
    	result.append( elements[0] );
    	
    	// 后续的字符串
    	for( int i=1; i<size; i++ ){
    		result.append( delim );
    		result.append( elements[i] );
    	}
    	
    	return result.toString();
    }
    
    public String toString()
    {
    	StringBuilder result = new StringBuilder();
    	if( size != 0 ){
        	// 第一个字符串
        	result.append( elements[0] );
        	
        	// 后续的字符串
        	for( int i=1; i<size; i++ ){
        		result.append( ',' );
        		result.append( elements[i] );
        	}
    	}
    	
    	return result.toString();
    }
    

    /**
     * 查找数据
     * @param elem
     * @return
     */
    public int indexOf( String elem )
    {
    	if (elem == null) {
    		for (int i = 0; i < size; i++){
    			if (elements[i] == null){
    				return i;
    			}
    		}
    	}
    	else {
    		for (int i = 0; i < size; i++){
    			if (elem.compareTo(elements[i]) == 0){
    				return i;
    			}
    		}
    	}
    	
    	return -1;
    }

    /**
     * 从数组的末尾开始查找数据是否存在
     * @param elem
     * @return
     */
    public int lastIndexOf( String elem )
    {
    	if (elem == null) {
    		for (int i = size-1; i >= 0; i--){
    			if (elements[i]==null){
    				return i;
    			}
    		}
    	}
    	else {
    		for (int i = size-1; i >= 0; i--){
    			if (elem.compareTo(elements[i]) == 0){
    				return i;
    			}
    		}
    	}
    	
    	return -1;
    }
    
}
