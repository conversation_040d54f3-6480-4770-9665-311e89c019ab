package cn.gwssi.common.common.array;

import org.apache.log4j.Logger;

import java.io.Serializable;
import java.util.Iterator;
import java.util.NoSuchElementException;
import java.util.RandomAccess;

public abstract class ArrayBase<T> implements RandomAccess, Serializable, Cloneable, Iterator<T>, Iterable<T>
{
	private static final long serialVersionUID = 200901080101009006L;
	private static Logger log = Logger.getLogger(ArrayBase.class);
	
	/**
	 * 空闲的数组大小
	 */
	private static final int CAPACITY = 20;
	
	/**
	 * 记录大小
	 */
	protected int size = 0;
	
	/**
	 * 记录列表
	 */
	protected transient T[] elements = makeArray( CAPACITY );
	
	/**
	 * 生成数组
	 * @param len
	 * @return
	 */
	protected abstract T[] makeArray( int len );

    public T[] toArray()
    {
    	T[] result = makeArray( size );
    	System.arraycopy( elements, 0, result, 0, size );
    	return result;
    }
    
    /**
     * 生成数组
     * @return
     */
    public T[] toArray( T a[] )
    {
    	if( a.length < size ){
            a = makeArray( size );
    	}
    	
    	System.arraycopy( elements, 0, a, 0, size );

        if( a.length > size ){
            a[size] = null;
        }

        return a;
    }

	/**
	 * 取指定的记录
	 * @param index
	 * @return
	 */
	public T get( int index )
	{
		rangeCheck( index );
		return elements[index];
	}

	/**
	 * 设置内容
	 * @param index
	 * @param element
	 * @return
	 */
    public T set(int index, T element)
    {
    	rangeCheck( index );
    	T oldValue = elements[index];
    	elements[index] = element;
    	return oldValue;
    }

	/**
	 * 增加记录
	 * @param record
	 */
	public boolean add( T record )
	{
		// 增加数据
		ensureCapacity( size + 1 );
		elements[size++] = record;
		return true;
	}

	public boolean add( T[] record )
	{
		int num = record.length;
		ensureCapacity( size + num );
		System.arraycopy(record, 0, elements, size, num);
        size += num;
        return num != 0;
	}

    public void add( int index, T record )
    {
    	if( index > size || index < 0 ){
			throw new IndexOutOfBoundsException(
					"记录越界，记录数量[" + String.valueOf(size) +
					"]，增加的记录编号：" + String.valueOf(index)
			);
		}
    	
    	ensureCapacity( size+1 );
    	System.arraycopy( elements, index, elements, index + 1, size - index );
    	elements[index] = record;
    	size++;
    }

    /**
     * 删除记录
     * @param index
     * @return
     */
    public T remove(int index)
    {
    	rangeCheck( index );
    	T oldValue = elements[index];

    	int numMoved = size - index - 1;
    	if (numMoved > 0){
    		System.arraycopy( elements, index+1, elements, index, numMoved );
    	}
    	
    	elements[--size] = null; // Let gc do its work
    	
    	return oldValue;
    }
    
    /**
     * 删除指定的记录
     * @param record
     */
    public void remove( T record )
    {
    	if( record == null ){
    		return;
    	}
    	
    	for( int ii = 0; ii < size; ii++ ){
			if( record.equals(elements[ii]) ){
				remove( ii );
				break;
			}
		}
    }


	/**
	 * 取记录数量
	 * @return
	 */
	public int size()
	{
		return size;
	}
	
	/**
	 * 判断是否空
	 * @return
	 */
    public boolean isEmpty() {
    	return size == 0;
    }


    /**
     * 判断数据是否存在
     * @param elem
     * @return
     */
    public boolean contains( T elem )
    {
		return indexOf( elem ) >= 0;
    }

    /**
     * 查找数据
     * @param elem
     * @return
     */
    public int indexOf( T elem )
    {
    	if (elem == null) {
    		for (int i = 0; i < size; i++){
    			if (elements[i] == null){
    				return i;
    			}
    		}
    	}
    	else {
    		for (int i = 0; i < size; i++){
    			if (elem.equals(elements[i])){
    				return i;
    			}
    		}
    	}
    	
    	return -1;
    }

    /**
     * 从数组的末尾开始查找数据是否存在
     * @param elem
     * @return
     */
    public int lastIndexOf( T elem )
    {
    	if (elem == null) {
    		for (int i = size-1; i >= 0; i--){
    			if (elements[i]==null){
    				return i;
    			}
    		}
    	}
    	else {
    		for (int i = size-1; i >= 0; i--){
    			if (elem.equals(elements[i])){
    				return i;
    			}
    		}
    	}
    	
    	return -1;
    }
    
    /**
     * 移动块
     * @param fromIndex
     * @param toIndex
     */
    protected void removeRange(int fromIndex, int toIndex)
    {
    	int numMoved = size - toIndex;
        System.arraycopy( elements, toIndex, elements, fromIndex, numMoved );
        
        // Let gc do its work
        int newSize = size - (toIndex-fromIndex);
        while (size != newSize){
        	elements[--size] = null;
        }
    }

    /**
     * 压缩空间
     */
    public void trimToSize()
    {
    	int oldCapacity = elements.length;
    	if (size < oldCapacity) {
    		T oldData[] = elements;
    		elements = makeArray( size );
    		System.arraycopy(oldData, 0, elements, 0, size);
    	}
    }
    
    /**
     * 复制记录
     */
    @SuppressWarnings("unchecked")
	public Object clone()
    {
    	try { 
    		ArrayBase<T> v = (ArrayBase<T>)super.clone();
    		v.elements = makeArray( size );
    		System.arraycopy(elements, 0, v.elements, 0, size);
    		return v;
    	}
    	catch (CloneNotSupportedException e) {
    		// this shouldn't happen, since we are Cloneable
    		throw new InternalError();
    	}
    }

    /**
     * 清空数组
     */
    public void clear()
    {
    	// Let gc do its work
    	for (int i = 0; i < size; i++){
    		elements[i] = null;
    	}
    	
    	size = 0;
    }
    
	/**
	 * 检查是否越界
	 * @param index
	 */
	protected  void rangeCheck( int index )
	{
		if( index >= size ){
			throw new IndexOutOfBoundsException( "记录越界，记录数量[" +
					String.valueOf(size) + "]，取记录的编号：" + String.valueOf(index)
			);
		}
	}
	
	/**
	 * 扩展空间，不支持多线程
	 * @param minCapacity
	 */
    protected void ensureCapacity( int minCapacity )
    {
    	int oldCapacity = elements.length;
    	if( minCapacity > oldCapacity ){
    		T oldData[] = elements;
    		int newCapacity = oldCapacity + CAPACITY;
    	    if( newCapacity < minCapacity ){
    	    	newCapacity = minCapacity;
    	    }

        	try{
	    	    elements = makeArray( newCapacity );
	    	    System.arraycopy( oldData, 0, elements, 0, size );
        	}
        	catch( RuntimeException e ){
        		log.error( "array error:" + oldCapacity + "," + minCapacity + "," + newCapacity + "," + size );
        		throw e;
        	}
    	}
    }
    
    /**
     * 保存数据 serialize it
     * @param s
     * @throws java.io.IOException
     */
    private void writeObject(java.io.ObjectOutputStream s)
    	throws java.io.IOException
    {
    	// Write out element count, and any hidden stuff
    	s.defaultWriteObject();
    	
    	// Write out array length
    	s.writeInt( elements.length );
    	
    	// Write out all elements in the proper order.
    	for ( int i=0; i<size; i++ ){
    		s.writeObject(elements[i]);
    	}
    }

    /**
     * 从文件中读取对象
     * @param s
     * @throws java.io.IOException
     * @throws ClassNotFoundException
     */
    @SuppressWarnings("unchecked")
	private void readObject(java.io.ObjectInputStream s)
    	throws java.io.IOException, ClassNotFoundException
    {
    	// Read in size, and any hidden stuff
    	s.defaultReadObject();
    	
    	// Read in array length and allocate array
    	int arrayLength = s.readInt();
        elements = makeArray( arrayLength );
        
        // Read in all elements in the proper order.
        for (int i=0; i<size; i++){
        	elements[i] = (T)s.readObject();
        }
    }

    /* ************* 开始 Iterator 的方法 ********************* */
    /**
     * Iterator中的指针
     */
    protected int pos = 0;
    
    public void setPosition( int pos )
    {
    	this.pos = (pos >=0 && pos < size) ? pos : 0;
    }
	
    /**
     * Move to next element in the array.
     *
     * @return The next object in the array.
     */
    public T next()
    {
        if( pos >= size ){
            // 没有下一个数据
            throw new NoSuchElementException( "下标越界: " + pos + " / " + size );
        }
        
        return elements[pos++];
    }

    /**
     * Check to see if there is another element in the array.
     *
     * @return Whether there is another element.
     */
    public boolean hasNext()
    {
        if( pos < size ){
        	return true;
        }
        else{
        	// 重置，下次循环的时候可以使用
        	pos = 0;
        	return false;
        }
    }

    /**
     * 删除当前数据
     */
    public void remove()
    {
    	if( pos >= size ){
        	// 没有下一个数据
        	throw new NoSuchElementException( "下标越界: " + pos + " / " + size );
    	}
    	
    	remove( pos );
    }

    /* ************* 结束 Iterator 的方法 ********************* */

	public Iterator<T> iterator()
	{
		pos = 0;
		return this;
	}
    
}
