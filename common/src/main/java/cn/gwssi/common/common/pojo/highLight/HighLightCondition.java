package cn.gwssi.common.common.pojo.highLight;

import java.io.Serializable;
import java.util.*;

public class HighLightCondition extends AbstractHighLight<HighLightCondition> implements Serializable {
    private final List<Field> fields = new ArrayList<>();

    private String encoder;

    private boolean useExplicitFieldOrder = false;

    public HighLightCondition() {}

    /**
     * Read from a stream.
     */
    public HighLightCondition(String encoder, boolean useExplicitFieldOrder, List<Field> fields) {
        encoder(encoder);
        useExplicitFieldOrder(useExplicitFieldOrder);
        fields(fields);
    }

    /**
     * Read from a stream.
     */
    public HighLightCondition(String[] preTags, String[] postTags, Integer fragmentSize, Integer numOfFragments,
                              String highlighterType, String fragmenter, String order, Boolean highlightFilter,
                              Boolean forceSource, BoundaryScannerType boundaryScannerType, Integer boundaryMaxScan,
                              char[] boundaryChars, String boundaryScannerLocale, Integer noMatchSize,
                              Integer phraseLimit, Map<String, Object> options, Boolean requireFieldMatch,
                              String encoder, boolean useExplicitFieldOrder, List<Field> fields) {
        super(preTags, postTags, fragmentSize, numOfFragments, highlighterType, fragmenter, order, highlightFilter,
                forceSource, boundaryScannerType, boundaryMaxScan, boundaryChars, boundaryScannerLocale, noMatchSize,
                phraseLimit, options, requireFieldMatch);
        encoder(encoder);
        useExplicitFieldOrder(useExplicitFieldOrder);
        fields(fields);
    }

    /**
     * Adds a field to be highlighted with default fragment size of 100 characters, and
     * default number of fragments of 5 using the default encoder
     *
     * @param name The field to highlight
     */
    public HighLightCondition field(String name) {
        return field(new Field(name));
    }

    /**
     * Adds a field to be highlighted with a provided fragment size (in characters), and
     * default number of fragments of 5.
     *
     * @param name         The field to highlight
     * @param fragmentSize The size of a fragment in characters
     */
    public HighLightCondition field(String name, int fragmentSize) {
        return field(new Field(name).fragmentSize(fragmentSize));
    }


    /**
     * Adds a field to be highlighted with a provided fragment size (in characters), and
     * a provided (maximum) number of fragments.
     *
     * @param name              The field to highlight
     * @param fragmentSize      The size of a fragment in characters
     * @param numberOfFragments The (maximum) number of fragments
     */
    public HighLightCondition field(String name, int fragmentSize, int numberOfFragments) {
        return field(new Field(name).fragmentSize(fragmentSize).numOfFragments(numberOfFragments));
    }

    /**
     * Adds a field to be highlighted with a provided fragment size (in characters), and
     * a provided (maximum) number of fragments.
     *
     * @param name              The field to highlight
     * @param fragmentSize      The size of a fragment in characters
     * @param numberOfFragments The (maximum) number of fragments
     * @param fragmentOffset    The offset from the start of the fragment to the start of the highlight
     */
    public HighLightCondition field(String name, int fragmentSize, int numberOfFragments, int fragmentOffset) {
        return field(new Field(name).fragmentSize(fragmentSize).numOfFragments(numberOfFragments)
                .fragmentOffset(fragmentOffset));
    }

    public HighLightCondition field(Field field) {
        fields.add(field);
        return this;
    }

    void fields(List<Field> fields) {
        this.fields.addAll(fields);
    }

    public List<Field> fields() {
        return this.fields;
    }

    /**
     * Set a tag scheme that encapsulates a built in pre and post tags. The allowed schemes
     * are <tt>styled</tt> and <tt>default</tt>.
     *
     * @param schemaName The tag scheme name
     */
    public HighLightCondition tagsSchema(String schemaName) {
        if (!"default".equals(schemaName) && !"styled".equals(schemaName)) {
            throw new IllegalArgumentException("Unknown tag schema [" + schemaName + "]");
        }
        return this;
    }

    /**
     * Set encoder for the highlighting
     * are <tt>styled</tt> and <tt>default</tt>.
     *
     * @param encoder name
     */
    public HighLightCondition encoder(String encoder) {
        this.encoder = encoder;
        return this;
    }

    /**
     * Getter for {@link #encoder(String)}
     */
    public String encoder() {
        return this.encoder;
    }

    /**
     * Send the fields to be highlighted using a syntax that is specific about the order in which they should be highlighted.
     *
     * @return this for chaining
     */
    public HighLightCondition useExplicitFieldOrder(boolean useExplicitFieldOrder) {
        this.useExplicitFieldOrder = useExplicitFieldOrder;
        return this;
    }

    /**
     * Gets value set with {@link #useExplicitFieldOrder(boolean)}
     */
    public Boolean useExplicitFieldOrder() {
        return this.useExplicitFieldOrder;
    }

    static Character[] convertCharArray(char[] array) {
        if (array == null) {
            return null;
        }
        Character[] charArray = new Character[array.length];
        for (int i = 0; i < array.length; i++) {
            charArray[i] = array[i];
        }
        return charArray;
    }

    @Override
    protected int doHashCode() {
        return Objects.hash(encoder, useExplicitFieldOrder, fields);
    }

    @Override
    protected boolean doEquals(HighLightCondition other) {
        return Objects.equals(encoder, other.encoder) &&
                Objects.equals(useExplicitFieldOrder, other.useExplicitFieldOrder) &&
                Objects.equals(fields, other.fields);
    }

    public static class Field extends AbstractHighLight<Field> {

        private final String name;

        int fragmentOffset = -1;

        String[] matchedFields;

        public Field(String name) {
            this.name = name;
        }

        /**
         * Read from a stream.
         */
        public Field(String name, int fragmentOffset, String[] matchedFields) {
            this.name = name;
            this.fragmentOffset = fragmentOffset;
            this.matchedFields = matchedFields;
        }

        /**
         * Read from a stream.
         */
        public Field(String[] preTags, String[] postTags, Integer fragmentSize, Integer numOfFragments,
                     String highlighterType, String fragmenter, String order, Boolean highlightFilter,
                     Boolean forceSource, BoundaryScannerType boundaryScannerType, Integer boundaryMaxScan,
                     char[] boundaryChars, String boundaryScannerLocale, Integer noMatchSize,
                     Integer phraseLimit, Map<String, Object> options, Boolean requireFieldMatch,
                     String name, int fragmentOffset, String[] matchedFields) {
            super(preTags, postTags, fragmentSize, numOfFragments, highlighterType, fragmenter, order, highlightFilter,
                    forceSource, boundaryScannerType, boundaryMaxScan, boundaryChars, boundaryScannerLocale, noMatchSize,
                    phraseLimit, options, requireFieldMatch);
            this.name = name;
            this.fragmentOffset = fragmentOffset;
            this.matchedFields = matchedFields;
        }

        public String name() {
            return name;
        }

        public Field fragmentOffset(int fragmentOffset) {
            this.fragmentOffset = fragmentOffset;
            return this;
        }

        public int fragmentOffset() {
            return this.fragmentOffset;
        }

        /**
         * Set the matched fields to highlight against this field data.  Default to null, meaning just
         * the named field.  If you provide a list of fields here then don't forget to include name as
         * it is not automatically included.
         */
        public Field matchedFields(String... matchedFields) {
            this.matchedFields = matchedFields;
            return this;
        }

        public String[] matchedFields() {
            return this.matchedFields;
        }

        @Override
        protected int doHashCode() {
            return Objects.hash(name, fragmentOffset, Arrays.hashCode(matchedFields));
        }

        @Override
        protected boolean doEquals(Field other) {
            return Objects.equals(name, other.name) &&
                    Objects.equals(fragmentOffset, other.fragmentOffset) &&
                    Arrays.equals(matchedFields, other.matchedFields);
        }
    }

    public enum Order {
        NONE, SCORE;

        public static Order fromString(String order) {
            if (order.toUpperCase(Locale.ROOT).equals(SCORE.name())) {
                return Order.SCORE;
            }
            return NONE;
        }

        @Override
        public String toString() {
            return name().toLowerCase(Locale.ROOT);
        }
    }

    public enum BoundaryScannerType {
        CHARS, WORD, SENTENCE;

        public static BoundaryScannerType fromString(String boundaryScannerType) {
            return valueOf(boundaryScannerType.toUpperCase(Locale.ROOT));
        }

        @Override
        public String toString() {
            return name().toLowerCase(Locale.ROOT);
        }
    }
}
