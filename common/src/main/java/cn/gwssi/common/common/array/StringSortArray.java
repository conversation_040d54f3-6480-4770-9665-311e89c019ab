package cn.gwssi.common.common.array;

public class StringSortArray extends SortArrayBase<StringObject> {
	private static final long serialVersionUID = 7759961533346125088L;
	
	/**
	 * 
	 * @param value
	 */
	public void add(String value) {
		this.add(new StringObject(value));
	}
	
	@Override
	protected int compare(StringObject e1, StringObject e2) {
		if((e1 == null || e1.getValue() == null) && (e2 == null || e2.getValue() == null)) {
			return 0;
		}
		
		if(e1 == null || e1.getValue() == null) {
			return -1;
		}
		
		if(e2 == null || e2.getValue() == null) {
			return 1;
		}
		
		return e1.getValue().compareTo(e2.getValue());
	}

	@Override
	protected int compare(StringObject e1, String key) {
		if((e1 == null || e1.getValue() == null) && (key == null)) {
			return 0;
		}
		
		if(e1 == null || e1.getValue() == null) {
			return -1;
		}
		
		if(key == null) {
			return 1;
		}
		
		return e1.getValue().compareTo(key);
	}

	@Override
	protected StringObject[] makeArray(int len) {
		return new StringObject[len];
	}

}
