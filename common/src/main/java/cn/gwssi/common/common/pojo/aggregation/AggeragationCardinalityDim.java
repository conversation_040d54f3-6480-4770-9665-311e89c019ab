package cn.gwssi.common.common.pojo.aggregation;

import java.io.Serializable;

public class AggeragationCardinalityDim extends AggregationDim implements Serializable {

    private static final long serialVersionUID = 5407979005938776624L;

    private long precision;

    private String groupBy;
    private String groupBy2;
    private String sort;
    private boolean isAsc;

    public AggeragationCardinalityDim(String field) {
        this.setFieldName(field);
    }

    public AggeragationCardinalityDim(String fieldName, String groupBy, String groupBy2, String sort, boolean isAsc) {
        this(fieldName);
        this.groupBy = groupBy;
        this.groupBy2 = groupBy2;
        this.sort = sort;
        this.isAsc = isAsc;
    }

    public long getPrecision() {
        return precision;
    }

    public void setPrecision(long precision) {
        this.precision = precision;
    }

    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    public String getGroupBy2() {
        return groupBy2;
    }

    public void setGroupBy2(String groupBy2) {
        this.groupBy2 = groupBy2;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public boolean isAsc() {
        return isAsc;
    }

    public void setAsc(boolean asc) {
        isAsc = asc;
    }
}
