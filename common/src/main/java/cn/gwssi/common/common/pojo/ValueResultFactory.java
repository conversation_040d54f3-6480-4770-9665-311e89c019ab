package cn.gwssi.common.common.pojo;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ValueResultFactory {
	private Map<String, Class<? extends ValueResult>> valueResultMap = new HashMap<String, Class<? extends ValueResult>>();
	
	private static ValueResultFactory instance = null;
	
	private ValueResultFactory() {}
	/**
	 * 
	 * @return
	 */
	public static ValueResultFactory getInstance() {
		if(instance == null) {
			instance = new ValueResultFactory();
		}
		
		return instance;
	}
	
	public void addValueResultMap(String fieldName, Class<? extends ValueResult> valueResultClazz) {
		valueResultMap.put(fieldName, valueResultClazz);
	}
	
	public Class<? extends ValueResult> getValueResultClazz(String fieldName) {
		return valueResultMap.get(fieldName);
	}
}
