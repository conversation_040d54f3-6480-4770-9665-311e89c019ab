package cn.gwssi.common.common.pojo;

public class NestedField {
	
	//String indexName, String nestedName, String nestedDesc, String name, boolean getIsArray, boolean notNull,  String type, String description, String analyzer
	
	//索引名称
	private String indexName ;

	//集合名称
	private String nestedName;
	
	//集合描述
	private String nestedDesc;
	
	//字段名称
	private String name;
	
	//是否为数组
	private boolean isArray;
	
	//是否可以为空
	private boolean notNull;
	
	//字段类型
	private String type;
	
	//字段描述
	private String description;
	
	//分词器名称
	private String analyzer;

	private String alias;

	private String format;

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public String getNestedName() {
		return nestedName;
	}

	public void setNestedName(String nestedName) {
		this.nestedName = nestedName;
	}

	public String getNestedDesc() {
		return nestedDesc;
	}

	public void setNestedDesc(String nestedDesc) {
		this.nestedDesc = nestedDesc;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isArray() {
		return isArray;
	}

	public void setArray(boolean isArray) {
		this.isArray = isArray;
	}

	public boolean isNotNull() {
		return notNull;
	}

	public void setNotNull(boolean notNull) {
		this.notNull = notNull;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getAnalyzer() {
		return analyzer;
	}

	public void setAnalyzer(String analyzer) {
		this.analyzer = analyzer;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}
}
