package cn.gwssi.common.common.pojo;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class UpdateResult {
	/**
	 * 成功_id 列表
	 */
	private List<String> successList = new ArrayList<String>();

	/**
	 * 失败_id 列表
	 */
	private List<String> failList = new ArrayList<String>();
	
	/**
	 * 
	 * @param _id
	 */
	public void addSuccessRecord(String _id) {
		successList.add(_id);
	}
	
	/**
	 * 
	 * @param _id
	 */
	public void addFailRecord(String _id) {
		failList.add(_id);
	}

	public List<String> getSuccessList() {
		return successList;
	}

	public void setSuccessList(List<String> successList) {
		this.successList = successList;
	}

	public List<String> getFailList() {
		return failList;
	}

	public void setFailList(List<String> failList) {
		this.failList = failList;
	}	
}
