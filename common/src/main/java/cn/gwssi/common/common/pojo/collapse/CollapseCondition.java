package cn.gwssi.common.common.pojo.collapse;

import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CollapseCondition implements Serializable {

    private static final long serialVersionUID = 7918544565025811748L;

    public CollapseCondition() {}

    public CollapseCondition(String field) {
        this.field = field;
    }

    private String field;
    private SortFieldList sorts;
    private List<InnerHitCondition> innerHits;
    private int size;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public SortFieldList getSorts() {
        return sorts;
    }

    public void setSorts(SortFieldList sorts) {
        this.sorts = sorts;
    }

    public List<InnerHitCondition> getInnerHits() {
        return innerHits;
    }

    public void setInnerHits(List<InnerHitCondition> innerHits) {
        this.innerHits = innerHits;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public CollapseCondition addSort(SortField sort) {
        if (null == sorts) {
            sorts = new SortFieldList();
        }

        this.sorts.add(sort);
        return this;
    }

    public CollapseCondition addInnerHits(InnerHitCondition innerHit) {
        if (null == innerHits) {
            innerHits = new ArrayList<>();
        }

        this.innerHits.add(innerHit);
        return this;
    }

    @Override
    public CollapseCondition clone() {
        CollapseCondition condition = new CollapseCondition();
        condition.setField(this.field);
        condition.setSize(this.size);

        // clone sort
        SortFieldList sortsSource = this.sorts;
        SortFieldList sortsTarget = null;
        if (null != sortsSource && sortsSource.size() > 0) {
            sortsTarget = new SortFieldList();
            for (SortField source : sortsSource) {
                sortsTarget.add(source.clone());
            }
        }

        // clone innerHit
        List<InnerHitCondition> innerSource = this.innerHits;
        List<InnerHitCondition> innerTarget = null;
        if (null != innerSource && innerSource.size() > 0) {
            innerTarget = new ArrayList<>();
            for (InnerHitCondition inner : innerSource) {
                innerTarget.add(inner.clone());
            }
        }

        condition.setSorts(sortsTarget);
        condition.setInnerHits(innerTarget);
        return condition;
    }
}
