package cn.gwssi.common.common.pojo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by duq on 2018/11/1.
 */
public class IndexSettings implements Serializable {
    private static final long serialVersionUID = -9013580309318844476L;

    private String indexName;

    private String typeName;

    private String numberOfShards;

    private String numberOfReplicas;

    private List<ColumnAndField> fields;

    private List<NestedField> nestedFields;

    private List<VirtualField1> virtualFields;

    private String createTime;

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getNumberOfShards() {
        return numberOfShards;
    }

    public void setNumberOfShards(String numberOfShards) {
        this.numberOfShards = numberOfShards;
    }

    public String getNumberOfReplicas() {
        return numberOfReplicas;
    }

    public void setNumberOfReplicas(String numberOfReplicas) {
        this.numberOfReplicas = numberOfReplicas;
    }

    public List<ColumnAndField> getFields() {
        return fields;
    }

    public void setFields(List<ColumnAndField> fields) {
        this.fields = fields;
    }

    public List<NestedField> getNestedFields() {
        return nestedFields;
    }

    public void setNestedFields(List<NestedField> nestedFields) {
        this.nestedFields = nestedFields;
    }

    public List<VirtualField1> getVirtualFields() {
        return virtualFields;
    }

    public void setVirtualFields(List<VirtualField1> virtualFields) {
        this.virtualFields = virtualFields;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
