package cn.gwssi.common.common;

import cn.gwssi.common.exception.code.ErrorCode1;
import cn.gwssi.common.exception.code.ErrorCode2;
import cn.gwssi.common.exception.code.ErrorCode3;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPDaoErrCodes {
	/**
	 * ip_type配置信息 name为空
	 */
	public static ErrorCode1 IPTYPE_CONFIG_ERROR = new ErrorCode1("DAO000", "解析配置文件{0}时，出现错误！");

	/**
	 * ip_type配置信息 name为空
	 */
	public static ErrorCode1 IPTYPE_NAME_ERROR = new ErrorCode1("DAO100", "配置文件{0}中， ip_type的name属性为空！");

	/**
	 * column配置信息 name为空
	 */
	public static ErrorCode1 COLUMN_NAME_ERROR = new ErrorCode1("DAO100", "配置文件{0}中， 有column配置信息的name属性为空！");

	/**
	 * field配置信息 name为空
	 */
	public static ErrorCode1 FIELD_NAME_ERROR = new ErrorCode1("DAO200", "配置文件{0}中， 有field配置信息的name属性为空！");

	/**
	 * field配置的column不存在
	 */
	public static ErrorCode3 COLUMN_NOT_EXIST_ERROR = new ErrorCode3("DAO200", "配置文件{0}中， field{1}配置的column{2}不存在！");

	/**
	 * 嵌套配置信息 name 为空
	 */
	public static ErrorCode1 NESTED_NAME_ERROR = new ErrorCode1("DAO500", "配置文件{0}中， 有nested配置信息的name属性为空！");

	/**
	 * 虛字段配置信息 name 为空
	 */
	public static ErrorCode1 VIRTUAL_FIELD_NAME_ERROR = new ErrorCode1("DAO700", "配置文件{0}中， 有virtual_field配置信息的name属性为空！");

	/**
	 * 虛字段配置信息 name 为空
	 */
	public static ErrorCode3 VIRTUAL_FIELD_ERROR3 = new ErrorCode3("DAO701", "配置文件{0}中，没有定义虚字段{1}引用的IPField {2}");

	/**
	 * 日期配置字段中 name 为空
	 */
	public static ErrorCode1 DATE_NAME_ERROR = new ErrorCode1("DAO900", "配置文件{0}中， 有date_field配置信息的name属性为空！");

	/**
	 * 日期配置字段中  空值错误
	 */
	public static ErrorCode2 DATE_ATTRIBUTE_ERROR = new ErrorCode2("DAO901", "配置文件{0}中， date_field = {1}中缺少配置项！");
}
