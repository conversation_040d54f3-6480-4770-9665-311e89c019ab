package cn.gwssi.common.common;


import cn.gwssi.common.exception.code.ErrorCode1;
import cn.gwssi.common.exception.code.ErrorCode2;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPErrCodes {
	public static final ErrorCode1 JAVA_INSTANCE_ERROR  	= new ErrorCode1("103109", "类{0}实例化时错误");
	public static final ErrorCode1 JAVA_ACCESS_LIMIT 		= new ErrorCode1("103104", "类{0}没有存取权限");
	
	/**
	 * 字段不适合做统计分析
	 */
	public static final ErrorCode2 IP_AGGREGATION_DIM_ERROR0 = new ErrorCode2("agg000", "字段{0}类型为{1}，不适合做统计分析");

	/**
	 * 多维统计分析时，维度参数个数小于3个
	 */
	public static final ErrorCode1 IP_AGGREGATION_DIM_ERROR1 = new ErrorCode1("agg001", "多维统计分析时，维度参数个数为{0}，小于三个，请使用一维或者二维统计分析接口");

	/**
	 * 统计分析，维度参数错误， filter value个数大于 size
	 */
	public static final ErrorCode2 IP_AGGREGATION_DIM_ERROR2 = new ErrorCode2("agg002", "统计数据时，维度参数设置错误， FilterValue[{0}]大于size[{1}]");
	
}
