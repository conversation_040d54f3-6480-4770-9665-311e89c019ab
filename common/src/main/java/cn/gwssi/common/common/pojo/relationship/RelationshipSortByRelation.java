package cn.gwssi.common.common.pojo.relationship;


import cn.gwssi.common.common.array.SortArrayBase;

/**
 * 
 * <AUTHOR>
 *
 */
public class RelationshipSortByRelation extends SortArrayBase<RelationshipPojo> {
	private static final long serialVersionUID = -2214321093251241063L;

	@Override
	protected int compare(RelationshipPojo e1, RelationshipPojo e2) {
		return e1.getRelation().compareTo(e2.getRelation());
	}

	@Override
	protected int compare(RelationshipPojo e1, String relation) {
		return e1.getRelation().compareTo(relation);
	}

	@Override
	protected RelationshipPojo[] makeArray(int len) {
		return new RelationshipPojo[len];
	}
}
