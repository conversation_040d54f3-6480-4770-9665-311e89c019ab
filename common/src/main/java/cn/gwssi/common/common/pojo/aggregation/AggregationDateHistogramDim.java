package cn.gwssi.common.common.pojo.aggregation;


import org.joda.time.DateTimeZone;

import java.io.Serializable;
import java.time.ZoneId;

public class AggregationDateHistogramDim extends AggregationDim implements Serializable {
    private static final long serialVersionUID = 5407979005938776624L;

    private AggregationDHInterval interval;

    private DateTimeZone timeZone;  // es7 以下使用

    private ZoneId zoneId;  // es7 使用

    private String offset;

    private String format;

    private long minDocCount;

    public AggregationDateHistogramDim() {}

    public AggregationDateHistogramDim(String fieldName) {
        super(fieldName);
    }

    public AggregationDHInterval getInterval() {
        return interval;
    }

    public void setInterval(AggregationDHInterval interval) {
        this.interval = interval;
    }

    public DateTimeZone getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(DateTimeZone timeZone) {
        this.timeZone = timeZone;
    }

    public ZoneId getZoneId() {
        return zoneId;
    }

    public void setZoneId(ZoneId zoneId) {
        this.zoneId = zoneId;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

}

