package cn.gwssi.common.util;


import cn.gwssi.common.exception.IPRuntimeException;
import cn.gwssi.common.exception.code.ErrorCode1;
import cn.gwssi.common.exception.code.ErrorCode2;

import java.util.Collection;

/**
 * 
 * <AUTHOR>
 *
 */
public class Args {
	/**
	 * 参数错误
	 */
	public static final ErrorCode1 IP_ARGS_ERROR1 = new ErrorCode1("arg001", "参数错误:{0}");

	/**
	 * 参数错误
	 */
	public static final ErrorCode2 IP_ARGS_ERROR2 = new ErrorCode2("arg001", "参数错误:{0}, 实参值为{1}");
	
    public static void check(final boolean expression, final String message) throws IPRuntimeException {
        if (!expression) {
            throw Args.IP_ARGS_ERROR1.runException(message);
        }
    }

    public static void check(final boolean expression, final String message, final Object... args) throws IPRuntimeException{
        if (!expression) {
            throw Args.IP_ARGS_ERROR1.runException(String.format(message, args));
        }
    }

    public static void check(final boolean expression, final String message, final Object arg) {
        if (!expression) {
            throw Args.IP_ARGS_ERROR1.runException(String.format(message, arg));
        }
    }

    public static <T> T notNull(final T argument, final String name) {
        if (argument == null) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为NULL");
        }
        return argument;
    }

    public static <T extends CharSequence> T notEmpty(final T argument, final String name) {
        if (argument == null) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为NULL");
        }
        if (TextUtils.isEmpty(argument)) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为空字符串");
        }
        return argument;
    }

    public static <T extends CharSequence> T notBlank(final T argument, final String name) {
        if (argument == null) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为NULL");
        }
        if (TextUtils.isBlank(argument)) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为Blank");
        }
        return argument;
    }

    public static <T extends CharSequence> T containsNoBlanks(final T argument, final String name) {
        if (argument == null) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为NULL");
        }
        if (TextUtils.containsBlanks(argument)) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以包含Blank");
        }
        return argument;
    }

    public static <E, T extends Collection<E>> T notEmpty(final T argument, final String name) {
        if (argument == null) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为NULL");
        }
        if (argument.isEmpty()) {
            throw Args.IP_ARGS_ERROR1.runException("[" + name + "]不可以为empty");
        }
        return argument;
    }

    public static int positive(final int n, final String name) {
        if (n <= 0) {
            throw Args.IP_ARGS_ERROR2.runException("[" + name + "]必须为正数", String.valueOf(n));
        }
        return n;
    }

    public static long positive(final long n, final String name) {
        if (n <= 0) {
            throw Args.IP_ARGS_ERROR2.runException("[" + name + "]必须为正数", String.valueOf(n));
        }
        return n;
    }

    public static int notNegative(final int n, final String name) {
        if (n < 0) {
            throw Args.IP_ARGS_ERROR2.runException("[" + name + "]不可以为负数", String.valueOf(n));
        }
        return n;
    }

    public static long notNegative(final long n, final String name) {
        if (n < 0) {
            throw Args.IP_ARGS_ERROR2.runException("[" + name + "]不可以为负数", String.valueOf(n));
        }
        return n;
    }

}
