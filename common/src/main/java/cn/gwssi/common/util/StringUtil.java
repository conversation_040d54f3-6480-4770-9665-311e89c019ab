package cn.gwssi.common.util;

import java.util.Collection;

/**
 * 
 * <AUTHOR>
 *
 */
public class StringUtil {
	public static final String zeroString = "00000000000000000000000000000000";
	/**
	 * 比较两个字符串内容是否相同
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static boolean equals(String str1, String str2) {
		if(str1 == null && str2 == null) {
			return true;
		}
		
		if(str1 == null || str2 == null) {
			return false;
		}

		return str1.compareTo(str2) == 0 ? true : false;
	}
	
	/**
	 * 比较两个字符串（忽略大小写后）是否相同
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static boolean equalsIgnoreCase(String str1, String str2) {
		if(str1 == null && str2 == null) {
			return true;
		}
		
		if(str1 == null || str2 == null) {
			return false;
		}
		
		return str1.compareToIgnoreCase(str2) == 0 ? true : false;
	}
	
	/**
	 * 
	 * @param value
	 * @return
	 */
	public static boolean isNullOrEmpty(String value) {
		return (value == null || value.compareTo("") == 0) ? true : false;
	}
	
	/**
	 * 右对齐字符串，前面加[0]
	 * @param src
	 * @param len
	 * @return
	 */
	public static String right(String src, int len )
	{
		int srcLength = src.length();
		if( srcLength > len ){
			src = src.substring(0, len);
		}
		else if( srcLength < len ){
			src = zeroString.substring(0, len-srcLength) + src;
		}
		
		return src;
	}	
	
	/**
	 * 合并字符串
	 * 
	 * @param src
	 * @param len
	 * @return
	 */
	public static String join(Collection<String> src) {
		StringBuilder buf = new StringBuilder();
		boolean flag = false;
		if(src != null && !src.isEmpty()) {
			for(String str : src) {
				if(flag) {
					buf.append(", ");
				}
				else {
					flag = true;
				}
				buf.append(str);
			}
		}
		
		return buf.toString();
	}

}
