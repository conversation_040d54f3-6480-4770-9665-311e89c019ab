package cn.gwssi.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberUtil {

    // 计算百分比
    public static float calPercent(long count, long sum) {
        return (count <= 0 || sum <= 0) ? 0.00f : new BigDecimal(count).multiply(new BigDecimal(100))
                .divide(new BigDecimal(sum), 2, RoundingMode.HALF_UP).floatValue();
    }

    // 计算分数
    public static double calScore(double score, double score_min, double score_diff) {
        return new BigDecimal(score - score_min).divide(new BigDecimal(score_diff), 6, RoundingMode.HALF_UP).doubleValue();
    }

}
