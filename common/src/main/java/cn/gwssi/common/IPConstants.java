package cn.gwssi.common;


import com.google.gson.Gson;

/**
 * 
 * <AUTHOR>
 *
 */
public class IPConstants {
	/**
	 * GSON 对象
	 */
	public static final Gson GSON = new Gson();

	/**
	 * 缺省起始行
	 */
	public static final int DEFAULT_START_ROW = 0;

	/**
	 * 缺省每页行数
	 */
	public static final int DEFAULT_PAGE_ROW = 10;

    /**
     * raw字段
     */
    public static final String FULL_FIELD_NAME_ENDWITH = ".raw";

    /**
     * 聚合结果，sum_other_doc_count 映射的字段
     */
    public static final String AGG_OTHER = "_OTHER";

    /**
     * 聚合结果，missing
     */
    public static final String AGG_MISSING = "_MISSING";
	
	/**
	 * 检索时支持的最大记录数
	 */
	public static final int MAX_SEARCH_ROW = 10000;	
	
	/**
	 * 关联关系分析时，单次分析的最大数量
	 */
	public static final int MAX_ANALYSIS_ROW = 5000;

	/**
	 * 根据表达式更新时, 每次更新的数量最大数
	 */
	public static final int BATCH_UPDATE_MAX_ROW = 500;

    /**
     * R 要查询的字段
     */
    public static final String RELATE_FIELD_R = "pn";

    /**
     * RAD RPD R 要查询的字段
     */
    public static final String RELATE_FIELD = "an,pn";

    /**
     * RAD RPD 要返回的字段
     */
    public static final String RELATE_RETURN_FIELD = "tacd";

    /**
     * RAD RPD 要返回的字段
     */
    public static final String RELATE_RETURN_FIELD2 = "pnc";

    /**
     * RAD RPD 要返回的字段
     */
    public static final String RELATE_RETURN_FIELD3 = "prdad";

    /**
     * RAD RPD R 要查询的全文字段
     */
    public static final String[] RELATE_RETURN_FIELDS = new String[]{"pid", "pd", "ipphpn", "ipphan"};
//    public static final String[] RELATE_RETURN_FIELDS = new String[]{"ticn", "abcn", "iclmcn", "dclmcn", "tfcn", "tbcn", "iscn", "ddcn", "secn", "otherdesccn"};

    /**
     * cardinality 聚合的名称
     */
    public static final String CARDINALITY_AGG_NAME = "DISTINCT";

    /**
     * 向量检索的 window_size
     */
    public static final int VECTOR_WINDOW_SIZE = 2000;

    /**
     * 向量检索的最大条数
     */
    public static final int VECTOR_MAX_ROW = 2000;

    /**
     * RAD RPD 要查询的字段如果没有值，则取这个值
     */
    public static final String RELATE_FIELD_VALUE = "20991231";

    public static final String ERROR_MAX_CLAUSES = "too_many_clauses";

    public static final String ERROR_MAX_BUCKETS = "too_many_buckets_exception";

    public static final String ERROR_EMPTY_STACK = "empty_stack_exception";

    public static final String FIELD_PACG = "pacg";

    public static final String FIELD_PACG_CN = "申请人组";

    public static final String[] FIELD_PACG_ELEMENTS = new String[]{"apcsdcn", "apcsden", "apcsdjp", "apcsdo"};

}
