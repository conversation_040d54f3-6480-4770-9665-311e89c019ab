package cn.gwssi.common.exception;

/**
 * 数据异常
 * 
 * <AUTHOR>
 *
 */
public class IPDataException extends IPException 
{
   private static final long serialVersionUID = 200511110101004001L;

   /**
    * @param errName 错误代码
    * @param message 错误描述
    * @param cause 上一级异常
    */
   public IPDataException(String errName, String message, Throwable cause)
   {
       super(errName, message, cause);
   }

   /**
    * @param errName 错误代码
    * @param cause 上一级异常
    */
   protected IPDataException(String errName, Throwable cause)
   {
       super(errName, cause);
   }

   /**
    * @param errName 错误代码
    * @param message 错误描述
    */
   public IPDataException(String errName, String message)
   {
       super(errName, message);
   }

   /**
    * @param errName 错误代码
    */
   protected IPDataException(String errName)
   {
       super(errName);
   }
}
