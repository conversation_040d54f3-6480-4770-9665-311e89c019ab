package cn.gwssi.common.exception;

/**
 * 错误
 *
 * <AUTHOR>
 */
public class IPErrorException extends IPException {
    private static final long serialVersionUID = 200511110101004002L;

    /**
     * @param errName 错误代码
     * @param message 错误描述
     * @param cause   上一级异常
     */
    public IPErrorException(String errName, String message, Throwable cause) {
        super(errName, message, cause);
    }

    /**
     * @param errName 错误代码
     * @param message 错误描述
     * @param obj     其它参数，例如记录错误位置的对象
     */
    public IPErrorException(String errName, String message, Object obj) {
        super(errName, message, obj);
    }

    /**
     * @param errName 错误代码
     * @param cause   上一级异常
     */
    protected IPErrorException(String errName, Throwable cause) {
        super(errName, cause);
    }

    /**
     * @param errName 错误代码
     * @param message 错误描述
     */
    public IPErrorException(String errName, String message) {
        super(errName, message);
    }

    /**
     * @param errName 错误代码
     */
    protected IPErrorException(String errName) {
        super(errName);
    }
}
