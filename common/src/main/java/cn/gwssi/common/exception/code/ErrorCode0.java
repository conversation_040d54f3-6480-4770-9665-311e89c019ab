package cn.gwssi.common.exception.code;


import cn.gwssi.common.exception.IPErrorException;
import cn.gwssi.common.exception.IPException;
import cn.gwssi.common.exception.IPRuntimeException;

/**
 * 参数个数为0的ErrorCode
 * 
 * <AUTHOR>
 *
 */
public class ErrorCode0 extends ErrorCode
{
	public ErrorCode0(String errCode, String errDesc )
	{
		this.errCode = errCode;
		this.errDesc = errDesc;
	}

	public ErrorMsg format( )
	{
		return new ErrorMsg(errCode, errDesc);
	}

	public IPException exception()
	{
		return new IPErrorException( errCode, errDesc );
	}
	
	public IPException exception( Throwable cause )
	{
		return new IPErrorException( errCode, errDesc, cause );
	}

    public IPException exception( Object object )
    {
        return new IPErrorException( errCode, errDesc, object );
    }
	
	public IPRuntimeException runException()
	{
		return new IPRuntimeException( errCode, errDesc );
	}
	
	public IPRuntimeException runException( Throwable cause )
	{
		return new IPRuntimeException( errCode, errDesc, cause );
	}
}
