package cn.gwssi.common.exception;

public class IPErrorItem {
	private String sysName;
	private String errName;
	private String errCode;
	private String errDesc;

	/**
	 * @param errName
	 * @param errCode
	 * @param errDesc
	 */
	public IPErrorItem(String name, String value) {
		int ptr;

		// 取错误代码和描述
		ptr = value.indexOf(":");
		if (ptr < 0) {
			errCode = "999999";
			errDesc = value;
		} else {
			errCode = value.substring(0, ptr);
			errDesc = value.substring(ptr + 1);
		}

		// 系统名称和错误名称
		ptr = name.indexOf(":");
		if (ptr < 0) {
			sysName = "";
			errName = name;
		} else {
			sysName = name.substring(0, ptr);
			errName = name.substring(ptr + 1);
		}
	}

	public IPErrorItem() {
		this.sysName = "";
		this.errName = "";
		this.errCode = "";
		this.errDesc = "";
	}

	public String getSysName() {
		return sysName;
	}

	public void setSysName(String sysName) {
		this.sysName = sysName;
	}

	/**
	 * @return java.lang.String
	 */
	public String getErrName() {
		return this.errName;
	}

	/**
	 * @param errName
	 */
	public void setErrName(String errName) {
		this.errName = errName;
	}

	/**
	 * @return java.lang.String
	 */
	public String getErrCode() {
		return this.errCode;
	}

	/**
	 * @param errCode
	 */
	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	/**
	 * @return java.lang.String
	 */
	public String getErrDesc() {
		return this.errDesc;
	}

	/**
	 * @param errDesc
	 */
	public void setErrDesc(String errDesc) {
		this.errDesc = errDesc;
	}
}
