package cn.gwssi.common.exception;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

/**
 * IP异常
 */
public abstract class IPException extends Exception {
	private static final long serialVersionUID = 200901080101009010L;

	private static final String CRLT_DELIM = "\n";
	/**
	 * 错误代码
	 */
	protected String errCode;

	/**
	 * 错误发生时的一些重要参数的值
	 */
	protected HashMap<String, String> context = null;

	/**
	 * 保存错误描述的列表信息，当程序截获到异常时，在抛出这个异常时可以增加一些辅助的错误信息
	 */
	protected List<String> detailMessage;

    /**
     * 错误详细信息的对象，例如错误的位置对象
     */
	protected Object paramObject;

	/**
	 * 如果[true]，输出错误信息，否则不生成输出信息
	 */
	protected boolean printFlag = true;

	public IPException() {
		super();

		// 初始化属性
		context = null;
		detailMessage = null;
		this.setErrCode(null);
	}

	public IPException(String errCode) {
		super();

		// 初始化属性
		context = null;
		detailMessage = null;

		// 调用setErrCode函数设置属性errCode
		this.setErrCode(errCode);
	}

	public IPException(String errCode, String message) {
		super(message);

		// 初始化属性
		context = null;
		detailMessage = null;

		this.setErrCode(errCode);
	}

	public IPException(String errCode, Throwable cause) {
		super(cause);

		// 设置属性
		context = null;
		detailMessage = null;

		// 如果 errCode 不为空，调用setErrCode函数设置属性errCode
		if (errCode != null && errCode.length() != 0) {
			this.setErrCode(errCode);
		}
		// 如果 errCode 为空，并且cause是IPException类派生出来的，则从cause中获取errCode
		else if (cause instanceof IPException) {
			this.errCode = ((IPException) cause).getErrCode();
		} else {
			this.setErrCode(null);
		}
	}

	public IPException(String errCode, String message, Throwable cause) {
		// 调用父类的初始化函数，初始化message和cause
		super(message, cause);

		context = null;
		detailMessage = null;

		// 如果 errCode 不为空
		if (errCode != null && errCode.length() != 0) {
			this.setErrCode(errCode);
		}
		// 如果 errCode 为空，并且cause是IPException类派生出来的，则从cause中获取errCode
		else if (cause instanceof IPException) {
			this.errCode = ((IPException) cause).getErrCode();
		} else {
			this.setErrCode(null);
		}
	}

    public IPException(String errCode, String message, Object object) {
        // 调用父类的初始化函数，初始化message和cause
        super(message);

        context = null;
        detailMessage = null;

        // 如果 errCode 不为空
        if (errCode != null && errCode.length() != 0) {
            this.setErrCode(errCode);
        }

        this.paramObject = object;
    }

	/**
	 * 设置属性errCode
	 * 
	 * @param errCode
	 */
	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	/**
	 * 获取属性errCode
	 * 
	 * @return java.lang.String
	 */
	public String getErrCode() {
		return this.errCode;
	}

	/**
	 * 增加错误描述信息
	 * 
	 * @param message
	 *            错误描述
	 */
	public void addMessage(String message) {
		if (detailMessage == null) {
			detailMessage = new ArrayList<String>();
		}

		detailMessage.add(message);
	}

	/**
	 * 增加错误描述信息
	 * 
	 * @param className
	 *            类名称
	 * @param message
	 *            错误描述
	 */
	public void addMessage(String className, String message) {
		if (detailMessage == null) {
			detailMessage = new ArrayList<String>();
		}

		detailMessage.add(className + " : " + message);
	}

	/**
	 * 取错误信息
	 */
	@Override
	public String getMessage() {
		StringBuilder str = new StringBuilder(64);
		String s = super.getMessage();
		if (s != null) {
			str.append(s);
		}

		if (detailMessage != null) {
			for (int ii = 0; ii < detailMessage.size(); ii++) {
				str.append("\n").append(detailMessage.get(ii));
			}
		}

		return str.toString();
	}

	/**
	 * 原始的错误信息
	 * 
	 * @return
	 */
	public String getExceptionMessage() {
		return super.getMessage();
	}

	/**
	 * 
	 * @return
	 */
	public List<String> getDetailMessage() {
		return this.detailMessage;
	}

	/**
	 * 在context中增加变量内容，如果context为空，先生成属性
	 * 
	 * @param name
	 * @param value
	 */
	public void putValue(String name, String value) {
		if (context == null) {
			context = new HashMap<String, String>();
		}

		context.put(name, value);
	}

	/**
	 * 获取属性context
	 * 
	 * @return java.util.HashMap
	 */
	public HashMap<String, String> getContext() {
		return this.context;
	}

	/**
	 * 按以下格式生成错误信息  错误代码：errCode； 错误描述：errDesc；
	 * 错误原因：getMessage()； >>：detailMessage中记录的错误描述 上下文信息：context
	 * 如果cause不为空，在后面添加 换行 << cause.toString()
	 * 
	 * @return java.lang.String
	 */
	@Override
	public String toString() {
		// 错误信息
		StringBuilder errLog = new StringBuilder();

		// 显示错误代码
		errLog.append("错误代码：").append(this.errCode).append(CRLT_DELIM);

		// 显示起始错误
		errLog.append("错误信息：").append(super.getMessage()).append(CRLT_DELIM);

		// 显示其他错误
		if (detailMessage != null) {
			for (int ii = 0; ii < detailMessage.size(); ii++) {
				errLog.append("  >>：").append(detailMessage.get(ii))
						.append(CRLT_DELIM);
			}
		}

		// 显示上下文信息
		if (context != null) {
			errLog.append("错误时上下文信息:" + CRLT_DELIM);
			Set<Entry<String, String>> set = context.entrySet();
			for (Entry<String, String> e : set) {
				String value = e.getValue();
				if (value != null) {
					errLog.append("  >>:").append(e.getKey()).append(" = ")
							.append(value).append(CRLT_DELIM);
				}
			}
		}

		// 如果cause不为空，在后面添加
		Throwable c = getCause();
		if (c != null) {
			errLog.append("错误原因:").append(CRLT_DELIM);
			errLog.append(c.toString());
		}

		return errLog.toString();
	}

	/**
	 * 禁止log4j重复打印错误信息
	 */
	@Override
	public void printStackTrace(PrintWriter s) {
		// 只输出一次
		if (printFlag) {
			printFlag = false;
			super.printStackTrace(s);
		}
	}

	// 这里可以重复输出内容，否则只能输出一次
	public void printStackTrace(VectorWriter s) {
		super.printStackTrace(s);
	}

    public Object getParamObject() {
        return paramObject;
    }
}
