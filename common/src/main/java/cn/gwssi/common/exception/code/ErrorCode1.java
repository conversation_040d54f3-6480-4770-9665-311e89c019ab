package cn.gwssi.common.exception.code;


import cn.gwssi.common.exception.IPErrorException;
import cn.gwssi.common.exception.IPRuntimeException;
import cn.gwssi.common.exception.IPException;

import java.text.MessageFormat;

/**
 * 参数个数为1的ErrorCode
 * 
 * <AUTHOR>
 *
 */
public class ErrorCode1 extends ErrorCode
{
	private MessageFormat format;
	
	public ErrorCode1(String errCode, String errDesc )
	{
		this.errCode = errCode;
		this.format = new MessageFormat(errDesc);
	}

	public ErrorMsg format( String arg1 )
	{
		Object args[] = {arg1};
		return new ErrorMsg(errCode, format.format(args));
	}

	public IPException exception(String arg1 )
	{
		Object args[] = {arg1};
		return new IPErrorException( errCode, format.format(args) );
	}
	
	public IPException exception(String arg1, Throwable cause )
	{
		Object args[] = {arg1};
		return new IPErrorException( errCode, format.format(args), cause );
	}

    public IPException exception(String arg1, Object arg2)
    {
        Object args[] = {arg1};
        return new IPErrorException( errCode, format.format(args), arg2 );
    }
	
	/**
	 * 运行时异常
	 * 
	 * 
	 * @param arg1
	 * @return
	 */
	public IPRuntimeException runException(String arg1 )
	{
		Object args[] = {arg1};
		return new IPRuntimeException( errCode, format.format(args) );
	}

	/**
	 * 运行时异常
	 * 
	 * @param arg1
	 * @param cause
	 * @return
	 */
	public IPRuntimeException runException(String arg1, Throwable cause )
	{
		Object args[] = {arg1};
		return new IPRuntimeException( errCode, format.format(args), cause );
	}
}
