<?xml version='1.0' encoding='UTF-8'?>
<type index="trademark_open_pq" name="trademark_open" is_default="true" primary_key="_id" number_of_shards="1" number_of_replicas="0">
    <columns>
        <column description="miu" is_array="false" name="miu" not_null="false" type="item_no" />
        <column description="nc" is_array="false" name="nc" not_null="false" type="item_no" />
        <column description="rn" is_array="false" name="rn" not_null="false" type="item_no" />
        <column description="image" is_array="false" name="image" not_null="false" type="image" />
    </columns>
    <fields>
        <field alias="miu" analyzer="null" column_name="miu" description="miu" name="miu" />
        <field alias="nc" analyzer="null" column_name="nc" description="nc" name="nc" />
        <field alias="rn" analyzer="null" column_name="rn" description="rn" name="rn" />
        <field alias="image" analyzer="null" column_name="image" description="image" name="image" />
    </fields>
</type>