<?xml version="1.0" encoding="UTF-8"?>
<type name="oa_file_center_pro" index="oa_file_center_pro" number_of_shards="12" number_of_replicas="0">
    <columns>
        <column is_array="false" name="title" not_null="false" type="tiny_text" />
        <column is_array="false" name="docId" not_null="false" type="item_no" />
        <column is_array="false" name="content" not_null="false" type="long_text" />
        <column is_array="false" name="docType" not_null="false" type="item_no" />
        <column is_array="false" name="subDocType" not_null="false" type="item_no" />
        <column is_array="false" name="thirdDocType" not_null="false" type="item_no" />
        <column is_array="false" name="dataTypeName" not_null="false" type="item_no" />
        <column is_array="false" name="secondDataTypeName" not_null="false" type="item_no" />
        <column is_array="false" name="thirdDataTypeName" not_null="false" type="item_no" />
        <column is_array="false" name="drafterId" not_null="false" type="item_no" />
        <column is_array="false" name="drafter" not_null="false" type="item_no" />
        <column is_array="false" name="drafterDepId" not_null="false" type="item_no" />
        <column is_array="false" name="drafterDep" not_null="false" type="item_no" />
        <column is_array="false" name="drafterOrgType" not_null="false" type="item_no" />
        <column is_array="false" name="drafterParentOrgId" not_null="false" type="item_no" />
        <column is_array="false" name="drafterParentOrgName" not_null="false" type="item_no" />
        <column is_array="false" name="drafterDate" not_null="false" type="item_no" />
        <column is_array="false" name="drafterDateAgg" not_null="false" type="date" />
        <column is_array="false" name="docNumber" not_null="false" type="item_no" />
        <column is_array="false" name="laiwenDepName" not_null="false" type="item_no" />
        <column is_array="false" name="receiveDepName" not_null="false" type="item_no" />
        <column is_array="false" name="receiveDocNumber" not_null="false" type="item_no" />
        <column is_array="false" name="receiveDepId" not_null="false" type="item_no" />
        <column is_array="false" name="publishTime" not_null="false" type="item_no" />
        <column is_array="false" name="publishTimeAgg" not_null="false" type="date" />
        <column is_array="false" name="instanceId" not_null="false" type="item_no" />
        <column is_array="false" name="status" not_null="false" type="item_no" />
        <column is_array="true" name="drafterOrgPath" not_null="false" type="item_no" />
        <column is_array="false" name="refleshTimeasmp" not_null="false" type="long" />
        <column is_array="false" name="securityLevel" not_null="false" type="item_no" />
        <column is_array="false" name="securityDateLimit" not_null="false" type="item_no" />
        <column is_array="false" name="news" not_null="false" type="item_no" />
        <column is_array="formKey" name="formKey" not_null="false" type="item_no" />
    </columns>
    <fields>
        <field alias="标题" analyzer="ik_without_html_tag" column_name="title" name="title">
            <multi_field analyzer="null" column_name="raw" name="title_raw" />
        </field>
        <field alias="主键" analyzer="null" column_name="docId" name="docId" />
        <field alias="正文" analyzer="ik_without_html_tag" column_name="content" name="content">
            <multi_field analyzer="null" column_name="raw" name="content_raw" />
        </field>
        <field alias="文件类型(大)" analyzer="null" column_name="docType" name="docType" />
        <field alias="文件类型(补充)" analyzer="null" column_name="subDocType" name="subDocType" />
        <field alias="数据类型标记" analyzer="null" column_name="thirdDocType" name="thirdDocType" />
        <field alias="数据类型名称" analyzer="null" column_name="dataTypeName" name="dataTypeName" />
        <field alias="二级数据类型名称" analyzer="null" column_name="secondDataTypeName" name="secondDataTypeName" />
        <field alias="三级级数据类型名称" analyzer="null" column_name="thirdDataTypeName" name="thirdDataTypeName" />
        <field alias="拟稿人ID" analyzer="null" column_name="drafterId" name="drafterId" />
        <field alias="拟稿人" analyzer="null" column_name="drafter" name="drafter"/>
        <field alias="拟稿部门id" analyzer="null" column_name="drafterDepId" name="drafterDepId" />
        <field alias="拟稿部门" analyzer="null" column_name="drafterDep" name="drafterDep" />
        <field alias="拟稿人机构类型" analyzer="null" column_name="drafterOrgType" name="drafterOrgType" />
        <field alias="拟稿人上级机构id" analyzer="null" column_name="drafterParentOrgId" name="drafterParentOrgId" />
        <field alias="拟稿人上级机构名称" analyzer="null" column_name="drafterParentOrgName" name="drafterParentOrgName" />
        <field alias="拟稿时间" analyzer="null" column_name="drafterDate" name="drafterDate" />
        <field alias="拟稿日期" analyzer="null" column_name="drafterDateAgg" name="drafterDateAgg" />
        <field alias="文号" analyzer="null" column_name="docNumber" name="docNumber" />
        <field alias="来文单位名称" analyzer="null" column_name="laiwenDepName" name="laiwenDepName" />
        <field alias="收文单位名称" analyzer="null" column_name="receiveDepName" name="receiveDepName" />
        <field alias="收文文号" analyzer="null" column_name="receiveDocNumber" name="receiveDocNumber" />
        <field alias="收文单位ID" analyzer="null" column_name="receiveDepId" name="receiveDepId" />
        <field alias="发布时间" analyzer="null" column_name="publishTime" name="publishTime" />
        <field alias="发布日期" analyzer="null" column_name="publishTimeAgg" name="publishTimeAgg" />
        <field alias="流程实例id" analyzer="null" column_name="instanceId" name="instanceId" />
        <field alias="流程状态" analyzer="null" column_name="status" name="status" />
        <field alias="拟稿人的组织机构" analyzer="null" column_name="drafterOrgPath" name="drafterOrgPath" />
        <field alias="刷新时间" analyzer="null" column_name="refleshTimeasmp" name="refleshTimeasmp" />
        <field alias="密级" analyzer="null" column_name="securityLevel" name="securityLevel" />
        <field alias="脱密时间" analyzer="null" column_name="securityDateLimit" name="securityDateLimit" />
        <field alias="新闻数据标识" analyzer="null" column_name="news" name="news" />
        <field alias="表单key" analyzer="null" column_name="formKey" name="formKey" />
     </fields>
     <nested name="taskOpinions">
        <columns>
            <column is_array="false" name="approverTime" not_null="false" type="item_no" />
            <column is_array="false" name="approverTimeAgg" not_null="false" type="date" />
            <column is_array="false" name="approverId" not_null="false" type="item_no" />
            <column is_array="false" name="approverOrgPath" not_null="false" type="item_no" />
            <column is_array="false" name="taskId" not_null="false" type="item_no" />
            <column is_array="false" name="approverName" not_null="false" type="tiny_text" />
            <column is_array="false" name="opinion" not_null="false" type="long_text" />
        </columns>
        <field alias="办理时间(审批时间)" analyzer="none" column_name="approverTime" name="approverTime" />
        <field alias="办理日期(审批日期)" analyzer="none" column_name="approverTimeAgg" name="approverTimeAgg" />
        <field alias="办理人ID(审批人ID)" analyzer="none" column_name="approverId" name="approverId" />
        <field alias="办理人的组织机构" analyzer="none" column_name="approverOrgPath" name="approverOrgPath" />
        <field alias="任务id" analyzer="none" column_name="taskId" name="taskId" />
        <field alias="办理人(审批人名字)" analyzer="ik_without_html_tag" column_name="approverName" name="approverName">
            <multi_field analyzer="null" column_name="raw" name="approverName_raw" />
        </field>
        <field alias="办理意见" analyzer="ik_without_html_tag" column_name="opinion" name="opinion">
            <multi_field analyzer="null" column_name="raw" name="opinion_raw" />
        </field>
    </nested>
    <nested name="attachs">
        <columns>
            <column is_array="false" name="attachTitle" not_null="false" type="tiny_text" />
            <column is_array="false" name="attachContent" not_null="false" type="long_text" />
        </columns>
        <field alias="附件标题" analyzer="ik_without_html_tag" column_name="attachTitle" name="attachTitle">
            <multi_field analyzer="null" column_name="raw" name="attachTitle_raw" />
        </field>
         <field alias="附件内容" analyzer="ik_without_html_tag" column_name="attachContent" name="attachContent">
            <multi_field analyzer="null" column_name="raw" name="attachContent_raw" />
        </field>
    </nested>
    <nested name="grants">
        <columns>
            <column is_array="false" name="opintionTaskId" not_null="false" type="item_no" />
            <column is_array="false" name="orgName" not_null="false" type="item_no" />
            <column is_array="false" name="orgPaths" not_null="false" type="item_no" />
            <column is_array="false" name="orgId" not_null="false" type="item_no" />
        </columns>
        <field alias="权限节点的taskId" analyzer="none" column_name="opintionTaskId" name="opintionTaskId" />
        <field alias="组织机构名称" analyzer="none" column_name="orgName" name="orgName" />
        <field alias="组织机构path" analyzer="none" column_name="orgPaths" name="orgPaths" />
        <field alias="组织机构id" analyzer="none" column_name="orgId" name="orgId" />
    </nested>
    <virtual_fields>
        <virtual_field name="words" alias="查询条件(虚拟字段)" fields="content, title, attachContent, opinion" />
    </virtual_fields>
    <date_fields>
        <date_field name="publishTimeAgg" format="yyyy-MM-dd" />
        <date_field name="drafterDateAgg" format="yyyy-MM-dd" />
        <date_field name="approverTimeAgg" format="yyyy-MM-dd" />
  </date_fields>

</type>