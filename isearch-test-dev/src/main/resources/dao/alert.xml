<?xml version='1.0' encoding='UTF-8'?>

<type index="gwuomp_alert" name="gwuomp_alert" is_default="true" primary_key="id" number_of_shards="1" number_of_replicas="0">
    <columns>
        <column description="主键" is_array="false" name="id" not_null="true" type="item_no"/>
        <column description="kafka接收时间" is_array="false" name="accessTime" not_null="true" type="date"/>
        <column description="服务接收时间" is_array="false" name="createTime" not_null="true" type="date"/>
        <column description="更新时间" is_array="false" name="updateTime" not_null="true" type="date"/>
        <column description="告警时间" is_array="false" name="alarmTime" not_null="true" type="date"/>
        <column description="告警结束时间" is_array="false" name="alarmEndTime" not_null="true" type="date"/>
        <column description="告警处理时间" is_array="false" name="processUpdateTime" not_null="true" type="date"/>
        <column description="资产数据的主键id" is_array="false" name="assetsManageId" not_null="true" type="item_no"/>
        <column description="资产名称" is_array="false" name="assetsName" not_null="true" type="tiny_text"/>
        <column description="规则ID" is_array="false" name="alarmRuleId" not_null="true" type="item_no"/>
        <column description="规则名称" is_array="false" name="alarmRuleName" not_null="true" type="tiny_text"/>
        <column description="IP地址" is_array="false" name="ipAddress" not_null="true" type="tiny_text"/>
        <column description="端口" is_array="false" name="port" not_null="true" type="item_no"/>
        <column description="告警等级" is_array="false" name="alarmLevel" not_null="true" type="item_no"/>
        <column description="告警等级名称" is_array="false" name="alarmLevelName" not_null="true" type="item_no"/>
        <column description="告警名称" is_array="false" name="alarmName" not_null="true" type="tiny_text"/>
        <column description="告警类型" is_array="false" name="alarmType" not_null="true" type="item_no"/>
        <column description="告警实际监测值" is_array="false" name="alarmValue" not_null="true" type="item_no"/>
        <column description="告警类型中文" is_array="false" name="alarmTypeName" not_null="true" type="item_no"/>
        <column description="告警对象" is_array="false" name="alarmObject" not_null="true" type="item_no"/>
        <column description="告警对象中文" is_array="false" name="alarmObjectName" not_null="true" type="item_no"/>
        <column description="告警描述" is_array="false" name="alarmDesc" not_null="true" type="tiny_text"/>
        <column description="告警状态" is_array="false" name="alarmStatus" not_null="true" type="item_no"/>
        <column description="告警状态中文" is_array="false" name="alarmStatusName" not_null="true" type="item_no"/>
        <column description="处理人名称" is_array="false" name="processUser" not_null="true" type="tiny_text"/>
        <column description="处理人ID" is_array="false" name="processUserId" not_null="true" type="item_no"/>
        <column description="普罗米修斯告警状态" is_array="false" name="status" not_null="true" type="item_no"/>
        <column description="普罗米修斯告警ID" is_array="false" name="fpid" not_null="true" type="item_no"/>
        <column description="附言" is_array="false" name="alarmText" not_null="true" type="tiny_text"/>
        <column description="告警来源代码" is_array="false" name="alarmSource" not_null="true" type="item_no"/>
        <column description="告警来源中文" is_array="false" name="alarmSourceName" not_null="true" type="item_no"/>
        <column description="告警原始值" is_array="false" name="alarmOriginal" not_null="true" type="item_no"/>
    </columns>
    <fields>
        <field alias="主键" analyzer="null" column_name="id" description="主键" name="id"/>
        <field alias="kafka接收时间" analyzer="null" column_name="accessTime" description="kafka接收时间" name="accessTime"/>
        <field alias="服务接收时间" analyzer="null" column_name="createTime" description="服务接收时间" name="createTime"/>
        <field alias="更新时间" analyzer="null" column_name="updateTime" description="更新时间" name="updateTime"/>
        <field alias="告警时间" analyzer="null" column_name="alarmTime" description="告警时间" name="alarmTime"/>
        <field alias="告警结束时间" analyzer="null" column_name="alarmEndTime" description="告警结束时间" name="alarmEndTime"/>
        <field alias="告警处理时间" analyzer="null" column_name="processUpdateTime" description="告警处理时间" name="processUpdateTime"/>
        <field alias="资产数据的主键id" analyzer="null" column_name="assetsManageId" description="资产数据的主键id" name="assetsManageId"/>
        <field alias="资产名称" analyzer="null" column_name="assetsName" description="资产名称" name="assetsName">
            <multi_field analyzer="null" column_name="raw" description="资产名称" name="assetsName_raw"/>
        </field>
        <field alias="规则ID" analyzer="null" column_name="alarmRuleId" description="规则ID" name="alarmRuleId"/>
        <field alias="规则名称" analyzer="null" column_name="alarmRuleName" description="规则名称" name="alarmRuleName">
            <multi_field analyzer="null" column_name="raw" description="规则名称" name="alarmRuleName_raw"/>
        </field>
        <field alias="IP地址" analyzer="null" column_name="ipAddress" description="IP地址" name="ipAddress">
            <multi_field analyzer="null" column_name="raw" description="IP地址" name="ipAddress_raw"/>
        </field>
        <field alias="端口" analyzer="null" column_name="port" description="端口" name="port"/>
        <field alias="告警等级" analyzer="null" column_name="alarmLevel" description="告警等级" name="alarmLevel"/>
        <field alias="告警等级名称" analyzer="null" column_name="alarmLevelName" description="告警等级名称" name="alarmLevelName"/>
        <field alias="告警名称" analyzer="null" column_name="alarmName" description="告警名称" name="alarmName">
            <multi_field analyzer="null" column_name="raw" description="告警名称" name="alarmName_raw"/>
        </field>
        <field alias="告警类型" analyzer="null" column_name="alarmType" description="告警类型" name="alarmType"/>
        <field alias="告警实际监测值" analyzer="null" column_name="alarmValue" description="告警实际监测值" name="alarmValue"/>
        <field alias="告警类型中文" analyzer="null" column_name="alarmTypeName" description="告警类型中文" name="alarmTypeName"/>
        <field alias="告警对象" analyzer="null" column_name="alarmObject" description="告警对象" name="alarmObject"/>
        <field alias="告警对象中文" analyzer="null" column_name="alarmObjectName" description="告警对象中文" name="alarmObjectName"/>
        <field alias="告警描述" analyzer="null" column_name="alarmDesc" description="告警描述" name="alarmDesc"/>
        <field alias="告警状态" analyzer="null" column_name="alarmStatus" description="告警状态" name="alarmStatus"/>
        <field alias="告警状态中文" analyzer="null" column_name="alarmStatusName" description="告警状态中文" name="alarmStatusName"/>
        <field alias="处理人名称" analyzer="null" column_name="processUser" description="处理人名称" name="processUser">
            <multi_field analyzer="null" column_name="raw" description="处理人名称" name="processUser_raw"/>
        </field>
        <field alias="处理人ID" analyzer="null" column_name="processUserId" description="处理人ID" name="processUserId"/>
        <field alias="普罗米修斯告警状态" analyzer="null" column_name="status" description="普罗米修斯告警状态" name="status"/>
        <field alias="普罗米修斯告警ID" analyzer="null" column_name="fpid" description="普罗米修斯告警ID" name="fpid"/>
        <field alias="附言" analyzer="null" column_name="alarmText" description="附言" name="alarmText"/>
        <field alias="告警来源代码" analyzer="null" column_name="alarmSource" description="告警来源代码" name="alarmSource"/>
        <field alias="告警来源中文" analyzer="null" column_name="alarmSourceName" description="告警来源中文" name="alarmSourceName"/>
        <field alias="告警原始值" analyzer="null" column_name="alarmOriginal" description="告警原始值" name="alarmOriginal"/>
    </fields>
    <date_fields>
        <date_field name="accessTime" format="yyyy-MM-dd HH:mm:ss"/>
        <date_field name="createTime" format="yyyy-MM-dd HH:mm:ss"/>
        <date_field name="updateTime" format="yyyy-MM-dd HH:mm:ss"/>
        <date_field name="alarmTime" format="yyyy-MM-dd HH:mm:ss"/>
        <date_field name="alarmEndTime" format="yyyy-MM-dd HH:mm:ss"/>
        <date_field name="processUpdateTime" format="yyyy-MM-dd HH:mm:ss"/>
    </date_fields>
</type>