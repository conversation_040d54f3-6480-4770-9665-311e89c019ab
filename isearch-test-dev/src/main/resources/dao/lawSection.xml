<?xml version='1.0' encoding='UTF-8'?>
<!--
    index:               索引名称
    name：               类型名称
    primary_key：        主键
    number_of_shards：   分片数量
    number_of_replicas： 副本数量
-->
<type index="lawsection" name="lawsection" is_default="true" primary_key="sectionId" number_of_shards="1" number_of_replicas="0">
    <columns>
        <!--
             description：字段描述
             is_array：   字段是否数组
             name：       字段名
             not_null：   字段是否不为空
             type：       字段类型，有以下几种类型
                号码类型：   item_no
                布尔类型：   boolean、bool
                数字类型：   byte、short、integer、long、float、double
                短文本类型： tiny_text
                长文本类型： long_text
                日期类型：   date
                图像类型：   image
                二进制类型： binary
         -->
        <column description="主键" is_array="false" name="sectionId" not_null="true" type="tiny_text" />
        <column description="法律法规主键" is_array="false" name="lawId" not_null="false" type="tiny_text" />
        <column description="序号" is_array="false" name="seq" not_null="false" type="long" />
        <column description="编号" is_array="false" name="bNum" not_null="false" type="item_no"/>
        <column description="章号" is_array="false" name="zNum" not_null="false" type="tiny_text" />
        <column description="节号" is_array="false" name="jNum" not_null="false" type="tiny_text" />
        <column description="条号" is_array="false" name="tNum" not_null="false" type="tiny_text" />
        <column description="款号" is_array="false" name="kNum" not_null="false" type="tiny_text" />
        <column description="项号" is_array="false" name="xNum" not_null="false" type="tiny_text" />
        <column description="目号" is_array="false" name="mNum" not_null="false" type="tiny_text" />
        <column description="标签" is_array="false" name="label" not_null="false" type="tiny_text" />
        <column description="条款内容" is_array="false" name="lawContent" not_null="false" type="long_text" />
        <column description="类型" is_array="false" name="secType" not_null="false" type="tiny_text" />
        <column description="父条款id" is_array="false" name="pId" not_null="false" type="tiny_text" />

        <column description="删除标识" is_array="false" name="deleteFlag" not_null="false" type="tiny_text" />
        <column description="创建人ID" is_array="false" name="createId" not_null="false" type="tiny_text" />
        <column description="创建时间" is_array="false" name="createDate" not_null="false" type="date" />
        <column description="修改人ID" is_array="false" name="updateId" not_null="false" type="tiny_text" />
        <column description="修改时间" is_array="false" name="updateDate" not_null="false" type="date" />
        <!--<column description="号码类型" is_array="false" name="itemType" not_null="false" type="item_no" />-->
        <!--<column description="布尔类型" is_array="false" name="boolType" not_null="false" type="boolean" />-->
        <!--<column description="数字类型" is_array="false" name="numberType" not_null="false" type="long" />-->
        <!--<column description="短文本类型" is_array="false" name="tinyTextType" not_null="false" type="tiny_text" />-->
        <!--<column description="长文本类型" is_array="false" name="longTextType" not_null="false" type="long_text" />-->
        <!--<column description="日期类型" is_array="false" name="dateType" not_null="false" type="date" />-->
        <!--<column description="图像类型" is_array="false" name="imageType" not_null="false" type="image" />-->
        <!--<column description="二进制类型" is_array="false" name="binaryType" not_null="false" type="binary" />-->
    </columns>
    <fields>
        <!--
             alias：      字段别名
             analyzer：   字段分词器，有关字段类型和字段分词器的相关规则，见帮助文档
             column_name：字段名
             description：字段描述
             name：       字段名
         -->
        <!--<field alias="主键" analyzer="null" column_name="id" description="主键" name="id" />-->
        <!--<field alias="号码类型" analyzer="null" column_name="itemType" description="号码类型" name="itemType" />-->
        <!--<field alias="布尔类型" analyzer="null" column_name="boolType" description="布尔类型" name="boolType" />-->
        <!--<field alias="数字类型" analyzer="null" column_name="numberType" description="数字类型" name="numberType" />-->
        <!--<field alias="短文本类型" analyzer="lowercase_analyzer" column_name="tinyTextType" description="短文本类型" name="tinyTextType" />-->
        <!--<field alias="长文本类型" analyzer="standard" column_name="longTextType" description="长文本类型" name="longTextType">-->
            <!--&lt;!&ndash;-->
                <!--多值字段：同时需要存储分词和不分词字段时使用-->
            <!--&ndash;&gt;-->
            <!--<multi_field analyzer="null" column_name="raw" description="申请号原始" name="longTextType_raw" />-->
        <!--</field>-->
        <!--<field alias="日期类型" analyzer="null" column_name="dateType" description="日期类型" name="dateType" />-->
        <!--<field alias="图像类型" analyzer="null" column_name="imageType" description="图像类型" name="imageType" />-->
        <!--<field alias="二进制类型" analyzer="null" column_name="binaryType" description="二进制类型" name="binaryType" />-->


        <field alias="主键" analyzer="null" column_name="sectionId" description="主键" name="sectionId" />
        <field alias="法律法规主键" analyzer="null" column_name="lawId" description="号码类型" name="lawId" />
        <field alias="序号" analyzer="null" column_name="seq" description="序号" name="seq" />
        <field alias="编号" analyzer="null" column_name="bNum" description="编号" name="bNum" />
        <field alias="章号" analyzer="null" column_name="zNum" description="章号" name="zNum" />
        <field alias="节号" analyzer="null" column_name="jNum" description="节号" name="jNum" />
        <field alias="条号" analyzer="null" column_name="tNum" description="条号" name="tNum" />
        <field alias="款号" analyzer="null" column_name="kNum" description="款号" name="kNum" />
        <field alias="项号" analyzer="null" column_name="xNum" description="项号" name="xNum" />
        <field alias="目号" analyzer="null" column_name="mNum" description="目号" name="mNum" />
        <field alias="标签" analyzer="null" column_name="label" description="标签" name="label" />
        <field alias="条款内容" analyzer="standard" column_name="lawContent" description="条款内容" name="lawContent" />
        <field alias="类型" analyzer="null" column_name="secType" description="类型" name="type" />
        <field alias="父条款id" analyzer="null" column_name="pId" description="父条款id" name="pId" />
        <field alias="删除标识" analyzer="null" column_name="deleteFlag" description="删除标识" name="deleteFlag"/>
        <field alias="创建人ID" analyzer="null" column_name="createId" description="创建人ID" name="createId" />
        <field alias="创建时间" analyzer="null" column_name="createDate" description="创建时间" name="createDate" />
        <field alias="修改人ID" analyzer="null" column_name="updateId" description="修改人ID" name="updateId" />
        <field alias="修改时间" analyzer="null" column_name="updateDate" description="修改时间" name="updateDate" />
    </fields>
    <!--
        description： 嵌套字段描述
        name：        嵌套字段名称
    -->
    <!--<nested description="嵌套字段" name="nestField">-->
        <!--<columns>-->
            <!--<column description="被嵌套字段1" is_array="false" name="nestedField1" not_null="false" type="item_no" />-->
            <!--<column description="被嵌套字段2" is_array="false" name="nestedField2" not_null="false" type="long_text" />-->
        <!--</columns>-->
        <!--<field alias="被嵌套字段1" analyzer="null" column_name="nestedField1" description="被嵌套字段1" name="nestedField1" />-->
        <!--<field alias="被嵌套字段2" analyzer="null" column_name="nestedField2" description="被嵌套字段2" name="nestedField2">-->
            <!--<multi_field analyzer="null" column_name="raw" description="被嵌套字段2" name="nestedField2_raw" />-->
        <!--</field>-->
    <!--</nested>-->
    <!--<virtual_fields>-->
        <!--&lt;!&ndash;-->
            <!--name：  虚拟字段名称-->
            <!--alias： 虚拟字段别名-->
            <!--fields: 虚拟字段包含的字段，可以包含普通字段和被嵌套字段-->
        <!--&ndash;&gt;-->
        <!--<virtual_field name="virtual" alias="虚拟字段" fields="tinyTextType, nestedField1" />-->
    <!--</virtual_fields>-->
    <date_fields>
        <!--
            name：  日期字段名称
            format：日期字段格式
        -->
        <date_field name="createDate" format="yyyy-MM-dd HH:mm:ss" />
        <date_field name="updateDate" format="yyyy-MM-dd HH:mm:ss" />
    </date_fields>
</type>