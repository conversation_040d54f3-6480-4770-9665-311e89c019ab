{"settings": {"index": {"number_of_shards": 1, "number_of_replicas": 0, "analysis": {"filter": {"english_stemmer": {"type": "stemmer", "language": "english"}, "my_stopwords": {"type": "stop", "stopwords": ["\n", "\r\n"]}, "english_possessive_stemmer": {"type": "stemmer", "language": "possessive_english"}, "english_stop": {"type": "stop", "stopwords": "_english_"}}, "analyzer": {"lowercase_analyzer": {"filter": ["lowercase"], "type": "custom", "tokenizer": "keyword"}, "english_without_html_tag": {"filter": ["english_possessive_stemmer", "lowercase", "english_stop", "english_stemmer"], "char_filter": ["html_strip", "n_char_filter", "rn_char_filter"], "type": "custom", "tokenizer": "standard"}, "ik_without_html_tag": {"filter": ["lowercase"], "char_filter": ["html_strip", "n_char_filter", "rn_char_filter"], "type": "custom", "tokenizer": "ik_max_word"}, "2gram_analyzer": {"tokenizer": "2gram_tokenizer"}}, "tokenizer": {"2gram_tokenizer": {"type": "nGram", "min_gram": "1", "max_gram": "2", "token_chars": []}}, "char_filter": {"n_char_filter": {"pattern": "\n", "type": "pattern_replace", "replacement": ""}, "rn_char_filter": {"pattern": "\r\n", "type": "pattern_replace", "replacement": ""}}}}}, "mappings": {"_source": {"excludes": ["*_vec", "mbi", "tacd"]}, "properties": {"apfcjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "infen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "tepc": {"type": "text", "analyzer": "smart_analyzer"}, "tew": {"type": "text", "analyzer": "smart_analyzer"}, "pnne": {"type": "keyword"}, "tbjp": {"type": "text", "analyzer": "smart_analyzer"}, "sfctganc": {"type": "keyword"}, "fdapid": {"type": "keyword"}, "pnno": {"type": "keyword"}, "tfo": {"type": "text", "analyzer": "smart_analyzer"}, "ac": {"type": "text", "analyzer": "smart_analyzer"}, "daxx": {"type": "keyword"}, "ad": {"type": "date", "format": "yyyymmdd"}, "ctpnc": {"type": "keyword"}, "apfoad": {"type": "text", "analyzer": "smart_analyzer"}, "pctao": {"type": "keyword"}, "pctan": {"type": "keyword"}, "inad": {"type": "text", "analyzer": "smart_analyzer"}, "pdym": {"type": "date", "format": "yyyymm"}, "pctai": {"type": "keyword"}, "pctaw": {"type": "keyword"}, "pctas": {"type": "keyword"}, "seo": {"type": "text", "analyzer": "smart_analyzer"}, "sep": {"type": "text", "analyzer": "smart_analyzer"}, "fdapeded": {"type": "date", "format": "yyyymmdd"}, "sfc": {"type": "keyword"}, "abcn": {"type": "text", "analyzer": "smart_analyzer"}, "pagadz": {"type": "text", "analyzer": "smart_analyzer"}, "dclmjp": {"type": "text", "analyzer": "smart_analyzer"}, "prnedb": {"type": "keyword"}, "pagads": {"type": "text", "analyzer": "smart_analyzer"}, "iso": {"type": "text", "analyzer": "smart_analyzer"}, "asfo": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "sfctgpnc": {"type": "keyword"}, "pctco": {"type": "keyword"}, "pagadp": {"type": "keyword"}, "pagadc": {"type": "keyword"}, "pagadd": {"type": "text", "analyzer": "smart_analyzer"}, "asfoadcn": {"type": "text", "analyzer": "smart_analyzer"}, "fdacns": {"type": "text", "analyzer": "smart_analyzer"}, "seisc": {"type": "keyword"}, "pag": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "tio": {"type": "text", "analyzer": "smart_analyzer"}, "da": {"type": "keyword"}, "excn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "par": {"type": "text", "analyzer": "smart_analyzer"}, "asfos": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asfot": {"type": "keyword"}, "info": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "prneep": {"type": "keyword"}, "ctcoc": {"type": "keyword"}, "aben": {"type": "text", "analyzer": "smart_analyzer"}, "ady": {"type": "date", "format": "yyyy"}, "asfcso": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "isen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "tesc": {"type": "text", "analyzer": "smart_analyzer"}, "fclmcn": {"type": "text", "analyzer": "smart_analyzer"}, "apfods": {"type": "text", "analyzer": "smart_analyzer"}, "ctgcoc": {"type": "keyword"}, "apfodp": {"type": "keyword"}, "prneo": {"type": "keyword"}, "exen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apfodz": {"type": "text", "analyzer": "smart_analyzer"}, "pagadll": {"type": "geo_point"}, "seimc": {"type": "keyword"}, "fi": {"type": "keyword"}, "apfodc": {"type": "keyword"}, "iclmcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "apfodd": {"type": "text", "analyzer": "smart_analyzer"}, "ctnput": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "ft": {"type": "keyword"}, "pdf": {"type": "date", "format": "yyyymmdd"}, "apfoco": {"type": "keyword"}, "clmen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "tfen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "gd": {"type": "date", "format": "yyyymmdd"}, "pdy": {"type": "date", "format": "yyyy"}, "infjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "cpcmsg": {"type": "keyword"}, "tbjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "ddcn": {"type": "text", "analyzer": "smart_analyzer"}, "ipcmsc": {"type": "keyword"}, "cpcmsc": {"type": "keyword"}, "fdadf": {"type": "keyword"}, "locc": {"type": "keyword"}, "agtjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctgn": {"type": "keyword"}, "fdacp": {"type": "keyword"}, "sephc": {"type": "text", "analyzer": "smart_analyzer"}, "apfoadcn": {"type": "text", "analyzer": "smart_analyzer"}, "inadc": {"type": "keyword"}, "ctgc": {"type": "keyword"}, "fdacn": {"type": "text", "analyzer": "smart_analyzer"}, "docdbfc": {"type": "keyword"}, "asfoadjp": {"type": "text", "analyzer": "smart_analyzer"}, "exjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctgefc": {"type": "keyword"}, "sls": {"type": "keyword"}, "isud": {"type": "date", "format": "yyyymmdd"}, "iclmjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "asfjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "cacd": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "locsc": {"type": "keyword"}, "apfo": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "tfcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "clmo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "dclmen": {"type": "text", "analyzer": "smart_analyzer"}, "ftc": {"type": "keyword"}, "neic": {"type": "keyword"}, "dden": {"type": "text", "analyzer": "smart_analyzer"}, "neicc": {"type": "keyword"}, "abjp": {"type": "text", "analyzer": "smart_analyzer"}, "man": {"type": "keyword"}, "asfcscn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "agto": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ftl": {"type": "keyword"}, "fdac": {"type": "text", "analyzer": "smart_analyzer"}, "fdae": {"type": "text", "analyzer": "smart_analyzer"}, "fdai": {"type": "keyword"}, "ptcn": {"type": "keyword"}, "fdaan": {"type": "keyword"}, "fdaat": {"type": "keyword"}, "apfoaden": {"type": "text", "analyzer": "smart_analyzer"}, "fdaai": {"type": "keyword"}, "fdat": {"type": "keyword"}, "mbi": {"type": "text", "analyzer": "smart_analyzer"}, "agcjp": {"type": "text", "analyzer": "smart_analyzer"}, "efid": {"type": "long"}, "asfodcc": {"type": "keyword"}, "inadp": {"type": "text", "analyzer": "smart_analyzer"}, "apfcsen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "assbadco": {"type": "keyword"}, "paftus": {"type": "keyword"}, "tbo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "pid": {"type": "keyword"}, "abspic": {"type": "keyword"}, "andb": {"type": "keyword"}, "dclmcn": {"type": "text", "analyzer": "smart_analyzer"}, "peec": {"type": "text", "analyzer": "smart_analyzer"}, "cpc": {"type": "keyword"}, "nemic": {"type": "keyword"}, "asfcsen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "li": {"type": "keyword"}, "cpcsq": {"type": "keyword"}, "cpcs": {"type": "keyword"}, "prdye": {"type": "date", "format": "yyyy"}, "tbcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "cpcq": {"type": "keyword"}, "ticn": {"type": "text", "analyzer": "smart_analyzer"}, "fclmjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "cpcm": {"type": "keyword"}, "cpcl": {"type": "keyword"}, "cpci": {"type": "keyword"}, "cpcg": {"type": "keyword"}, "ctgsfc": {"type": "keyword"}, "cpcc": {"type": "keyword"}, "cpcsc": {"type": "keyword"}, "apfodcc": {"type": "keyword"}, "appcc": {"type": "keyword"}, "fdaped": {"type": "date", "format": "yyyymmdd"}, "cpcsg": {"type": "keyword"}, "apfcscn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "agten": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apfos": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "iclmcn": {"type": "text", "analyzer": "smart_analyzer"}, "apfot": {"type": "keyword"}, "asfodd": {"type": "text", "analyzer": "smart_analyzer"}, "apfcen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asfodc": {"type": "keyword"}, "fctgc": {"type": "keyword"}, "fclmo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "anc": {"type": "keyword"}, "ane": {"type": "keyword"}, "tben_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "govi": {"type": "keyword"}, "asfoco": {"type": "keyword"}, "asfoaden": {"type": "text", "analyzer": "smart_analyzer"}, "agccn": {"type": "text", "analyzer": "smart_analyzer"}, "prce": {"type": "keyword"}, "ano": {"type": "keyword"}, "ctaac": {"type": "keyword"}, "recs": {"type": "keyword"}, "reeinv": {"type": "keyword"}, "ple": {"type": "keyword"}, "asfodz": {"type": "text", "analyzer": "smart_analyzer"}, "dclmo": {"type": "text", "analyzer": "smart_analyzer"}, "tacd_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "licec": {"type": "keyword"}, "asfods": {"type": "text", "analyzer": "smart_analyzer"}, "rpt": {"type": "keyword"}, "abot": {"type": "text", "analyzer": "smart_analyzer"}, "ddjp": {"type": "text", "analyzer": "smart_analyzer"}, "asfodp": {"type": "keyword"}, "dclmc": {"type": "keyword"}, "dut": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "apfccn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "cst": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "agtadco": {"type": "keyword"}, "peecs": {"type": "text", "analyzer": "smart_analyzer"}, "apfoadjp": {"type": "text", "analyzer": "smart_analyzer"}, "recd": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "agcen": {"type": "text", "analyzer": "smart_analyzer"}, "ctc": {"type": "keyword"}, "pd": {"type": "date", "format": "yyyymmdd"}, "recn": {"type": "keyword"}, "tien": {"type": "text", "analyzer": "smart_analyzer"}, "pl": {"type": "integer"}, "ctn": {"type": "keyword"}, "exp": {"type": "integer"}, "apl": {"type": "keyword"}, "exo": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "pctpt": {"type": "keyword"}, "pt": {"type": "keyword"}, "pnc": {"type": "keyword"}, "asfcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "prec": {"type": "keyword"}, "pnndb": {"type": "keyword"}, "pv": {"type": "keyword"}, "pne": {"type": "keyword"}, "pctpo": {"type": "keyword"}, "pnk": {"type": "keyword"}, "prem": {"type": "keyword"}, "isjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "pno": {"type": "keyword"}, "pres": {"type": "keyword"}, "docn": {"type": "keyword"}, "assbadc": {"type": "keyword"}, "asfcsjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "secn": {"type": "text", "analyzer": "smart_analyzer"}, "dafm": {"type": "keyword"}, "assc": {"type": "keyword"}, "cus": {"type": "keyword"}, "clmjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "agtad": {"type": "text", "analyzer": "smart_analyzer"}, "agtadp": {"type": "text", "analyzer": "smart_analyzer"}, "prde": {"type": "date", "format": "yyyymmdd"}, "asfoado": {"type": "text", "analyzer": "smart_analyzer"}, "assbadd": {"type": "text", "analyzer": "smart_analyzer"}, "agtadc": {"type": "keyword"}, "assbads": {"type": "text", "analyzer": "smart_analyzer"}, "efpnc": {"type": "keyword"}, "assbadp": {"type": "keyword"}, "ctgput": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "sfrpn": {"type": "keyword"}, "sfanc": {"type": "keyword"}, "assbadz": {"type": "text", "analyzer": "smart_analyzer"}, "reec": {"type": "keyword"}, "rl": {"type": "keyword"}, "rn": {"type": "keyword"}, "abjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "rs": {"type": "keyword"}, "asfoad": {"type": "text", "analyzer": "smart_analyzer"}, "cpcmg": {"type": "keyword"}, "ry": {"type": "keyword"}, "anndb": {"type": "keyword"}, "asfen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "assbad": {"type": "text", "analyzer": "smart_analyzer"}, "cpcms": {"type": "keyword"}, "seed": {"type": "date", "format": "yyyymmdd"}, "pafg": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "abot_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "cpcmc": {"type": "keyword"}, "tijp": {"type": "text", "analyzer": "smart_analyzer"}, "tfjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "seen": {"type": "text", "analyzer": "smart_analyzer"}, "apoc": {"type": "keyword"}, "iclmen": {"type": "text", "analyzer": "smart_analyzer"}, "seicc": {"type": "keyword"}, "asfcjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "adym": {"type": "date", "format": "yyyymm"}, "agtcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "aassc": {"type": "keyword"}, "apfodll": {"type": "geo_point"}, "tfcn": {"type": "text", "analyzer": "smart_analyzer"}, "asfco": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asfcs": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asfodll": {"type": "geo_point"}, "pre": {"type": "keyword"}, "desn": {"type": "keyword"}, "asfccn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ipcms": {"type": "keyword"}, "sfid": {"type": "long"}, "uc": {"type": "keyword"}, "inadco": {"type": "keyword"}, "sfcoc": {"type": "keyword"}, "efanc": {"type": "keyword"}, "clmjp": {"type": "text", "analyzer": "smart_analyzer"}, "dclmen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "apfoado": {"type": "text", "analyzer": "smart_analyzer"}, "invc": {"type": "keyword"}, "pctnd": {"type": "date", "format": "yyyymmdd"}, "anno": {"type": "keyword"}, "ipcml": {"type": "keyword"}, "ipcmc": {"type": "keyword"}, "anne": {"type": "keyword"}, "tfen": {"type": "text", "analyzer": "smart_analyzer"}, "ipcmg": {"type": "keyword"}, "ipcmh": {"type": "keyword"}, "iclmjp": {"type": "text", "analyzer": "smart_analyzer"}, "apfjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asfcen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "seic": {"type": "keyword"}, "pledgepee": {"type": "text", "analyzer": "smart_analyzer"}, "rpap": {"type": "keyword"}, "pts": {"type": "keyword"}, "ctut": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "passc": {"type": "keyword"}, "legal": {"type": "keyword"}, "pndb": {"type": "keyword"}, "fclmolec": {"type": "keyword"}, "apfcsjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "neimc": {"type": "keyword"}, "patus": {"type": "keyword"}, "fclmo": {"type": "text", "analyzer": "smart_analyzer"}, "fdauc": {"type": "keyword"}, "isad": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "pctpn": {"type": "keyword"}, "efcoc": {"type": "keyword"}, "fclmcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "pve": {"type": "keyword"}, "pvl": {"type": "keyword"}, "ctput": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "agco": {"type": "text", "analyzer": "smart_analyzer"}, "pvt": {"type": "keyword"}, "fclmen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "ctganc": {"type": "keyword"}, "apfco": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apfcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apfcs": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "tfjp": {"type": "text", "analyzer": "smart_analyzer"}, "loc": {"type": "keyword"}, "dclmo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "casn": {"type": "keyword"}, "neisc": {"type": "keyword"}, "iscn": {"type": "text", "analyzer": "smart_analyzer"}, "sejp": {"type": "text", "analyzer": "smart_analyzer"}, "flag": {"type": "keyword"}, "crut": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "iso_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "abcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "apfen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctgpnc": {"type": "keyword"}, "fclmen": {"type": "text", "analyzer": "smart_analyzer"}, "wocos": {"type": "keyword"}, "dclmjp_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "ipcsc": {"type": "keyword"}, "ctnpc": {"type": "keyword"}, "clmcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "ipcsq": {"type": "keyword"}, "dan": {"type": "keyword"}, "isen": {"type": "text", "analyzer": "smart_analyzer"}, "fdapp": {"type": "keyword"}, "fdapn": {"type": "text", "analyzer": "smart_analyzer"}, "efpdf": {"type": "date", "format": "yyyymmdd"}, "liplf": {"type": "text", "analyzer": "smart_analyzer"}, "ucm": {"type": "keyword"}, "clmen": {"type": "text", "analyzer": "smart_analyzer"}, "tbcn": {"type": "text", "analyzer": "smart_analyzer"}, "ucl": {"type": "keyword"}, "septi": {"type": "text", "analyzer": "smart_analyzer"}, "aben_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "fclmjp": {"type": "text", "analyzer": "smart_analyzer"}, "oppo": {"type": "keyword"}, "ptus": {"type": "keyword"}, "pexp": {"type": "integer"}, "plet": {"type": "keyword"}, "ples": {"type": "keyword"}, "iclmen_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "clmbc": {"type": "keyword"}, "pagad": {"type": "text", "analyzer": "smart_analyzer"}, "clmc": {"type": "keyword"}, "tbo": {"type": "text", "analyzer": "smart_analyzer"}, "pagadco": {"type": "keyword"}, "cpscs": {"type": "keyword"}, "image_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "eed": {"type": "date", "format": "yyyymmdd"}, "tacd": {"type": "text", "analyzer": "smart_analyzer"}, "pledgepees": {"type": "text", "analyzer": "smart_analyzer"}, "iclmc": {"type": "keyword"}, "iclmo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "tben": {"type": "text", "analyzer": "smart_analyzer"}, "dclmcn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "eey": {"type": "date", "format": "yyyy"}, "clmcn": {"type": "text", "analyzer": "smart_analyzer"}, "plec": {"type": "keyword"}, "sfctgcoc": {"type": "keyword"}, "efc": {"type": "keyword"}, "infcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "fctc": {"type": "keyword"}, "inq": {"type": "keyword"}, "ddo": {"type": "text", "analyzer": "smart_analyzer"}, "clmt": {"type": "keyword"}, "isjp": {"type": "text", "analyzer": "smart_analyzer"}, "clmo": {"type": "text", "analyzer": "smart_analyzer"}, "tfo_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "docdbpt": {"type": "keyword"}, "pagadcc": {"type": "keyword"}, "fic": {"type": "keyword"}, "ipcc": {"type": "keyword"}, "fil": {"type": "keyword"}, "ipcg": {"type": "keyword"}, "fim": {"type": "keyword"}, "iscn_vec": {"type": "gw_knn_ready", "dim": 4096, "lsh_params": {"n_hashes": 64, "band": 1, "model_path": ""}, "pq_params": {"n_bits": 8, "n_centroids": 512, "model_path": ""}}, "ipch": {"type": "keyword"}, "ipcl": {"type": "keyword"}, "seps": {"type": "keyword"}, "ipcm": {"type": "keyword"}, "ipcr": {"type": "keyword"}, "sepp": {"type": "keyword"}, "ipcs": {"type": "keyword"}, "clmac": {"type": "keyword"}, "sepn": {"type": "keyword"}, "ipcq": {"type": "keyword"}, "apfcso": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ipc": {"type": "keyword"}, "citation": {"type": "nested", "properties": {"ctan": {"type": "keyword"}, "ctco": {"type": "keyword"}, "ctpdy": {"type": "date", "format": "yyyy"}, "cts": {"type": "text", "analyzer": "smart_analyzer"}, "ctaps": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctefpn": {"type": "keyword"}, "ctx": {"type": "keyword"}, "cty": {"type": "keyword"}, "ctcg": {"type": "keyword"}, "ctad": {"type": "date", "format": "yyyymmdd"}, "ctpn": {"type": "keyword"}, "cta": {"type": "keyword"}, "fct": {"type": "keyword"}, "ctsfpn": {"type": "keyword"}, "ctpd": {"type": "date", "format": "yyyymmdd"}, "fctap": {"type": "text", "analyzer": "smart_analyzer"}, "ctp": {"type": "keyword"}, "ctap": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}}}, "applicantinfo": {"type": "nested", "properties": {"apo": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcs": {"type": "keyword"}, "apcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcl": {"type": "keyword"}, "apci": {"type": "keyword"}, "apos": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcadll": {"type": "keyword"}, "apradp": {"type": "keyword"}, "apclc": {"type": "keyword"}, "apoadll": {"type": "keyword"}, "apuscc": {"type": "keyword"}, "apost": {"type": "keyword"}, "apoadjp": {"type": "text", "analyzer": "smart_analyzer"}, "apoad": {"type": "text", "analyzer": "smart_analyzer"}, "apcet": {"type": "date", "format": "yyyymmdd"}, "aprad": {"type": "text", "analyzer": "smart_analyzer"}, "apoadz": {"type": "text", "analyzer": "smart_analyzer"}, "apradc": {"type": "keyword"}, "apradd": {"type": "text", "analyzer": "smart_analyzer"}, "apoads": {"type": "text", "analyzer": "smart_analyzer"}, "apfn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apoadcc": {"type": "keyword"}, "aprn": {"type": "keyword"}, "apoadd": {"type": "text", "analyzer": "smart_analyzer"}, "apoadc": {"type": "keyword"}, "apne": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apoadp": {"type": "keyword"}, "apoado": {"type": "text", "analyzer": "smart_analyzer"}, "apoaden": {"type": "text", "analyzer": "smart_analyzer"}, "apradll": {"type": "keyword"}, "apoadcn": {"type": "text", "analyzer": "smart_analyzer"}, "apct": {"type": "keyword"}, "apoadco": {"type": "keyword"}}}, "assigneeinfo": {"type": "nested", "properties": {"asoadc": {"type": "keyword"}, "ascn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asoadco": {"type": "keyword"}, "asoadcn": {"type": "text", "analyzer": "smart_analyzer"}, "asost": {"type": "keyword"}, "asen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asoadd": {"type": "text", "analyzer": "smart_analyzer"}, "asjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asoaden": {"type": "text", "analyzer": "smart_analyzer"}, "asos": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asoadjp": {"type": "text", "analyzer": "smart_analyzer"}, "asoado": {"type": "text", "analyzer": "smart_analyzer"}, "asoadll": {"type": "keyword"}, "asoadcc": {"type": "keyword"}, "asoadp": {"type": "keyword"}, "asoads": {"type": "text", "analyzer": "smart_analyzer"}, "asoad": {"type": "text", "analyzer": "smart_analyzer"}, "asoadz": {"type": "text", "analyzer": "smart_analyzer"}, "aso": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}}}, "currenapplicantinfo": {"type": "nested", "properties": {"apcs": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcadco": {"type": "keyword"}, "apco": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcadcn": {"type": "text", "analyzer": "smart_analyzer"}, "apccn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "apcaden": {"type": "text", "analyzer": "smart_analyzer"}, "apcadp": {"type": "keyword"}, "apcado": {"type": "text", "analyzer": "smart_analyzer"}, "apcads": {"type": "text", "analyzer": "smart_analyzer"}, "apcadcc": {"type": "keyword"}, "apcadz": {"type": "text", "analyzer": "smart_analyzer"}, "apcadjp": {"type": "text", "analyzer": "smart_analyzer"}, "apcadc": {"type": "keyword"}, "apcst": {"type": "keyword"}}}, "lawstatus": {"type": "nested", "properties": {"rd": {"type": "date", "format": "yyyymmdd"}, "leus": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "dcd": {"type": "date", "format": "yyyymmdd"}, "expt": {"type": "keyword"}, "le": {"type": "keyword"}, "itvd": {"type": "keyword"}, "leci": {"type": "keyword"}, "lst": {"type": "text", "analyzer": "smart_analyzer"}, "leusi": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "wd": {"type": "date", "format": "yyyymmdd"}}}, "invalidinfo": {"type": "nested", "properties": {"invme": {"type": "text", "analyzer": "smart_analyzer"}, "invapd": {"type": "date", "format": "yyyymmdd"}, "invle": {"type": "text", "analyzer": "smart_analyzer"}, "invap": {"type": "text", "analyzer": "smart_analyzer"}, "invlb": {"type": "text", "analyzer": "smart_analyzer"}, "invd": {"type": "keyword"}, "invdp": {"type": "text", "analyzer": "smart_analyzer"}, "invaps": {"type": "text", "analyzer": "smart_analyzer"}, "invdd": {"type": "date", "format": "yyyymmdd"}, "reeinvt": {"type": "keyword"}, "invtext": {"type": "text", "analyzer": "smart_analyzer"}, "invae": {"type": "text", "analyzer": "smart_analyzer"}, "invinn": {"type": "keyword"}, "invn": {"type": "keyword"}}}, "priority": {"type": "nested", "properties": {"prnno": {"type": "keyword"}, "prc": {"type": "keyword"}, "prd": {"type": "date", "format": "yyyymmdd"}, "prno": {"type": "keyword"}, "prndb": {"type": "keyword"}, "prnn": {"type": "keyword"}, "prnndb": {"type": "keyword"}, "prnne": {"type": "keyword"}, "prdy": {"type": "date", "format": "yyyy"}, "prt": {"type": "keyword"}, "prcoc": {"type": "keyword"}, "prnep": {"type": "keyword"}, "prdym": {"type": "date", "format": "yyyymm"}}}, "pledge": {"type": "nested", "properties": {"plen": {"type": "keyword"}, "plerd": {"type": "keyword"}, "por": {"type": "text", "analyzer": "smart_analyzer"}, "pleed": {"type": "date", "format": "yyyymmdd"}, "pors": {"type": "text", "analyzer": "smart_analyzer"}, "pley": {"type": "date", "format": "yyyy"}, "prerd": {"type": "date", "format": "yyyymmdd"}, "plrcd": {"type": "date", "format": "yyyymmdd"}, "plepret": {"type": "keyword"}, "preed": {"type": "date", "format": "yyyymmdd"}}}, "permissioninfo": {"type": "nested", "properties": {"licet": {"type": "keyword"}, "lices": {"type": "keyword"}, "liceey": {"type": "date", "format": "yyyy"}, "liceed": {"type": "date", "format": "yyyymmdd"}, "liceee": {"type": "text", "analyzer": "smart_analyzer"}, "liceors": {"type": "text", "analyzer": "smart_analyzer"}, "liceees": {"type": "text", "analyzer": "smart_analyzer"}, "liced": {"type": "date", "format": "yyyymmdd"}, "licest": {"type": "date", "format": "yyyymmdd"}, "liceor": {"type": "text", "analyzer": "smart_analyzer"}, "licesc": {"type": "keyword"}, "licetd": {"type": "date", "format": "yyyymmdd"}, "lice": {"type": "keyword"}, "licen": {"type": "keyword"}, "liceeec": {"type": "text", "analyzer": "smart_analyzer"}}}, "currentassigneeinfo": {"type": "nested", "properties": {"ascen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asco": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ascadp": {"type": "keyword"}, "ascads": {"type": "text", "analyzer": "smart_analyzer"}, "asccn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ascadco": {"type": "keyword"}, "ascado": {"type": "text", "analyzer": "smart_analyzer"}, "ascst": {"type": "keyword"}, "ascjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ascadcc": {"type": "keyword"}, "ascadc": {"type": "keyword"}, "ascadcn": {"type": "text", "analyzer": "smart_analyzer"}, "ascadz": {"type": "text", "analyzer": "smart_analyzer"}, "ascaden": {"type": "text", "analyzer": "smart_analyzer"}, "ascs": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ascadjp": {"type": "text", "analyzer": "smart_analyzer"}}}, "examiner_assiant": {"type": "nested", "properties": {"aexo": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "aexen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "aexcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "aexjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}}}, "review": {"type": "nested", "properties": {"reeap": {"type": "text", "analyzer": "smart_analyzer"}, "reeinvcoadd": {"type": "text", "analyzer": "smart_analyzer"}, "reeaps": {"type": "text", "analyzer": "smart_analyzer"}, "reele": {"type": "text", "analyzer": "smart_analyzer"}, "reeme": {"type": "text", "analyzer": "smart_analyzer"}, "ohd": {"type": "date", "format": "yyyymmdd"}, "reeinvcop": {"type": "keyword"}, "reelb": {"type": "text", "analyzer": "smart_analyzer"}, "reeinvca": {"type": "text", "analyzer": "smart_analyzer"}, "reeinn": {"type": "keyword"}, "reetext": {"type": "text", "analyzer": "smart_analyzer"}, "reedd": {"type": "date", "format": "yyyymmdd"}, "reed": {"type": "keyword"}, "reeinvcof": {"type": "text", "analyzer": "smart_analyzer"}, "reeae": {"type": "text", "analyzer": "smart_analyzer"}, "reeinvcoc": {"type": "keyword"}, "reeapd": {"type": "date", "format": "yyyymmdd"}, "reen": {"type": "keyword"}, "reedp": {"type": "text", "analyzer": "smart_analyzer"}, "reeinvco": {"type": "text", "analyzer": "smart_analyzer"}}}, "inventorinfo": {"type": "nested", "properties": {"inro": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inccn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inrcn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "incn": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inco": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "injp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inrjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ino": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "incjp": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "incen": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "inren": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}}}, "litigations": {"type": "nested", "properties": {"licg": {"type": "keyword"}, "lides": {"type": "text", "analyzer": "smart_analyzer"}, "liales": {"type": "text", "analyzer": "smart_analyzer"}, "liala": {"type": "text", "analyzer": "smart_analyzer"}, "livd": {"type": "date", "format": "yyyymmdd"}, "lialas": {"type": "text", "analyzer": "smart_analyzer"}, "licp": {"type": "keyword"}, "lico": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "licn": {"type": "keyword"}, "liale": {"type": "text", "analyzer": "smart_analyzer"}, "lics": {"type": "keyword"}, "liti": {"type": "text", "analyzer": "smart_analyzer"}, "litg": {"type": "keyword"}, "lipc": {"type": "keyword"}, "lipj": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "lijd": {"type": "date", "format": "yyyymmdd"}, "lija": {"type": "keyword"}, "lifd": {"type": "date", "format": "yyyymmdd"}, "lipl": {"type": "text", "analyzer": "smart_analyzer"}, "litp": {"type": "text", "analyzer": "smart_analyzer"}, "lide": {"type": "text", "analyzer": "smart_analyzer"}, "lipp": {"type": "text", "analyzer": "smart_analyzer"}, "lint": {"type": "keyword"}, "lic": {"type": "keyword"}, "lift": {"type": "text", "analyzer": "smart_analyzer"}, "lijv": {"type": "keyword"}, "liju": {"type": "text", "analyzer": "smart_analyzer"}, "lipls": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "litps": {"type": "text", "analyzer": "smart_analyzer"}, "liscn": {"type": "keyword"}, "lip": {"type": "text", "analyzer": "smart_analyzer"}, "lify": {"type": "date", "format": "yyyy"}, "liprd": {"type": "text", "analyzer": "smart_analyzer"}, "lica": {"type": "text", "analyzer": "smart_analyzer"}, "liaju": {"type": "text", "analyzer": "smart_analyzer"}, "liaa": {"type": "keyword"}}}, "transferinfo": {"type": "nested", "properties": {"assaad": {"type": "text", "analyzer": "smart_analyzer"}, "ass": {"type": "keyword"}, "oar": {"type": "keyword"}, "asseeco": {"type": "keyword"}, "assorad": {"type": "text", "analyzer": "smart_analyzer"}, "assaadco": {"type": "keyword"}, "assaaads": {"type": "text", "analyzer": "smart_analyzer"}, "assaadp": {"type": "keyword"}, "assors": {"type": "text", "analyzer": "smart_analyzer"}, "assr": {"type": "text", "analyzer": "smart_analyzer"}, "assey": {"type": "date", "format": "yyyy"}, "assees": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "assaadrc": {"type": "text", "analyzer": "smart_analyzer"}, "assn": {"type": "keyword"}, "assco": {"type": "text", "analyzer": "smart_analyzer"}, "assaada": {"type": "text", "analyzer": "smart_analyzer"}, "assed": {"type": "date", "format": "yyyymmdd"}, "assee": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "asscoadd": {"type": "text", "analyzer": "smart_analyzer"}, "assd": {"type": "date", "format": "yyyymmdd"}, "assor": {"type": "text", "analyzer": "smart_analyzer"}, "assaaddz": {"type": "text", "analyzer": "smart_analyzer"}}}, "patentfamilysim": {"type": "nested", "properties": {"ef": {"type": "keyword"}, "docdbf": {"type": "keyword"}, "sf": {"type": "keyword"}, "famut": {"type": "date", "format": "yyyymmdd hh:mm:ss"}, "sfco": {"type": "text", "analyzer": "smart_analyzer"}, "efco": {"type": "text", "analyzer": "smart_analyzer"}, "sfpdf": {"type": "date", "format": "yyyymmdd"}}}, "cited": {"type": "nested", "properties": {"ctgsfpn": {"type": "keyword"}, "ctgs": {"type": "keyword"}, "ctgpn": {"type": "keyword"}, "ctgp": {"type": "keyword"}, "ctgapc": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctnp": {"type": "keyword"}, "ctgad": {"type": "date", "format": "yyyymmdd"}, "fctg": {"type": "keyword"}, "ctgap": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctgan": {"type": "keyword"}, "ctgefpn": {"type": "keyword"}, "ctgco": {"type": "keyword"}, "fctgap": {"type": "text", "analyzer": "smart_analyzer"}, "ctgaps": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "smart_analyzer"}, "ctgpd": {"type": "date", "format": "yyyymmdd"}}}}}}