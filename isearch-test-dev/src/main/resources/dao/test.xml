<?xml version='1.0' encoding='UTF-8'?>
<!--    index:               索引名称    name：               类型名称    primary_key：        主键    number_of_shards：   分片数量    number_of_replicas： 副本数量-->
<type index="test" name="test" is_default="true" primary_key="id" number_of_shards="5" number_of_replicas="1">
    <columns>
        <!--
        description：字段描述
        is_array：   字段是否数组
        name：       字段名
        not_null：   字段是否不为空
        type：       字段类型，有以下几种类型
        号码类型：   item_no
        布尔类型：   boolean、bool
        数字类型：   byte、short、integer、long、float、double
        短文本类型： tiny_text
        长文本类型： long_text
        日期类型：   date
        图像类型：   image
        二进制类型： binary
        -->
        <column description="主键" is_array="false" name="id" not_null="false" type="item_no"/>
        <column description="号码类型" is_array="false" name="itemType" not_null="false" type="item_no"/>
        <column description="布尔类型" is_array="false" name="boolType" not_null="false" type="boolean"/>
        <column description="数字类型" is_array="false" name="numberType" not_null="false" type="long"/>
        <column description="短文本类型" is_array="false" name="tinyTextType" not_null="false" type="tiny_text"/>
        <column description="长文本类型" is_array="false" name="longTextType" not_null="false" type="long_text"/>
        <column description="日期类型" is_array="false" name="dateType" not_null="false" type="date"/>
        <column description="图像类型" is_array="false" name="imageType" not_null="false" type="image"/>
        <column description="二进制类型" is_array="false" name="binaryType" not_null="false" type="binary"/>
    </columns>
    <fields>
        <!--
        alias：      字段别名
        analyzer：   字段分词器，有关字段类型和字段分词器的相关规则，见帮助文档
        column_name：字段名
        description：字段描述
        name：       字段名
        -->
        <field alias="主键" analyzer="null" column_name="id" description="主键" name="id"/>
        <field alias="号码类型" analyzer="null" column_name="itemType" description="号码类型" name="itemType"/>
        <field alias="布尔类型" analyzer="null" column_name="boolType" description="布尔类型" name="boolType"/>
        <field alias="数字类型" analyzer="null" column_name="numberType" description="数字类型" name="numberType"/>
        <field alias="短文本类型" analyzer="lowercase_analyzer" column_name="tinyTextType" description="短文本类型"
               name="tinyTextType"/>
        <field alias="长文本类型" analyzer="standard" column_name="longTextType" description="长文本类型" name="longTextType">
            <!--                多值字段：同时需要存储分词和不分词字段时使用            -->
            <multi_field analyzer="null" column_name="raw" description="申请号原始" name="longTextType_raw"/>
        </field>
        <field alias="日期类型" analyzer="null" column_name="dateType" description="日期类型" name="dateType"/>
        <field alias="图像类型" analyzer="null" column_name="imageType" description="图像类型" name="imageType"/>
        <field alias="二进制类型" analyzer="null" column_name="binaryType" description="二进制类型" name="binaryType"/>
    </fields>
    <!--
    description： 嵌套字段描述
    name：        嵌套字段名称
    -->
    <nested description="嵌套字段" name="nestField">
        <columns>
            <column description="被嵌套字段1" is_array="false" name="nestedField1" not_null="false" type="item_no"/>
            <column description="被嵌套字段2" is_array="false" name="nestedField2" not_null="false" type="long_text"/>
        </columns>
        <field alias="被嵌套字段1" analyzer="null" column_name="nestedField1" description="被嵌套字段1" name="nestedField1"/>
        <field alias="被嵌套字段2" analyzer="null" column_name="nestedField2" description="被嵌套字段2" name="nestedField2">
            <multi_field analyzer="null" column_name="raw" description="被嵌套字段2" name="nestedField2_raw"/>
        </field>
    </nested>
    <virtual_fields>
        <!--
        name：  虚拟字段名称
        alias： 虚拟字段别名
        fields: 虚拟字段包含的字段，可以包含普通字段和被嵌套字段
        -->
        <virtual_field name="virtual" alias="虚拟字段" fields="tinyTextType, nestedField1"/>
    </virtual_fields>
    <date_fields>
        <!--
        name：  日期字段名称
        format：日期字段格式
        -->
        <date_field name="dateType" format="yyyy-MM-dd HH:mm:ss"/>
    </date_fields>
</type>