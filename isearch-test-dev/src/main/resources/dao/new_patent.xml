<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<type index="cbs_patent" is_default="true" name="_doc" number_of_replicas="0" number_of_shards="1" primary_key="pid">
    <columns>
        <column description="专利编码" is_array="true" name="pid" not_null="false" type="item_no"/>
        <column description="标题中文" is_array="true" name="ticn" not_null="false" type="tiny_text"/>
        <column description="标题英文" is_array="true" name="tien" not_null="false" type="tiny_text"/>
        <column description="标题日文" is_array="true" name="tijp" not_null="false" type="tiny_text"/>
        <column description="标题原文" is_array="true" name="tio" not_null="false" type="tiny_text"/>
        <column description="摘要中文" is_array="true" name="abcn" not_null="false" type="long_text"/>
        <column description="摘要英文" is_array="true" name="aben" not_null="false" type="long_text"/>
        <column description="摘要日文" is_array="true" name="abjp" not_null="false" type="long_text"/>
        <column description="摘要原文" is_array="true" name="abot" not_null="false" type="long_text"/>
        <column description="权利要求类型英文" is_array="true" name="clmten" not_null="false" type="item_no"/>
        <column description="权利要求类型中文" is_array="true" name="clmtcn" not_null="false" type="item_no"/>
        <column description="权利要求类型代码" is_array="true" name="clmtabbr" not_null="false" type="item_no"/>
        <column description="首项权利要求中文" is_array="true" name="fclmcn" not_null="false" type="long_text"/>
        <column description="首项权利要求英文" is_array="true" name="fclmen" not_null="false" type="long_text"/>
        <column description="首项权利要求日文" is_array="true" name="fclmjp" not_null="false" type="long_text"/>
        <column description="首项权利要求原文" is_array="true" name="fclmo" not_null="false" type="long_text"/>
        <column description="独立权利要求中文" is_array="true" name="iclmcn" not_null="false" type="long_text"/>
        <column description="独立权利要求英文" is_array="true" name="iclmen" not_null="false" type="long_text"/>
        <column description="独立权利要求日文" is_array="true" name="iclmjp" not_null="false" type="long_text"/>
        <column description="独立权利要求原文" is_array="true" name="iclmo" not_null="false" type="long_text"/>
        <column description="从属权利要求中文" is_array="true" name="dclmcn" not_null="false" type="long_text"/>
        <column description="从属权利要求英文" is_array="true" name="dclmen" not_null="false" type="long_text"/>
        <column description="从属权利要求日文" is_array="true" name="dclmjp" not_null="false" type="long_text"/>
        <column description="从属权利要求原文" is_array="true" name="dclmo" not_null="false" type="long_text"/>
        <column description="技术领域中文" is_array="true" name="tfcn" not_null="false" type="long_text"/>
        <column description="技术领域英文" is_array="true" name="tfen" not_null="false" type="long_text"/>
        <column description="技术领域日文" is_array="true" name="tfjp" not_null="false" type="long_text"/>
        <column description="技术领域原文" is_array="true" name="tfo" not_null="false" type="long_text"/>
        <column description="背景技术中文" is_array="true" name="tbcn" not_null="false" type="long_text"/>
        <column description="背景技术英文" is_array="true" name="tben" not_null="false" type="long_text"/>
        <column description="背景技术日文" is_array="true" name="tbjp" not_null="false" type="long_text"/>
        <column description="背景技术原文" is_array="true" name="tbo" not_null="false" type="long_text"/>
        <column description="发明内容中文" is_array="true" name="iscn" not_null="false" type="long_text"/>
        <column description="发明内容英文" is_array="true" name="isen" not_null="false" type="long_text"/>
        <column description="发明内容日文" is_array="true" name="isjp" not_null="false" type="long_text"/>
        <column description="发明内容原文" is_array="true" name="iso" not_null="false" type="long_text"/>
        <column description="附图说明中文" is_array="true" name="ddcn" not_null="false" type="long_text"/>
        <column description="附图说明英文" is_array="true" name="dden" not_null="false" type="long_text"/>
        <column description="附图说明日文" is_array="true" name="ddjp" not_null="false" type="long_text"/>
        <column description="附图说明原文" is_array="true" name="ddo" not_null="false" type="long_text"/>
        <column description="具体实施方式中文" is_array="true" name="secn" not_null="false" type="long_text"/>
        <column description="具体实施方式英文" is_array="true" name="seen" not_null="false" type="long_text"/>
        <column description="具体实施方式日文" is_array="true" name="sejp" not_null="false" type="long_text"/>
        <column description="具体实施方式原文" is_array="true" name="seo" not_null="false" type="long_text"/>
        <column description="技术功效句" is_array="true" name="tesc" not_null="false" type="tiny_text"/>
        <column description="技术功效短语" is_array="true" name="tepc" not_null="false" type="tiny_text"/>
        <column description="技术功效词" is_array="true" name="tew" not_null="false" type="tiny_text"/>
        <column description="标题关键字中文" is_array="true" name="titlewordzh" not_null="false" type="tiny_text"/>
        <column description="标题关键字英文" is_array="true" name="titleworden" not_null="false" type="tiny_text"/>
        <column description="摘要关键字中文" is_array="true" name="abstractwordzh" not_null="false" type="tiny_text"/>
        <column description="摘要关键字英文" is_array="true" name="abstractworden" not_null="false" type="tiny_text"/>
        <column description="权利要求关键字中文" is_array="true" name="claimswordzh" not_null="false" type="tiny_text"/>
        <column description="权利要求关键字英文" is_array="true" name="claimsworden" not_null="false" type="tiny_text"/>
        <column description="说明书关键字中文" is_array="true" name="descriptionwordzh" not_null="false" type="tiny_text"/>
        <column description="说明书关键字英文" is_array="true" name="descriptionworden" not_null="false" type="tiny_text"/>
        <column description="申请语言" is_array="true" name="apl" not_null="false" type="item_no"/>
        <column description="专利类型(DOCDB)" is_array="true" name="docdbpt" not_null="false" type="item_no"/>
        <column description="专利类型(中国)" is_array="true" name="ptcn" not_null="false" type="item_no"/>
        <column description="专利类型(美国)" is_array="true" name="ptus" not_null="false" type="item_no"/>
        <column description="专利类型-简单" is_array="true" name="pts" not_null="false" type="item_no"/>
        <column description="授权日" is_array="true" name="gd" not_null="false" type="date"/>
        <column description="提出实审时长" is_array="true" name="pexp" not_null="false" type="integer"/>
        <column description="审查时长" is_array="true" name="exp" not_null="false" type="integer"/>
        <column description="实质审查生效日" is_array="true" name="seed" not_null="false" type="date"/>
        <column description="申请日" is_array="true" name="ad" not_null="false" type="date"/>
        <column description="申请年" is_array="true" name="ady" not_null="false" type="date"/>
        <column description="申请年月" is_array="true" name="adym" not_null="false" type="date"/>
        <column description="申请号DOCDB" is_array="true" name="andb" not_null="false" type="item_no"/>
        <column description="申请号号码DOCDB" is_array="true" name="anndb" not_null="false" type="item_no"/>
        <column description="申请号原始" is_array="true" name="ano" not_null="false" type="item_no"/>
        <column description="申请号码原始" is_array="true" name="anno" not_null="false" type="item_no"/>
        <column description="申请号EPO类型" is_array="true" name="ane" not_null="false" type="item_no"/>
        <column description="申请号码IPPH类型" is_array="true" name="anne" not_null="false" type="item_no"/>
        <column description="PCT国际申请申请号" is_array="true" name="pctan" not_null="false" type="item_no"/>
        <column description="ipph格式的申请号" is_array="true" name="ipphan" not_null="false" type="item_no"/>
        <column description="受理局" is_array="true" name="anc" not_null="false" type="item_no"/>
        <column description="公开（公告）号DOCDB" is_array="true" name="pndb" not_null="false" type="item_no"/>
        <column description="公开（公告）号号码DOCDB" is_array="true" name="pnndb" not_null="false" type="item_no"/>
        <column description="公开（公告）号原始" is_array="true" name="pno" not_null="false" type="item_no"/>
        <column description="公开（公告）号码原始" is_array="true" name="pnno" not_null="false" type="item_no"/>
        <column description="公开（公告）号EPO" is_array="true" name="pne" not_null="false" type="item_no"/>
        <column description="公开（公告）号码EPO" is_array="true" name="pnne" not_null="false" type="item_no"/>
        <column description="PCT国际申请公开号" is_array="true" name="pctpn" not_null="false" type="item_no"/>
        <column description="ipph格式的公开号" is_array="true" name="ipphpn" not_null="false" type="item_no"/>
        <column description="公开日" is_array="true" name="pd" not_null="false" type="date"/>
        <column description="公开年" is_array="true" name="pdy" not_null="false" type="date"/>
        <column description="公开年月" is_array="true" name="pdym" not_null="false" type="date"/>
        <column description="公开类型-中文" is_array="true" name="ptzh" not_null="false" type="item_no"/>
        <column description="公开类型-代码" is_array="true" name="ptabbr" not_null="false" type="item_no"/>
        <column description="首次公开日" is_array="true" name="pdf" not_null="false" type="date"/>
        <column description="公开国别" is_array="true" name="pnc" not_null="false" type="item_no"/>
        <column description="IPC分类" is_array="true" name="ipc" not_null="false" type="tiny_text"/>
        <column description="IPC分类-部" is_array="true" name="ipcs" not_null="false" type="item_no"/>
        <column description="IPC分类-大类" is_array="true" name="ipcc" not_null="false" type="item_no"/>
        <column description="IPC分类-小类" is_array="true" name="ipcsc" not_null="false" type="item_no"/>
        <column description="IPC分类-大组" is_array="true" name="ipcg" not_null="false" type="item_no"/>
        <column description="IPC主分类" is_array="true" name="ipcm" not_null="false" type="tiny_text"/>
        <column description="IPC主分类-部" is_array="true" name="ipcms" not_null="false" type="item_no"/>
        <column description="IPC主分类-大类" is_array="true" name="ipcmc" not_null="false" type="item_no"/>
        <column description="IPC主分类-小类" is_array="true" name="ipcmsc" not_null="false" type="item_no"/>
        <column description="IPC主分类-大组" is_array="true" name="ipcmg" not_null="false" type="item_no"/>
        <column description="CPC分类" is_array="true" name="cpc" not_null="false" type="tiny_text"/>
        <column description="CPC发明点分类" is_array="true" name="cpci" not_null="false" type="item_no"/>
        <column description="C-Sets" is_array="true" name="cpccs" not_null="false" type="item_no"/>
        <column description="CPC分类-部" is_array="true" name="cpcs" not_null="false" type="item_no"/>
        <column description="CPC分类-大类" is_array="true" name="cpcc" not_null="false" type="item_no"/>
        <column description="CPC分类-小类" is_array="true" name="cpcsc" not_null="false" type="item_no"/>
        <column description="CPC分类-大组" is_array="true" name="cpcg" not_null="false" type="item_no"/>
        <column description="CPC分类-小组" is_array="true" name="cpcsg" not_null="false" type="item_no"/>
        <column description="CPC主分类" is_array="true" name="cpcm" not_null="false" type="tiny_text"/>
        <column description="CPC主分类-部" is_array="true" name="cpcms" not_null="false" type="item_no"/>
        <column description="CPC主分类-大类" is_array="true" name="cpcmc" not_null="false" type="item_no"/>
        <column description="CPC主分类-小类" is_array="true" name="cpcmsc" not_null="false" type="item_no"/>
        <column description="CPC主分类-大组" is_array="true" name="cpcmg" not_null="false" type="item_no"/>
        <column description="CPC主分类-小组" is_array="true" name="cpcmsg" not_null="false" type="item_no"/>
        <column description="LOC分类" is_array="true" name="loc" not_null="false" type="tiny_text"/>
        <column description="LOC分类-大类" is_array="true" name="locc" not_null="false" type="item_no"/>
        <column description="LOC分类-小类" is_array="true" name="locsc" not_null="false" type="item_no"/>
        <column description="美国UPC分类" is_array="true" name="uc" not_null="false" type="item_no"/>
        <column description="美国UPC主分类" is_array="true" name="ucm" not_null="false" type="item_no"/>
        <column description="日本FI分类" is_array="true" name="fi" not_null="false" type="item_no"/>
        <column description="日本FI主分类" is_array="true" name="fim" not_null="false" type="item_no"/>
        <column description="日本F-term分类" is_array="true" name="ft" not_null="false" type="item_no"/>
        <column description="国民经济行业主分类" is_array="true" name="neimc" not_null="false" type="item_no"/>
        <column description="国民经济行业分类门类" is_array="true" name="neic" not_null="false" type="item_no"/>
        <column description="国民经济行业分类大类" is_array="true" name="neicc" not_null="false" type="item_no"/>
        <column description="国民经济行业分类中类" is_array="true" name="neimic" not_null="false" type="item_no"/>
        <column description="国民经济行业分类" is_array="true" name="neisc" not_null="false" type="tiny_text"/>
        <column description="战略性新兴产业主分类" is_array="true" name="seimc" not_null="false" type="item_no"/>
        <column description="战略性新兴产业门类" is_array="true" name="seic" not_null="false" type="item_no"/>
        <column description="战略性新兴产业大类" is_array="true" name="seicc" not_null="false" type="item_no"/>
        <column description="战略性新兴产业分类" is_array="true" name="seisc" not_null="false" type="tiny_text"/>
        <column description="标准第一原始申请人" is_array="true" name="apfos" not_null="false" type="tiny_text"/>
        <column description="标准第一原始申请人中文" is_array="true" name="apfscn" not_null="false" type="tiny_text"/>
        <column description="标准第一原始申请人英文" is_array="true" name="apfsen" not_null="false" type="tiny_text"/>
        <column description="标准第一原始申请人日文" is_array="true" name="apfsjp" not_null="false" type="tiny_text"/>
        <column description="第一原始申请人" is_array="true" name="apfo" not_null="false" type="tiny_text"/>
        <column description="第一原始申请人中文" is_array="true" name="apfcn" not_null="false" type="tiny_text"/>
        <column description="第一原始申请人英文" is_array="true" name="apfen" not_null="false" type="tiny_text"/>
        <column description="第一原始申请人日文" is_array="true" name="apfjp" not_null="false" type="tiny_text"/>
        <column description="第一原始申请人类型-缩写" is_array="true" name="pafotabbr" not_null="false" type="item_no"/>
        <column description="第一原始申请人类型-中文名称" is_array="true" name="pafotcn" not_null="false" type="item_no"/>
        <column description="第一原始申请人国家" is_array="true" name="apfoco" not_null="false" type="item_no"/>
        <column description="第一原始专利权人" is_array="true" name="asfo" not_null="false" type="tiny_text"/>
        <column description="标准第一原始专利权人" is_array="true" name="asfos" not_null="false" type="tiny_text"/>
        <column description="第一原始专利权人中文" is_array="true" name="asfcn" not_null="false" type="tiny_text"/>
        <column description="第一原始专利权人英文" is_array="true" name="asfen" not_null="false" type="tiny_text"/>
        <column description="第一原始专利权人日文" is_array="true" name="asfjp" not_null="false" type="tiny_text"/>
        <column description="第一原始专利权人国家" is_array="true" name="asfoco" not_null="false" type="item_no"/>
        <column description="美国专利权人类型" is_array="true" name="patus" not_null="false" type="item_no"/>
        <column description="美国第一专利权人类型" is_array="true" name="paftus" not_null="false" type="item_no"/>
        <column description="第一当前申请人中文" is_array="true" name="apfccn" not_null="false" type="tiny_text"/>
        <column description="第一当前申请人英文" is_array="true" name="apfcen" not_null="false" type="tiny_text"/>
        <column description="第一当前申请人日文" is_array="true" name="apfcjp" not_null="false" type="tiny_text"/>
        <column description="第一当前申请人原始" is_array="true" name="apfco" not_null="false" type="tiny_text"/>
        <column description="标准第一当前申请人中文" is_array="true" name="apfcscn" not_null="false" type="tiny_text"/>
        <column description="标准第一当前申请人英文" is_array="true" name="apfcsen" not_null="false" type="tiny_text"/>
        <column description="标准第一当前申请人日文" is_array="true" name="apfcsjp" not_null="false" type="tiny_text"/>
        <column description="标准第一当前申请人原始" is_array="true" name="apfcso" not_null="false" type="tiny_text"/>
        <column description="第一当前专利权人中文" is_array="true" name="asfccn" not_null="false" type="tiny_text"/>
        <column description="第一当前专利权人英文" is_array="true" name="asfcen" not_null="false" type="tiny_text"/>
        <column description="第一当前专利权人日文" is_array="true" name="asfcjp" not_null="false" type="tiny_text"/>
        <column description="第一当前专利权人原始" is_array="true" name="asfco" not_null="false" type="tiny_text"/>
        <column description="标准第一当前专利权人中文" is_array="true" name="asfcscn" not_null="false" type="tiny_text"/>
        <column description="标准第一当前专利权人英文" is_array="true" name="asfcsen" not_null="false" type="tiny_text"/>
        <column description="标准第一当前专利权人日文" is_array="true" name="asfcsjp" not_null="false" type="tiny_text"/>
        <column description="标准第一当前专利权人原始" is_array="true" name="asfcso" not_null="false" type="tiny_text"/>
        <column description="授权时专利权人" is_array="true" name="pag" not_null="false" type="tiny_text"/>
        <column description="授权时第一专利权人" is_array="true" name="pafg" not_null="false" type="tiny_text"/>
        <column description="授权时专利权人地址" is_array="true" name="pagad" not_null="false" type="long_text"/>
        <column description="授权时专利权人国家" is_array="true" name="pagadco" not_null="false" type="item_no"/>
        <column description="授权时专利权人省" is_array="true" name="pagadp" not_null="false" type="tiny_text"/>
        <column description="授权时专利权人地市" is_array="true" name="pagadc" not_null="false" type="tiny_text"/>
        <column description="授权时专利权人区县" is_array="true" name="pagadd" not_null="false" type="long_text"/>
        <column description="授权时专利权人街道" is_array="true" name="pagads" not_null="false" type="long_text"/>
        <column description="授权时专利权人园区" is_array="true" name="pagadz" not_null="false" type="long_text"/>
        <column description="授权时专利权人经纬度" is_array="true" name="pagadll" not_null="false" type="geo_point"/>
        <column description="授权时专利权人国省代码" is_array="true" name="pagadcc" not_null="false" type="item_no"/>
        <column description="异议方" is_array="true" name="oppo" not_null="false" type="tiny_text"/>
        <column description="第一发明人中文" is_array="true" name="infcn" not_null="false" type="tiny_text"/>
        <column description="第一发明人英文" is_array="true" name="infen" not_null="false" type="tiny_text"/>
        <column description="第一发明人日文" is_array="true" name="infjp" not_null="false" type="tiny_text"/>
        <column description="第一发明人原始" is_array="true" name="info" not_null="false" type="tiny_text"/>
        <column description="代理人中文" is_array="true" name="agtcn" not_null="false" type="tiny_text"/>
        <column description="代理人英文" is_array="true" name="agten" not_null="false" type="tiny_text"/>
        <column description="代理人日文" is_array="true" name="agtjp" not_null="false" type="tiny_text"/>
        <column description="代理人原始" is_array="true" name="agto" not_null="false" type="tiny_text"/>
        <column description="代理人地址" is_array="true" name="agtad" not_null="false" type="long_text"/>
        <column description="代理人国别" is_array="true" name="agtadco" not_null="false" type="item_no"/>
        <column description="代理人城市" is_array="true" name="agtadc" not_null="false" type="tiny_text"/>
        <column description="代理人所在州" is_array="true" name="agtadp" not_null="false" type="long_text"/>
        <column description="代理机构中文" is_array="true" name="agccn" not_null="false" type="tiny_text"/>
        <column description="代理机构英文" is_array="true" name="agcen" not_null="false" type="tiny_text"/>
        <column description="代理机构日文" is_array="true" name="agcjp" not_null="false" type="tiny_text"/>
        <column description="代理机构原文" is_array="true" name="agco" not_null="false" type="tiny_text"/>
        <column description="审查员中文" is_array="true" name="excn" not_null="false" type="tiny_text"/>
        <column description="审查员英文" is_array="true" name="exen" not_null="false" type="tiny_text"/>
        <column description="审查员日文" is_array="true" name="exjp" not_null="false" type="tiny_text"/>
        <column description="审查员原始" is_array="true" name="exo" not_null="false" type="tiny_text"/>
        <column description="最早优先权国" is_array="true" name="prce" not_null="false" type="item_no"/>
        <column description="最早优先权号码原始" is_array="true" name="prneo" not_null="false" type="item_no"/>
        <column description="最早优先权号码DOCDB" is_array="true" name="prnedb" not_null="false" type="item_no"/>
        <column description="最早优先权号码EPO" is_array="true" name="prneep" not_null="false" type="item_no"/>
        <column description="最早优先权日" is_array="true" name="prde" not_null="false" type="date"/>
        <column description="最早优先权年" is_array="true" name="prdye" not_null="false" type="date"/>
        <column description="最早优先权日/申请日" is_array="true" name="prdad" not_null="false" type="date"/>
        <column description="引证专利数量" is_array="true" name="ctc" not_null="false" type="integer"/>
        <column description="引证公开号数量" is_array="true" name="ctpnc" not_null="false" type="integer"/>
        <column description="引证申请号数量" is_array="true" name="ctanc" not_null="false" type="integer"/>
        <column description="家族引证数量" is_array="true" name="fctc" not_null="false" type="integer"/>
        <column description="引证国别数量" is_array="true" name="ctcoc" not_null="false" type="integer"/>
        <column description="引证号码" is_array="true" name="ctn" not_null="false" type="item_no"/>
        <column description="被引证专利数量" is_array="true" name="ctgc" not_null="false" type="integer"/>
        <column description="被引证专利公开号数量" is_array="true" name="ctgpnc" not_null="false" type="integer"/>
        <column description="被引证专利申请号数量" is_array="true" name="ctganc" not_null="false" type="integer"/>
        <column description="家族被引证数量" is_array="true" name="fctgc" not_null="false" type="integer"/>
        <column description="简单同族被引专利公开号数量" is_array="true" name="sfctgpnc" not_null="false" type="integer"/>
        <column description="简单家族被引专利申请号数量" is_array="true" name="sfctganc" not_null="false" type="integer"/>
        <column description="简单家族被引专利国别数量" is_array="true" name="sfctgcoc" not_null="false" type="integer"/>
        <column description="被引专利简单同族数量" is_array="true" name="ctgsfc" not_null="false" type="integer"/>
        <column description="被引专利扩展同族数量" is_array="true" name="ctgefc" not_null="false" type="integer"/>
        <column description="非专利引证数量" is_array="true" name="ctnpc" not_null="false" type="integer"/>
        <column description="被引证号码" is_array="true" name="ctgn" not_null="false" type="item_no"/>
        <column description="被引证专利国别数量" is_array="true" name="ctgcoc" not_null="false" type="integer"/>
        <column description="PCT进入国家阶段日" is_array="true" name="pctnd" not_null="false" type="date"/>
        <column description="PCT指定国" is_array="true" name="pctco" not_null="false" type="item_no"/>
        <column description="简单同族数量" is_array="true" name="sfc" not_null="false" type="integer"/>
        <column description="简单同族申请号数量" is_array="true" name="sfanc" not_null="false" type="integer"/>
        <column description="简单同族公开号数量" is_array="true" name="sfpnc" not_null="false" type="integer"/>
        <column description="简单同族国家数" is_array="true" name="sfcoc" not_null="false" type="integer"/>
        <column description="简单同族代表性专利" is_array="true" name="sfrpn" not_null="false" type="item_no"/>
        <column description="扩展同族数量" is_array="true" name="efc" not_null="false" type="integer"/>
        <column description="扩展同族公开号数量" is_array="true" name="efpnc" not_null="false" type="integer"/>
        <column description="扩展同族申请号数量" is_array="true" name="efanc" not_null="false" type="integer"/>
        <column description="扩展同族国家数" is_array="true" name="efcoc" not_null="false" type="integer"/>
        <column description="DOCDB同族数量" is_array="true" name="docdbfc" not_null="false" type="integer"/>
        <column description="简单同族号" is_array="true" name="sfid" not_null="false" type="item_no"/>
        <column description="扩展同族号" is_array="true" name="efid" not_null="false" type="item_no"/>
        <column description="专利有效性-中文" is_array="true" name="slscn" not_null="false" type="item_no"/>
        <column description="专利有效性-代码" is_array="true" name="slsabbr" not_null="false" type="item_no"/>
        <column description="当前法律状态-中文" is_array="true" name="legalcn" not_null="false" type="item_no"/>
        <column description="当前法律状态-缩写" is_array="true" name="legalabbr" not_null="false" type="item_no"/>
        <column description="法律状态更新日" is_array="true" name="lsud" not_null="false" type="date"/>
        <column description="预估到期日" is_array="true" name="eed" not_null="false" type="date"/>
        <column description="预估到期年" is_array="true" name="eey" not_null="false" type="date"/>
        <column description="法律状态公告日" is_array="true" name="lsad" not_null="false" type="date"/>
        <column description="专利寿命" is_array="true" name="pl" not_null="false" type="integer"/>
        <column description="独立权利要求数量" is_array="true" name="iclmc" not_null="false" type="integer"/>
        <column description="从属权利要求数量" is_array="true" name="dclmc" not_null="false" type="integer"/>
        <column description="首项权利要求字数" is_array="true" name="fclmolec" not_null="false" type="integer"/>
        <column description="权利要求数量" is_array="true" name="clmc" not_null="false" type="integer"/>
        <column description="申请时权利要求数量" is_array="true" name="clmac" not_null="false" type="integer"/>
        <column description="授权时权利要求数量" is_array="true" name="clmbc" not_null="false" type="integer"/>
        <column description="文献页数" is_array="true" name="docn" not_null="false" type="integer"/>
        <column description="说明书页数" is_array="true" name="desn" not_null="false" type="integer"/>
        <column description="当前申请人数量" is_array="true" name="cappc" not_null="false" type="integer"/>
        <column description="原始申请人数量" is_array="true" name="apc" not_null="false" type="integer"/>
        <column description="原始专利权人数量" is_array="true" name="asc" not_null="false" type="integer"/>
        <column description="当前专利权人数量" is_array="true" name="appzcc" not_null="false" type="integer"/>
        <column description="发明人数量" is_array="true" name="inq" not_null="false" type="integer"/>
        <column description="CPC分类数量" is_array="true" name="cpcq" not_null="false" type="integer"/>
        <column description="CPC小类数量" is_array="true" name="cpcsq" not_null="false" type="integer"/>
        <column description="IPC分类数量" is_array="true" name="ipcq" not_null="false" type="integer"/>
        <column description="IPC小类数量" is_array="true" name="ipcsq" not_null="false" type="integer"/>
        <column description="FI分类号数量" is_array="true" name="fic" not_null="false" type="integer"/>
        <column description="FT分类号数量" is_array="true" name="ftc" not_null="false" type="integer"/>
        <column description="政府利益" is_array="true" name="govi" not_null="false" type="tiny_text"/>
        <column description="PCT路径进入专利" is_array="true" name="pctpt" not_null="false" type="item_no"/>
        <column description="专利价值度" is_array="true" name="pv" not_null="false" type="integer"/>
        <column description="法律价值度" is_array="true" name="pvl" not_null="false" type="integer"/>
        <column description="经济价值度" is_array="true" name="pve" not_null="false" type="integer"/>
        <column description="技术价值度" is_array="true" name="pvt" not_null="false" type="integer"/>
        <column description="奖励等级" is_array="true" name="rl" not_null="false" type="tiny_text"/>
        <column description="奖励名称" is_array="true" name="rn" not_null="false" type="tiny_text"/>
        <column description="奖励届次" is_array="true" name="rs" not_null="false" type="tiny_text"/>
        <column description="奖励年度" is_array="true" name="ry" not_null="false" type="tiny_text"/>
        <column description="标准数据源" is_array="true" name="seps" not_null="false" type="tiny_text"/>
        <column description="标准项目" is_array="true" name="sepp" not_null="false" type="tiny_text"/>
        <column description="标准号" is_array="true" name="sepn" not_null="false" type="tiny_text"/>
        <column description="标准标题" is_array="true" name="septi" not_null="false" type="tiny_text"/>
        <column description="标准持有公司" is_array="true" name="sephc" not_null="false" type="tiny_text"/>
        <column description="所有标准专利" is_array="true" name="sep" not_null="false" type="tiny_text"/>
        <column description="无优先权专利" is_array="true" name="prem" not_null="false" type="item_no"/>
        <column description="是否海关专利" is_array="true" name="cus" not_null="false" type="item_no"/>
        <column description="备案号" is_array="true" name="recn" not_null="false" type="tiny_text"/>
        <column description="备案时间" is_array="true" name="recd" not_null="false" type="date"/>
        <column description="取消时间" is_array="true" name="cacd" not_null="false" type="date"/>
        <column description="备案状态" is_array="true" name="recs" not_null="false" type="item_no"/>
        <column description="审批意见" is_array="true" name="ac" not_null="false" type="tiny_text"/>
        <column description="事件标签" is_array="true" name="flag" not_null="false" type="item_no"/>
        <column description="FDA-CAS号" is_array="true" name="casn" not_null="false" type="item_no"/>
        <column description="FDA-中文名称" is_array="true" name="fdac" not_null="false" type="tiny_text"/>
        <column description="FDA-英文名称" is_array="true" name="fdae" not_null="false" type="tiny_text"/>
        <column description="FDA-申请号" is_array="true" name="fdaan" not_null="false" type="item_no"/>
        <column description="FDA-申请类型" is_array="true" name="fdaat" not_null="false" type="tiny_text"/>
        <column description="FDA-公司名称" is_array="true" name="fdacn" not_null="false" type="tiny_text"/>
        <column description="FDA-公司简称" is_array="true" name="fdacns" not_null="false" type="tiny_text"/>
        <column description="FDA-产品号" is_array="true" name="fdapid" not_null="false" type="item_no"/>
        <column description="FDA-商品名" is_array="true" name="fdapn" not_null="false" type="tiny_text"/>
        <column description="FDA-活性成分" is_array="true" name="fdaai" not_null="false" type="tiny_text"/>
        <column description="FDA-靶点" is_array="true" name="fdat" not_null="false" type="tiny_text"/>
        <column description="FDA-适应症" is_array="true" name="fdai" not_null="false" type="tiny_text"/>
        <column description="FDA-专利到期日" is_array="true" name="fdaped" not_null="false" type="date"/>
        <column description="FDA-PED药物专利到期日" is_array="true" name="fdapeded" not_null="false" type="date"/>
        <column description="FDA-剂型" is_array="true" name="fdadf" not_null="false" type="tiny_text"/>
        <column description="FDA-是否为物质专利" is_array="true" name="fdacp" not_null="false" type="item_no"/>
        <column description="FDA-是否为产品专利" is_array="true" name="fdapp" not_null="false" type="item_no"/>
        <column description="FDA-专利用途代码" is_array="true" name="fdauc" not_null="false" type="item_no"/>
        <column description="是否诉讼" is_array="true" name="li" not_null="false" type="item_no"/>
        <column description="许可次数" is_array="true" name="licec" not_null="false" type="integer"/>
        <column description="转让次数" is_array="true" name="assc" not_null="false" type="integer"/>
        <column description="申请权转让次数" is_array="true" name="aassc" not_null="false" type="integer"/>
        <column description="专利权转让次数" is_array="true" name="passc" not_null="false" type="integer"/>
        <column description="是否复审无效" is_array="true" name="reeinv" not_null="false" type="item_no"/>
        <column description="复审次数" is_array="true" name="reec" not_null="false" type="integer"/>
        <column description="无效次数" is_array="true" name="invc" not_null="false" type="integer"/>
        <column description="是否质押" is_array="true" name="ple" not_null="false" type="item_no"/>
        <column description="是否保全" is_array="true" name="pre" not_null="false" type="item_no"/>
        <column description="当前质权人" is_array="true" name="peec" not_null="false" type="tiny_text"/>
        <column description="[标准]当前质权人" is_array="true" name="peecs" not_null="false" type="tiny_text"/>
        <column description="质押期限" is_array="true" name="plet" not_null="false" type="integer"/>
        <column description="质押备案阶段-中文" is_array="true" name="plescn" not_null="false" type="item_no"/>
        <column description="质押备案阶段-代码" is_array="true" name="plesabbr" not_null="false" type="item_no"/>
        <column description="保全阶段-中文" is_array="true" name="prescn" not_null="false" type="item_no"/>
        <column description="保全阶段-代码" is_array="true" name="presabbr" not_null="false" type="item_no"/>
        <column description="保全次数" is_array="true" name="prec" not_null="false" type="integer"/>
        <column description="质押次数" is_array="true" name="plec" not_null="false" type="integer"/>
        <column description="关联案件申请号" is_array="true" name="rpap" not_null="false" type="item_no"/>
        <column description="关联案件类型(缩写)" is_array="true" name="rptabbr" not_null="false" type="item_no"/>
        <column description="关联案件类型(中文)" is_array="true" name="rptcn" not_null="false" type="item_no"/>
        <column description="WO国家阶段" is_array="true" name="wocos" not_null="false" type="item_no"/>
        <column description="文献代码" is_array="true" name="pnk" not_null="false" type="item_no"/>
        <column description="母案" is_array="true" name="man" not_null="false" type="item_no"/>
        <column description="分案" is_array="true" name="dan" not_null="false" type="item_no"/>
        <column description="一案双申单值" is_array="true" name="da" not_null="false" type="item_no"/>
        <column description="一案双申" is_array="true" name="das" not_null="false" type="item_no"/>
        <column description="主要著录信息" is_array="true" name="mbi" not_null="false" type="long_text"/>
        <column description="号码" is_array="true" name="nu" not_null="false" type="item_no"/>
        <column description="说明书其他中文" is_array="true" name="otherdesccn" not_null="false" type="long_text"/>
        <column description="说明书其它英文" is_array="true" name="otherdescen" not_null="false" type="long_text"/>
        <column description="说明书其它日文" is_array="true" name="otherdescjp" not_null="false" type="long_text"/>
        <column description="说明书其它原文" is_array="true" name="otherdesco" not_null="false" type="long_text"/>
        <column description="说明书" is_array="true" name="des" not_null="false" type="long_text"/>
        <column description="全文中文" is_array="true" name="tacdcn" not_null="false" type="long_text"/>
        <column description="全文英文" is_array="true" name="tacden" not_null="false" type="long_text"/>
        <column description="全文日文" is_array="true" name="tacdjp" not_null="false" type="long_text"/>
        <column description="全文语义中文" is_array="true" name="tacdcn_vec" not_null="false" type="semantic"/>
        <column description="全文语义日文" is_array="true" name="tacdjp_vec" not_null="false" type="semantic"/>
        <column description="全文语义英文" is_array="true" name="tacden_vec" not_null="false" type="semantic"/>
        <column description="图片检索" is_array="true" name="image" not_null="false" type="image"/>
        <column description="P" is_array="true" name="p" not_null="false" type="image"/>
        <column description="R" is_array="true" name="r" not_null="false" type="semantic"/>
        <column description="RAD" is_array="true" name="rad" not_null="false" type="item_no"/>
        <column description="RPD" is_array="true" name="rpd" not_null="false" type="item_no"/>
        <column description="_score" is_array="true" name="_score" not_null="false" type="item_no"/>
        <column description="专利最新命中" is_array="true" name="patentchgdates" not_null="false" type="date"/>
        <column description="法律状态变更" is_array="true" name="legaltimestamp" not_null="false" type="date"/>
        <column description="专利进入时审" is_array="true" name="reetimestamp" not_null="false" type="date"/>
        <column description="专利授权时间" is_array="true" name="legatimestamp" not_null="false" type="date"/>
        <column description="专利失效时间" is_array="true" name="legtimestamp" not_null="false" type="date"/>
        <column description="专利权转让时间" is_array="true" name="passctimestamp" not_null="false" type="date"/>
        <column description="专利权人变更时间" is_array="true" name="changetimestamp" not_null="false" type="date"/>
        <column description="复审无效信息更新时间" is_array="true" name="invctimestamp" not_null="false" type="date"/>
        <column description="质押信息更新时间" is_array="true" name="plectimestamp" not_null="false" type="date"/>
        <column description="诉讼信息更新时间" is_array="true" name="lictimestamp" not_null="false" type="date"/>
        <column description="海关备案信息更新时间" is_array="true" name="customstimestamp" not_null="false" type="date"/>
        <column description="许可信息更新时间" is_array="true" name="licectimestamp" not_null="false" type="date"/>
        <column description="说明书变化更新时间" is_array="true" name="desntimestamp" not_null="false" type="date"/>
        <column description="入库时间" is_array="true" name="instime" not_null="false" type="date"/>
        <column description="申请号合并使用" is_array="true" name="anm" not_null="false" type="item_no"/>
        <column description="是否许可" is_array="true" name="lice" not_null="false" type="item_no"/>
        <column description="当前许可备案阶段" is_array="true" name="licesc" not_null="false" type="item_no"/>
        <column description="当前被许可人" is_array="true" name="liceeec" not_null="false" type="tiny_text"/>
        <column description="工商别名" is_array="true" name="apfn" not_null="false" type="tiny_text"/>
        <column description="工商英文名" is_array="true" name="apne" not_null="false" type="tiny_text"/>
        <column description="工商注册地址" is_array="true" name="aprad" not_null="false" type="long_text"/>
        <column description="工商注册地址省" is_array="true" name="apradp" not_null="false" type="item_no"/>
        <column description="工商注册地址地市" is_array="true" name="apradc" not_null="false" type="item_no"/>
        <column description="工商注册地址区县" is_array="true" name="apradd" not_null="false" type="long_text"/>
        <column description="工商注册地址街道" is_array="true" name="aprads" not_null="false" type="long_text"/>
        <column description="工商注册地址园区" is_array="true" name="apradz" not_null="false" type="long_text"/>
        <column description="工商地址经纬度" is_array="true" name="apradll" not_null="false" type="geo_point"/>
        <column description="联系地址经纬度" is_array="true" name="apcadll" not_null="false" type="geo_point"/>
        <column description="工商公司类型" is_array="true" name="apct" not_null="false" type="tiny_text"/>
        <column description="工商成立日期" is_array="true" name="apcet" not_null="false" type="date"/>
        <column description="工商统一社会信用代码" is_array="true" name="apuscc" not_null="false" type="item_no"/>
        <column description="工商注册号" is_array="true" name="aprn" not_null="false" type="item_no"/>
        <column description="工商企业状态" is_array="true" name="apcs" not_null="false" type="item_no"/>
        <column description="工商上市代码" is_array="true" name="apclc" not_null="false" type="item_no"/>
        <column description="企业所属行业" is_array="true" name="apci" not_null="false" type="tiny_text"/>
        <column description="企业标签" is_array="true" name="apcl" not_null="false" type="tiny_text"/>
        <column description="决定类型-中文" is_array="true" name="reeinvtcn" not_null="false" type="item_no"/>
        <column description="决定类型-缩写" is_array="true" name="reeinvtabbr" not_null="false" type="item_no"/>
        <column description="诉讼次数" is_array="true" name="lic" not_null="false" type="integer"/>
        <column description="X引证文献" is_array="true" name="ctx" not_null="false" type="tiny_text"/>
        <column description="Y引证文献" is_array="true" name="cty" not_null="false" type="tiny_text"/>
        <column description="A引证文献" is_array="true" name="cta" not_null="false" type="tiny_text"/>
        <column description="家族引证申请人" is_array="true" name="fctap" not_null="false" type="tiny_text"/>
        <column description="家族引证" is_array="true" name="fct" not_null="false" type="item_no"/>
        <column description="家族被引证" is_array="true" name="fctg" not_null="false" type="item_no"/>
        <column description="家族被引证申请人" is_array="true" name="fctgap" not_null="false" type="tiny_text"/>
        <column description="优先权国家数量" is_array="true" name="prcoc" not_null="false" type="integer"/>
        <column description="是否转让" is_array="true" name="ass" not_null="false" type="item_no"/>
        <column description="转让后当前地址" is_array="true" name="assaadrc" not_null="false" type="long_text"/>
    </columns>
    <fields>
        <field alias="专利编码" analyzer="null" column_name="pid" description="专利编码" name="pid">
            <multi_field analyzer="null" column_name="raw" description="专利编码" name="pid_raw"/>
        </field>
        <field alias="标题中文" analyzer="smart_analyzer" column_name="ticn" description="标题中文" name="ticn"/>
        <field alias="标题英文" analyzer="smart_analyzer" column_name="tien" description="标题英文" name="tien"/>
        <field alias="标题日文" analyzer="smart_analyzer" column_name="tijp" description="标题日文" name="tijp"/>
        <field alias="标题原文" analyzer="smart_analyzer" column_name="tio" description="标题原文" name="tio"/>
        <field alias="摘要中文" analyzer="smart_analyzer" column_name="abcn" description="摘要中文" name="abcn"/>
        <field alias="摘要英文" analyzer="smart_analyzer" column_name="aben" description="摘要英文" name="aben"/>
        <field alias="摘要日文" analyzer="smart_analyzer" column_name="abjp" description="摘要日文" name="abjp"/>
        <field alias="摘要原文" analyzer="smart_analyzer" column_name="abot" description="摘要原文" name="abot"/>
        <field alias="权利要求类型英文" analyzer="null" column_name="clmten" description="权利要求类型英文" name="clmten">
            <multi_field analyzer="null" column_name="raw" description="权利要求类型英文" name="clmten_raw"/>
        </field>
        <field alias="权利要求类型中文" analyzer="null" column_name="clmtcn" description="权利要求类型中文" name="clmtcn">
            <multi_field analyzer="null" column_name="raw" description="权利要求类型中文" name="clmtcn_raw"/>
        </field>
        <field alias="权利要求类型代码" analyzer="null" column_name="clmtabbr" description="权利要求类型代码" name="clmtabbr">
            <multi_field analyzer="null" column_name="raw" description="权利要求类型代码" name="clmtabbr_raw"/>
        </field>
        <field alias="首项权利要求中文" analyzer="smart_analyzer" column_name="fclmcn" description="首项权利要求中文" name="fclmcn"/>
        <field alias="首项权利要求英文" analyzer="smart_analyzer" column_name="fclmen" description="首项权利要求英文" name="fclmen"/>
        <field alias="首项权利要求日文" analyzer="smart_analyzer" column_name="fclmjp" description="首项权利要求日文" name="fclmjp"/>
        <field alias="首项权利要求原文" analyzer="smart_analyzer" column_name="fclmo" description="首项权利要求原文" name="fclmo"/>
        <field alias="独立权利要求中文" analyzer="smart_analyzer" column_name="iclmcn" description="独立权利要求中文" name="iclmcn"/>
        <field alias="独立权利要求英文" analyzer="smart_analyzer" column_name="iclmen" description="独立权利要求英文" name="iclmen"/>
        <field alias="独立权利要求日文" analyzer="smart_analyzer" column_name="iclmjp" description="独立权利要求日文" name="iclmjp"/>
        <field alias="独立权利要求原文" analyzer="smart_analyzer" column_name="iclmo" description="独立权利要求原文" name="iclmo"/>
        <field alias="从属权利要求中文" analyzer="smart_analyzer" column_name="dclmcn" description="从属权利要求中文" name="dclmcn"/>
        <field alias="从属权利要求英文" analyzer="smart_analyzer" column_name="dclmen" description="从属权利要求英文" name="dclmen"/>
        <field alias="从属权利要求日文" analyzer="smart_analyzer" column_name="dclmjp" description="从属权利要求日文" name="dclmjp"/>
        <field alias="从属权利要求原文" analyzer="smart_analyzer" column_name="dclmo" description="从属权利要求原文" name="dclmo"/>
        <field alias="技术领域中文" analyzer="smart_analyzer" column_name="tfcn" description="技术领域中文" name="tfcn"/>
        <field alias="技术领域英文" analyzer="smart_analyzer" column_name="tfen" description="技术领域英文" name="tfen"/>
        <field alias="技术领域日文" analyzer="smart_analyzer" column_name="tfjp" description="技术领域日文" name="tfjp"/>
        <field alias="技术领域原文" analyzer="smart_analyzer" column_name="tfo" description="技术领域原文" name="tfo"/>
        <field alias="背景技术中文" analyzer="smart_analyzer" column_name="tbcn" description="背景技术中文" name="tbcn"/>
        <field alias="背景技术英文" analyzer="smart_analyzer" column_name="tben" description="背景技术英文" name="tben"/>
        <field alias="背景技术日文" analyzer="smart_analyzer" column_name="tbjp" description="背景技术日文" name="tbjp"/>
        <field alias="背景技术原文" analyzer="smart_analyzer" column_name="tbo" description="背景技术原文" name="tbo"/>
        <field alias="发明内容中文" analyzer="smart_analyzer" column_name="iscn" description="发明内容中文" name="iscn"/>
        <field alias="发明内容英文" analyzer="smart_analyzer" column_name="isen" description="发明内容英文" name="isen"/>
        <field alias="发明内容日文" analyzer="smart_analyzer" column_name="isjp" description="发明内容日文" name="isjp"/>
        <field alias="发明内容原文" analyzer="smart_analyzer" column_name="iso" description="发明内容原文" name="iso"/>
        <field alias="附图说明中文" analyzer="smart_analyzer" column_name="ddcn" description="附图说明中文" name="ddcn"/>
        <field alias="附图说明英文" analyzer="smart_analyzer" column_name="dden" description="附图说明英文" name="dden"/>
        <field alias="附图说明日文" analyzer="smart_analyzer" column_name="ddjp" description="附图说明日文" name="ddjp"/>
        <field alias="附图说明原文" analyzer="smart_analyzer" column_name="ddo" description="附图说明原文" name="ddo"/>
        <field alias="具体实施方式中文" analyzer="smart_analyzer" column_name="secn" description="具体实施方式中文" name="secn"/>
        <field alias="具体实施方式英文" analyzer="smart_analyzer" column_name="seen" description="具体实施方式英文" name="seen"/>
        <field alias="具体实施方式日文" analyzer="smart_analyzer" column_name="sejp" description="具体实施方式日文" name="sejp"/>
        <field alias="具体实施方式原文" analyzer="smart_analyzer" column_name="seo" description="具体实施方式原文" name="seo"/>
        <field alias="技术功效句" analyzer="smart_analyzer" column_name="tesc" description="技术功效句" name="tesc"/>
        <field alias="技术功效短语" analyzer="smart_analyzer" column_name="tepc" description="技术功效短语" name="tepc">
            <multi_field analyzer="null" column_name="raw" description="技术功效短语" name="tepc_raw"/>
        </field>
        <field alias="技术功效词" analyzer="smart_analyzer" column_name="tew" description="技术功效词" name="tew"/>
        <field alias="标题关键字中文" analyzer="smart_analyzer" column_name="titlewordzh" description="标题关键字中文" name="titlewordzh"/>
        <field alias="标题关键字英文" analyzer="smart_analyzer" column_name="titleworden" description="标题关键字英文" name="titleworden"/>
        <field alias="摘要关键字中文" analyzer="smart_analyzer" column_name="abstractwordzh" description="摘要关键字中文" name="abstractwordzh"/>
        <field alias="摘要关键字英文" analyzer="smart_analyzer" column_name="abstractworden" description="摘要关键字英文" name="abstractworden"/>
        <field alias="权利要求关键字中文" analyzer="smart_analyzer" column_name="claimswordzh" description="权利要求关键字中文" name="claimswordzh"/>
        <field alias="权利要求关键字英文" analyzer="smart_analyzer" column_name="claimsworden" description="权利要求关键字英文" name="claimsworden"/>
        <field alias="说明书关键字中文" analyzer="smart_analyzer" column_name="descriptionwordzh" description="说明书关键字中文" name="descriptionwordzh"/>
        <field alias="说明书关键字英文" analyzer="smart_analyzer" column_name="descriptionworden" description="说明书关键字英文" name="descriptionworden"/>
        <field alias="申请语言" analyzer="null" column_name="apl" description="申请语言" name="apl">
            <multi_field analyzer="null" column_name="raw" description="申请语言" name="apl_raw"/>
        </field>
        <field alias="专利类型(DOCDB)" analyzer="null" column_name="docdbpt" description="专利类型(DOCDB)" name="docdbpt"/>
        <field alias="专利类型(中国)" analyzer="null" column_name="ptcn" description="专利类型(中国)" name="ptcn"/>
        <field alias="专利类型(美国)" analyzer="null" column_name="ptus" description="专利类型(美国)" name="ptus"/>
        <field alias="专利类型-简单" analyzer="null" column_name="pts" description="专利类型-简单" name="pts"/>
        <field alias="授权日" analyzer="null" column_name="gd" description="授权日" name="gd"/>
        <field alias="提出实审时长" analyzer="null" column_name="pexp" description="提出实审时长" name="pexp"/>
        <field alias="审查时长" analyzer="null" column_name="exp" description="审查时长" name="exp"/>
        <field alias="实质审查生效日" analyzer="null" column_name="seed" description="实质审查生效日" name="seed"/>
        <field alias="申请日" analyzer="null" column_name="ad" description="申请日" name="ad"/>
        <field alias="申请年" analyzer="null" column_name="ady" description="申请年" name="ady"/>
        <field alias="申请年月" analyzer="null" column_name="adym" description="申请年月" name="adym"/>
        <field alias="申请号DOCDB" analyzer="null" column_name="andb" description="申请号DOCDB" name="andb">
            <multi_field analyzer="null" column_name="raw" description="申请号DOCDB" name="andb_raw"/>
        </field>
        <field alias="申请号号码DOCDB" analyzer="null" column_name="anndb" description="申请号号码DOCDB" name="anndb">
            <multi_field analyzer="null" column_name="raw" description="申请号号码DOCDB" name="anndb_raw"/>
        </field>
        <field alias="申请号原始" analyzer="null" column_name="ano" description="申请号原始" name="ano">
            <multi_field analyzer="null" column_name="raw" description="申请号原始" name="ano_raw"/>
        </field>
        <field alias="申请号码原始" analyzer="null" column_name="anno" description="申请号码原始" name="anno">
            <multi_field analyzer="null" column_name="raw" description="申请号码原始" name="anno_raw"/>
        </field>
        <field alias="申请号EPO类型" analyzer="null" column_name="ane" description="申请号EPO类型" name="ane">
            <multi_field analyzer="null" column_name="raw" description="申请号EPO类型" name="ane_raw"/>
        </field>
        <field alias="申请号码IPPH类型" analyzer="null" column_name="anne" description="申请号码IPPH类型" name="anne">
            <multi_field analyzer="null" column_name="raw" description="申请号码IPPH类型" name="anne_raw"/>
        </field>
        <field alias="PCT国际申请申请号" analyzer="null" column_name="pctan" description="PCT国际申请申请号" name="pctan">
            <multi_field analyzer="null" column_name="raw" description="PCT国际申请申请号" name="pctan_raw"/>
        </field>
        <field alias="ipph格式的申请号" analyzer="null" column_name="ipphan" description="ipph格式的申请号" name="ipphan">
            <multi_field analyzer="null" column_name="raw" description="ipph格式的申请号" name="ipphan_raw"/>
        </field>
        <field alias="受理局" analyzer="null" column_name="anc" description="受理局" name="anc"/>
        <field alias="公开（公告）号DOCDB" analyzer="null" column_name="pndb" description="公开（公告）号DOCDB" name="pndb">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号DOCDB" name="pndb_raw"/>
        </field>
        <field alias="公开（公告）号号码DOCDB" analyzer="null" column_name="pnndb" description="公开（公告）号号码DOCDB" name="pnndb">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号号码DOCDB" name="pnndb_raw"/>
        </field>
        <field alias="公开（公告）号原始" analyzer="null" column_name="pno" description="公开（公告）号原始" name="pno">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号原始" name="pno_raw"/>
        </field>
        <field alias="公开（公告）号码原始" analyzer="null" column_name="pnno" description="公开（公告）号码原始" name="pnno">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号码原始" name="pnno_raw"/>
        </field>
        <field alias="公开（公告）号EPO" analyzer="null" column_name="pne" description="公开（公告）号EPO" name="pne">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号EPO" name="pne_raw"/>
        </field>
        <field alias="公开（公告）号码EPO" analyzer="null" column_name="pnne" description="公开（公告）号码EPO" name="pnne">
            <multi_field analyzer="null" column_name="raw" description="公开（公告）号码EPO" name="pnne_raw"/>
        </field>
        <field alias="PCT国际申请公开号" analyzer="null" column_name="pctpn" description="PCT国际申请公开号" name="pctpn">
            <multi_field analyzer="null" column_name="raw" description="PCT国际申请公开号" name="pctpn_raw"/>
        </field>
        <field alias="ipph格式的公开号" analyzer="null" column_name="ipphpn" description="ipph格式的公开号" name="ipphpn">
            <multi_field analyzer="null" column_name="raw" description="ipph格式的公开号" name="ipphpn_raw"/>
        </field>
        <field alias="公开日" analyzer="null" column_name="pd" description="公开日" name="pd"/>
        <field alias="公开年" analyzer="null" column_name="pdy" description="公开年" name="pdy"/>
        <field alias="公开年月" analyzer="null" column_name="pdym" description="公开年月" name="pdym"/>
        <field alias="公开类型-中文" analyzer="null" column_name="ptzh" description="公开类型-中文" name="ptzh">
            <multi_field analyzer="null" column_name="raw" description="公开类型-中文" name="ptzh_raw"/>
        </field>
        <field alias="公开类型-代码" analyzer="null" column_name="ptabbr" description="公开类型-代码" name="ptabbr">
            <multi_field analyzer="null" column_name="raw" description="公开类型-代码" name="ptabbr_raw"/>
        </field>
        <field alias="首次公开日" analyzer="null" column_name="pdf" description="首次公开日" name="pdf"/>
        <field alias="公开国别" analyzer="null" column_name="pnc" description="公开国别" name="pnc"/>
        <field alias="IPC分类" analyzer="2gram_analyzer" column_name="ipc" description="IPC分类" name="ipc">
            <multi_field analyzer="null" column_name="raw" description="IPC分类" name="ipc_raw"/>
        </field>
        <field alias="IPC分类-部" analyzer="2gram_analyzer" column_name="ipcs" description="IPC分类-部" name="ipcs"/>
        <field alias="IPC分类-大类" analyzer="2gram_analyzer" column_name="ipcc" description="IPC分类-大类" name="ipcc"/>
        <field alias="IPC分类-小类" analyzer="2gram_analyzer" column_name="ipcsc" description="IPC分类-小类" name="ipcsc"/>
        <field alias="IPC分类-大组" analyzer="2gram_analyzer" column_name="ipcg" description="IPC分类-大组" name="ipcg"/>
        <field alias="IPC主分类" analyzer="2gram_analyzer" column_name="ipcm" description="IPC主分类" name="ipcm">
            <multi_field analyzer="null" column_name="raw" description="IPC主分类" name="ipcm_raw"/>
        </field>
        <field alias="IPC主分类-部" analyzer="2gram_analyzer" column_name="ipcms" description="IPC主分类-部" name="ipcms"/>
        <field alias="IPC主分类-大类" analyzer="2gram_analyzer" column_name="ipcmc" description="IPC主分类-大类" name="ipcmc"/>
        <field alias="IPC主分类-小类" analyzer="2gram_analyzer" column_name="ipcmsc" description="IPC主分类-小类" name="ipcmsc"/>
        <field alias="IPC主分类-大组" analyzer="2gram_analyzer" column_name="ipcmg" description="IPC主分类-大组" name="ipcmg"/>
        <field alias="CPC分类" analyzer="2gram_analyzer" column_name="cpc" description="CPC分类" name="cpc">
            <multi_field analyzer="null" column_name="raw" description="CPC分类" name="cpc_raw"/>
        </field>
        <field alias="CPC发明点分类" analyzer="null" column_name="cpci" description="CPC发明点分类" name="cpci">
            <multi_field analyzer="null" column_name="raw" description="CPC发明点分类" name="cpci_raw"/>
        </field>
        <field alias="C-Sets" analyzer="null" column_name="cpccs" description="C-Sets" name="cpccs">
            <multi_field analyzer="null" column_name="raw" description="C-Sets" name="cpccs_raw"/>
        </field>
        <field alias="CPC分类-部" analyzer="2gram_analyzer" column_name="cpcs" description="CPC分类-部" name="cpcs"/>
        <field alias="CPC分类-大类" analyzer="2gram_analyzer" column_name="cpcc" description="CPC分类-大类" name="cpcc"/>
        <field alias="CPC分类-小类" analyzer="2gram_analyzer" column_name="cpcsc" description="CPC分类-小类" name="cpcsc"/>
        <field alias="CPC分类-大组" analyzer="2gram_analyzer" column_name="cpcg" description="CPC分类-大组" name="cpcg"/>
        <field alias="CPC分类-小组" analyzer="2gram_analyzer" column_name="cpcsg" description="CPC分类-小组" name="cpcsg"/>
        <field alias="CPC主分类" analyzer="2gram_analyzer" column_name="cpcm" description="CPC主分类" name="cpcm">
            <multi_field analyzer="null" column_name="raw" description="CPC主分类" name="cpcm_raw"/>
        </field>
        <field alias="CPC主分类-部" analyzer="2gram_analyzer" column_name="cpcms" description="CPC主分类-部" name="cpcms"/>
        <field alias="CPC主分类-大类" analyzer="2gram_analyzer" column_name="cpcmc" description="CPC主分类-大类" name="cpcmc"/>
        <field alias="CPC主分类-小类" analyzer="2gram_analyzer" column_name="cpcmsc" description="CPC主分类-小类" name="cpcmsc"/>
        <field alias="CPC主分类-大组" analyzer="2gram_analyzer" column_name="cpcmg" description="CPC主分类-大组" name="cpcmg"/>
        <field alias="CPC主分类-小组" analyzer="2gram_analyzer" column_name="cpcmsg" description="CPC主分类-小组" name="cpcmsg"/>
        <field alias="LOC分类" analyzer="2gram_analyzer" column_name="loc" description="LOC分类" name="loc">
            <multi_field analyzer="null" column_name="raw" description="LOC分类" name="loc_raw"/>
        </field>
        <field alias="LOC分类-大类" analyzer="2gram_analyzer" column_name="locc" description="LOC分类-大类" name="locc"/>
        <field alias="LOC分类-小类" analyzer="null" column_name="locsc" description="LOC分类-小类" name="locsc">
            <multi_field analyzer="null" column_name="raw" description="LOC分类-小类" name="locsc_raw"/>
        </field>
        <field alias="美国UPC分类" analyzer="null" column_name="uc" description="美国UPC分类" name="uc"/>
        <field alias="美国UPC主分类" analyzer="null" column_name="ucm" description="美国UPC主分类" name="ucm">
            <multi_field analyzer="null" column_name="raw" description="美国UPC主分类" name="ucm_raw"/>
        </field>
        <field alias="日本FI分类" analyzer="null" column_name="fi" description="日本FI分类" name="fi"/>
        <field alias="日本FI主分类" analyzer="null" column_name="fim" description="日本FI主分类" name="fim">
            <multi_field analyzer="null" column_name="raw" description="日本FI主分类" name="fim_raw"/>
        </field>
        <field alias="日本F-term分类" analyzer="null" column_name="ft" description="日本F-term分类" name="ft"/>
        <field alias="国民经济行业主分类" analyzer="null" column_name="neimc" description="国民经济行业主分类" name="neimc"/>
        <field alias="国民经济行业分类门类" analyzer="null" column_name="neic" description="国民经济行业分类门类" name="neic"/>
        <field alias="国民经济行业分类大类" analyzer="null" column_name="neicc" description="国民经济行业分类大类" name="neicc"/>
        <field alias="国民经济行业分类中类" analyzer="null" column_name="neimic" description="国民经济行业分类中类" name="neimic"/>
        <field alias="国民经济行业分类" analyzer="2gram_analyzer" column_name="neisc" description="国民经济行业分类" name="neisc">
            <multi_field analyzer="null" column_name="raw" description="国民经济行业分类" name="neisc_raw"/>
        </field>
        <field alias="战略性新兴产业主分类" analyzer="null" column_name="seimc" description="战略性新兴产业主分类" name="seimc">
            <multi_field analyzer="null" column_name="raw" description="战略性新兴产业主分类" name="seimc_raw"/>
        </field>
        <field alias="战略性新兴产业门类" analyzer="null" column_name="seic" description="战略性新兴产业门类" name="seic"/>
        <field alias="战略性新兴产业大类" analyzer="null" column_name="seicc" description="战略性新兴产业大类" name="seicc"/>
        <field alias="战略性新兴产业分类" analyzer="2gram_analyzer" column_name="seisc" description="战略性新兴产业分类" name="seisc">
            <multi_field analyzer="null" column_name="raw" description="战略性新兴产业分类" name="seisc_raw"/>
        </field>
        <field alias="标准第一原始申请人" analyzer="smart_analyzer" column_name="apfos" description="标准第一原始申请人" name="apfos">
            <multi_field analyzer="null" column_name="raw" description="标准第一原始申请人" name="apfos_raw"/>
        </field>
        <field alias="标准第一原始申请人中文" analyzer="smart_analyzer" column_name="apfscn" description="标准第一原始申请人中文" name="apfscn">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人" name="apfscn_raw"/>
        </field>
        <field alias="标准第一原始申请人英文" analyzer="smart_analyzer" column_name="apfsen" description="标准第一原始申请人英文" name="apfsen">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人" name="apfsen_raw"/>
        </field>
        <field alias="标准第一原始申请人日文" analyzer="smart_analyzer" column_name="apfsjp" description="标准第一原始申请人日文" name="apfsjp">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人" name="apfsjp_raw"/>
        </field>
        <field alias="第一原始申请人" analyzer="smart_analyzer" column_name="apfo" description="第一原始申请人" name="apfo">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人" name="apfo_raw"/>
        </field>
        <field alias="第一原始申请人中文" analyzer="smart_analyzer" column_name="apfcn" description="第一原始申请人中文" name="apfcn">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人中文" name="apfcn_raw"/>
        </field>
        <field alias="第一原始申请人英文" analyzer="smart_analyzer" column_name="apfen" description="第一原始申请人英文" name="apfen">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人英文" name="apfen_raw"/>
        </field>
        <field alias="第一原始申请人日文" analyzer="smart_analyzer" column_name="apfjp" description="第一原始申请人日文" name="apfjp">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人日文" name="apfjp_raw"/>
        </field>
        <field alias="第一原始申请人类型-缩写" analyzer="null" column_name="pafotabbr" description="第一原始申请人类型-缩写" name="pafotabbr"/>
        <field alias="第一原始申请人类型-中文名称" analyzer="null" column_name="pafotcn" description="第一原始申请人类型-中文名称" name="pafotcn">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人类型-中文名称" name="pafotcn_raw"/>
        </field>
        <field alias="第一原始申请人国家" analyzer="null" column_name="apfoco" description="第一原始申请人国家" name="apfoco">
            <multi_field analyzer="null" column_name="raw" description="第一原始申请人国家" name="apfoco_raw"/>
        </field>
        <field alias="第一原始专利权人" analyzer="smart_analyzer" column_name="asfo" description="第一原始专利权人" name="asfo">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人" name="asfo_raw"/>
        </field>
        <field alias="标准第一原始专利权人" analyzer="smart_analyzer" column_name="asfos" description="标准第一原始专利权人" name="asfos">
            <multi_field analyzer="null" column_name="raw" description="标准第一原始专利权人" name="asfos_raw"/>
        </field>
        <field alias="第一原始专利权人中文" analyzer="smart_analyzer" column_name="asfcn" description="第一原始专利权人中文" name="asfcn">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人中文" name="asfcn_raw"/>
        </field>
        <field alias="第一原始专利权人英文" analyzer="smart_analyzer" column_name="asfen" description="第一原始专利权人英文" name="asfen">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人英文" name="asfen_raw"/>
        </field>
        <field alias="第一原始专利权人日文" analyzer="smart_analyzer" column_name="asfjp" description="第一原始专利权人日文" name="asfjp">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人日文" name="asfjp_raw"/>
        </field>
        <field alias="第一原始专利权人国家" analyzer="null" column_name="asfoco" description="第一原始专利权人国家" name="asfoco">
            <multi_field analyzer="null" column_name="raw" description="第一原始专利权人国家" name="asfoco_raw"/>
        </field>
        <field alias="美国专利权人类型" analyzer="null" column_name="patus" description="美国专利权人类型" name="patus">
            <multi_field analyzer="null" column_name="raw" description="美国专利权人类型" name="patus_raw"/>
        </field>
        <field alias="美国第一专利权人类型" analyzer="null" column_name="paftus" description="美国第一专利权人类型" name="paftus">
            <multi_field analyzer="null" column_name="raw" description="美国第一专利权人类型" name="paftus_raw"/>
        </field>
        <field alias="第一当前申请人中文" analyzer="smart_analyzer" column_name="apfccn" description="第一当前申请人中文" name="apfccn">
            <multi_field analyzer="null" column_name="raw" description="第一当前申请人中文" name="apfccn_raw"/>
        </field>
        <field alias="第一当前申请人英文" analyzer="smart_analyzer" column_name="apfcen" description="第一当前申请人英文" name="apfcen">
            <multi_field analyzer="null" column_name="raw" description="第一当前申请人英文" name="apfcen_raw"/>
        </field>
        <field alias="第一当前申请人日文" analyzer="smart_analyzer" column_name="apfcjp" description="第一当前申请人日文" name="apfcjp">
            <multi_field analyzer="null" column_name="raw" description="第一当前申请人日文" name="apfcjp_raw"/>
        </field>
        <field alias="第一当前申请人原始" analyzer="smart_analyzer" column_name="apfco" description="第一当前申请人原始" name="apfco">
            <multi_field analyzer="null" column_name="raw" description="第一当前申请人原始" name="apfco_raw"/>
        </field>
        <field alias="标准第一当前申请人中文" analyzer="smart_analyzer" column_name="apfcscn" description="标准第一当前申请人中文" name="apfcscn">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前申请人中文" name="apfcscn_raw"/>
        </field>
        <field alias="标准第一当前申请人英文" analyzer="smart_analyzer" column_name="apfcsen" description="标准第一当前申请人英文" name="apfcsen">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前申请人英文" name="apfcsen_raw"/>
        </field>
        <field alias="标准第一当前申请人日文" analyzer="smart_analyzer" column_name="apfcsjp" description="标准第一当前申请人日文" name="apfcsjp">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前申请人日文" name="apfcsjp_raw"/>
        </field>
        <field alias="标准第一当前申请人原始" analyzer="smart_analyzer" column_name="apfcso" description="标准第一当前申请人原始" name="apfcso">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前申请人原始" name="apfcso_raw"/>
        </field>
        <field alias="第一当前专利权人中文" analyzer="smart_analyzer" column_name="asfccn" description="第一当前专利权人中文" name="asfccn">
            <multi_field analyzer="null" column_name="raw" description="第一当前专利权人中文" name="asfccn_raw"/>
        </field>
        <field alias="第一当前申请(专利权)人英文" analyzer="smart_analyzer" column_name="asfcen" description="第一当前专利权人英文" name="asfcen">
            <multi_field analyzer="null" column_name="raw" description="第一当前专利权人英文" name="asfcen_raw"/>
        </field>
        <field alias="第一当前专利权人日文" analyzer="smart_analyzer" column_name="asfcjp" description="第一当前专利权人日文" name="asfcjp">
            <multi_field analyzer="null" column_name="raw" description="第一当前专利权人日文" name="asfcjp_raw"/>
        </field>
        <field alias="第一当前专利权人原始" analyzer="smart_analyzer" column_name="asfco" description="第一当前专利权人原始" name="asfco">
            <multi_field analyzer="null" column_name="raw" description="第一当前专利权人原始" name="asfco_raw"/>
        </field>
        <field alias="标准第一当前专利权人中文" analyzer="smart_analyzer" column_name="asfcscn" description="标准第一当前专利权人中文" name="asfcscn">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前专利权人中文" name="asfcscn_raw"/>
        </field>
        <field alias="标准第一当前专利权人英文" analyzer="smart_analyzer" column_name="asfcsen" description="标准第一当前专利权人英文" name="asfcsen">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前专利权人英文" name="asfcsen_raw"/>
        </field>
        <field alias="标准第一当前专利权人日文" analyzer="smart_analyzer" column_name="asfcsjp" description="标准第一当前专利权人日文" name="asfcsjp">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前专利权人日文" name="asfcsjp_raw"/>
        </field>
        <field alias="标准第一当前专利权人原始" analyzer="smart_analyzer" column_name="asfcso" description="标准第一当前专利权人原始" name="asfcso">
            <multi_field analyzer="null" column_name="raw" description="标准第一当前专利权人原始" name="asfcso_raw"/>
        </field>
        <field alias="授权时专利权人" analyzer="smart_analyzer" column_name="pag" description="授权时专利权人" name="pag">
            <multi_field analyzer="null" column_name="raw" description="授权时专利权人" name="pag_raw"/>
        </field>
        <field alias="授权时第一专利权人" analyzer="smart_analyzer" column_name="pafg" description="授权时第一专利权人" name="pafg">
            <multi_field analyzer="null" column_name="raw" description="授权时第一专利权人" name="pafg_raw"/>
        </field>
        <field alias="授权时专利权人地址" analyzer="smart_analyzer" column_name="pagad" description="授权时专利权人地址" name="pagad"/>
        <field alias="授权时专利权人国家" analyzer="null" column_name="pagadco" description="授权时专利权人国家" name="pagadco">
            <multi_field analyzer="null" column_name="raw" description="授权时专利权人国家" name="pagadco_raw"/>
        </field>
        <field alias="授权时专利权人省" analyzer="null" column_name="pagadp" description="授权时专利权人省" name="pagadp">
            <multi_field analyzer="null" column_name="raw" description="授权时专利权人省" name="pagadp_raw"/>
        </field>
        <field alias="授权时专利权人地市" analyzer="null" column_name="pagadc" description="授权时专利权人地市" name="pagadc">
            <multi_field analyzer="null" column_name="raw" description="授权时专利权人地市" name="pagadc_raw"/>
        </field>
        <field alias="授权时专利权人区县" analyzer="smart_analyzer" column_name="pagadd" description="授权时专利权人区县" name="pagadd"/>
        <field alias="授权时专利权人街道" analyzer="smart_analyzer" column_name="pagads" description="授权时专利权人街道" name="pagads"/>
        <field alias="授权时专利权人园区" analyzer="smart_analyzer" column_name="pagadz" description="授权时专利权人园区" name="pagadz"/>
        <field alias="授权时专利权人经纬度" analyzer="null" column_name="pagadll" description="授权时专利权人经纬度" name="pagadll"/>
        <field alias="授权时专利权人国省代码" analyzer="null" column_name="pagadcc" description="授权时专利权人国省代码" name="pagadcc">
            <multi_field analyzer="null" column_name="raw" description="授权时专利权人国省代码" name="pagadcc_raw"/>
        </field>
        <field alias="异议方" analyzer="null" column_name="oppo" description="异议方" name="oppo"/>
        <field alias="第一发明人中文" analyzer="smart_analyzer" column_name="infcn" description="第一发明人中文" name="infcn">
            <multi_field analyzer="null" column_name="raw" description="第一发明人中文" name="infcn_raw"/>
        </field>
        <field alias="第一发明人英文" analyzer="smart_analyzer" column_name="infen" description="第一发明人英文" name="infen">
            <multi_field analyzer="null" column_name="raw" description="第一发明人英文" name="infen_raw"/>
        </field>
        <field alias="第一发明人日文" analyzer="smart_analyzer" column_name="infjp" description="第一发明人日文" name="infjp">
            <multi_field analyzer="null" column_name="raw" description="第一发明人日文" name="infjp_raw"/>
        </field>
        <field alias="第一发明人原始" analyzer="smart_analyzer" column_name="info" description="第一发明人原始" name="info">
            <multi_field analyzer="null" column_name="raw" description="第一发明人原始" name="info_raw"/>
        </field>
        <field alias="代理人中文" analyzer="smart_analyzer" column_name="agtcn" description="代理人中文" name="agtcn">
            <multi_field analyzer="null" column_name="raw" description="代理人中文" name="agtcn_raw"/>
        </field>
        <field alias="代理人英文" analyzer="smart_analyzer" column_name="agten" description="代理人英文" name="agten">
            <multi_field analyzer="null" column_name="raw" description="代理人英文" name="agten_raw"/>
        </field>
        <field alias="代理人日文" analyzer="smart_analyzer" column_name="agtjp" description="代理人日文" name="agtjp">
            <multi_field analyzer="null" column_name="raw" description="代理人日文" name="agtjp_raw"/>
        </field>
        <field alias="代理人原始" analyzer="smart_analyzer" column_name="agto" description="代理人原始" name="agto">
            <multi_field analyzer="null" column_name="raw" description="代理人原始" name="agto_raw"/>
        </field>
        <field alias="代理人地址" analyzer="smart_analyzer" column_name="agtad" description="代理人地址" name="agtad"/>
        <field alias="代理人国别" analyzer="null" column_name="agtadco" description="代理人国别" name="agtadco">
            <multi_field analyzer="null" column_name="raw" description="代理人国别" name="agtadco_raw"/>
        </field>
        <field alias="代理人城市" analyzer="null" column_name="agtadc" description="代理人城市" name="agtadc"/>
        <field alias="代理人所在州" analyzer="smart_analyzer" column_name="agtadp" description="代理人所在州" name="agtadp"/>
        <field alias="代理机构中文" analyzer="smart_analyzer" column_name="agccn" description="代理机构中文" name="agccn">
            <multi_field analyzer="null" column_name="raw" description="代理机构中文" name="agccn_raw"/>
        </field>
        <field alias="代理机构英文" analyzer="smart_analyzer" column_name="agcen" description="代理机构英文" name="agcen">
            <multi_field analyzer="null" column_name="raw" description="代理机构英文" name="agcen_raw"/>
        </field>
        <field alias="代理机构日文" analyzer="smart_analyzer" column_name="agcjp" description="代理机构日文" name="agcjp">
            <multi_field analyzer="null" column_name="raw" description="代理机构日文" name="agcjp_raw"/>
        </field>
        <field alias="代理机构原文" analyzer="smart_analyzer" column_name="agco" description="代理机构原文" name="agco">
            <multi_field analyzer="null" column_name="raw" description="代理机构原文" name="agco_raw"/>
        </field>
        <field alias="审查员中文" analyzer="smart_analyzer" column_name="excn" description="审查员中文" name="excn">
            <multi_field analyzer="null" column_name="raw" description="审查员中文" name="excn_raw"/>
        </field>
        <field alias="审查员英文" analyzer="smart_analyzer" column_name="exen" description="审查员英文" name="exen">
            <multi_field analyzer="null" column_name="raw" description="审查员英文" name="exen_raw"/>
        </field>
        <field alias="审查员日文" analyzer="smart_analyzer" column_name="exjp" description="审查员日文" name="exjp">
            <multi_field analyzer="null" column_name="raw" description="审查员日文" name="exjp_raw"/>
        </field>
        <field alias="审查员原始" analyzer="smart_analyzer" column_name="exo" description="审查员原始" name="exo">
            <multi_field analyzer="null" column_name="raw" description="审查员原始" name="exo_raw"/>
        </field>
        <field alias="最早优先权国" analyzer="null" column_name="prce" description="最早优先权国" name="prce"/>
        <field alias="最早优先权号码原始" analyzer="null" column_name="prneo" description="最早优先权号码原始" name="prneo">
            <multi_field analyzer="null" column_name="raw" description="最早优先权号码原始" name="prneo_raw"/>
        </field>
        <field alias="最早优先权号码DOCDB" analyzer="null" column_name="prnedb" description="最早优先权号码DOCDB" name="prnedb">
            <multi_field analyzer="null" column_name="raw" description="最早优先权号码DOCDB" name="prnedb_raw"/>
        </field>
        <field alias="最早优先权号码EPO" analyzer="null" column_name="prneep" description="最早优先权号码EPO" name="prneep">
            <multi_field analyzer="null" column_name="raw" description="最早优先权号码EPO" name="prneep_raw"/>
        </field>
        <field alias="最早优先权日" analyzer="null" column_name="prde" description="最早优先权日" name="prde"/>
        <field alias="最早优先权年" analyzer="null" column_name="prdye" description="最早优先权年" name="prdye"/>
        <field alias="最早优先权日/申请日" analyzer="null" column_name="prdad" description="最早优先权日/申请日" name="prdad"/>
        <field alias="引证专利数量" analyzer="null" column_name="ctc" description="引证专利数量" name="ctc"/>
        <field alias="引证公开号数量" analyzer="null" column_name="ctpnc" description="引证公开号数量" name="ctpnc"/>
        <field alias="引证申请号数量" analyzer="null" column_name="ctanc" description="引证申请号数量" name="ctanc"/>
        <field alias="家族引证数量" analyzer="null" column_name="fctc" description="家族引证数量" name="fctc"/>
        <field alias="引证国别数量" analyzer="null" column_name="ctcoc" description="引证国别数量" name="ctcoc"/>
        <field alias="引证号码" analyzer="null" column_name="ctn" description="引证号码" name="ctn">
            <multi_field analyzer="null" column_name="raw" description="引证号码" name="ctn_raw"/>
        </field>
        <field alias="被引证专利数量" analyzer="null" column_name="ctgc" description="被引证专利数量" name="ctgc"/>
        <field alias="被引证专利公开号数量" analyzer="null" column_name="ctgpnc" description="被引证专利公开号数量" name="ctgpnc"/>
        <field alias="被引证专利申请号数量" analyzer="null" column_name="ctganc" description="被引证专利申请号数量" name="ctganc"/>
        <field alias="家族被引证数量" analyzer="null" column_name="fctgc" description="家族被引证数量" name="fctgc"/>
        <field alias="简单同族被引专利公开号数量" analyzer="null" column_name="sfctgpnc" description="简单同族被引专利公开号数量" name="sfctgpnc"/>
        <field alias="简单家族被引专利申请号数量" analyzer="null" column_name="sfctganc" description="简单家族被引专利申请号数量" name="sfctganc"/>
        <field alias="简单家族被引专利国别数量" analyzer="null" column_name="sfctgcoc" description="简单家族被引专利国别数量" name="sfctgcoc"/>
        <field alias="被引专利简单同族数量" analyzer="null" column_name="ctgsfc" description="被引专利简单同族数量" name="ctgsfc"/>
        <field alias="被引专利扩展同族数量" analyzer="null" column_name="ctgefc" description="被引专利扩展同族数量" name="ctgefc"/>
        <field alias="非专利引证数量" analyzer="null" column_name="ctnpc" description="非专利引证数量" name="ctnpc"/>
        <field alias="被引证号码" analyzer="null" column_name="ctgn" description="被引证号码" name="ctgn">
            <multi_field analyzer="null" column_name="raw" description="被引证号码" name="ctgn_raw"/>
        </field>
        <field alias="被引证专利国别数量" analyzer="null" column_name="ctgcoc" description="被引证专利国别数量" name="ctgcoc"/>
        <field alias="PCT进入国家阶段日" analyzer="null" column_name="pctnd" description="PCT进入国家阶段日" name="pctnd"/>
        
        <field alias="PCT指定国" analyzer="null" column_name="pctco" description="PCT指定国" name="pctco"/>
        <field alias="简单同族数量" analyzer="null" column_name="sfc" description="简单同族数量" name="sfc"/>
        <field alias="简单同族公开号数量" analyzer="null" column_name="sfpnc" description="简单同族公开号数量" name="sfpnc"/>
        <field alias="简单同族申请号数量" analyzer="null" column_name="sfanc" description="简单同族申请号数量" name="sfanc"/>
        <field alias="简单同族国家数" analyzer="null" column_name="sfcoc" description="简单同族国家数" name="sfcoc"/>
        <field alias="简单同族代表性专利" analyzer="null" column_name="sfrpn" description="简单同族代表性专利" name="sfrpn">
            <multi_field analyzer="null" column_name="raw" description="简单同族代表性专利" name="sfrpn_raw"/>
        </field>
        <field alias="扩展同族数量" analyzer="null" column_name="efc" description="扩展同族数量" name="efc"/>
        <field alias="扩展同族公开号数量" analyzer="null" column_name="efpnc" description="扩展同族公开号数量" name="efpnc"/>
        <field alias="扩展同族申请号数量" analyzer="null" column_name="efanc" description="扩展同族申请号数量" name="efanc"/>
        <field alias="扩展同族国家数" analyzer="null" column_name="efcoc" description="扩展同族国家数" name="efcoc"/>
        <field alias="DOCDB同族数量" analyzer="null" column_name="docdbfc" description="DOCDB同族数量" name="docdbfc"/>
        <field alias="简单同族号" analyzer="null" column_name="sfid" description="简单同族号" name="sfid"/>
        <field alias="扩展同族号" analyzer="null" column_name="efid" description="扩展同族号" name="efid"/>
        <field alias="专利有效性-中文" analyzer="null" column_name="slscn" description="专利有效性-中文" name="slscn">
            <multi_field analyzer="null" column_name="raw" description="专利有效性-中文" name="slscn_raw"/>
        </field>
        <field alias="专利有效性-代码" analyzer="null" column_name="slsabbr" description="专利有效性-代码" name="slsabbr"/>
        <field alias="当前法律状态-中文" analyzer="null" column_name="legalcn" description="当前法律状态-中文" name="legalcn"/>
        <field alias="当前法律状态-缩写" analyzer="null" column_name="legalabbr" description="当前法律状态-缩写" name="legalabbr">
            <multi_field analyzer="null" column_name="raw" description="当前法律状态-缩写" name="legalabbr_raw"/>
        </field>
        <field alias="法律状态更新日" analyzer="null" column_name="lsud" description="法律状态更新日" name="lsud"/>
        <field alias="预估到期日" analyzer="null" column_name="eed" description="预估到期日" name="eed"/>
        <field alias="预估到期年" analyzer="null" column_name="eey" description="预估到期年" name="eey"/>
        <field alias="法律状态公告日" analyzer="null" column_name="lsad" description="法律状态公告日" name="lsad"/>
        <field alias="专利寿命" analyzer="null" column_name="pl" description="专利寿命" name="pl"/>
        <field alias="独立权利要求数量" analyzer="null" column_name="iclmc" description="独立权利要求数量" name="iclmc"/>
        <field alias="从属权利要求数量" analyzer="null" column_name="dclmc" description="从属权利要求数量" name="dclmc"/>
        <field alias="首项权利要求字数" analyzer="null" column_name="fclmolec" description="首项权利要求字数" name="fclmolec"/>
        <field alias="权利要求数量" analyzer="null" column_name="clmc" description="权利要求数量" name="clmc"/>
        <field alias="申请时权利要求数量" analyzer="null" column_name="clmac" description="申请时权利要求数量" name="clmac"/>
        <field alias="授权时权利要求数量" analyzer="null" column_name="clmbc" description="授权时权利要求数量" name="clmbc"/>
        <field alias="文献页数" analyzer="null" column_name="docn" description="文献页数" name="docn"/>
        <field alias="说明书页数" analyzer="null" column_name="desn" description="说明书页数" name="desn"/>
        <field alias="当前申请人数量" analyzer="null" column_name="cappc" description="当前申请人数量" name="cappc"/>
        <field alias="原始申请人数量" analyzer="null" column_name="apc" description="原始申请人数量" name="apc"/>
        <field alias="原始专利权人数量" analyzer="null" column_name="asc" description="原始专利权人数量" name="asc"/>
        <field alias="当前专利权人数量" analyzer="null" column_name="appzcc" description="当前专利权人数量" name="appzcc"/>
        <field alias="发明人数量" analyzer="null" column_name="inq" description="发明人数量" name="inq"/>
        <field alias="CPC分类数量" analyzer="null" column_name="cpcq" description="CPC分类数量" name="cpcq"/>
        <field alias="CPC小类数量" analyzer="null" column_name="cpcsq" description="CPC小类数量" name="cpcsq"/>
        <field alias="IPC分类数量" analyzer="null" column_name="ipcq" description="IPC分类数量" name="ipcq"/>
        <field alias="IPC小类数量" analyzer="null" column_name="ipcsq" description="IPC小类数量" name="ipcsq"/>
        <field alias="FI分类号数量" analyzer="null" column_name="fic" description="FI分类号数量" name="fic"/>
        <field alias="FT分类号数量" analyzer="null" column_name="ftc" description="FT分类号数量" name="ftc"/>
        <field alias="政府利益" analyzer="null" column_name="govi" description="政府利益" name="govi"/>
        <field alias="PCT路径进入专利" analyzer="null" column_name="pctpt" description="PCT路径进入专利" name="pctpt">
            <multi_field analyzer="null" column_name="raw" description="PCT路径进入专利" name="pctpt_raw"/>
        </field>
        <field alias="专利价值度" analyzer="null" column_name="pv" description="专利价值度" name="pv"/>
        <field alias="法律价值度" analyzer="null" column_name="pvl" description="法律价值度" name="pvl"/>
        <field alias="经济价值度" analyzer="null" column_name="pve" description="经济价值度" name="pve"/>
        <field alias="技术价值度" analyzer="null" column_name="pvt" description="技术价值度" name="pvt"/>
        <field alias="奖励等级" analyzer="null" column_name="rl" description="奖励等级" name="rl">
            <multi_field analyzer="null" column_name="raw" description="奖励等级" name="rl_raw"/>
        </field>
        <field alias="奖励名称" analyzer="null" column_name="rn" description="奖励名称" name="rn">
            <multi_field analyzer="null" column_name="raw" description="奖励名称" name="rn_raw"/>
        </field>
        <field alias="奖励届次" analyzer="null" column_name="rs" description="奖励届次" name="rs">
            <multi_field analyzer="null" column_name="raw" description="奖励届次" name="rs_raw"/>
        </field>
        <field alias="奖励年度" analyzer="null" column_name="ry" description="奖励年度" name="ry"/>
        <field alias="标准数据源" analyzer="null" column_name="seps" description="标准数据源" name="seps">
            <multi_field analyzer="null" column_name="raw" description="标准数据源" name="seps_raw"/>
        </field>
        <field alias="标准项目" analyzer="null" column_name="sepp" description="标准项目" name="sepp">
            <multi_field analyzer="null" column_name="raw" description="标准项目" name="sepp_raw"/>
        </field>
        <field alias="标准号" analyzer="null" column_name="sepn" description="标准号" name="sepn">
            <multi_field analyzer="null" column_name="raw" description="标准号" name="sepn_raw"/>
        </field>
        <field alias="标准标题" analyzer="smart_analyzer" column_name="septi" description="标准标题" name="septi"/>
        <field alias="标准持有公司" analyzer="smart_analyzer" column_name="sephc" description="标准持有公司" name="sephc"/>
        <field alias="是否标准专利" analyzer="smart_analyzer" column_name="sep" description="是否标准专利" name="sep"/>
        <field alias="无优先权专利" analyzer="null" column_name="prem" description="无优先权专利" name="prem"/>
        <field alias="是否海关专利" analyzer="null" column_name="cus" description="是否海关专利" name="cus">
            <multi_field analyzer="null" column_name="raw" description="是否海关专利" name="cus_raw"/>
        </field>
        <field alias="备案号" analyzer="null" column_name="recn" description="备案号" name="recn"/>
        <field alias="备案时间" analyzer="null" column_name="recd" description="备案时间" name="recd"/>
        <field alias="取消时间" analyzer="null" column_name="cacd" description="取消时间" name="cacd"/>
        <field alias="备案状态" analyzer="null" column_name="recs" description="备案状态" name="recs">
            <multi_field analyzer="null" column_name="raw" description="备案状态" name="recs_raw"/>
        </field>
        <field alias="审批意见" analyzer="smart_analyzer" column_name="ac" description="审批意见" name="ac"/>
        <field alias="事件标签" analyzer="null" column_name="flag" description="事件标签" name="flag">
            <multi_field analyzer="null" column_name="raw" description="事件标签" name="flag_raw"/>
        </field>
        <field alias="FDA-CAS号" analyzer="null" column_name="casn" description="FDA-CAS号" name="casn">
            <multi_field analyzer="null" column_name="raw" description="FDA-CAS号" name="casn_raw"/>
        </field>
        <field alias="FDA-中文名称" analyzer="smart_analyzer" column_name="fdac" description="FDA-中文名称" name="fdac"/>
        <field alias="FDA-英文名称" analyzer="smart_analyzer" column_name="fdae" description="FDA-英文名称" name="fdae"/>
        <field alias="FDA-申请号" analyzer="null" column_name="fdaan" description="FDA-申请号" name="fdaan"/>
        <field alias="FDA-申请类型" analyzer="null" column_name="fdaat" description="FDA-申请类型" name="fdaat"/>
        <field alias="FDA-公司名称" analyzer="smart_analyzer" column_name="fdacn" description="FDA-公司名称" name="fdacn"/>
        <field alias="FDA-公司简称" analyzer="smart_analyzer" column_name="fdacns" description="FDA-公司简称" name="fdacns"/>
        <field alias="FDA-产品号" analyzer="null" column_name="fdapid" description="FDA-产品号" name="fdapid"/>
        <field alias="FDA-商品名" analyzer="smart_analyzer" column_name="fdapn" description="FDA-商品名" name="fdapn"/>
        <field alias="FDA-活性成分" analyzer="null" column_name="fdaai" description="FDA-活性成分" name="fdaai"/>
        <field alias="FDA-靶点" analyzer="null" column_name="fdat" description="FDA-靶点" name="fdat"/>
        <field alias="FDA-适应症" analyzer="null" column_name="fdai" description="FDA-适应症" name="fdai"/>
        <field alias="FDA-专利到期日" analyzer="null" column_name="fdaped" description="FDA-专利到期日" name="fdaped"/>
        <field alias="FDA-PED药物专利到期日" analyzer="null" column_name="fdapeded" description="FDA-PED药物专利到期日" name="fdapeded"/>
        <field alias="FDA-剂型" analyzer="null" column_name="fdadf" description="FDA-剂型" name="fdadf"/>
        <field alias="FDA-是否为物质专利" analyzer="null" column_name="fdacp" description="FDA-是否为物质专利" name="fdacp"/>
        <field alias="FDA-是否为产品专利" analyzer="null" column_name="fdapp" description="FDA-是否为产品专利" name="fdapp"/>
        <field alias="FDA-专利用途代码" analyzer="null" column_name="fdauc" description="FDA-专利用途代码" name="fdauc"/>
        <field alias="是否诉讼" analyzer="null" column_name="li" description="是否诉讼" name="li">
            <multi_field analyzer="null" column_name="raw" description="是否诉讼" name="li_raw"/>
        </field>
        <field alias="许可次数" analyzer="null" column_name="licec" description="许可次数" name="licec"/>
        <field alias="转让次数" analyzer="null" column_name="assc" description="转让次数" name="assc"/>
        <field alias="申请权转让次数" analyzer="null" column_name="aassc" description="申请权转让次数" name="aassc"/>
        <field alias="专利权转让次数" analyzer="null" column_name="passc" description="专利权转让次数" name="passc"/>
        <field alias="是否复审无效" analyzer="null" column_name="reeinv" description="是否复审无效" name="reeinv">
            <multi_field analyzer="null" column_name="raw" description="是否复审无效" name="reeinv_raw"/>
        </field>
        <field alias="复审次数" analyzer="null" column_name="reec" description="复审次数" name="reec"/>
        <field alias="无效次数" analyzer="null" column_name="invc" description="无效次数" name="invc"/>
        <field alias="专利质押" analyzer="null" column_name="ple" description="是否质押" name="ple">
            <multi_field analyzer="null" column_name="raw" description="是否质押" name="ple_raw"/>
        </field>
        <field alias="是否保全" analyzer="null" column_name="pre" description="是否保全" name="pre">
            <multi_field analyzer="null" column_name="raw" description="是否保全" name="pre_raw"/>
        </field>
        <field alias="当前质权人" analyzer="smart_analyzer" column_name="peec" description="当前质权人" name="peec">
            <multi_field analyzer="null" column_name="raw" description="当前质权人" name="peec_raw"/>
        </field>
        <field alias="[标准]当前质权人" analyzer="smart_analyzer" column_name="peecs" description="[标准]当前质权人" name="peecs">
            <multi_field analyzer="null" column_name="raw" description="[标准]当前质权人" name="peecs_raw"/>
        </field>
        <field alias="质押期限" analyzer="null" column_name="plet" description="质押期限" name="plet"/>
        <field alias="质押备案阶段-中文" analyzer="null" column_name="plescn" description="质押备案阶段-中文" name="plescn"/>
        <field alias="质押备案阶段-代码" analyzer="null" column_name="plesabbr" description="质押备案阶段-代码" name="plesabbr">
            <multi_field analyzer="null" column_name="raw" description="质押备案阶段-代码" name="plesabbr_raw"/>
        </field>
        <field alias="保全阶段-中文" analyzer="null" column_name="prescn" description="保全阶段-中文" name="prescn">
            <multi_field analyzer="null" column_name="raw" description="保全阶段-中文" name="prescn_raw"/>
        </field>
        <field alias="保全阶段-代码" analyzer="null" column_name="presabbr" description="保全阶段-代码" name="presabbr">
            <multi_field analyzer="null" column_name="raw" description="保全阶段-代码" name="presabbr_raw"/>
        </field>
        <field alias="保全次数" analyzer="null" column_name="prec" description="保全次数" name="prec"/>
        <field alias="质押次数" analyzer="null" column_name="plec" description="质押次数" name="plec"/>
        <field alias="关联案件申请号" analyzer="null" column_name="rpap" description="关联案件申请号" name="rpap">
            <multi_field analyzer="null" column_name="raw" description="关联案件申请号" name="rpap_raw"/>
        </field>
        <field alias="关联案件类型(缩写)" analyzer="null" column_name="rptabbr" description="关联案件类型(缩写)" name="rptabbr">
            <multi_field analyzer="null" column_name="raw" description="关联案件类型(缩写)" name="rptabbr_raw"/>
        </field>
        <field alias="关联案件类型(中文)" analyzer="null" column_name="rptcn" description="关联案件类型(中文)" name="rptcn">
            <multi_field analyzer="null" column_name="raw" description="关联案件类型(中文)" name="rptcn_raw"/>
        </field>
        <field alias="WO国家阶段" analyzer="null" column_name="wocos" description="WO国家阶段" name="wocos">
            <multi_field analyzer="null" column_name="raw" description="WO国家阶段" name="wocos_raw"/>
        </field>
        <field alias="文献代码" analyzer="null" column_name="pnk" description="文献代码" name="pnk">
            <multi_field analyzer="null" column_name="raw" description="文献代码" name="pnk_raw"/>
        </field>
        <field alias="母案" analyzer="null" column_name="man" description="母案" name="man">
            <multi_field analyzer="null" column_name="raw" description="母案" name="man_raw"/>
        </field>
        <field alias="分案" analyzer="null" column_name="dan" description="分案" name="dan">
            <multi_field analyzer="null" column_name="raw" description="分案" name="dan_raw"/>
        </field>
        <field alias="一案双申单值" analyzer="null" column_name="da" description="一案双申单值" name="da"/>
        <field alias="一案双申" analyzer="null" column_name="das" description="一案双申" name="das">
            <multi_field analyzer="null" column_name="raw" description="一案双申" name="das_raw"/>
        </field>
        <field alias="主要著录信息" analyzer="smart_analyzer" column_name="mbi" description="主要著录信息" name="mbi"/>
        <field alias="号码" analyzer="null" column_name="nu" description="号码" name="nu">
            <multi_field analyzer="null" column_name="raw" description="号码" name="nu_raw"/>
        </field>
        <field alias="说明书其他中文" analyzer="smart_analyzer" column_name="otherdesccn" description="说明书其他中文" name="otherdesccn"/>
        <field alias="说明书其它英文" analyzer="smart_analyzer" column_name="otherdescen" description="说明书其它英文" name="otherdescen"/>
        <field alias="说明书其它日文" analyzer="smart_analyzer" column_name="otherdescjp" description="说明书其它日文" name="otherdescjp"/>
        <field alias="说明书其它原文" analyzer="smart_analyzer" column_name="otherdesco" description="说明书其它原文" name="otherdesco"/>
        <field alias="说明书" analyzer="smart_analyzer" column_name="des" description="说明书" name="des"/>
        <field alias="全文中文" analyzer="smart_analyzer" column_name="tacdcn" description="全文中文" name="tacdcn"/>
        <field alias="全文日文" analyzer="smart_analyzer" column_name="tacden" description="全文日文" name="tacden"/>
        <field alias="全文英文" analyzer="smart_analyzer" column_name="tacdjp" description="全文英文" name="tacdjp"/>
        <field alias="全文语义中文" analyzer="null" column_name="tacdcn_vec" description="全文语义" name="tacdcn_vec"/>
        <field alias="全文语义日文" analyzer="null" column_name="tacdjp_vec" description="全文语义日文" name="tacdjp_vec"/>
        <field alias="全文语义英文" analyzer="null" column_name="tacden_vec" description="全文语义英文" name="tacden_vec"/>
        <field alias="图片检索" analyzer="null" column_name="image" description="图片检索" name="image"/>
        <field alias="P" analyzer="null" column_name="p" description="P" name="p"/>
        <field alias="R" analyzer="null" column_name="r" description="R" name="r"/>
        <field alias="RAD" analyzer="null" column_name="rad" description="RAD" name="rad">
            <multi_field analyzer="null" column_name="raw" description="RAD" name="rad_raw"/>
        </field>
        <field alias="RPD" analyzer="null" column_name="rpd" description="RPD" name="rpd">
            <multi_field analyzer="null" column_name="raw" description="RPD" name="rpd_raw"/>
        </field>
        <field alias="_score" analyzer="null" column_name="_score" description="_score" name="_score"/>
        <field alias="专利最新命中" analyzer="null" column_name="patentchgdates" description="专利最新命中" name="patentchgdates"/>
        <field alias="法律状态变更" analyzer="null" column_name="legaltimestamp" description="法律状态变更" name="legaltimestamp"/>
        <field alias="专利进入时审" analyzer="null" column_name="reetimestamp" description="专利进入时审" name="reetimestamp"/>
        <field alias="专利授权时间" analyzer="null" column_name="legatimestamp" description="专利授权时间" name="legatimestamp"/>
        <field alias="专利失效时间" analyzer="null" column_name="legtimestamp" description="专利失效时间" name="legtimestamp"/>
        <field alias="专利权转让时间" analyzer="null" column_name="passctimestamp" description="专利权转让时间" name="passctimestamp"/>
        <field alias="专利权人变更时间" analyzer="null" column_name="changetimestamp" description="专利权人变更时间" name="changetimestamp"/>
        <field alias="复审无效信息更新时间" analyzer="null" column_name="invctimestamp" description="复审无效信息更新时间" name="invctimestamp"/>
        <field alias="质押信息更新时间" analyzer="null" column_name="plectimestamp" description="质押信息更新时间" name="plectimestamp"/>
        <field alias="诉讼信息更新时间" analyzer="null" column_name="lictimestamp" description="诉讼信息更新时间" name="lictimestamp"/>
        <field alias="海关备案信息更新时间" analyzer="null" column_name="customstimestamp" description="海关备案信息更新时间" name="customstimestamp"/>
        <field alias="许可信息更新时间" analyzer="null" column_name="licectimestamp" description="许可信息更新时间" name="licectimestamp"/>
        <field alias="说明书变化更新时间" analyzer="null" column_name="desntimestamp" description="说明书变化更新时间" name="desntimestamp"/>
        <field alias="入库时间" analyzer="null" column_name="instime" description="入库时间" name="instime"/>
        <field alias="申请号合并使用" analyzer="null" column_name="anm" description="申请号合并使用" name="anm"/>
        <field alias="是否许可" analyzer="null" column_name="lice" description="是否许可" name="lice">
            <multi_field analyzer="null" column_name="raw" description="是否许可" name="lice_raw"/>
        </field>
        <field alias="当前许可备案阶段" analyzer="null" column_name="licesc" description="当前许可备案阶段" name="licesc"/>
        <field alias="当前被许可人" analyzer="smart_analyzer" column_name="liceeec" description="当前被许可人" name="liceeec">
            <multi_field analyzer="null" column_name="raw" description="当前被许可人" name="liceeec_raw"/>
        </field>
        <field alias="工商别名" analyzer="smart_analyzer" column_name="apfn" description="工商别名" name="apfn">
            <multi_field analyzer="null" column_name="raw" description="工商别名" name="apfn_raw"/>
        </field>
        <field alias="工商英文名" analyzer="smart_analyzer" column_name="apne" description="工商英文名" name="apne">
            <multi_field analyzer="null" column_name="raw" description="工商英文名" name="apne_raw"/>
        </field>
        <field alias="工商注册地址" analyzer="smart_analyzer" column_name="aprad" description="工商注册地址" name="aprad"/>
        <field alias="工商注册地址省" analyzer="null" column_name="apradp" description="工商注册地址省" name="apradp">
            <multi_field analyzer="null" column_name="raw" description="工商注册地址省" name="apradp_raw"/>
        </field>
        <field alias="工商注册地址地市" analyzer="null" column_name="apradc" description="工商注册地址地市" name="apradc">
            <multi_field analyzer="null" column_name="raw" description="工商注册地址地市" name="apradc_raw"/>
        </field>
        <field alias="工商注册地址区县" analyzer="smart_analyzer" column_name="apradd" description="工商注册地址区县" name="apradd"/>
        <field alias="工商注册地址街道" analyzer="smart_analyzer" column_name="aprads" description="工商注册地址街道" name="aprads"/>
        <field alias="工商注册地址园区" analyzer="smart_analyzer" column_name="apradz" description="工商注册地址园区" name="apradz"/>
        <field alias="工商地址经纬度" analyzer="null" column_name="apradll" description="工商地址经纬度" name="apradll"/>
        <field alias="联系地址经纬度" analyzer="null" column_name="apcadll" description="联系地址经纬度" name="apcadll"/>
        <field alias="工商公司类型" analyzer="null" column_name="apct" description="工商公司类型" name="apct"/>
        <field alias="工商成立日期" analyzer="null" column_name="apcet" description="工商成立日期" name="apcet"/>
        <field alias="工商统一社会信用代码" analyzer="null" column_name="apuscc" description="工商统一社会信用代码" name="apuscc">
            <multi_field analyzer="null" column_name="raw" description="工商统一社会信用代码" name="apuscc_raw"/>
        </field>
        <field alias="工商注册号" analyzer="null" column_name="aprn" description="工商注册号" name="aprn">
            <multi_field analyzer="null" column_name="raw" description="工商注册号" name="aprn_raw"/>
        </field>
        <field alias="工商企业状态" analyzer="null" column_name="apcs" description="工商企业状态" name="apcs">
            <multi_field analyzer="null" column_name="raw" description="工商企业状态" name="apcs_raw"/>
        </field>
        <field alias="工商上市代码" analyzer="null" column_name="apclc" description="工商上市代码" name="apclc">
            <multi_field analyzer="null" column_name="raw" description="工商上市代码" name="apclc_raw"/>
        </field>
        <field alias="企业所属行业" analyzer="null" column_name="apci" description="企业所属行业" name="apci"/>
        <field alias="企业标签" analyzer="null" column_name="apcl" description="企业标签" name="apcl"/>
        <field alias="决定类型-中文" analyzer="null" column_name="reeinvtcn" description="决定类型-中文" name="reeinvtcn">
            <multi_field analyzer="null" column_name="raw" description="决定类型-中文" name="reeinvtcn_raw"/>
        </field>
        <field alias="决定类型-缩写" analyzer="null" column_name="reeinvtabbr" description="决定类型-缩写" name="reeinvtabbr">
            <multi_field analyzer="null" column_name="raw" description="决定类型-缩写" name="reeinvtabbr_raw"/>
        </field>
        
        <field alias="诉讼次数" analyzer="null" column_name="lic" description="诉讼次数" name="lic"/>
        <field alias="X引证文献" analyzer="null" column_name="ctx" description="X引证文献" name="ctx"/>
        <field alias="Y引证文献" analyzer="null" column_name="cty" description="Y引证文献" name="cty"/>
        <field alias="A引证文献" analyzer="null" column_name="cta" description="A引证文献" name="cta"/>
        <field alias="家族引证申请人" analyzer="smart_analyzer" column_name="fctap" description="家族引证申请人" name="fctap">
            <multi_field analyzer="null" column_name="raw" description="家族引证申请人" name="fctap_raw"/>
        </field>
        <field alias="家族引证" analyzer="null" column_name="fct" description="家族引证" name="fct">
            <multi_field analyzer="null" column_name="raw" description="家族引证" name="fct_raw"/>
        </field>
        <field alias="家族被引证" analyzer="null" column_name="fctg" description="家族被引证" name="fctg">
            <multi_field analyzer="null" column_name="raw" description="家族被引证" name="fctg_raw"/>
        </field>
        <field alias="家族被引证申请人" analyzer="smart_analyzer" column_name="fctgap" description="家族被引证申请人" name="fctgap">
            <multi_field analyzer="null" column_name="raw" description="家族被引证申请人" name="fctgap_raw"/>
        </field>
        <field alias="优先权国家数量" analyzer="null" column_name="prcoc" description="优先权国家数量" name="prcoc"/>
        <field alias="是否转让" analyzer="null" column_name="ass" description="是否转让" name="ass">
            <multi_field analyzer="null" column_name="raw" description="是否转让" name="ass_raw"/>
        </field>
        <field alias="转让后当前地址" analyzer="smart_analyzer" column_name="assaadrc" description="转让后当前地址" name="assaadrc"/>
    </fields>
    <nested description="申请人信息" name="applicantinfo">
        <columns>
            <column description="申请人中文" is_array="true" name="apcn" not_null="false" type="tiny_text"/>
            <column description="申请人英文" is_array="true" name="apen" not_null="false" type="tiny_text"/>
            <column description="申请人日文" is_array="true" name="apjp" not_null="false" type="tiny_text"/>
            <column description="申请人原文" is_array="true" name="apo" not_null="false" type="tiny_text"/>
            <column description="标准原始申请人中文" is_array="true" name="aposcn" not_null="false" type="tiny_text"/>
            <column description="标准原始申请人英文" is_array="true" name="aposen" not_null="false" type="tiny_text"/>
            <column description="标准原始申请人日文" is_array="true" name="aposjp" not_null="false" type="tiny_text"/>
            <column description="标准原始申请人原文" is_array="true" name="aposo" not_null="false" type="tiny_text"/>
            <column description="原始申请人地址中文" is_array="true" name="apoadcn" not_null="false" type="long_text"/>
            <column description="原始申请人地址英文" is_array="true" name="apoaden" not_null="false" type="long_text"/>
            <column description="原始申请人地址日文" is_array="true" name="apoadjp" not_null="false" type="long_text"/>
            <column description="原始申请人地址原始" is_array="true" name="apoado" not_null="false" type="long_text"/>
            <column description="原始申请人国别" is_array="true" name="apoadco" not_null="false" type="item_no"/>
            <column description="原始申请人州/省" is_array="true" name="apoadp" not_null="false" type="tiny_text"/>
            <column description="原始申请人地市/市" is_array="true" name="apoadc" not_null="false" type="tiny_text"/>
            <column description="原始申请人区县" is_array="true" name="apoadd" not_null="false" type="tiny_text"/>
            <column description="原始申请人园区" is_array="true" name="apoadz" not_null="false" type="tiny_text"/>
            <column description="原始申请人州/省(统计字段)" is_array="true" name="apoadp_su" not_null="false" type="item_no"/>
            <column description="原始申请人地市/市(统计字段)" is_array="true" name="apoadc_su" not_null="false" type="item_no"/>
            <column description="原始申请人区县(统计字段)" is_array="true" name="apoadd_su" not_null="false" type="item_no"/>
            <column description="原始申请人园区(统计字段)" is_array="true" name="apoadz_su" not_null="false" type="item_no"/>
            <column description="原始申请人街道" is_array="true" name="apoads" not_null="false" type="long_text"/>
            <column description="原始申请人地址经纬度" is_array="true" name="apoadll" not_null="false" type="geo_point"/>
            <column description="原始申请人国省代码" is_array="true" name="apoadcc" not_null="false" type="item_no"/>
            <column description="标准原始申请人类型" is_array="true" name="apost" not_null="false" type="item_no"/>
            <column description="标准原始申请人类型-中文" is_array="true" name="apostcn" not_null="false" type="item_no"/>
        </columns>
        <field alias="申请人中文" analyzer="smart_analyzer" column_name="apcn" description="申请人中文" name="apcn">
            <multi_field analyzer="null" column_name="raw" description="申请人中文" name="apcn_raw"/>
        </field>
        <field alias="申请人英文" analyzer="smart_analyzer" column_name="apen" description="申请人英文" name="apen">
            <multi_field analyzer="null" column_name="raw" description="申请人英文" name="apen_raw"/>
        </field>
        <field alias="申请人日文" analyzer="smart_analyzer" column_name="apjp" description="申请人日文" name="apjp">
            <multi_field analyzer="null" column_name="raw" description="申请人日文" name="apjp_raw"/>
        </field>
        <field alias="申请人原文" analyzer="smart_analyzer" column_name="apo" description="申请人原文" name="apo">
            <multi_field analyzer="null" column_name="raw" description="申请人原文" name="apo_raw"/>
        </field>
        <field alias="标准原始申请人中文" analyzer="smart_analyzer" column_name="aposcn" description="标准原始申请人中文" name="aposcn">
            <multi_field analyzer="null" column_name="raw" description="标准原始申请人中文" name="aposcn_raw"/>
        </field>
        <field alias="标准原始申请人英文" analyzer="smart_analyzer" column_name="aposen" description="标准原始申请人英文" name="aposen">
            <multi_field analyzer="null" column_name="raw" description="标准原始申请人英文" name="aposen_raw"/>
        </field>
        <field alias="标准原始申请人日文" analyzer="smart_analyzer" column_name="aposjp" description="标准原始申请人日文" name="aposjp">
            <multi_field analyzer="null" column_name="raw" description="标准原始申请人日文" name="aposjp_raw"/>
        </field>
        <field alias="标准原始申请人原文" analyzer="smart_analyzer" column_name="aposo" description="标准原始申请人原文" name="aposo">
            <multi_field analyzer="null" column_name="raw" description="标准原始申请人原文" name="aposo_raw"/>
        </field>
        <field alias="原始申请人地址中文" analyzer="smart_analyzer" column_name="apoadcn" description="原始申请人地址中文" name="apoadcn"/>
        <field alias="原始申请人地址英文" analyzer="smart_analyzer" column_name="apoaden" description="原始申请人地址英文" name="apoaden"/>
        <field alias="原始申请人地址日文" analyzer="smart_analyzer" column_name="apoadjp" description="原始申请人地址日文" name="apoadjp"/>
        <field alias="原始申请人地址原始" analyzer="smart_analyzer" column_name="apoado" description="原始申请人地址原始" name="apoado"/>
        <field alias="原始申请人国别" analyzer="null" column_name="apoadco" description="原始申请人国别" name="apoadco"/>
        <field alias="原始申请人州/省" analyzer="smart_analyzer" column_name="apoadp" description="原始申请人州/省" name="apoadp"/>
        <field alias="原始申请人地市/市" analyzer="smart_analyzer" column_name="apoadc" description="原始申请人地市/市" name="apoadc"/>
        <field alias="原始申请人区县" analyzer="smart_analyzer" column_name="apoadd" description="原始申请人区县" name="apoadd"/>
        <field alias="原始申请人园区" analyzer="smart_analyzer" column_name="apoadz" description="原始申请人园区" name="apoadz"/>
        <field alias="原始申请人州/省(统计字段)" analyzer="null" column_name="apoadp_su" description="原始申请人州/省(统计字段)" name="apoadp_su"/>
        <field alias="原始申请人地市/市(统计字段)" analyzer="null" column_name="apoadc_su" description="原始申请人地市/市(统计字段)" name="apoadc_su"/>
        <field alias="原始申请人区县(统计字段)" analyzer="null" column_name="apoadd_su" description="原始申请人区县(统计字段)" name="apoadd_su"/>
        <field alias="原始申请人园区(统计字段)" analyzer="null" column_name="apoadz_su" description="原始申请人园区(统计字段)" name="apoadz_su"/>
        <field alias="原始申请人街道" analyzer="null" column_name="apoads" description="原始申请人街道" name="apoads"/>
        <field alias="原始申请人地址经纬度" analyzer="null" column_name="apoadll" description="原始申请人地址经纬度" name="apoadll"/>
        <field alias="原始申请人国省代码" analyzer="null" column_name="apoadcc" description="原始申请人国省代码" name="apoadcc">
            <multi_field analyzer="null" column_name="raw" description="原始申请人国省代码" name="apoadcc_raw"/>
        </field>
        <field alias="标准原始申请人类型" analyzer="null" column_name="apost" description="标准原始申请人类型" name="apost"/>
        <field alias="标准原始申请人类型-中文" analyzer="null" column_name="apostcn" description="标准原始申请人类型-中文" name="apostcn">
            <multi_field analyzer="null" column_name="raw" description="标准原始申请人类型-中文" name="apostcn_raw"/>
        </field>
    </nested>
    <nested description="专利权人信息" name="assigneeinfo">
        <columns>
            <column description="专利权人中文" is_array="true" name="ascn" not_null="false" type="tiny_text"/>
            <column description="专利权人英文" is_array="true" name="asen" not_null="false" type="tiny_text"/>
            <column description="专利权人日文" is_array="true" name="asjp" not_null="false" type="tiny_text"/>
            <column description="专利权人原文" is_array="true" name="aso" not_null="false" type="tiny_text"/>
            <column description="标准原始专利权人中文" is_array="true" name="asoscn" not_null="false" type="tiny_text"/>
            <column description="标准原始专利权人英文" is_array="true" name="asosen" not_null="false" type="tiny_text"/>
            <column description="标准原始专利权人日文" is_array="true" name="asosjp" not_null="false" type="tiny_text"/>
            <column description="标准原始专利权人原文" is_array="true" name="asoso" not_null="false" type="tiny_text"/>
            <column description="原始专利权人地址中文" is_array="true" name="asoadcn" not_null="false" type="long_text"/>
            <column description="原始专利权人地址英文" is_array="true" name="asoaden" not_null="false" type="long_text"/>
            <column description="原始专利权人地址日文" is_array="true" name="asoadjp" not_null="false" type="long_text"/>
            <column description="原始专利权人地址原始" is_array="true" name="asoado" not_null="false" type="long_text"/>
            <column description="原始专利权人国别" is_array="true" name="asoadco" not_null="false" type="item_no"/>
            <column description="原始专利权人州/省" is_array="true" name="asoadp" not_null="false" type="tiny_text"/>
            <column description="原始专利权人地市/市" is_array="true" name="asoadc" not_null="false" type="tiny_text"/>
            <column description="原始专利权人区县" is_array="true" name="asoadd" not_null="false" type="long_text"/>
            <column description="原始专利权人街道" is_array="true" name="asoads" not_null="false" type="long_text"/>
            <column description="原始专利权人园区" is_array="true" name="asoadz" not_null="false" type="long_text"/>
            <column description="原始专利权人地址经纬度" is_array="true" name="asoadll" not_null="false" type="geo_point"/>
            <column description="原始专利权人国省代码" is_array="true" name="asoadcc" not_null="false" type="item_no"/>
            <column description="标准原始专利权人类型" is_array="true" name="asost" not_null="false" type="item_no"/>
            <column description="标准原始专利权人类型-中文" is_array="true" name="asostcn" not_null="false" type="item_no"/>
        </columns>
        <field alias="专利权人中文" analyzer="smart_analyzer" column_name="ascn" description="专利权人中文" name="ascn">
            <multi_field analyzer="null" column_name="raw" description="专利权人中文" name="ascn_raw"/>
        </field>
        <field alias="专利权人英文" analyzer="smart_analyzer" column_name="asen" description="专利权人英文" name="asen">
            <multi_field analyzer="null" column_name="raw" description="专利权人英文" name="asen_raw"/>
        </field>
        <field alias="专利权人日文" analyzer="smart_analyzer" column_name="asjp" description="专利权人日文" name="asjp">
            <multi_field analyzer="null" column_name="raw" description="专利权人日文" name="asjp_raw"/>
        </field>
        <field alias="专利权人原文" analyzer="smart_analyzer" column_name="aso" description="专利权人原文" name="aso">
            <multi_field analyzer="null" column_name="raw" description="专利权人原文" name="aso_raw"/>
        </field>
        <field alias="标准原始专利权人中文" analyzer="smart_analyzer" column_name="asoscn" description="标准原始专利权人中文" name="asoscn">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人中文" name="asoscn_raw"/>
        </field>
        <field alias="标准原始专利权人英文" analyzer="smart_analyzer" column_name="asosen" description="标准原始专利权人英文" name="asosen">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人英文" name="asosen_raw"/>
        </field>
        <field alias="标准原始专利权人日文" analyzer="smart_analyzer" column_name="asosjp" description="标准原始专利权人日文" name="asosjp">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人日文" name="asosjp_raw"/>
        </field>
        <field alias="标准原始专利权人原文" analyzer="smart_analyzer" column_name="asoso" description="标准原始专利权人原文" name="asoso">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人原文" name="asoso_raw"/>
        </field>
        <field alias="原始专利权人地址中文" analyzer="smart_analyzer" column_name="asoadcn" description="原始专利权人地址中文" name="asoadcn"/>
        <field alias="原始专利权人地址英文" analyzer="smart_analyzer" column_name="asoaden" description="原始专利权人地址英文" name="asoaden"/>
        <field alias="原始专利权人地址日文" analyzer="smart_analyzer" column_name="asoadjp" description="原始专利权人地址日文" name="asoadjp"/>
        <field alias="原始专利权人地址原始" analyzer="smart_analyzer" column_name="asoado" description="原始专利权人地址原始" name="asoado"/>
        <field alias="原始专利权人国别" analyzer="null" column_name="asoadco" description="原始专利权人国别" name="asoadco">
            <multi_field analyzer="null" column_name="raw" description="原始专利权人国别" name="asoadco_raw"/>
        </field>
        <field alias="原始专利权人州/省" analyzer="null" column_name="asoadp" description="原始专利权人州/省" name="asoadp">
            <multi_field analyzer="null" column_name="raw" description="原始专利权人州/省" name="asoadp_raw"/>
        </field>
        <field alias="原始专利权人地市/市" analyzer="null" column_name="asoadc" description="原始专利权人地市/市" name="asoadc">
            <multi_field analyzer="null" column_name="raw" description="原始专利权人地市/市" name="asoadc_raw"/>
        </field>
        <field alias="原始专利权人区县" analyzer="smart_analyzer" column_name="asoadd" description="原始专利权人区县" name="asoadd"/>
        <field alias="原始专利权人街道" analyzer="smart_analyzer" column_name="asoads" description="原始专利权人街道" name="asoads"/>
        <field alias="原始专利权人园区" analyzer="smart_analyzer" column_name="asoadz" description="原始专利权人园区" name="asoadz"/>
        <field alias="原始专利权人地址经纬度" analyzer="null" column_name="asoadll" description="原始专利权人地址经纬度" name="asoadll"/>
        <field alias="原始专利权人国省代码" analyzer="null" column_name="asoadcc" description="原始专利权人国省代码" name="asoadcc">
            <multi_field analyzer="null" column_name="raw" description="原始专利权人国省代码" name="asoadcc_raw"/>
        </field>
        <field alias="标准原始专利权人类型" analyzer="null" column_name="asost" description="标准原始专利权人类型" name="asost">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人类型" name="asost_raw"/>
        </field>
        <field alias="标准原始专利权人类型-中文" analyzer="null" column_name="asostcn" description="标准原始专利权人类型-中文" name="asostcn">
            <multi_field analyzer="null" column_name="raw" description="标准原始专利权人类型-中文" name="asostcn_raw"/>
        </field>
    </nested>
    <nested description="当前申请人" name="currenapplicantinfo">
        <columns>
            <column description="申请人组" is_array="true" name="pacg" not_null="false" type="tiny_text"/>
            <column description="当前申请人中文" is_array="true" name="apccn" not_null="false" type="tiny_text"/>
            <column description="当前申请人英文" is_array="true" name="apcen" not_null="false" type="tiny_text"/>
            <column description="当前申请人日文" is_array="true" name="apcjp" not_null="false" type="tiny_text"/>
            <column description="当前申请人原始" is_array="true" name="apco" not_null="false" type="tiny_text"/>
            <column description="标准当前申请人中文" is_array="true" name="apcsdcn" not_null="false" type="tiny_text"/>
            <column description="标准当前申请人英文" is_array="true" name="apcsden" not_null="false" type="tiny_text"/>
            <column description="标准当前申请人日文" is_array="true" name="apcsdjp" not_null="false" type="tiny_text"/>
            <column description="标准当前申请人原文" is_array="true" name="apcsdo" not_null="false" type="tiny_text"/>
            <column description="当前申请人地址中文" is_array="true" name="apcadcn" not_null="false" type="long_text"/>
            <column description="当前申请人地址英文" is_array="true" name="apcaden" not_null="false" type="long_text"/>
            <column description="当前申请人地址日文" is_array="true" name="apcadjp" not_null="false" type="long_text"/>
            <column description="当前申请人地址原文" is_array="true" name="apcado" not_null="false" type="long_text"/>
            <column description="当前申请人国家" is_array="true" name="apcadco" not_null="false" type="item_no"/>
            <column description="当前申请人州/省" is_array="true" name="apcadp" not_null="false" type="tiny_text"/>
            <column description="当前申请人地市" is_array="true" name="apcadc" not_null="false" type="tiny_text"/>
            <column description="当前申请人街道" is_array="true" name="apcads" not_null="false" type="long_text"/>
            <column description="当前申请人区县" is_array="true" name="apcadd" not_null="false" type="tiny_text"/>
            <column description="当前申请人园区" is_array="true" name="apcadz" not_null="false" type="long_text"/>
            <column description="当前申请人国省代码" is_array="true" name="apcadcc" not_null="false" type="item_no"/>
            <column description="标准当前申请人类型" is_array="true" name="apcst" not_null="false" type="item_no"/>
            <column description="标准当前申请人类型-中文" is_array="true" name="apcstcn" not_null="false" type="item_no"/>
            <column description="当前申请人地址经纬度" is_array="true" name="apcaddrll" not_null="false" type="geo_point"/>
        </columns>
        <field alias="申请人组" analyzer="null" column_name="pacg" description="申请人组" name="pacg">
            <multi_field analyzer="null" column_name="raw" description="申请人组" name="pacg_raw"/>
        </field>
        <field alias="当前申请人中文" analyzer="smart_analyzer" column_name="apccn" description="当前申请人中文" name="apccn">
            <multi_field analyzer="null" column_name="raw" description="当前申请人中文" name="apccn_raw"/>
        </field>
        <field alias="当前申请人英文" analyzer="smart_analyzer" column_name="apcen" description="当前申请人英文" name="apcen">
            <multi_field analyzer="null" column_name="raw" description="当前申请人英文" name="apcen_raw"/>
        </field>
        <field alias="当前申请人日文" analyzer="smart_analyzer" column_name="apcjp" description="当前申请人日文" name="apcjp">
            <multi_field analyzer="null" column_name="raw" description="当前申请人日文" name="apcjp_raw"/>
        </field>
        <field alias="当前申请人原始" analyzer="smart_analyzer" column_name="apco" description="当前申请人原始" name="apco">
            <multi_field analyzer="null" column_name="raw" description="当前申请人原始" name="apco_raw"/>
        </field>
        <field alias="标准当前申请人中文" analyzer="smart_analyzer" column_name="apcsdcn" description="标准当前申请人中文" name="apcsdcn">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人中文" name="apcsdcn_raw"/>
        </field>
        <field alias="标准当前申请人英文" analyzer="smart_analyzer" column_name="apcsden" description="标准当前申请人英文" name="apcsden">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人英文" name="apcsden_raw"/>
        </field>
        <field alias="标准当前申请人日文" analyzer="smart_analyzer" column_name="apcsdjp" description="标准当前申请人日文" name="apcsdjp">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人日文" name="apcsdjp_raw"/>
        </field>
        <field alias="标准当前申请人原文" analyzer="smart_analyzer" column_name="apcsdo" description="标准当前申请人原文" name="apcsdo">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人原文" name="apcsdo_raw"/>
        </field>
        <field alias="当前申请人地址中文" analyzer="smart_analyzer" column_name="apcadcn" description="当前申请人地址中文" name="apcadcn"/>
        <field alias="当前申请人地址英文" analyzer="smart_analyzer" column_name="apcaden" description="当前申请人地址英文" name="apcaden"/>
        <field alias="当前申请人地址日文" analyzer="smart_analyzer" column_name="apcadjp" description="当前申请人地址日文" name="apcadjp"/>
        <field alias="当前申请人地址原文" analyzer="smart_analyzer" column_name="apcado" description="当前申请人地址原文" name="apcado"/>
        <field alias="当前申请人国家" analyzer="null" column_name="apcadco" description="当前申请人国家" name="apcadco">
            <multi_field analyzer="null" column_name="raw" description="当前申请人国家" name="apcadco_raw"/>
        </field>
        <field alias="当前申请人州/省" analyzer="null" column_name="apcadp" description="当前申请人州/省" name="apcadp">
            <multi_field analyzer="null" column_name="raw" description="当前申请人州/省" name="apcadp_raw"/>
        </field>
        <field alias="当前申请人地市" analyzer="null" column_name="apcadc" description="当前申请人地市" name="apcadc">
            <multi_field analyzer="null" column_name="raw" description="当前申请人地市" name="apcadc_raw"/>
        </field>
        <field alias="当前申请人街道" analyzer="smart_analyzer" column_name="apcads" description="当前申请人街道" name="apcads"/>
        <field alias="当前申请人区县" analyzer="null" column_name="apcadd" description="当前申请人区县" name="apcadd">
            <multi_field analyzer="null" column_name="raw" description="当前申请人区县" name="apcadd_raw"/>
        </field>
        <field alias="当前申请人园区" analyzer="smart_analyzer" column_name="apcadz" description="当前申请人园区" name="apcadz"/>
        <field alias="当前申请人国省代码" analyzer="null" column_name="apcadcc" description="当前申请人国省代码" name="apcadcc">
            <multi_field analyzer="null" column_name="raw" description="当前申请人国省代码" name="apcadcc_raw"/>
        </field>
        <field alias="标准当前申请人类型" analyzer="null" column_name="apcst" description="标准当前申请人类型" name="apcst">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人类型" name="apcst_raw"/>
        </field>
        <field alias="标准当前申请人类型-中文" analyzer="null" column_name="apcstcn" description="标准当前申请人类型-中文" name="apcstcn">
            <multi_field analyzer="null" column_name="raw" description="标准当前申请人类型-中文" name="apcstcn_raw"/>
        </field>
        <field alias="当前申请人地址经纬度" analyzer="null" column_name="apcaddrll" description="当前申请人地址经纬度" name="apcaddrll"/>
    </nested>
    <nested description="当前专利权人" name="currentassigneeinfo">
        <columns>
            <column description="当前专利权人中文" is_array="true" name="asccn" not_null="false" type="tiny_text"/>
            <column description="当前专利权人英文" is_array="true" name="ascen" not_null="false" type="tiny_text"/>
            <column description="当前专利权人日文" is_array="true" name="ascjp" not_null="false" type="tiny_text"/>
            <column description="当前专利权人原始" is_array="true" name="asco" not_null="false" type="tiny_text"/>
            <column description="标准当前专利权人中文" is_array="true" name="ascscn" not_null="false" type="tiny_text"/>
            <column description="标准当前专利权人英文" is_array="true" name="ascsen" not_null="false" type="tiny_text"/>
            <column description="标准当前专利权人日文" is_array="true" name="ascsjp" not_null="false" type="tiny_text"/>
            <column description="标准当前专利权人原文" is_array="true" name="ascso" not_null="false" type="tiny_text"/>
            <column description="当前专利权人地址中文" is_array="true" name="ascadcn" not_null="false" type="long_text"/>
            <column description="当前专利权人地址英文" is_array="true" name="ascaden" not_null="false" type="long_text"/>
            <column description="当前专利权人地址日文" is_array="true" name="ascadjp" not_null="false" type="long_text"/>
            <column description="当前专利权人地址原文" is_array="true" name="ascado" not_null="false" type="long_text"/>
            <column description="当前专利权人地址经纬度" is_array="true" name="ascadll" not_null="false" type="geo_point"/>
            <column description="当前专利权人国家" is_array="true" name="ascadco" not_null="false" type="item_no"/>
            <column description="当前专利权人州/省" is_array="true" name="ascadp" not_null="false" type="tiny_text"/>
            <column description="当前专利权人地市" is_array="true" name="ascadc" not_null="false" type="tiny_text"/>
            <column description="当前专利权人街道" is_array="true" name="ascads" not_null="false" type="long_text"/>
            <column description="当前专利权人区县" is_array="true" name="ascadd" not_null="false" type="tiny_text"/>
            <column description="当前专利权人州/省(统计字段)" is_array="true" name="ascadp_su" not_null="false" type="item_no"/>
            <column description="当前专利权人地市(统计字段)" is_array="true" name="ascadc_su" not_null="false" type="item_no"/>
            <column description="当前专利权人区县(统计字段)" is_array="true" name="ascadd_su" not_null="false" type="item_no"/>
            <column description="当前专利权人园区" is_array="true" name="ascadz" not_null="false" type="long_text"/>
            <column description="当前专利权人国省代码" is_array="true" name="ascadcc" not_null="false" type="item_no"/>
            <column description="标准当前专利权人类型" is_array="true" name="ascst" not_null="false" type="item_no"/>
            <column description="标准当前专利权人类型-中文" is_array="true" name="ascstcn" not_null="false" type="item_no"/>
        </columns>
        <field alias="当前专利权人中文" analyzer="smart_analyzer" column_name="asccn" description="当前专利权人中文" name="asccn">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人中文" name="asccn_raw"/>
        </field>
        <field alias="当前专利权人英文" analyzer="smart_analyzer" column_name="ascen" description="当前专利权人英文" name="ascen">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人英文" name="ascen_raw"/>
        </field>
        <field alias="当前专利权人日文" analyzer="smart_analyzer" column_name="ascjp" description="当前专利权人日文" name="ascjp">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人日文" name="ascjp_raw"/>
        </field>
        <field alias="当前专利权人原始" analyzer="smart_analyzer" column_name="asco" description="当前专利权人原始" name="asco">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人原始" name="asco_raw"/>
        </field>
        <field alias="标准当前专利权人中文" analyzer="smart_analyzer" column_name="ascscn" description="标准当前专利权人中文" name="ascscn">
            <multi_field analyzer="null" column_name="raw" description="标准当前专利权人中文" name="ascscn_raw"/>
        </field>
        <field alias="标准当前专利权人英文" analyzer="smart_analyzer" column_name="ascsen" description="标准当前专利权人英文" name="ascsen">
            <multi_field analyzer="null" column_name="raw" description="标准当前专利权人英文" name="ascsen_raw"/>
        </field>
        <field alias="标准当前专利权人日文" analyzer="smart_analyzer" column_name="ascsjp" description="标准当前专利权人日文" name="ascsjp">
            <multi_field analyzer="null" column_name="raw" description="标准当前专利权人日文" name="ascsjp_raw"/>
        </field>
        <field alias="标准当前专利权人原文" analyzer="smart_analyzer" column_name="ascso" description="标准当前专利权人原文" name="ascso">
            <multi_field analyzer="null" column_name="raw" description="标准当前专利权人原文" name="ascso_raw"/>
        </field>
        <field alias="当前专利权人地址中文" analyzer="smart_analyzer" column_name="ascadcn" description="当前专利权人地址中文" name="ascadcn"/>
        <field alias="当前专利权人地址英文" analyzer="smart_analyzer" column_name="ascaden" description="当前专利权人地址英文" name="ascaden"/>
        <field alias="当前专利权人地址日文" analyzer="smart_analyzer" column_name="ascadjp" description="当前专利权人地址日文" name="ascadjp"/>
        <field alias="当前专利权人地址原文" analyzer="smart_analyzer" column_name="ascado" description="当前专利权人地址原文" name="ascado"/>
        <field alias="当前专利权人地址经纬度" analyzer="smart_analyzer" column_name="ascadll" description="当前专利权人地址经纬度" name="ascadll"/>
        <field alias="当前专利权人国家" analyzer="null" column_name="ascadco" description="当前专利权人国家" name="ascadco">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人国家" name="ascadco_raw"/>
        </field>
        <field alias="当前专利权人州/省" analyzer="null" column_name="ascadp" description="当前专利权人州/省" name="ascadp">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人州/省" name="ascadp_raw"/>
        </field>
        <field alias="当前专利权人地市" analyzer="null" column_name="ascadc" description="当前专利权人地市" name="ascadc">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人地市" name="ascadc_raw"/>
        </field>
        <field alias="当前专利权人街道" analyzer="smart_analyzer" column_name="ascads" description="当前专利权人街道" name="ascads"/>
        <field alias="当前专利权人区县" analyzer="null" column_name="ascadd" description="当前专利权人区县" name="ascadd">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人区县" name="ascadd_raw"/>
        </field>
        <field alias="当前专利权人州/省(统计字段)" analyzer="null" column_name="ascadp_su" description="当前专利权人州/省(统计字段)" name="ascadp_su"/>
        <field alias="当前专利权人地市(统计字段)" analyzer="null" column_name="ascadc_su" description="当前专利权人地市(统计字段)" name="ascadc_su"/>
        <field alias="当前专利权人区县(统计字段)" analyzer="null" column_name="ascadd_su" description="当前专利权人区县(统计字段)" name="ascadd_su"/>
        <field alias="当前专利权人园区" analyzer="smart_analyzer" column_name="ascadz" description="当前专利权人园区" name="ascadz"/>
        <field alias="当前专利权人国省代码" analyzer="null" column_name="ascadcc" description="当前专利权人国省代码" name="ascadcc">
            <multi_field analyzer="null" column_name="raw" description="当前专利权人国省代码" name="ascadcc_raw"/>
        </field>
        <field alias="标准当前专利权人类型" analyzer="null" column_name="ascst" description="标准当前专利权人类型" name="ascst"/>
        <field alias="标准当前专利权人类型-中文" analyzer="null" column_name="ascstcn" description="标准当前专利权人类型-中文" name="ascstcn">
            <multi_field analyzer="null" column_name="raw" description="标准当前专利权人类型-中文" name="ascstcn_raw"/>
        </field>
    </nested>
    <nested description="发明人信息" name="inventorinfo">
        <columns>
            <column description="发明人中文" is_array="true" name="incn" not_null="false" type="tiny_text"/>
            <column description="发明人英文" is_array="true" name="inen" not_null="false" type="tiny_text"/>
            <column description="发明人日文" is_array="true" name="injp" not_null="false" type="tiny_text"/>
            <column description="发明人原文" is_array="true" name="ino" not_null="false" type="tiny_text"/>
            <column description="发明人地址" is_array="true" name="inad" not_null="false" type="long_text"/>
            <column description="发明人国别" is_array="true" name="inadco" not_null="false" type="item_no"/>
            <column description="发明人城市" is_array="true" name="inadc" not_null="false" type="tiny_text"/>
            <column description="发明人所在州" is_array="true" name="inadp" not_null="false" type="long_text"/>
            <column description="发明人国别(统计字段)" is_array="true" name="inadco_su" not_null="false" type="item_no"/>
            <column description="发明人城市(统计字段)" is_array="true" name="inadc_su" not_null="false" type="item_no"/>
        </columns>
        <field alias="发明人中文" analyzer="smart_analyzer" column_name="incn" description="发明人中文" name="incn">
            <multi_field analyzer="null" column_name="raw" description="发明人中文" name="incn_raw"/>
        </field>
        <field alias="发明人英文" analyzer="smart_analyzer" column_name="inen" description="发明人英文" name="inen">
            <multi_field analyzer="null" column_name="raw" description="发明人英文" name="inen_raw"/>
        </field>
        <field alias="发明人日文" analyzer="smart_analyzer" column_name="injp" description="发明人日文" name="injp">
            <multi_field analyzer="null" column_name="raw" description="发明人日文" name="injp_raw"/>
        </field>
        <field alias="发明人原文" analyzer="smart_analyzer" column_name="ino" description="发明人原文" name="ino">
            <multi_field analyzer="null" column_name="raw" description="发明人原文" name="ino_raw"/>
        </field>
        <field alias="发明人地址" analyzer="smart_analyzer" column_name="inad" description="发明人地址" name="inad"/>
        <field alias="发明人国别" analyzer="null" column_name="inadco" description="发明人国别" name="inadco">
            <multi_field analyzer="null" column_name="raw" description="发明人国别" name="inadco_raw"/>
        </field>
        <field alias="发明人城市" analyzer="null" column_name="inadc" description="发明人城市" name="inadc">
            <multi_field analyzer="null" column_name="raw" description="发明人城市" name="inadc_raw"/>
        </field>
        <field alias="发明人所在州" analyzer="smart_analyzer" column_name="inadp" description="发明人所在州" name="inadp"/>
        <field alias="发明人国别(统计字段)" analyzer="null" column_name="inadco_su" description="发明人国别(统计字段)" name="inadco_su"/>
        <field alias="发明人城市(统计字段)" analyzer="null" column_name="inadc_su" description="发明人城市(统计字段)" name="inadc_su"/>
    </nested>
    <nested description="发明人信息" name="currentinventorinfo">
        <columns>
            <column description="当前发明人中文" is_array="true" name="inccn" not_null="false" type="tiny_text"/>
            <column description="当前发明人英文" is_array="true" name="incen" not_null="false" type="tiny_text"/>
            <column description="当前发明人日文" is_array="true" name="incjp" not_null="false" type="tiny_text"/>
            <column description="当前发明人原始" is_array="true" name="inco" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="当前发明人中文" analyzer="smart_analyzer" column_name="inccn" description="当前发明人中文" name="inccn">
            <multi_field analyzer="null" column_name="raw" description="当前发明人中文" name="inccn_raw"/>
        </field>
        <field alias="当前发明人英文" analyzer="smart_analyzer" column_name="incen" description="当前发明人英文" name="incen">
            <multi_field analyzer="null" column_name="raw" description="当前发明人英文" name="incen_raw"/>
        </field>
        <field alias="当前发明人日文" analyzer="smart_analyzer" column_name="incjp" description="当前发明人日文" name="incjp">
            <multi_field analyzer="null" column_name="raw" description="当前发明人日文" name="incjp_raw"/>
        </field>
        <field alias="当前发明人原始" analyzer="smart_analyzer" column_name="inco" description="当前发明人原始" name="inco">
            <multi_field analyzer="null" column_name="raw" description="当前发明人原始" name="inco_raw"/>
        </field>
    </nested>
    <nested description="助理审查员" name="examiner_assiant">
        <columns>
            <column description="助理审查员中文" is_array="true" name="aexcn" not_null="false" type="tiny_text"/>
            <column description="助理审查员英文" is_array="true" name="aexen" not_null="false" type="tiny_text"/>
            <column description="助理审查员日文" is_array="true" name="aexjp" not_null="false" type="tiny_text"/>
            <column description="助理审查员原始" is_array="true" name="aexo" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="助理审查员中文" analyzer="smart_analyzer" column_name="aexcn" description="助理审查员中文" name="aexcn">
            <multi_field analyzer="null" column_name="raw" description="助理审查员中文" name="aexcn_raw"/>
        </field>
        <field alias="助理审查员英文" analyzer="smart_analyzer" column_name="aexen" description="助理审查员英文" name="aexen">
            <multi_field analyzer="null" column_name="raw" description="助理审查员英文" name="aexen_raw"/>
        </field>
        <field alias="助理审查员日文" analyzer="smart_analyzer" column_name="aexjp" description="助理审查员日文" name="aexjp">
            <multi_field analyzer="null" column_name="raw" description="助理审查员日文" name="aexjp_raw"/>
        </field>
        <field alias="助理审查员原始" analyzer="smart_analyzer" column_name="aexo" description="助理审查员原始" name="aexo">
            <multi_field analyzer="null" column_name="raw" description="助理审查员原始" name="aexo_raw"/>
        </field>
    </nested>
    <nested description="优先权" name="priority">
        <columns>
            <column description="优先权日" is_array="true" name="prd" not_null="false" type="date"/>
            <column description="优先权年" is_array="true" name="prdy" not_null="false" type="date"/>
            <column description="优先权年月" is_array="true" name="prdym" not_null="false" type="date"/>
            <column description="优先权号原始" is_array="true" name="prno" not_null="false" type="item_no"/>
            <column description="优先权号DOCDB" is_array="true" name="prndb" not_null="false" type="item_no"/>
            <column description="优先权号EPO" is_array="true" name="prnep" not_null="false" type="item_no"/>
            <column description="优先权号号码原始" is_array="true" name="prnno" not_null="false" type="item_no"/>
            
            <column description="优先权号号码DOCDB" is_array="true" name="prnndb" not_null="false" type="item_no"/>
            <column description="优先权号号码EPO" is_array="true" name="prnne" not_null="false" type="item_no"/>
            <column description="优先权类型" is_array="true" name="prt" not_null="false" type="item_no"/>
            <column description="优先权国别" is_array="true" name="prc" not_null="false" type="item_no"/>
        </columns>
        <field alias="优先权日" analyzer="null" column_name="prd" description="优先权日" name="prd"/>
        <field alias="优先权年" analyzer="null" column_name="prdy" description="优先权年" name="prdy"/>
        <field alias="优先权年月" analyzer="null" column_name="prdym" description="优先权年月" name="prdym"/>
        <field alias="优先权号原始" analyzer="null" column_name="prno" description="优先权号原始" name="prno">
            <multi_field analyzer="null" column_name="raw" description="优先权号原始" name="prno_raw"/>
        </field>
        <field alias="优先权号DOCDB" analyzer="null" column_name="prndb" description="优先权号DOCDB" name="prndb">
            <multi_field analyzer="null" column_name="raw" description="优先权号DOCDB" name="prndb_raw"/>
        </field>
        <field alias="优先权号EPO" analyzer="null" column_name="prnep" description="优先权号EPO" name="prnep">
            <multi_field analyzer="null" column_name="raw" description="优先权号EPO" name="prnep_raw"/>
        </field>
        <field alias="优先权号号码原始" analyzer="null" column_name="prnno" description="优先权号号码原始" name="prnno">
            <multi_field analyzer="null" column_name="raw" description="优先权号号码原始" name="prnno_raw"/>
        </field>
        <field alias="优先权号号码DOCDB" analyzer="null" column_name="prnndb" description="优先权号号码DOCDB" name="prnndb">
            <multi_field analyzer="null" column_name="raw" description="优先权号号码DOCDB" name="prnndb_raw"/>
        </field>
        <field alias="优先权号号码EPO" analyzer="null" column_name="prnne" description="优先权号号码EPO" name="prnne">
            <multi_field analyzer="null" column_name="raw" description="优先权号号码EPO" name="prnne_raw"/>
        </field>
        <field alias="优先权类型" analyzer="null" column_name="prt" description="优先权类型" name="prt">
            <multi_field analyzer="null" column_name="raw" description="优先权类型" name="prt_raw"/>
        </field>
        <field alias="优先权国别" analyzer="null" column_name="prc" description="优先权国别" name="prc"/>
    </nested>
    <nested description="引证信息" name="citation">
        <columns>
            <column description="引证申请日" is_array="true" name="ctad" not_null="false" type="date"/>
            <column description="引证公开日" is_array="true" name="ctpd" not_null="false" type="date"/>
            <column description="引证公开年" is_array="true" name="ctpdy" not_null="false" type="date"/>
            <column description="引证申请人" is_array="true" name="ctap" not_null="false" type="tiny_text"/>
            <column description="引证专利公开号" is_array="true" name="ctpn" not_null="false" type="item_no"/>
            <column description="引证专利申请号" is_array="true" name="ctan" not_null="false" type="item_no"/>
            <column description="[标准]引证申请人" is_array="true" name="ctaps" not_null="false" type="tiny_text"/>
            <column description="引证国别" is_array="true" name="ctco" not_null="false" type="item_no"/>
            <column description="引证类别" is_array="true" name="ctcg" not_null="false" type="item_no"/>
            <column description="引证方" is_array="true" name="ctp" not_null="false" type="item_no"/>
            <column description="引证来源" is_array="true" name="cts" not_null="false" type="item_no"/>
            <column description="非专利引证" is_array="true" name="ctnp" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="引证申请日" analyzer="null" column_name="ctad" description="引证申请日" name="ctad"/>
        <field alias="引证公开日" analyzer="null" column_name="ctpd" description="引证公开日" name="ctpd"/>
        <field alias="引证公开年" analyzer="null" column_name="ctpdy" description="引证公开年" name="ctpdy"/>
        <field alias="引证申请人" analyzer="smart_analyzer" column_name="ctap" description="引证申请人" name="ctap">
            <multi_field analyzer="null" column_name="raw" description="引证申请人" name="ctap_raw"/>
        </field>
        <field alias="引证专利公开号" analyzer="null" column_name="ctpn" description="引证专利公开号" name="ctpn">
            <multi_field analyzer="null" column_name="raw" description="引证专利公开号" name="ctpn_raw"/>
        </field>
        <field alias="引证专利申请号" analyzer="null" column_name="ctan" description="引证专利申请号" name="ctan">
            <multi_field analyzer="null" column_name="raw" description="引证专利申请号" name="ctan_raw"/>
        </field>
        <field alias="[标准]引证申请人" analyzer="smart_analyzer" column_name="ctaps" description="[标准]引证申请人" name="ctaps">
            <multi_field analyzer="null" column_name="raw" description="[标准]引证申请人" name="ctaps_raw"/>
        </field>
        <field alias="引证国别" analyzer="null" column_name="ctco" description="引证国别" name="ctco"/>
        <field alias="引证类别" analyzer="null" column_name="ctcg" description="引证类别" name="ctcg">
            <multi_field analyzer="null" column_name="raw" description="引证类别" name="ctcg_raw"/>
        </field>
        <field alias="引证方" analyzer="null" column_name="ctp" description="引证方" name="ctp">
            <multi_field analyzer="null" column_name="raw" description="引证方" name="ctp_raw"/>
        </field>
        <field alias="引证来源" analyzer="smart_analyzer" column_name="cts" description="引证来源" name="cts">
            <multi_field analyzer="null" column_name="raw" description="引证来源" name="cts_raw"/>
        </field>
        <field alias="非专利引证" analyzer="null" column_name="ctnp" description="非专利引证" name="ctnp"/>
    </nested>
    <nested description="被引证信息" name="cited">
        <columns>
            <column description="被引证申请日" is_array="true" name="ctgad" not_null="false" type="date"/>
            <column description="被引证公开日" is_array="true" name="ctgpd" not_null="false" type="date"/>
            <column description="被引证申请人" is_array="true" name="ctgap" not_null="false" type="tiny_text"/>
            <column description="被引证专利公开号" is_array="true" name="ctgpn" not_null="false" type="item_no"/>
            <column description="被引证专利申请号" is_array="true" name="ctgan" not_null="false" type="item_no"/>
            <column description="[标准]被引证申请人" is_array="true" name="ctgaps" not_null="false" type="tiny_text"/>
            <column description="被引证当前申请人" is_array="true" name="ctgapc" not_null="false" type="tiny_text"/>
            <column description="被引证国别" is_array="true" name="ctgco" not_null="false" type="item_no"/>
            <column description="被引方" is_array="true" name="ctgp" not_null="false" type="item_no"/>
            <column description="被引来源" is_array="true" name="ctgs" not_null="false" type="item_no"/>
        </columns>
        <field alias="被引证申请日" analyzer="null" column_name="ctgad" description="被引证申请日" name="ctgad"/>
        <field alias="被引证公开日" analyzer="null" column_name="ctgpd" description="被引证公开日" name="ctgpd"/>
        <field alias="被引证申请人" analyzer="smart_analyzer" column_name="ctgap" description="被引证申请人" name="ctgap">
            <multi_field analyzer="null" column_name="raw" description="被引证申请人" name="ctgap_raw"/>
        </field>
        <field alias="被引证专利公开号" analyzer="null" column_name="ctgpn" description="被引证专利公开号" name="ctgpn">
            <multi_field analyzer="null" column_name="raw" description="被引证专利公开号" name="ctgpn_raw"/>
        </field>
        <field alias="被引证专利申请号" analyzer="null" column_name="ctgan" description="被引证专利申请号" name="ctgan">
            <multi_field analyzer="null" column_name="raw" description="被引证专利申请号" name="ctgan_raw"/>
        </field>
        <field alias="[标准]被引证申请人" analyzer="smart_analyzer" column_name="ctgaps" description="[标准]被引证申请人" name="ctgaps">
            <multi_field analyzer="null" column_name="raw" description="[标准]被引证申请人" name="ctgaps_raw"/>
        </field>
        <field alias="被引证当前申请人" analyzer="smart_analyzer" column_name="ctgapc" description="被引证当前申请人" name="ctgapc">
            <multi_field analyzer="null" column_name="raw" description="被引证当前申请人" name="ctgapc_raw"/>
        </field>
        <field alias="被引证国别" analyzer="null" column_name="ctgco" description="被引证国别" name="ctgco"/>
        <field alias="被引方" analyzer="null" column_name="ctgp" description="被引方" name="ctgp">
            <multi_field analyzer="null" column_name="raw" description="被引方" name="ctgp_raw"/>
        </field>
        <field alias="被引来源" analyzer="null" column_name="ctgs" description="被引来源" name="ctgs">
            <multi_field analyzer="null" column_name="raw" description="被引来源" name="ctgs_raw"/>
        </field>
    </nested>
    <nested description="同族信息" name="patentfamilysim">
        <columns>
            <column description="简单同族" is_array="true" name="sf" not_null="false" type="item_no"/>
            <column description="DOCDB同族" is_array="true" name="docdbf" not_null="false" type="item_no"/>
            <column description="扩展同族" is_array="true" name="ef" not_null="false" type="item_no"/>
            <column description="简单同族国家/地区" is_array="true" name="sfco" not_null="false" type="tiny_text"/>
            <column description="扩展同族国家/地区" is_array="true" name="efco" not_null="false" type="long_text"/>
            <column description="简单同族首次公开日" is_array="true" name="sfpdf" not_null="false" type="date"/>
            <column description="扩展同族首次公开日" is_array="true" name="efpdf" not_null="false" type="date"/>
        </columns>
        <field alias="简单同族" analyzer="null" column_name="sf" description="简单同族" name="sf">
            <multi_field analyzer="null" column_name="raw" description="简单同族" name="sf_raw"/>
        </field>
        <field alias="DOCDB同族" analyzer="null" column_name="docdbf" description="DOCDB同族" name="docdbf">
            <multi_field analyzer="null" column_name="raw" description="DOCDB同族" name="docdbf_raw"/>
        </field>
        <field alias="扩展同族" analyzer="null" column_name="ef" description="扩展同族" name="ef">
            <multi_field analyzer="null" column_name="raw" description="扩展同族" name="ef_raw"/>
        </field>
        <field alias="简单同族国家/地区" analyzer="smart_analyzer" column_name="sfco" description="简单同族国家/地区" name="sfco">
            <multi_field analyzer="null" column_name="raw" description="简单同族国家/地区" name="sfco_raw"/>
        </field>
        <field alias="扩展同族国家/地区" analyzer="smart_analyzer" column_name="efco" description="扩展同族国家/地区" name="efco"/>
        <field alias="简单同族首次公开日" analyzer="null" column_name="sfpdf" description="简单同族首次公开日" name="sfpdf"/>
        <field alias="扩展同族首次公开日" analyzer="null" column_name="efpdf" description="扩展同族首次公开日" name="efpdf"/>
    </nested>
    <nested description="法律状态" name="lawstatus">
        <columns>
            <column description="法律事件分类(INPADOC)" is_array="true" name="leci" not_null="false" type="item_no"/>
            <column description="法律事件代码联合检索(INPADOC)" is_array="true" name="leusi" not_null="false" type="tiny_text"/>
            <column description="法律事件联合检索(中国)" is_array="true" name="leus" not_null="false" type="tiny_text"/>
            <column description="法律状态文字信息" is_array="true" name="lst" not_null="false" type="long_text"/>
            <column description="延长保护期" is_array="true" name="expt" not_null="false" type="integer"/>
            <column description="驳回日" is_array="true" name="rd" not_null="false" type="date"/>
            <column description="撤回日" is_array="true" name="wd" not_null="false" type="date"/>
            <column description="解密日" is_array="true" name="dcd" not_null="false" type="date"/>
            <column description="法律状态由未授权或失效变为有效" is_array="true" name="itvd" not_null="false" type="date"/>
        </columns>
        <field alias="法律事件分类(INPADOC)" analyzer="null" column_name="leci" description="法律事件分类(INPADOC)" name="leci">
            <multi_field analyzer="null" column_name="raw" description="法律事件分类(INPADOC)" name="leci_raw"/>
        </field>
        <field alias="法律事件代码联合检索(INPADOC)" analyzer="smart_analyzer" column_name="leusi" description="法律事件代码联合检索(INPADOC)" name="leusi">
            <multi_field analyzer="null" column_name="raw" description="法律事件代码联合检索(INPADOC)" name="leusi_raw"/>
        </field>
        <field alias="法律事件联合检索(中国)" analyzer="smart_analyzer" column_name="leus" description="法律事件联合检索(中国)" name="leus">
            <multi_field analyzer="null" column_name="raw" description="法律事件联合检索(中国)" name="leus_raw"/>
        </field>
        <field alias="法律状态文字信息" analyzer="smart_analyzer" column_name="lst" description="法律状态文字信息" name="lst"/>
        <field alias="延长保护期" analyzer="null" column_name="expt" description="延长保护期" name="expt"/>
        <field alias="驳回日" analyzer="null" column_name="rd" description="驳回日" name="rd"/>
        <field alias="撤回日" analyzer="null" column_name="wd" description="撤回日" name="wd"/>
        <field alias="解密日" analyzer="null" column_name="dcd" description="解密日" name="dcd"/>
        <field alias="法律状态由未授权或失效变为有效" analyzer="null" column_name="itvd" description="法律状态由未授权或失效变为有效" name="itvd"/>
    </nested>
    <nested description="诉讼信息" name="litigations">
        <columns>
            <column description="案件号" is_array="true" name="licn" not_null="false" type="tiny_text"/>
            <column description="上级案号" is_array="true" name="liscn" not_null="false" type="tiny_text"/>
            <column description="审理法院" is_array="true" name="lico" not_null="false" type="tiny_text"/>
            <column description="审判员" is_array="true" name="liju" not_null="false" type="tiny_text"/>
            <column description="陪审员" is_array="true" name="liaju" not_null="false" type="tiny_text"/>
            <column description="审判长" is_array="true" name="lipj" not_null="false" type="tiny_text"/>
            <column description="原告" is_array="true" name="lipl" not_null="false" type="tiny_text"/>
            <column description="[标准]原告" is_array="true" name="lipls" not_null="false" type="tiny_text"/>
            <column description="被告" is_array="true" name="lide" not_null="false" type="tiny_text"/>
            <column description="[标准]被告" is_array="true" name="lides" not_null="false" type="tiny_text"/>
            <column description="第三人" is_array="true" name="litp" not_null="false" type="tiny_text"/>
            <column description="[标准]第三人" is_array="true" name="litps" not_null="false" type="tiny_text"/>
            <column description="立案日期" is_array="true" name="lifd" not_null="false" type="date"/>
            <column description="文书日期" is_array="true" name="livd" not_null="false" type="date"/>
            <column description="审理程序" is_array="true" name="litg" not_null="false" type="item_no"/>
            <column description="诉讼案由" is_array="true" name="lica" not_null="false" type="tiny_text"/>
            <column description="案件类型" is_array="true" name="lint" not_null="false" type="item_no"/>
            <column description="案件标题" is_array="true" name="liti" not_null="false" type="tiny_text"/>
            <column description="案件全文" is_array="true" name="lift" not_null="false" type="long_text"/>
            <column description="结案日期" is_array="true" name="lijd" not_null="false" type="date"/>
            <column description="胜诉方" is_array="true" name="lipp" not_null="false" type="tiny_text"/>
            <column description="案件状态" is_array="true" name="lics" not_null="false" type="item_no"/>
            <column description="文书性质" is_array="true" name="lipc" not_null="false" type="item_no"/>
            <column description="法院级别" is_array="true" name="licg" not_null="false" type="item_no"/>
            <column description="法院所属省/市" is_array="true" name="licp" not_null="false" type="tiny_text"/>
            <column description="判决结果" is_array="true" name="lijv" not_null="false" type="tiny_text"/>
            <column description="申请赔偿总额" is_array="true" name="liaa" not_null="false" type="integer"/>
            <column description="判赔总额" is_array="true" name="lija" not_null="false" type="integer"/>
            <column description="立案年份" is_array="true" name="lify" not_null="false" type="date"/>
            <column description="涉及产品" is_array="true" name="liprd" not_null="false" type="tiny_text"/>
            <column description="原告代理人" is_array="true" name="liplag" not_null="false" type="long_text"/>
            <column description="原告代理机构" is_array="true" name="liplago" not_null="false" type="long_text"/>
            <column description="被告代理人" is_array="true" name="lideag" not_null="false" type="long_text"/>
            <column description="被告代理机构" is_array="true" name="lideago" not_null="false" type="long_text"/>
            <column description="第三代理人" is_array="true" name="litpag" not_null="false" type="long_text"/>
            <column description="第三代理机构" is_array="true" name="litpago" not_null="false" type="long_text"/>
        </columns>
        <field alias="案件号" analyzer="null" column_name="licn" description="案件号" name="licn"/>
        <field alias="上级案号" analyzer="null" column_name="liscn" description="上级案号" name="liscn"/>
        <field alias="审理法院" analyzer="smart_analyzer" column_name="lico" description="审理法院" name="lico">
            <multi_field analyzer="null" column_name="raw" description="审理法院" name="lico_raw"/>
        </field>
        <field alias="审判员" analyzer="smart_analyzer" column_name="liju" description="审判员" name="liju">
            <multi_field analyzer="null" column_name="raw" description="审判员" name="liju_raw"/>
        </field>
        <field alias="陪审员" analyzer="smart_analyzer" column_name="liaju" description="陪审员" name="liaju">
            <multi_field analyzer="null" column_name="raw" description="陪审员" name="liaju_raw"/>
        </field>
        <field alias="审判长" analyzer="smart_analyzer" column_name="lipj" description="审判长" name="lipj">
            <multi_field analyzer="null" column_name="raw" description="审判长" name="lipj_raw"/>
        </field>
        <field alias="原告" analyzer="smart_analyzer" column_name="lipl" description="原告" name="lipl">
            <multi_field analyzer="null" column_name="raw" description="原告" name="lipl_raw"/>
        </field>
        <field alias="[标准]原告" analyzer="smart_analyzer" column_name="lipls" description="[标准]原告" name="lipls">
            <multi_field analyzer="null" column_name="raw" description="[标准]原告" name="lipls_raw"/>
        </field>
        <field alias="被告" analyzer="smart_analyzer" column_name="lide" description="被告" name="lide">
            <multi_field analyzer="null" column_name="raw" description="被告" name="lide_raw"/>
        </field>
        <field alias="[标准]被告" analyzer="smart_analyzer" column_name="lides" description="[标准]被告" name="lides">
            <multi_field analyzer="null" column_name="raw" description="[标准]被告" name="lides_raw"/>
        </field>
        <field alias="第三人" analyzer="smart_analyzer" column_name="litp" description="第三人" name="litp">
            <multi_field analyzer="null" column_name="raw" description="第三人" name="litp_raw"/>
        </field>
        <field alias="[标准]第三人" analyzer="smart_analyzer" column_name="litps" description="[标准]第三人" name="litps">
            <multi_field analyzer="null" column_name="raw" description="[标准]第三人" name="litps_raw"/>
        </field>
        <field alias="立案日期" analyzer="null" column_name="lifd" description="立案日期" name="lifd"/>
        <field alias="文书日期" analyzer="null" column_name="livd" description="文书日期" name="livd"/>
        <field alias="审理程序" analyzer="null" column_name="litg" description="审理程序" name="litg">
            <multi_field analyzer="null" column_name="raw" description="审理程序" name="litg_raw"/>
        </field>
        <field alias="诉讼案由" analyzer="smart_analyzer" column_name="lica" description="诉讼案由" name="lica"/>
        <field alias="案件类型" analyzer="null" column_name="lint" description="案件类型" name="lint"/>
        <field alias="案件标题" analyzer="smart_analyzer" column_name="liti" description="案件标题" name="liti"/>
        <field alias="案件全文" analyzer="smart_analyzer" column_name="lift" description="案件全文" name="lift"/>
        <field alias="结案日期" analyzer="null" column_name="lijd" description="结案日期" name="lijd"/>
        <field alias="胜诉方" analyzer="smart_analyzer" column_name="lipp" description="胜诉方" name="lipp"/>
        <field alias="案件状态" analyzer="null" column_name="lics" description="案件状态" name="lics">
            <multi_field analyzer="null" column_name="raw" description="案件状态" name="lics_raw"/>
        </field>
        <field alias="文书性质" analyzer="null" column_name="lipc" description="文书性质" name="lipc">
            <multi_field analyzer="null" column_name="raw" description="文书性质" name="lipc_raw"/>
        </field>
        <field alias="法院级别" analyzer="null" column_name="licg" description="法院级别" name="licg">
            <multi_field analyzer="null" column_name="raw" description="法院级别" name="licg_raw"/>
        </field>
        <field alias="法院所属省/市" analyzer="null" column_name="licp" description="法院所属省/市" name="licp">
            <multi_field analyzer="null" column_name="raw" description="法院所属省/市" name="licp_raw"/>
        </field>
        <field alias="判决结果" analyzer="null" column_name="lijv" description="判决结果" name="lijv"/>
        <field alias="申请赔偿总额" analyzer="null" column_name="liaa" description="申请赔偿总额" name="liaa"/>
        <field alias="判赔总额" analyzer="null" column_name="lija" description="判赔总额" name="lija"/>
        <field alias="立案年份" analyzer="null" column_name="lify" description="立案年份" name="lify"/>
        <field alias="涉及产品" analyzer="smart_analyzer" column_name="liprd" description="涉及产品" name="liprd"/>
        <field alias="原告代理人" analyzer="smart_analyzer" column_name="liplag" description="原告代理人" name="liplag">
            <multi_field analyzer="null" column_name="raw" description="原告代理人" name="liplag_raw"/>
        </field>
        <field alias="原告代理机构" analyzer="smart_analyzer" column_name="liplago" description="原告代理机构" name="liplago"/>
        <field alias="被告代理人" analyzer="smart_analyzer" column_name="lideag" description="被告代理人" name="lideag">
            <multi_field analyzer="null" column_name="raw" description="被告代理人" name="lideag_raw"/>
        </field>
        <field alias="被告代理机构" analyzer="smart_analyzer" column_name="lideago" description="被告代理机构" name="lideago"/>
        <field alias="第三代理人" analyzer="smart_analyzer" column_name="litpag" description="第三代理人" name="litpag">
            <multi_field analyzer="null" column_name="raw" description="第三代理人" name="litpag_raw"/>
        </field>
        <field alias="第三代理机构" analyzer="smart_analyzer" column_name="litpago" description="第三代理机构" name="litpago"/>
    </nested>
    <nested description="许可信息" name="permissioninfo">
        <columns>
            <column description="许可类型-中文" is_array="true" name="licetcn" not_null="false" type="item_no"/>
            <column description="许可类型-代码" is_array="true" name="licetabbr" not_null="false" type="item_no"/>
            <column description="许可备案阶段-中文" is_array="true" name="licescn" not_null="false" type="item_no"/>
            <column description="许可备案阶段-代码" is_array="true" name="licesabbr" not_null="false" type="item_no"/>
            <column description="许可人" is_array="true" name="liceor" not_null="false" type="tiny_text"/>
            <column description="[标准]许可人" is_array="true" name="liceors" not_null="false" type="tiny_text"/>
            <column description="被许可人" is_array="true" name="liceee" not_null="false" type="tiny_text"/>
            <column description="[标准]被许可人" is_array="true" name="liceees" not_null="false" type="tiny_text"/>
            <column description="许可合同备案号" is_array="true" name="licen" not_null="false" type="item_no"/>
            <column description="许可生效日" is_array="true" name="liceed" not_null="false" type="date"/>
            <column description="许可生效年" is_array="true" name="liceey" not_null="false" type="date"/>
            <column description="许可合同履行起始日" is_array="true" name="licest" not_null="false" type="date"/>
            <column description="许可合同履行终止日" is_array="true" name="licetd" not_null="false" type="date"/>
        </columns>
        <field alias="许可类型-中文" analyzer="null" column_name="licetcn" description="许可类型-中文" name="licetcn"/>
        <field alias="许可类型-代码" analyzer="null" column_name="licetabbr" description="许可类型-代码" name="licetabbr">
            <multi_field analyzer="null" column_name="raw" description="许可类型-代码" name="licetabbr_raw"/>
        </field>
        <field alias="许可备案阶段-中文" analyzer="null" column_name="licescn" description="许可备案阶段-中文" name="licescn"/>
        <field alias="许可备案阶段-代码" analyzer="null" column_name="licesabbr" description="许可备案阶段-代码" name="licesabbr">
            <multi_field analyzer="null" column_name="raw" description="许可备案阶段-代码" name="licesabbr_raw"/>
        </field>
        <field alias="许可人" analyzer="smart_analyzer" column_name="liceor" description="许可人" name="liceor">
            <multi_field analyzer="null" column_name="raw" description="许可人" name="liceor_raw"/>
        </field>
        <field alias="[标准]许可人" analyzer="smart_analyzer" column_name="liceors" description="[标准]许可人" name="liceors">
            <multi_field analyzer="null" column_name="raw" description="[标准]许可人" name="liceors_raw"/>
        </field>
        <field alias="被许可人" analyzer="smart_analyzer" column_name="liceee" description="被许可人" name="liceee">
            <multi_field analyzer="null" column_name="raw" description="被许可人" name="liceee_raw"/>
        </field>
        <field alias="[标准]被许可人" analyzer="smart_analyzer" column_name="liceees" description="[标准]被许可人" name="liceees">
            <multi_field analyzer="null" column_name="raw" description="[标准]被许可人" name="liceees_raw"/>
        </field>
        <field alias="许可合同备案号" analyzer="null" column_name="licen" description="许可合同备案号" name="licen">
            <multi_field analyzer="null" column_name="raw" description="许可合同备案号" name="licen_raw"/>
        </field>
        <field alias="许可生效日" analyzer="null" column_name="liceed" description="许可生效日" name="liceed"/>
        <field alias="许可生效年" analyzer="null" column_name="liceey" description="许可生效年" name="liceey"/>
        <field alias="许可合同履行起始日" analyzer="null" column_name="licest" description="许可合同履行起始日" name="licest"/>
        <field alias="许可合同履行终止日" analyzer="null" column_name="licetd" description="许可合同履行终止日" name="licetd"/>
    </nested>
    <nested description="权利转移" name="transferinfo">
        <columns>
            <column description="转移原因-中文" is_array="true" name="assrcn" not_null="false" type="tiny_text"/>
            <column description="转移原因-代码" is_array="true" name="assrabbr" not_null="false" type="tiny_text"/>
            <column description="转让记录号" is_array="true" name="assn" not_null="false" type="item_no"/>
            <column description="让与人" is_array="true" name="assor" not_null="false" type="tiny_text"/>
            <column description="[标准]让与人" is_array="true" name="assors" not_null="false" type="tiny_text"/>
            <column description="受让人" is_array="true" name="assee" not_null="false" type="tiny_text"/>
            <column description="[标准]受让人" is_array="true" name="assees" not_null="false" type="tiny_text"/>
            <column description="转让记录日期" is_array="true" name="assd" not_null="false" type="date"/>
            <column description="转让生效日" is_array="true" name="assed" not_null="false" type="date"/>
            <column description="转让生效年" is_array="true" name="assey" not_null="false" type="date"/>
            <column description="受让人国别" is_array="true" name="asseeco" not_null="false" type="item_no"/>
            <column description="转让后地址" is_array="true" name="assaad" not_null="false" type="long_text"/>
            <column description="转让前地址" is_array="true" name="assbad" not_null="false" type="long_text"/>
            <column description="转让前国家" is_array="true" name="assbadco" not_null="false" type="item_no"/>
            <column description="转让前省" is_array="true" name="assbadp" not_null="false" type="tiny_text"/>
            <column description="转让前市" is_array="true" name="assbadc" not_null="false" type="tiny_text"/>
            <column description="转让前县" is_array="true" name="assbadd" not_null="false" type="long_text"/>
            <column description="转让前街道" is_array="true" name="assbads" not_null="false" type="long_text"/>
            <column description="转让前园区" is_array="true" name="assbadz" not_null="false" type="long_text"/>
            <column description="转让后州/省" is_array="true" name="assaadp" not_null="false" type="tiny_text"/>
            <column description="转让后市" is_array="true" name="assaadc" not_null="false" type="tiny_text"/>
            <column description="转让后县" is_array="true" name="assaadd" not_null="false" type="long_text"/>
            <column description="转让后街道" is_array="true" name="assaads" not_null="false" type="long_text"/>
            <column description="转让后园区" is_array="true" name="assaadz" not_null="false" type="long_text"/>
            <column description="转让联系人" is_array="true" name="assco" not_null="false" type="tiny_text"/>
            <column description="转让联系人地址" is_array="true" name="asscoadd" not_null="false" type="long_text"/>
        </columns>
        
        <field alias="转移原因-中文" analyzer="smart_analyzer" column_name="assrcn" description="转移原因-中文" name="assrcn"/>
        <field alias="转移原因-代码" analyzer="smart_analyzer" column_name="assrabbr" description="转移原因-代码" name="assrabbr"/>
        <field alias="转让记录号" analyzer="null" column_name="assn" description="转让记录号" name="assn"/>
        <field alias="让与人" analyzer="smart_analyzer" column_name="assor" description="让与人" name="assor">
            <multi_field analyzer="null" column_name="raw" description="让与人" name="assor_raw"/>
        </field>
        <field alias="[标准]让与人" analyzer="smart_analyzer" column_name="assors" description="[标准]让与人" name="assors">
            <multi_field analyzer="null" column_name="raw" description="[标准]让与人" name="assors_raw"/>
        </field>
        <field alias="受让人" analyzer="smart_analyzer" column_name="assee" description="受让人" name="assee">
            <multi_field analyzer="null" column_name="raw" description="受让人" name="assee_raw"/>
        </field>
        <field alias="[标准]受让人" analyzer="smart_analyzer" column_name="assees" description="[标准]受让人" name="assees">
            <multi_field analyzer="null" column_name="raw" description="[标准]受让人" name="assees_raw"/>
        </field>
        <field alias="转让记录日期" analyzer="null" column_name="assd" description="转让记录日期" name="assd"/>
        <field alias="转让生效日" analyzer="null" column_name="assed" description="转让生效日" name="assed"/>
        <field alias="转让生效年" analyzer="null" column_name="assey" description="转让生效年" name="assey"/>
        <field alias="受让人国别" analyzer="null" column_name="asseeco" description="受让人国别" name="asseeco">
            <multi_field analyzer="null" column_name="raw" description="受让人国别" name="asseeco_raw"/>
        </field>
        <field alias="转让前地址" analyzer="smart_analyzer" column_name="assbad" description="转让前地址" name="assbad"/>
        <field alias="转让前国家" analyzer="null" column_name="assbadco" description="转让前国家" name="assbadco">
            <multi_field analyzer="null" column_name="raw" description="转让前国家" name="assbadco_raw"/>
        </field>
        <field alias="转让前省" analyzer="null" column_name="assbadp" description="转让前省" name="assbadp">
            <multi_field analyzer="null" column_name="raw" description="转让前省" name="assbadp_raw"/>
        </field>
        <field alias="转让前市" analyzer="null" column_name="assbadc" description="转让前市" name="assbadc">
            <multi_field analyzer="null" column_name="raw" description="转让前市" name="assbadc_raw"/>
        </field>
        <field alias="转让前县" analyzer="smart_analyzer" column_name="assbadd" description="转让前县" name="assbadd"/>
        <field alias="转让前街道" analyzer="smart_analyzer" column_name="assbads" description="转让前街道" name="assbads"/>
        <field alias="转让前园区" analyzer="smart_analyzer" column_name="assbadz" description="转让前园区" name="assbadz"/>
        <field alias="转让后地址" analyzer="smart_analyzer" column_name="assaad" description="转让后地址" name="assaad"/>
        <field alias="转让后州/省" analyzer="null" column_name="assaadp" description="转让后州/省" name="assaadp">
            <multi_field analyzer="null" column_name="raw" description="转让后州/省" name="assaadp_raw"/>
        </field>
        <field alias="转让后市" analyzer="null" column_name="assaadc" description="转让后市" name="assaadc">
            <multi_field analyzer="null" column_name="raw" description="转让后市" name="assaadc_raw"/>
        </field>
        <field alias="转让后县" analyzer="smart_analyzer" column_name="assaadd" description="转让后县" name="assaadd"/>
        <field alias="转让后街道" analyzer="smart_analyzer" column_name="assaads" description="转让后街道" name="assaads"/>
        <field alias="转让后园区" analyzer="smart_analyzer" column_name="assaadz" description="转让后园区" name="assaadz"/>
        <field alias="转让联系人" analyzer="smart_analyzer" column_name="assco" description="转让联系人" name="assco">
            <multi_field analyzer="null" column_name="raw" description="转让联系人" name="assco_raw"/>
        </field>
        <field alias="转让联系人地址" analyzer="smart_analyzer" column_name="asscoadd" description="转让联系人地址" name="asscoadd"/>
    </nested>
    <nested description="复审信息" name="review">
        <columns>
            <column description="复审请求人" is_array="true" name="reeap" not_null="false" type="tiny_text"/>
            <column description="[标准]复审请求人" is_array="true" name="reeaps" not_null="false" type="tiny_text"/>
            <column description="复审全文" is_array="true" name="reetext" not_null="false" type="long_text"/>
            <column description="复审决定号" is_array="true" name="reen" not_null="false" type="item_no"/>
            <column description="复审委内编号" is_array="true" name="reeinn" not_null="false" type="item_no"/>
            <column description="复审请求日" is_array="true" name="reeapd" not_null="false" type="date"/>
            <column description="复审决定日" is_array="true" name="reedd" not_null="false" type="date"/>
            <column description="复审决定类型-中文" is_array="true" name="reedcn" not_null="false" type="item_no"/>
            <column description="复审决定类型-缩写" is_array="true" name="reedabbr" not_null="false" type="item_no"/>
            <column description="复审决定要点" is_array="true" name="reedp" not_null="false" type="long_text"/>
            <column description="复审法律依据" is_array="true" name="reelb" not_null="false" type="tiny_text"/>
            <column description="复审主审员" is_array="true" name="reeme" not_null="false" type="tiny_text"/>
            <column description="复审参审员" is_array="true" name="reeae" not_null="false" type="tiny_text"/>
            <column description="复审合议组组长" is_array="true" name="reele" not_null="false" type="tiny_text"/>
            <column description="复审案由" is_array="true" name="reeinvca" not_null="false" type="long_text"/>
            <column description="复审联系人" is_array="true" name="reeinvco" not_null="false" type="tiny_text"/>
            <column description="复审联系人地址" is_array="true" name="reeinvcoadd" not_null="false" type="long_text"/>
            <column description="复审联系人省" is_array="true" name="reeinvcop" not_null="false" type="tiny_text"/>
            <column description="复审联系人城市" is_array="true" name="reeinvcoc" not_null="false" type="tiny_text"/>
            <column description="复审联系人单位" is_array="true" name="reeinvcof" not_null="false" type="long_text"/>
        </columns>
        <field alias="复审请求人" analyzer="smart_analyzer" column_name="reeap" description="复审请求人" name="reeap">
            <multi_field analyzer="null" column_name="raw" description="复审请求人" name="reeap_raw"/>
        </field>
        <field alias="[标准]复审请求人" analyzer="smart_analyzer" column_name="reeaps" description="[标准]复审请求人" name="reeaps">
            <multi_field analyzer="null" column_name="raw" description="[标准]复审请求人" name="reeaps_raw"/>
        </field>
        <field alias="复审全文" analyzer="smart_analyzer" column_name="reetext" description="复审全文" name="reetext"/>
        <field alias="复审决定号" analyzer="null" column_name="reen" description="复审决定号" name="reen">
            <multi_field analyzer="null" column_name="raw" description="复审决定号" name="reen_raw"/>
        </field>
        <field alias="复审委内编号" analyzer="null" column_name="reeinn" description="复审委内编号" name="reeinn">
            <multi_field analyzer="null" column_name="raw" description="复审委内编号" name="reeinn_raw"/>
        </field>
        <field alias="复审请求日" analyzer="null" column_name="reeapd" description="复审请求日" name="reeapd"/>
        <field alias="复审决定日" analyzer="null" column_name="reedd" description="复审决定日" name="reedd"/>
        <field alias="复审决定类型-中文" analyzer="null" column_name="reedcn" description="复审决定类型-中文" name="reedcn">
            <multi_field analyzer="null" column_name="raw" description="复审决定类型-中文" name="reedcn_raw"/>
        </field>
        <field alias="复审决定类型-缩写" analyzer="null" column_name="reedabbr" description="复审决定类型-缩写" name="reedabbr"/>
        <field alias="复审决定要点" analyzer="smart_analyzer" column_name="reedp" description="复审决定要点" name="reedp"/>
        <field alias="复审法律依据" analyzer="null" column_name="reelb" description="复审法律依据" name="reelb">
            <multi_field analyzer="null" column_name="raw" description="复审法律依据" name="reelb_raw"/>
        </field>
        <field alias="复审主审员" analyzer="smart_analyzer" column_name="reeme" description="复审主审员" name="reeme">
            <multi_field analyzer="null" column_name="raw" description="复审主审员" name="reeme_raw"/>
        </field>
        <field alias="复审参审员" analyzer="smart_analyzer" column_name="reeae" description="复审参审员" name="reeae">
            <multi_field analyzer="null" column_name="raw" description="复审参审员" name="reeae_raw"/>
        </field>
        <field alias="复审合议组组长" analyzer="smart_analyzer" column_name="reele" description="复审合议组组长" name="reele">
            <multi_field analyzer="null" column_name="raw" description="复审合议组组长" name="reele_raw"/>
        </field>
        <field alias="复审案由" analyzer="smart_analyzer" column_name="reeinvca" description="复审案由" name="reeinvca"/>
        <field alias="复审联系人" analyzer="smart_analyzer" column_name="reeinvco" description="复审联系人" name="reeinvco">
            <multi_field analyzer="null" column_name="raw" description="复审联系人" name="reeinvco_raw"/>
        </field>
        <field alias="复审联系人地址" analyzer="smart_analyzer" column_name="reeinvcoadd" description="复审联系人地址" name="reeinvcoadd"/>
        <field alias="复审联系人省" analyzer="null" column_name="reeinvcop" description="复审联系人省" name="reeinvcop"/>
        <field alias="复审联系人城市" analyzer="null" column_name="reeinvcoc" description="复审联系人城市" name="reeinvcoc"/>
        <field alias="复审联系人单位" analyzer="smart_analyzer" column_name="reeinvcof" description="复审联系人单位" name="reeinvcof"/>
    </nested>
    <nested description="无效信息" name="invalidinfo">
        <columns>
            <column description="无效请求人" is_array="true" name="invap" not_null="false" type="tiny_text"/>
            <column description="[标准]无效请求人" is_array="true" name="invaps" not_null="false" type="tiny_text"/>
            <column description="无效全文" is_array="true" name="invtext" not_null="false" type="long_text"/>
            <column description="无效决定号" is_array="true" name="invn" not_null="false" type="item_no"/>
            <column description="无效委内编号" is_array="true" name="invinn" not_null="false" type="item_no"/>
            <column description="无效请求日" is_array="true" name="invapd" not_null="false" type="date"/>
            <column description="无效决定日" is_array="true" name="invdd" not_null="false" type="date"/>
            <column description="无效决定类型-中文" is_array="true" name="invdcn" not_null="false" type="item_no"/>
            <column description="无效决定类型-缩写" is_array="true" name="invdabbr" not_null="false" type="item_no"/>
            <column description="无效决定要点" is_array="true" name="invdp" not_null="false" type="long_text"/>
            <column description="无效法律依据" is_array="true" name="invlb" not_null="false" type="tiny_text"/>
            <column description="无效主审员" is_array="true" name="invme" not_null="false" type="tiny_text"/>
            <column description="无效参审员" is_array="true" name="invae" not_null="false" type="tiny_text"/>
            <column description="无效合议组组长" is_array="true" name="invle" not_null="false" type="tiny_text"/>
            <column description="无效案由" is_array="true" name="invca" not_null="false" type="long_text"/>
            <column description="无效联系人" is_array="true" name="invco" not_null="false" type="long_text"/>
            <column description="无效联系人地址" is_array="true" name="invcoadd" not_null="false" type="long_text"/>
            <column description="无效联系人省" is_array="true" name="invcop" not_null="false" type="long_text"/>
            <column description="无效联系人城市" is_array="true" name="invcoc" not_null="false" type="long_text"/>
            <column description="无效联系人单位" is_array="true" name="invcof" not_null="false" type="long_text"/>
        </columns>
        <field alias="无效请求人" analyzer="smart_analyzer" column_name="invap" description="无效请求人" name="invap">
            <multi_field analyzer="null" column_name="raw" description="无效请求人" name="invap_raw"/>
        </field>
        <field alias="[标准]无效请求人" analyzer="smart_analyzer" column_name="invaps" description="[标准]无效请求人" name="invaps">
            <multi_field analyzer="null" column_name="raw" description="[标准]无效请求人" name="invaps_raw"/>
        </field>
        <field alias="无效全文" analyzer="smart_analyzer" column_name="invtext" description="无效全文" name="invtext"/>
        <field alias="无效决定号" analyzer="null" column_name="invn" description="无效决定号" name="invn">
            <multi_field analyzer="null" column_name="raw" description="无效决定号" name="invn_raw"/>
        </field>
        <field alias="无效委内编号" analyzer="null" column_name="invinn" description="无效委内编号" name="invinn">
            <multi_field analyzer="null" column_name="raw" description="无效委内编号" name="invinn_raw"/>
        </field>
        <field alias="无效请求日" analyzer="null" column_name="invapd" description="无效请求日" name="invapd"/>
        <field alias="无效决定日" analyzer="null" column_name="invdd" description="无效决定日" name="invdd"/>
        <field alias="无效决定类型-中文" analyzer="null" column_name="invdcn" description="无效决定类型-中文" name="invdcn">
            <multi_field analyzer="null" column_name="raw" description="无效决定类型-中文" name="invdcn_raw"/>
        </field>
        <field alias="无效决定类型-缩写" analyzer="null" column_name="invdabbr" description="无效决定类型-缩写" name="invdabbr"/>
        <field alias="无效决定要点" analyzer="smart_analyzer" column_name="invdp" description="无效决定要点" name="invdp"/>
        <field alias="无效法律依据" analyzer="smart_analyzer" column_name="invlb" description="无效法律依据" name="invlb">
            <multi_field analyzer="null" column_name="raw" description="无效法律依据" name="invlb_raw"/>
        </field>
        <field alias="无效主审员" analyzer="smart_analyzer" column_name="invme" description="无效主审员" name="invme">
            <multi_field analyzer="null" column_name="raw" description="无效主审员" name="invme_raw"/>
        </field>
        <field alias="无效参审员" analyzer="smart_analyzer" column_name="invae" description="无效参审员" name="invae">
            <multi_field analyzer="null" column_name="raw" description="无效参审员" name="invae_raw"/>
        </field>
        <field alias="无效合议组组长" analyzer="smart_analyzer" column_name="invle" description="无效合议组组长" name="invle">
            <multi_field analyzer="null" column_name="raw" description="无效合议组组长" name="invle_raw"/>
        </field>
        <field alias="无效案由" analyzer="smart_analyzer" column_name="invca" description="无效案由" name="invca"/>
        <field alias="无效联系人" analyzer="smart_analyzer" column_name="invco" description="无效联系人" name="invco">
            <multi_field analyzer="null" column_name="raw" description="无效联系人" name="invco_raw"/>
        </field>
        <field alias="无效联系人地址" analyzer="smart_analyzer" column_name="invcoadd" description="无效联系人地址" name="invcoadd"/>
        <field alias="无效联系人省" analyzer="smart_analyzer" column_name="invcop" description="无效联系人省" name="invcop"/>
        <field alias="无效联系人城市" analyzer="smart_analyzer" column_name="invcoc" description="无效联系人城市" name="invcoc"/>
        <field alias="无效联系人单位" analyzer="smart_analyzer" column_name="invcof" description="无效联系人单位" name="invcof"/>
    </nested>
    <nested description="口审信息" name="oral">
        <columns>
            <column description="口审类型-中文" is_array="true" name="orkcn" not_null="false" type="item_no"/>
            <column description="口审类型-缩写" is_array="true" name="orkabbr" not_null="false" type="item_no"/>
            <column description="口审日期" is_array="true" name="ohd" not_null="false" type="date"/>
        </columns>
        <field alias="口审类型-中文" analyzer="null" column_name="orkcn" description="口审类型-中文" name="orkcn">
            <multi_field analyzer="null" column_name="raw" description="口审类型-中文" name="orkcn_raw"/>
        </field>
        <field alias="口审类型-缩写" analyzer="null" column_name="orkabbr" description="口审类型-缩写" name="orkabbr"/>
        <field alias="口审日期" analyzer="null" column_name="ohd" description="口审日期" name="ohd"/>
    </nested>
    <nested description="质押信息" name="pledge">
        <columns>
            <column description="保全生效日" is_array="true" name="preed" not_null="false" type="date"/>
            <column description="保全解除日" is_array="true" name="prerd" not_null="false" type="date"/>
            <column description="质押保全类型-中文" is_array="true" name="plepretcn" not_null="false" type="item_no"/>
            <column description="质押保全类型-代码" is_array="true" name="plepretabbr" not_null="false" type="item_no"/>
            <column description="质权人" is_array="true" name="pee" not_null="false" type="tiny_text"/>
            <column description="[标准]质权人" is_array="true" name="pees" not_null="false" type="tiny_text"/>
            <column description="出质人" is_array="true" name="por" not_null="false" type="tiny_text"/>
            <column description="[标准]出质人" is_array="true" name="pors" not_null="false" type="tiny_text"/>
            <column description="质押登记号" is_array="true" name="plen" not_null="false" type="item_no"/>
            <column description="质押生效日" is_array="true" name="pleed" not_null="false" type="date"/>
            <column description="质押变更日" is_array="true" name="plrcd" not_null="false" type="date"/>
            <column description="质押年" is_array="true" name="pley" not_null="false" type="date"/>
            <column description="质押解除日" is_array="true" name="plerd" not_null="false" type="date"/>
        </columns>
        <field alias="保全生效日" analyzer="null" column_name="preed" description="保全生效日" name="preed"/>
        <field alias="保全解除日" analyzer="null" column_name="prerd" description="保全解除日" name="prerd"/>
        <field alias="质押保全类型-中文" analyzer="null" column_name="plepretcn" description="质押保全类型-中文" name="plepretcn">
            <multi_field analyzer="null" column_name="raw" description="质押保全类型-中文" name="plepretcn_raw"/>
        </field>
        <field alias="质押保全类型-代码" analyzer="null" column_name="plepretabbr" description="质押保全类型-代码" name="plepretabbr">
            <multi_field analyzer="null" column_name="raw" description="质押保全类型-代码" name="plepretabbr_raw"/>
        </field>
        <field alias="质权人" analyzer="smart_analyzer" column_name="pee" description="质权人" name="pee">
            <multi_field analyzer="null" column_name="raw" description="质权人" name="pee_raw"/>
        </field>
        <field alias="[标准]质权人" analyzer="smart_analyzer" column_name="pees" description="[标准]质权人" name="pees">
            <multi_field analyzer="null" column_name="raw" description="[标准]质权人" name="pees_raw"/>
        </field>
        <field alias="出质人" analyzer="smart_analyzer" column_name="por" description="出质人" name="por">
            <multi_field analyzer="null" column_name="raw" description="出质人" name="por_raw"/>
        </field>
        <field alias="[标准]出质人" analyzer="smart_analyzer" column_name="pors" description="[标准]出质人" name="pors">
            <multi_field analyzer="null" column_name="raw" description="[标准]出质人" name="pors_raw"/>
        </field>
        <field alias="质押登记号" analyzer="null" column_name="plen" description="质押登记号" name="plen">
            <multi_field analyzer="null" column_name="raw" description="质押登记号" name="plen_raw"/>
        </field>
        <field alias="质押生效日" analyzer="null" column_name="pleed" description="质押生效日" name="pleed"/>
        <field alias="质押变更日" analyzer="null" column_name="plrcd" description="质押变更日" name="plrcd"/>
        <field alias="质押年" analyzer="null" column_name="pley" description="质押年" name="pley"/>
        <field alias="质押解除日" analyzer="null" column_name="plerd" description="质押解除日" name="plerd"/>
    </nested>
    <nested description="化学式" name="chemicalinfo">
        <columns>
            <column description="化学式ID" is_array="true" name="chid" not_null="false" type="item_no"/>
            <column description="职能符" is_array="true" name="chrl" not_null="false" type="tiny_text"/>
            <column description="化学式名称" is_array="true" name="chmn" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="化学式ID" analyzer="null" column_name="chid" description="化学式ID" name="chid"/>
        <field alias="职能符" analyzer="null" column_name="chrl" description="职能符" name="chrl"/>
        <field alias="化学式名称" analyzer="null" column_name="chmn" description="化学式名称" name="chmn"/>
    </nested>
    <nested description="历史专利权人" name="historypatenteeinfo">
        <columns>
            <column description="历史专利权人中文" is_array="true" name="pahcn" not_null="false" type="tiny_text"/>
            <column description="历史专利权人英文" is_array="true" name="pahen" not_null="false" type="tiny_text"/>
            <column description="历史专利权人日文" is_array="true" name="pahjp" not_null="false" type="tiny_text"/>
            <column description="历史专利权人原文" is_array="true" name="paho" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="历史专利权人中文" analyzer="null" column_name="pahcn" description="历史专利权人中文" name="pahcn">
            <multi_field analyzer="null" column_name="raw" description="历史专利权人中文" name="pahcn_raw"/>
        </field>
        <field alias="历史专利权人英文" analyzer="null" column_name="pahen" description="历史专利权人英文" name="pahen">
            <multi_field analyzer="null" column_name="raw" description="历史专利权人英文" name="pahen_raw"/>
        </field>
        <field alias="历史专利权人日文" analyzer="null" column_name="pahjp" description="历史专利权人日文" name="pahjp">
            <multi_field analyzer="null" column_name="raw" description="历史专利权人日文" name="pahjp_raw"/>
        </field>
        <field alias="历史专利权人原文" analyzer="null" column_name="paho" description="历史专利权人原文" name="paho">
            <multi_field analyzer="null" column_name="raw" description="历史专利权人原文" name="paho_raw"/>
        </field>
    </nested>
    <nested description="历史申请人" name="historyapplicantinfo">
        <columns>
            <column description="历史申请人中文" is_array="true" name="pafhcn" not_null="false" type="tiny_text"/>
            <column description="历史申请人英文" is_array="true" name="pafhen" not_null="false" type="tiny_text"/>
            <column description="历史申请人日文" is_array="true" name="pafhjp" not_null="false" type="tiny_text"/>
            <column description="历史申请人原文" is_array="true" name="pafho" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="历史申请人中文" analyzer="null" column_name="pafhcn" description="历史申请人中文" name="pafhcn">
            <multi_field analyzer="null" column_name="raw" description="历史申请人中文" name="pafhcn_raw"/>
        </field>
        <field alias="历史申请人英文" analyzer="null" column_name="pafhen" description="历史申请人英文" name="pafhen">
            <multi_field analyzer="null" column_name="raw" description="历史申请人英文" name="pafhen_raw"/>
        </field>
        <field alias="历史申请人日文" analyzer="null" column_name="pafhjp" description="历史申请人日文" name="pafhjp">
            <multi_field analyzer="null" column_name="raw" description="历史申请人日文" name="pafhjp_raw"/>
        </field>
        <field alias="历史申请人原文" analyzer="null" column_name="pafho" description="历史申请人原文" name="pafho">
            <multi_field analyzer="null" column_name="raw" description="历史申请人原文" name="pafho_raw"/>
        </field>
    </nested>
    <nested description="历史发明人" name="historyinventorinfo">
        <columns>
            <column description="历史发明人中文" is_array="true" name="inhcn" not_null="false" type="tiny_text"/>
            <column description="历史发明人英文" is_array="true" name="inhen" not_null="false" type="tiny_text"/>
            <column description="历史发明人日文" is_array="true" name="inhjp" not_null="false" type="tiny_text"/>
            <column description="历史发明人原文" is_array="true" name="inho" not_null="false" type="tiny_text"/>
        </columns>
        <field alias="历史发明人中文" analyzer="null" column_name="inhcn" description="历史发明人中文" name="inhcn">
            <multi_field analyzer="null" column_name="raw" description="历史发明人中文" name="inhcn_raw"/>
        </field>
        <field alias="历史发明人英文" analyzer="null" column_name="inhen" description="历史发明人英文" name="inhen">
            <multi_field analyzer="null" column_name="raw" description="历史发明人英文" name="inhen_raw"/>
        </field>
        <field alias="历史发明人日文" analyzer="null" column_name="inhjp" description="历史发明人日文" name="inhjp">
            <multi_field analyzer="null" column_name="raw" description="历史发明人日文" name="inhjp_raw"/>
        </field>
        <field alias="历史发明人原文" analyzer="null" column_name="inho" description="历史发明人原文" name="inho">
            <multi_field analyzer="null" column_name="raw" description="历史发明人原文" name="inho_raw"/>
        </field>
    </nested>
    <virtual_fields>
        <virtual_field alias="申请公开号" fields="andb, ano, ane, pndb, pno, pne" name="anpn"/>
        <virtual_field alias="标题" fields="ticn, tien, tijp, tio" name="ti"/>
        <virtual_field alias="摘要" fields="abcn, aben, abjp, abot" name="ab"/>
        <virtual_field alias="权利要求" fields="dclmcn, dclmen, dclmjp, dclmo, iclmcn, iclmen, iclmjp, iclmo" name="clm"/>
        <virtual_field alias="权利要求中文" fields="dclmcn, iclmcn" name="clmcn"/>
        <virtual_field alias="权利要求英文" fields="dclmen, iclmen" name="clmen"/>
        <virtual_field alias="权利要求日文" fields="dclmjp, iclmjp" name="clmjp"/>
        <virtual_field alias="权利要求原文" fields="dclmo, iclmo" name="clmo"/>
        <virtual_field alias="首项权利要求" fields="fclmcn, fclmen, fclmjp, fclmo" name="fclm"/>
        <virtual_field alias="独立权利要求" fields="iclmcn, iclmen, iclmjp, iclmo" name="iclm"/>
        <virtual_field alias="从属权利要求" fields="dclmcn, dclmen, dclmjp, dclmo" name="dclm"/>
        <virtual_field alias="说明书中文" fields="tfcn, tbcn, iscn, ddcn, secn, otherdesccn" name="descn"/>
        <virtual_field alias="说明书英文" fields="tfen, tben, isen, dden, secn, otherdescen" name="desen"/>
        <virtual_field alias="说明书日文" fields="tbjp, tfjp, isjp, ddjp, sejp, otherdescjp" name="desjp"/>
        <virtual_field alias="说明书原文" fields="tbo, tfo, iso, ddo, seo, otherdesco" name="deso"/>
        <virtual_field alias="技术领域" fields="tfcn, tfen, tfjp, tfo" name="tf"/>
        <virtual_field alias="背景技术" fields="tbcn, tben, tbjp, tbo" name="tb"/>
        <virtual_field alias="发明内容" fields="iscn, isen, isjp, iso" name="is"/>
        <virtual_field alias="附图说明" fields="ddcn, dden, ddjp, ddo" name="dd"/>
        <virtual_field alias="具体实施方式" fields="secn, seen, sejp, seo" name="se"/>
        <virtual_field alias="标题/摘要/权利要求" fields="abcn, aben, abjp, abot, ticn, tien, tijp, tio, dclmcn, dclmen, dclmjp, dclmo, iclmcn, iclmen, iclmjp, iclmo" name="tac"/>
        <virtual_field alias="标题/摘要" fields="abcn, aben, abjp, abot, ticn, tien, tijp, tio" name="ta"/>
        <virtual_field alias="全文" fields="tacdcn, tacden, tacdjp, tio, abot, dclmo, iclmo, tfo, tbo, iso, ddo, seo, otherdesco" name="tacd"/>
        <virtual_field alias="申请号" fields="andb, ano, ane" name="an"/>
        <virtual_field alias="申请号号码" fields="anno, anndb, anne" name="ann"/>
        <virtual_field alias="公开(公告)号" fields="pndb, pno, pne" name="pn"/>
        <virtual_field alias="公开(公告)号号码" fields="pnndb, pnno, pnne" name="pnn"/>
        <virtual_field alias="分类号" fields="ipc,cpc,uc,fi,ft,loc" name="class"/>
        <virtual_field alias="申请(专利权)人" fields="apcn,apen,apjp,apo, aposcn,aposen,aposjp,aposo,ascn,asen,asjp,aso,asoscn,asosen,asosjp,asoso,pahcn,pahen,pahjp,paho,asccn,ascen,ascjp,asco,pafhcn,pafhen,pafhjp,pafho,apccn,apcen,apcjp,apco" name="pa"/>
        <virtual_field alias="申请(专利权)人中文" fields="apcn, aposcn, ascn, asoscn, pahcn, asccn, pafhcn, apccn" name="pacn"/>
        <virtual_field alias="申请(专利权)人英文" fields="apen, aposen, asen, asosen, pahen, ascen, pafhen, apcen" name="paen"/>
        <virtual_field alias="申请(专利权)人日文" fields="apjp, aposjp, asjp, asosjp, pahjp, ascjp, pafhjp, apcjp" name="pajp"/>
        <virtual_field alias="申请(专利权)人原文" fields="apo, aposo, aso, asoso, paho, asco, pafho, apco" name="pao"/>
        <!--应用添加字段：paor-->
        <virtual_field alias="原始申请(专利权)人" fields="apcn, apen, apjp, apo, ascn, asen, asjp, aso" name="paor"/>
        <virtual_field alias="[标准]原始申请(专利权)人" fields="aposcn, aposen, aposjp, aposo, asoscn, asosen, asosjp, asoso" name="paos"/>
        <virtual_field alias="原始申请(专利权)人地址" fields="apoadcn, apoaden, apoadjp, apoado, asoadcn, asoaden, asoadjp, asoado" name="paoad"/>
        <virtual_field alias="原始申请(专利权)人国别" fields="apoadco, asoadco" name="paoadco"/>
        <virtual_field alias="原始申请(专利权)人州/省" fields="apoadp, asoadp" name="paoadp"/>
        <virtual_field alias="原始申请(专利权)人地市" fields="apoadc, asoadc" name="paoadc"/>
        <virtual_field alias="原始申请(专利权)人区县" fields="apoadd, asoadd" name="paoadd"/>
        <virtual_field alias="原始申请(专利权)人街道" fields="apoads, asoads" name="paoads"/>
        <virtual_field alias="原始申请(专利权)人园区" fields="apoadz, asoadz" name="paoadz"/>
        <virtual_field alias="原始申请(专利权)人地址经纬度" fields="apoadll, asoadll" name="paoadll"/>
        <virtual_field alias="原始申请(专利权)人国省代码" fields="apoadcc, asoadcc" name="paoadcc"/>
        <virtual_field alias="地址" fields="apoadcn, apoaden, apoadjp, apoado, inad, agtad, assaad, assaadrc" name="add"/>
        <virtual_field alias="权利要求类型" fields="clmtcn, clmten, clmtabbr" name="clmt"/>
        <virtual_field alias="[标准]原始申请(专利权)人类型" fields="apost, asost, apostcn, asostcn" name="paost"/>
        <virtual_field alias="第一原始申请(专利权)人" fields="apfcn, apfen, apfjp, apfo, asfcn, asfen, asfjp, asfo" name="pafo"/>
        <virtual_field alias="[标准]第一原始申请(专利权)人" fields="apfscn, apfsen, apfsjp, apfos, asfos" name="pafos"/>
        <virtual_field alias="第一原始申请(专利权)人国家" fields="apfoco, asfoco" name="pafoco"/>
        <virtual_field alias="当前申请(专利权)人数量" fields="cappc, appzcc" name="appcc"/>
        <virtual_field alias="当前申请(专利权)人" fields="apccn, asccn, apcen, ascen, apcjp, ascjp, apco, asco" name="pac"/>
        <!--应用添加字段：papac-->
        <virtual_field alias="原始当前申请(专利权)人" fields="apccn, asccn, apcen, ascen, apcjp, ascjp, apco, asco, apcn, apen, apjp, apo, aposcn, aposen, aposjp, aposo, ascn, asen, asjp, aso, asoscn, asosen, asosjp, asoso" name="papac"/>
        <virtual_field alias="[标准]当前申请(专利权)人" fields="apcsdcn, apcsden, apcsdjp, apcsdo, ascscn, ascsen, ascsjp, ascso" name="pacs"/>
        <virtual_field alias="当前申请(专利权)人地址" fields="apcadcn, ascadcn, apcaden, ascaden, apcadjp, ascadjp, apcado, ascado" name="pacad"/>
        <virtual_field alias="当前申请(专利权)人国家" fields="apcadco, ascadco" name="pacadco"/>
        <virtual_field alias="当前申请(专利权)人州/省" fields="apcadp, ascadp" name="pacadp"/>
        <virtual_field alias="当前申请(专利权)人地市" fields="apcadc, ascadc" name="pacadc"/>
        <virtual_field alias="当前申请(专利权)人街道" fields="apcads, ascads" name="pacads"/>
        <virtual_field alias="当前申请(专利权)人园区" fields="apcadz, ascadz" name="pacadz"/>
        <virtual_field alias="当前申请(专利权)人国省代码" fields="apcadcc, ascadcc" name="pacadcc"/>
        <virtual_field alias="[标准]当前申请(专利权)人类型" fields="apcst, ascst, apcstcn, ascstcn" name="pacst"/>
        <virtual_field alias="第一原始申请人类型" fields="pafotabbr, pafotcn" name="pafot"/>
        <virtual_field alias="公开类型" fields="ptzh, ptabbr" name="pt"/>
        <virtual_field alias="专利有效性" fields="slscn, slsabbr" name="sls"/>
        <virtual_field alias="许可类型" fields="licetcn, licetabbr" name="licet"/>
        <virtual_field alias="许可备案阶段" fields="licescn, licesabbr" name="lices"/>
        <virtual_field alias="决定类型" fields="reeinvtcn, reeinvtabbr" name="reeinvt"/>
        <virtual_field alias="复审决定" fields="reedcn, reedabbr" name="reed"/>
        <virtual_field alias="无效决定" fields="invdcn, invdabbr" name="invd"/>
        <virtual_field alias="口审类型" fields="orkcn, orkabbr" name="ork"/>
        <virtual_field alias="质押保全类型" fields="plepretcn, plepretabbr" name="plepret"/>
        <virtual_field alias="质押备案阶段" fields="plescn, plesabbr" name="ples"/>
        <virtual_field alias="保全阶段" fields="prescn, presabbr" name="pres"/>
        <virtual_field alias="专利转移原因" fields="assrcn, assrabbr" name="assr"/>
        <virtual_field alias="关联案件类型" fields="rptabbr, rptcn" name="rpt"/>
        <virtual_field alias="第一当前申请(专利权)人" fields="apfccn, asfccn, apfcen, asfcen, apfcjp, asfcjp, apfco, asfco" name="pafc"/>
        <virtual_field alias="[标准]第一当前申请(专利权)人" fields="apfcscn, asfcscn, apfcsen, asfcsen, apfcsjp, asfcsjp, apfcso, asfcso" name="pafcs"/>
        <virtual_field alias="发明人" fields="incn, inen, injp, ino" name="in"/>
        <virtual_field alias="当前发明人" fields="inccn, incen, incjp, inco" name="inc"/>
        <virtual_field alias="第一发明人" fields="infcn, infen, infjp, info" name="inf"/>
        <virtual_field alias="代理人" fields="agtcn, agten, agtjp, agto" name="agt"/>
        <virtual_field alias="代理机构" fields="agccn, agcjp, agcen, agco" name="agc"/>
        <virtual_field alias="审查员" fields="excn, exen, exjp, exo" name="ex"/>
        <virtual_field alias="助理审查员" fields="aexcn, aexen, aexjp, aexo" name="aex"/>
        <virtual_field alias="优先权号" fields="prno, prndb, prnep" name="prn"/>
        <virtual_field alias="优先权号号码" fields="prnno, prnndb, prnne" name="prnn"/>
        <virtual_field alias="最早优先权号码" fields="prneo, prnedb, prneep" name="prne"/>
        <virtual_field alias="引证专利" fields="ctpn, ctan" name="ct"/>
        <virtual_field alias="被引证专利" fields="ctgpn, ctgan" name="ctg"/>
        <virtual_field alias="引证或被引证专利" fields="ctpn, ctan, ctgpn, ctgan" name="ctorctg"/>
        <virtual_field alias="让与或受让人" fields="assor, assee" name="assororee"/>
        <virtual_field alias="原始申请(专利权)人数量" fields="apc, asc" name="apoc"/>
        <virtual_field alias="当事人" fields="lipl, lide, litp" name="lip"/>
        <virtual_field alias="当事人委托代理人" fields="liplag, lideag, litpag" name="lipag"/>
        <virtual_field alias="当事人委托律所" fields="liplago, lideago, litpago" name="liplf"/>
        <virtual_field alias="当前法律状态" fields="legalcn, legalabbr" name="legal"/>
        <virtual_field alias="复审无效案由" fields="invca, reeinvca" name="reinca"/>
        <virtual_field alias="复审无效联系人" fields="invco, reeinvco" name="reinco"/>
        <virtual_field alias="复审无效联系人地址" fields="invcoadd, reeinvcoadd" name="reincoadd"/>
        <virtual_field alias="复审无效联系人省" fields="invcop, reeinvcop" name="reincop"/>
        <virtual_field alias="复审无效联系人城市" fields="invcoc, reeinvcoc" name="reincoc"/>
        <virtual_field alias="复审无效联系人单位" fields="invcof, reeinvcof" name="reincof"/>
        <virtual_field alias="相关权利人" fields="pahcn, pahen, pahjp, paho, asccn, ascen, ascjp, asco" name="par"/>
        <virtual_field alias="相关发明人" fields="inhcn, inhen, inhjp, inho, inccn, incen, incjp, ino" name="inr"/>
        <virtual_field alias="当前申请(专利权)人区县" fields="apcadd, ascadd" name="pacadd"/>
        <virtual_field alias="当前申请(专利权)人地址经纬度" fields="apcaddrll, ascadll" name="pacadll"/>
    </virtual_fields>
    <date_fields>
        <date_field format="yyyyMMdd" name="gd"/>
        <date_field format="yyyyMMdd" name="seed"/>
        <date_field format="yyyyMMdd" name="ad"/>
        <date_field format="yyyy" name="ady"/>
        <date_field format="yyyyMM" name="adym"/>
        <date_field format="yyyyMMdd" name="pd"/>
        <date_field format="yyyy" name="pdy"/>
        <date_field format="yyyyMM" name="pdym"/>
        <date_field format="yyyyMMdd" name="pdf"/>
        <date_field format="yyyyMMdd" name="apcet"/>
        <date_field format="yyyyMMdd" name="prd"/>
        <date_field format="yyyyMMdd" name="prdad"/>
        <date_field format="yyyy" name="prdy"/>
        <date_field format="yyyyMM" name="prdym"/>
        <date_field format="yyyyMMdd" name="prde"/>
        <date_field format="yyyy" name="prdye"/>
        <date_field format="yyyyMMdd" name="ctad"/>
        <date_field format="yyyyMMdd" name="ctpd"/>
        <date_field format="yyyy" name="ctpdy"/>
        <date_field format="yyyyMMdd" name="ctgad"/>
        <date_field format="yyyyMMdd" name="ctgpd"/>
        <date_field format="yyyyMMdd" name="pctnd"/>
        <date_field format="yyyyMMdd" name="sfpdf"/>
        <date_field format="yyyyMMdd" name="efpdf"/>
        <date_field format="yyyyMMdd" name="rd"/>
        <date_field format="yyyyMMdd" name="wd"/>
        <date_field format="yyyyMMdd" name="dcd"/>
        <date_field format="yyyyMMdd" name="lsud"/>
        <date_field format="yyyyMMdd" name="eed"/>
        <date_field format="yyyy" name="eey"/>
        <date_field format="yyyyMMdd HH:mm:ss" name="lsad"/>
        <date_field format="yyyyMMdd HH:mm:ss" name="recd"/>
        <date_field format="yyyyMMdd HH:mm:ss" name="cacd"/>
        <date_field format="yyyyMMdd" name="fdaped"/>
        <date_field format="yyyyMMdd" name="fdapeded"/>
        <date_field format="yyyyMMdd" name="lifd"/>
        <date_field format="yyyyMMdd" name="livd"/>
        <date_field format="yyyyMMdd" name="lijd"/>
        <date_field format="yyyy" name="lify"/>
        <date_field format="yyyyMMdd" name="liceed"/>
        <date_field format="yyyy" name="liceey"/>
        <date_field format="yyyyMMdd" name="licest"/>
        <date_field format="yyyyMMdd" name="licetd"/>
        <date_field format="yyyyMMdd" name="assd"/>
        <date_field format="yyyyMMdd" name="assed"/>
        <date_field format="yyyy" name="assey"/>
        <date_field format="yyyyMMdd" name="reeapd"/>
        <date_field format="yyyyMMdd" name="reedd"/>
        <date_field format="yyyyMMdd" name="ohd"/>
        <date_field format="yyyyMMdd" name="invapd"/>
        <date_field format="yyyyMMdd" name="invdd"/>
        <date_field format="yyyyMMdd" name="pleed"/>
        <date_field format="yyyyMMdd" name="plrcd"/>
        <date_field format="yyyy" name="pley"/>
        <date_field format="yyyyMMdd" name="preed"/>
        <date_field format="yyyyMMdd" name="prerd"/>
        <date_field format="yyyyMMdd" name="patentchgdates"/>
        <date_field format="yyyyMMdd" name="legaltimestamp"/>
        <date_field format="yyyyMMdd" name="reetimestamp"/>
        <date_field format="yyyyMMdd" name="legatimestamp"/>
        <date_field format="yyyyMMdd" name="legtimestamp"/>
        <date_field format="yyyyMMdd" name="passctimestamp"/>
        <date_field format="yyyyMMdd" name="changetimestamp"/>
        <date_field format="yyyyMMdd" name="invctimestamp"/>
        <date_field format="yyyyMMdd" name="plectimestamp"/>
        <date_field format="yyyyMMdd" name="lictimestamp"/>
        <date_field format="yyyyMMdd" name="customstimestamp"/>
        <date_field format="yyyyMMdd" name="licectimestamp"/>
        <date_field format="yyyyMMdd" name="desntimestamp"/>
        <date_field format="yyyyMMdd HH:mm:ss" name="instime"/>
        <date_field format="yyyyMMdd" name="itvd"/>
        <date_field format="yyyyMMdd" name="plerd"/>
    </date_fields>
</type>