<?xml version='1.0' encoding='UTF-8'?>
<!--
    index:               索引名称
    name：               类型名称
    primary_key：        主键
    number_of_shards：   分片数量
    number_of_replicas： 副本数量
-->
<!--
     description：字段描述
     is_array：   字段是否数组
     name：       字段名
     not_null：   字段是否不为空
     type：       字段类型，有以下几种类型
        号码类型：   item_no
        布尔类型：   boolean、bool
        数字类型：   byte、short、integer、long、float、double
        短文本类型： tiny_text
        长文本类型： long_text
        日期类型：   date
        图像类型：   image
        二进制类型： binary
 -->
<!--<column description="主键" is_array="false" name="id" not_null="false" type="item_no" />-->
<!--<column description="号码类型" is_array="false" name="itemType" not_null="false" type="item_no" />-->
<!--<column description="布尔类型" is_array="false" name="boolType" not_null="false" type="boolean" />-->
<!--<column description="数字类型" is_array="false" name="numberType" not_null="false" type="long" />-->
<!--<column description="短文本类型" is_array="false" name="tinyTextType" not_null="false" type="tiny_text" />-->
<!--<column description="长文本类型" is_array="false" name="longTextType" not_null="false" type="long_text" />-->
<!--<column description="日期类型" is_array="false" name="dateType" not_null="false" type="date" />-->
<!--<column description="图像类型" is_array="false" name="imageType" not_null="false" type="image" />-->
<!--<column description="二进制类型" is_array="false" name="binaryType" not_null="false" type="binary" />-->

<type index="law" name="law" is_default="true" primary_key="lawId" number_of_shards="1" number_of_replicas="0">
    <columns>
        
        <column description="主键" is_array="false" name="lawId" not_null="true" type="tiny_text" />
        <column description="法律法规标题" is_array="false" name="lawTitle" not_null="false" type="tiny_text" />
        <column description="发文字号" is_array="false" name="lassueDocumentNum" not_null="false" type="tiny_text" />
        <column description="发布日期" is_array="false" name="publishDate" not_null="false" type="date" />
        <column description="发布部门" is_array="false" name="publishDep" not_null="false" type="item_no" />
        <column description="施行日期" is_array="false" name="enforceDate" not_null="false" type="date" />
        <column description="法律效力" is_array="false" name="lawForce" not_null="false" type="tiny_text" />
        <column description="效力范围" is_array="false" name="validtyScope" not_null="false" type="tiny_text" />
        <column description="时效性" is_array="false" name="timeLiness" not_null="false" type="tiny_text" />
        <column description="废止日期" is_array="false" name="annulmentDate" not_null="false" type="date" />
        <column description="题注" is_array="false" name="creditLine" not_null="false" type="tiny_text" />
        <column description="法律法规类别" is_array="false" name="category" not_null="false" type="tiny_text" />
        <column description="发布正文路径" is_array="false" name="lassueDocumentContentPath" not_null="false" type="tiny_text" />
        <column description="法律法规内容路径" is_array="false" name="lawContentPath" not_null="false" type="tiny_text" />
        <column description="法律法规状态" is_array="false" name="lawStatus" not_null="false" type="tiny_text" />
        <column description="录入部门" is_array="false" name="inputDep" not_null="false" type="tiny_text" />
        <column description="录入人" is_array="false" name="inputUser" not_null="false" type="tiny_text" />
        <column description="标签" is_array="false" name="label" not_null="false" type="tiny_text" />
        <column description="前版法律法规ID" is_array="false" name="lawPId" not_null="false" type="tiny_text" />
        <column description="原版法律法规ID" is_array="false" name="lawOId" not_null="false" type="tiny_text" />
        <column description="法律法规富文本" is_array="false" name="lawContentHtml" not_null="false" type="long_text" />
        <column description="法律法规全文（清洗后）" is_array="false" name="content" not_null="false" type="long_text" />
        <column description="删除标识" is_array="false" name="deleteFlag" not_null="false" type="tiny_text" />
        <column description="创建人ID" is_array="false" name="createId" not_null="false" type="tiny_text" />
        <column description="创建时间" is_array="false" name="createDate" not_null="false" type="date" />
        <column description="修改人ID" is_array="false" name="updateId" not_null="false" type="tiny_text" />
        <column description="修改时间" is_array="false" name="updateDate" not_null="false" type="date" />
    </columns>
    <fields>
        <!--
             alias：      字段别名
             analyzer：   字段分词器，有关字段类型和字段分词器的相关规则，见帮助文档
             column_name：字段名
             description：字段描述
             name：       字段名
         -->
        <!--<field alias="主键" analyzer="null" column_name="id" description="主键" name="id" />-->
        <!--<field alias="号码类型" analyzer="null" column_name="itemType" description="号码类型" name="itemType" />-->
        <!--<field alias="布尔类型" analyzer="null" column_name="boolType" description="布尔类型" name="boolType" />-->
        <!--<field alias="数字类型" analyzer="null" column_name="numberType" description="数字类型" name="numberType" />-->
        <!--<field alias="短文本类型" analyzer="lowercase_analyzer" column_name="tinyTextType" description="短文本类型" name="tinyTextType" />-->
        <!--<field alias="长文本类型" analyzer="standard" column_name="longTextType" description="长文本类型" name="longTextType">-->
        <!--&lt;!&ndash;-->
        <!--多值字段：同时需要存储分词和不分词字段时使用-->
        <!--&ndash;&gt;-->
        <!--<multi_field analyzer="null" column_name="raw" description="申请号原始" name="longTextType_raw" />-->
        <!--</field>-->
        <!--<field alias="日期类型" analyzer="null" column_name="dateType" description="日期类型" name="dateType" />-->
        <!--<field alias="图像类型" analyzer="null" column_name="imageType" description="图像类型" name="imageType" />-->
        <!--<field alias="二进制类型" analyzer="null" column_name="binaryType" description="二进制类型" name="binaryType" />-->
        <field alias="主键" analyzer="null" column_name="lawId" description="主键" name="lawId" />
        <field alias="法律法规标题" analyzer="standard" column_name="lawTitle" description="法律法规标题" name="lawTitle">
            <multi_field analyzer="null" column_name="raw" description="法律法规标题" name="lawTitle_raw" />
        </field>
        <field alias="发文字号" analyzer="null" column_name="lassueDocumentNum" description="发文字号" name="lassueDocumentNum">
            <multi_field analyzer="null" column_name="raw" description="发文字号" name="lassueDocumentNum_raw" />
        </field>
        <field alias="发布日期" analyzer="null" column_name="publishDate" description="发布日期" name="publishDate">
            <multi_field analyzer="null" column_name="raw" description="发布日期" name="publishDate_raw" />
        </field>
        <field alias="发布部门" analyzer="null" column_name="publishDep" description="发布部门" name="publishDep">
            <multi_field analyzer="null" column_name="raw" description="发布部门" name="publishDep_raw" />
        </field>
        <field alias="施行日期" analyzer="null" column_name="enforceDate" description="施行日期" name="enforceDate">
            <multi_field analyzer="null" column_name="raw" description="施行日期" name="enforceDate_raw" />
        </field>
        <field alias="法律效力" analyzer="null" column_name="lawForce" description="法律效力" name="lawForce">
            <multi_field analyzer="null" column_name="raw" description="法律效力" name="lawForce_raw" />
        </field>
        <field alias="效力范围" analyzer="null" column_name="validtyScope" description="效力范围" name="validtyScope" />
        <field alias="时效性" analyzer="null" column_name="timeLiness" description="时效性" name="timeLiness">
            <multi_field analyzer="null" column_name="raw" description="时效性" name="timeLiness_raw" />
        </field>
        <field alias="废止日期" analyzer="null" column_name="annulmentDate" description="废止日期" name="annulmentDate" />
        <field alias="题注" analyzer="null" column_name="creditLine" description="题注" name="creditLine" />
        <field alias="法律法规类别" analyzer="null" column_name="category" description="法律法规类别" name="category">
            <multi_field analyzer="null" column_name="raw" description="法律法规类别" name="category_raw" />
        </field>
        <field alias="发布正文路径" analyzer="null" column_name="lassueDocumentContentPath" description="发布正文路径" name="lassueDocumentContentPath" />
        <field alias="法律法规内容路径" analyzer="null" column_name="lawContentPath" description="法律法规内容路径" name="lawContentPath" />
        <field alias="法律法规状态" analyzer="null" column_name="lawStatus" description="法律法规状态" name="lawStatus">
            <multi_field analyzer="null" column_name="raw" description="法律法规状态" name="lawStatus_raw" />
        </field>
        <field alias="录入部门" analyzer="null" column_name="inputDep" description="录入部门" name="inputDep" />
        <field alias="录入人" analyzer="null" column_name="inputUser" description="录入人" name="inputUser" />
        <field alias="标签" analyzer="null" column_name="label" description="标签" name="label" />
        <field alias="前版法律法规ID" analyzer="null" column_name="lawPId" description="前版法律法规ID" name="lawPId" />
        <field alias="原版法律法规ID" analyzer="null" column_name="lawOId" description="原版法律法规ID" name="lawOId" />
        <field alias="法律法规富文本" analyzer="null" column_name="lawContentHtml" description="法律法规富文本" name="lawContentHtml" />
        <field alias="法律法规全文（清洗后）" analyzer="standard" column_name="content" description="法律法规全文（清洗后）" name="content">
            <multi_field analyzer="null" column_name="raw" description="法律法规全文（清洗后）" name="content_raw" />
        </field>
        <field alias="删除标识" analyzer="null" column_name="deleteFlag" description="删除标识" name="deleteFlag">
            <multi_field analyzer="null" column_name="raw" description="删除标识" name="deleteFlag_raw" />
        </field>
        <field alias="创建人ID" analyzer="null" column_name="createId" description="创建人ID" name="createId" />
        <field alias="创建时间" analyzer="null" column_name="createDate" description="创建时间" name="createDate" />
        <field alias="修改人ID" analyzer="null" column_name="updateId" description="修改人ID" name="updateId" />
        <field alias="修改时间" analyzer="null" column_name="updateDate" description="修改时间" name="updateDate" />
    </fields>
    <!--
        description： 嵌套字段描述
        name：        嵌套字段名称
    -->
    <!--<nested description="嵌套字段" name="lawSectionPOS">-->
    <!--<columns>-->
    <!--<column description="被嵌套字段1" is_array="false" name="nestedField1" not_null="false" type="item_no" />-->
    <!--<column description="被嵌套字段2" is_array="false" name="nestedField2" not_null="false" type="long_text" />-->
    <!--</columns>-->
    <!--<field alias="被嵌套字段1" analyzer="null" column_name="nestedField1" description="被嵌套字段1" name="nestedField1" />-->
    <!--<field alias="被嵌套字段2" analyzer="null" column_name="nestedField2" description="被嵌套字段2" name="nestedField2">-->
    <!--<multi_field analyzer="null" column_name="raw" description="被嵌套字段2" name="nestedField2_raw" />-->
    <!--</field>-->
    <!--</nested>-->
    <!--<virtual_fields>-->
    <!--&lt;!&ndash;-->
    <!--name：  虚拟字段名称-->
    <!--alias： 虚拟字段别名-->
    <!--fields: 虚拟字段包含的字段，可以包含普通字段和被嵌套字段-->
    <!--&ndash;&gt;-->
    <!--<virtual_field name="lawId" alias="主键ID" fields="lawId" />-->
    <!--</virtual_fields>-->
    <date_fields>
        <!--
            name：  日期字段名称
            format：日期字段格式
        -->
        <date_field name="publishDate" format="yyyy-MM-dd" />
        <date_field name="enforceDate" format="yyyy-MM-dd" />
        <date_field name="annulmentDate" format="yyyy-MM-dd" />
        <date_field name="createDate" format="yyyy-MM-dd HH:mm:ss" />
        <date_field name="updateDate" format="yyyy-MM-dd HH:mm:ss" />
    </date_fields>
</type>