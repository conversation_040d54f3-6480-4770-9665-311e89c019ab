package client;

import cn.gwssi.DeserializerFactory;
import cn.gwssi.isearch.plugins.converter.ExpressionConverter;
import cn.gwssi.isearch.plugins.search.ExpressionServiceImpl;
import cn.gwssi.syntax.condition.Condition;
import org.apache.commons.io.FileUtils;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.client.transport.TransportClient;
import org.junit.jupiter.api.Test;

import java.io.File;

/**
 * <AUTHOR>
 */
public class FST2QueryBuilder {

    @Test
    public void generateQuery() throws Exception {
//        TransportClient transportClient = ClientCreator.createClient("elasticsearch-7.9.3", "192.168.211.160", 20073);
        TransportClient transportClient = ClientCreator.createClient("elasticsearch-7.9.3", "127.0.0.1", 9200);

        String condition = FileUtils.readFileToString(new File("D:\\contents.txt"));
        SearchRequestBuilder searchRequestBuilder = transportClient.prepareSearch();
        new ExpressionServiceImpl().extractCondition(condition, searchRequestBuilder);
        System.out.println(searchRequestBuilder.toString());
    }

    @Test
    public void generateQuery3() throws Exception {
        String condition = FileUtils.readFileToString(new File("D:\\contents.txt"));
        Condition conditionVO = decode(condition, Condition.class);
        ExpressionConverter converter = new ExpressionConverter(conditionVO.getIpCondition(), 10);
        System.out.println(converter.getQueryBuilder().toString());
        System.out.println(converter.getRescorerBuilders().toString());
    }

    private <T> T decode(String msg, Class<T> clazz)
    {
        return DeserializerFactory.builder()
                .setThreadSafe(true)
                .setSoftReferences(false)
                .setMaximunCapacity(10)
                .build()
                .readFromByString(msg, clazz);
    }

}
