package client;

import cn.gwssi.data.service.client.Client;
import cn.gwssi.data.util.FeatureClient;
import cn.gwssi.isearch.client.config.HttpClientConfig;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.TransportAddress;
import org.elasticsearch.transport.client.PreBuiltTransportClient;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ClientCreator {

    public static Client createClient(String url) {
        FeatureClient.setImageFeatureUrl("http://*************:20079");
        FeatureClient.setSemanticFeatureUrl("http://*************:20079");

        HttpClientConfig clientConfig = new HttpClientConfig.Builder(url)
                .multiThreaded(true).maxTotalConnection(200).defaultMaxTotalConnectionPerRoute(50)
                .connTimeout(30 * 1000).readTimeout(2 * 60 * 1000).maxConnectionIdleTime(60, TimeUnit.SECONDS)
                .defaultCredentials("elastic", "111111")
                .build();

        return new Client(clientConfig);
    }

    public static Client createClient(String url, String path) {
        FeatureClient.setImageFeatureUrl("http://***************:20079");
        FeatureClient.setSemanticFeatureUrl("http://***************:20079");

        HttpClientConfig clientConfig = new HttpClientConfig.Builder(url)
                .multiThreaded(true).maxTotalConnection(200).defaultMaxTotalConnectionPerRoute(50)
                .connTimeout(30 * 1000).readTimeout(2 * 60 * 1000).maxConnectionIdleTime(60, TimeUnit.SECONDS)
                .maxTotalConnection(50)
//                .defaultCredentials("elastic", "111111")
                .build();

        return new Client(clientConfig, path);
    }

    public static TransportClient createClient(String clusterName, String ip, int port) throws UnknownHostException {
//        // version == 2
//        Settings settings = Settings.settingsBuilder().put("cluster.name", clusterName).build();
//        TransportClient client = TransportClient.builder().settings(settings).build()
//                .addTransportAddress(new InetSocketTransportAddress(InetAddress.getByName(ip), port));

        // version == 5
        Settings settings = Settings.builder().put("cluster.name", clusterName).build();
        TransportClient client = new PreBuiltTransportClient(settings)
                .addTransportAddress(new TransportAddress(InetAddress.getByName(ip), port));

        // version == 6
//        TransportClient client = new PreBuiltTransportClient(Settings.EMPTY)
//                .addTransportAddress(new TransportAddress(InetAddress.getByName("127.0.0.1"), 9200));

        return client;
    }

}
