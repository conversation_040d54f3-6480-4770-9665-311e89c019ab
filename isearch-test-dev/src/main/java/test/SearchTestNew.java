package test;

import client.ClientCreator;
import cn.gwssi.common.common.pojo.FieldList;
import cn.gwssi.common.common.pojo.IPSearchResult;
import cn.gwssi.common.common.pojo.SortField;
import cn.gwssi.common.common.pojo.SortFieldList;
import cn.gwssi.common.common.pojo.aggregation.AggregationDim;
import cn.gwssi.common.common.pojo.aggregation.AggregationMetricsDim;
import cn.gwssi.common.common.pojo.collapse.CollapseCondition;
import cn.gwssi.common.common.pojo.highLight.HighLightCondition;
import cn.gwssi.data.service.client.Client;
import cn.gwssi.data.util.FeatureClient;
import cn.gwssi.syntax.condition.QueryCondition;
import cn.gwssi.syntax.condition.parser.ParserError;
import cn.gwssi.syntax.condition.scannerValue.ValueDate;
import cn.gwssi.syntax.parser.converter.ExpressionConverter;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.patent.entity.Patent;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SearchTestNew {

    @Test
    public void search() throws Exception {
        Client client = ClientCreator.createClient("http://127.0.0.1:9200", "dao");
        String expr = "alarmObject in (1) AND alarmTime >= 2024-06-01 14:38:24 AND alarmTime <= 2024-06-01 15:38:24";
//        expr = FileUtils.readLines(new File("D:\\contents.txt"), StandardCharsets.UTF_8).get(0);
        String defaultField = "mbi";
        String index = "cbs_patent";
        List<String> indexes = new ArrayList<>();
        indexes.add(index);

        SortFieldList sortFields = new SortFieldList();
        SortField sortField = new SortField("_score", SortField.IPSorting.DESC);
        SortField sortField1 = new SortField("ctc", SortField.IPSorting.DESC);
        sortFields.add(sortField);
        sortFields.add(sortField1);

        Map<String, List<String>> groups = new HashMap<>();
        List<String> values = new ArrayList<>();
        values.add("value1");
        values.add("value2");
        groups.put("group1", values);
        values.add("value3");
        groups.put("group2", values);

        HighLightCondition hlCondition = new HighLightCondition();
        hlCondition.highlighterType("plain");
        hlCondition.field("tio").fragmentSize(4).highlighterType("plain");

        AggregationDim dim = new AggregationDim("ipAddress", 1000);
        AggregationDim dim2 = new AggregationDim("port", 1000);
        AggregationDim dim3 = new AggregationMetricsDim("alarmLevel", AggregationMetricsDim.MetricsType.MAX);
        dim2.setAggregationDim(dim3);

        CollapseCondition collapseCondition = new CollapseCondition("sfid");
//        collapseCondition.addSort(new SortField("anc", "CN,US"))
//                .addSort(new SortField("ad", SortField.IPSorting.DESC)).setSize(5);

        FieldList fieldList = new FieldList(new String[]{"pid", "tio", "ano", "pno", "anm"});
        QueryCondition<Patent> condition = new QueryCondition<Patent>().setIndexName(index).setQuery(expr).setDefaultField(defaultField)
//                .setHighLightCondition(hlCondition)
                .setCollapseCondition(collapseCondition)
//                .setGroups(groups)
                .setAggregationDims(new AggregationDim[]{dim,dim2})
//                .setFieldList(fieldList).setSortFields(sortFields)
                .setFrom(0).setSize(100).setGsonClass(Patent.class).setUnique(false);

        ParserError error = client.searchRequest().checkExpress(index, index, expr, "mbi");
//        HashSet<String> sets = new HashSet<>();
//        sets.add("p");
//        System.out.println(client.searchRequest().extractValues(expr, "mbi", null, null));
//        client.aggregationRequest().dimCount(condition);
        System.out.println(client.searchRequest().analysis(condition));
//        FileUtils.writeStringToFile(new File("D:\\dsl1.json"), client.searchRequest().analysis(condition));
        FeatureClient.setSearchPreference("");
        FeatureClient.setSearchType(null);
        client.aggregationRequest().dimCount(condition);
        IPSearchResult<Patent> result = client.searchRequest().search(condition);
//        String result = client.searchRequest().searchStr(condition);
        System.out.println();

//        System.out.println(result.getRecords().get(0).getPid());
    }
}
