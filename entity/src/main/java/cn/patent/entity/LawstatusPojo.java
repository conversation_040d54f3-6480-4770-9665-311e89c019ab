package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 3:58 下午
 */
@Data
public class LawstatusPojo {

    //法律事件
    private String le;
    //法律事件分类(INPADOC)
    private String leci;
    //法律事件代码联合定位(INPADOC)
    private String leusi;
    //法律事件联合定位(中国)
    private String leus;
    //法律状态文字信息
    private String lst;
    //延长保护期
    private String expt;
    //驳回日
    private String rd;
    //撤回日
    private String wd;
    //解密日
    private String dcd;
    //法律状态由未授权或失效变为有效
    private String itvd;
}
