package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 4:40 下午
 */
@Data
public class LitigationsPojo  {

    //案件号
    private String licn;
    //上级案号
    private String liscn;
    //审理法院
    private String lico;
    //审判员
    private String liju;
    //陪审员
    private String liaju;
    //审判长
    private String lipj;
    //原告
    private String lipl;
    //标准原告
    private String lipls;
    //原告代理人
    private String liplag;
    //原告代理机构
    private String liplago;
    //被告
    private String lide;
    //标准被告
    private String lides;
    //被告代理人
    private String lideag;
    //被告代理机构
    private String lideago;
    //第三人
    private String litp;
    //标准第三人
    private String litps;
    //第三代理人
    private String litpag;
    //第三代理机构
    private String litpago;
    //立案日期
    private String lifd;
    //裁判日期
    private String livd;
    //审理程序
    private String litg;
    //案由
    private String lica;
    //案件类型
    private String lint;
    //案件标题
    private String liti;
    //案件全文
    private String lift;
    //结案日期
    private String lijd;
    //胜诉方
    private String lipp;
    //案件状态
    private String lics;
    //文书性质
    private String lipc;
    //法院级别
    private String licg;
    //法院所属省/市
    private String licp;
    //判决结果
    private String lijv;
    //申请赔偿总额
    private String liaa;
    //判赔总额
    private String lija;
    //立案年份
    private String lify;
    //涉及产品
    private String liprd;
}
