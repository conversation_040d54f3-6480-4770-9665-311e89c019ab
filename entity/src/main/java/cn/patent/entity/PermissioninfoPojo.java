package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 4:48 下午
 */
@Data
public class PermissioninfoPojo  {
    //许可类型
    private String licetcn;
    //许可类型-缩写
    private String licetabbr;
    //许可备案阶段-中文
    private String licescn;
    //许可备案阶段-缩写
    private String licesabbr;
    //许可人
    private List<String> liceor;
    //标准许可人
    private List<String> liceors;
    //被许可人
    private List<String> liceee;
    //标准被许可人
    private List<String> liceees;
    //许可合同备案号
    private String licen;
    //许可合同备案日
    private String liced;
    //许可生效日
    private String liceed;
    //许可生效年
    private String liceey;
    //许可合同履行起始日
    private String licest;
    //许可合同履行终止日
    private String licetd;
}
