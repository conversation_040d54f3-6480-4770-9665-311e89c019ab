package cn.patent.entity;

import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 10:03 上午
 */
@Data
public class InventorinfoPojo {
    //发明人中文
    private String incn;
    //发明人英文
    private String inen;
    //发明人日文
    private String injp;
    //发明人原文
    private List<String> ino;
    //发明人地址
    private List<String> inad;
    //发明人国别
    private List<String> inadco;
    //发明人国别（用作统计）
    private String inadco_su;
    //发明人城市
    private List<String> inadc;
    //发明人城市(用作统计)
    private String inadc_su;
    //发明人所在州
    private List<String> inadp;
}
