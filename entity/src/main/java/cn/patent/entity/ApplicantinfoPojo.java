package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/9 4:16 下午
 */
@Data
public class ApplicantinfoPojo  {

    //申请人中文
    private String apcn;
    //申请人英文
    private String apen;
    //申请人日文
    private String apjp;
    //申请人原文
    private List<String> apo;
    //标准原始申请人原文
    private List<String> aposo;
    //标准原始申请人中文
    private String aposcn;
    //标准原始申请人英文
    private String aposen;
    //标准原始申请人日文
    private String aposjp;
    //原始申请人地址中文
    private String apoadcn;
    //原始申请人地址英文
    private String apoaden;
    //原始申请人地址日文
    private String apoadjp;
    //原始申请人地址原始
    private String apoado;
    //原始申请人国别
    private List<String> apoadco;
    //原始申请人州/省
    private List<String> apoadp;
    //原始申请人州/省(统计字段)
    private String apoadp_su;
    //原始申请人地市/市
    private List<String> apoadc;
    //原始申请人地市/市（统计字段）
    private String apoadc_su;
    //原始申请人区县
    private List<String> apoadd;
    //原始申请人区县(统计字段)
    private String apoadd_su;
    //原始申请人街道
    private List<String> apoads;
    //原始申请人园区
    private List<String> apoadz;
//    //原始申请人地址经纬度
//    private List<GeoPoint> apoadll;
    //原始申请人国省代码(中国）
    private List<String> apoadcc;
    //标准原始申请人类型-缩写
    private String apost;
    //标准原始申请人类型-中文
    private String apostcn;

}
