package cn.patent.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 10:23 上午
 */
@Data
public class PriorityPojo {

    //优先权日
    private String prd;

    //优先权年
    private String prdy;
    //优先权年月
    private String prdym;
    //优先权号原始
    private String prno;
    //优先权号DOCDB
    private String prndb;
    //优先权号EPO
    private String prnep;
    //优先权号号码
    private String prnn;
    //优先权号号码原始
    private String prnno;
    //优先权号号码DOCDB
    private String prnndb;
    //优先权号号码EPO
    private String prnne;
    //优先权类型
    private String prt;
    //优先权国家个数
    private String prcoc;
    //优先权国别
    private String prc;
}
