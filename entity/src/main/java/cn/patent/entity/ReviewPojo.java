package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 5:28 下午
 */
@Data
public class ReviewPojo  {

    //复审请求人
    private List<String> reeap;
    
    //标准复审请求人
    private List<String> reeaps;
    
    //复审全文(从文件获取)
    private String reetext;

    //案由（取全文第一段）
    private String reeinvca;
    
    //复审决定号
    private String reen;
    
    //复审委内编号
    private String reeinn;
    
    //复审请求日
    private String reeapd;
    
    //复审决定日
    private String reedd;

    //复审决定
    private String reeden;
    //复审决定-中文
    private String reedcn;
    //复审决定-缩写
    private String reedabbr;

    //复审决定要点
    private String reedp;
    
    //复审法律依据
    private List<String> reelb;
    
    //复审主审员
    private String reeme;
    
    //复审参审员
    private String reeae;
    
    //复审合议组组长
    private String reele;

    //联系人
    private List<String> reeinvco;
    
    //联系人地址
    private List<String> reeinvcoadd;
    
    //联系人省
    private List<String> reeinvcop;
    
    //联系人城市
    private List<String> reeinvcoc;
    
    //联系人单位
    private List<String> reeinvcof;

}
