package cn.patent.entity;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 10:46 上午
 */
@Data
public class CitationPojo  {

    //引证申请日
    private List<String> ctad;
    //引证公开日
    private List<String> ctpd;
    //引证公开年
    private List<String> ctpdy;
    //引证申请人
    private List<String> ctap;
    //引证专利公开号
    private List<String> ctpn;
    //引证专利申请号
    private List<String> ctan;
    //标准引证申请人
    private List<String> ctaps;
    //引证国别
    private String ctco;
    //引证类别
    private String ctcg;
    //引证方
    private String ctp;
    //引证来源
    private String cts;
    //引证简单同族公开号
    private List<List<String>> ctsfpn;
    //引证扩展同族公开号
    private List<List<String>> ctefpn;
}
