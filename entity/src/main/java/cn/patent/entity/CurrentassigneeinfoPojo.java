package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/9 5:38 下午
 */
@Data
public class CurrentassigneeinfoPojo {

    //当前专利权人中文
    private String asccn;
    //当前专利权人英文
    private String ascen;
    //当前专利权人日文
    private String ascjp;
    //当前专利权人原始
    private List<String> asco;
    //标准当前专利权人原文
    private List<String> ascso;
    //标准当前专利权人中文
    private String ascscn;
    //标准当前专利权人英文
    private String ascsen;
    //标准当前专利权人日文
    private String ascsjp;
    //当前专利权人地址中文
    private String ascadcn;
    //当前专利权人地址英文
    private String ascaden;
    //当前专利权人地址日文
    private String ascadjp;
    //当前专利权人地址原文
    private String ascado;
    //当前专利权人国家
    private List<String> ascadco;
    //当前专利权人州/省
    private List<String> ascadp;
    //当前专利权人州/省（统计字段）
    private String ascadp_su;
    //当前专利权人地市
    private List<String> ascadc;
    //当前专利权人地市(统计字段)
    private String ascadc_su;
    //当前专利权人区县
    private List<String> ascads;
    //当前专利权人区县(统计字段)
    private String ascads_su;
    //当前专利权人园区
    private List<String> ascadz;
    //当前专利权人国省代码(中国）
    private List<String> ascadcc;
    //标准当前专利权人类型-缩写
    private String ascst;
    //标准当前专利权人类型-中文
    private String ascstcn;
}
