package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/9 4:34 下午
 */
@Data
public class AssigneeinfoPojo {

    //专利权人中文
    private String ascn;
    //专利权人英文
    private String asen;
    //专利权人日文
    private String asjp;
    //专利权人原文
    private List<String> aso;
    //标准原始专利权人原文
    private List<String> asoso;
    //标准原始专利权人中文
    private String asoscn;
    //标准原始专利权人英文
    private String asosen;
    //标准原始专利权人日文
    private String asosjp;
    //原始专利权人地址中文
    private String asoadcn;
    //原始专利权人地址英文
    private String asoaden;
    //原始专利权人地址日文
    private String asoadjp;
    //原始专利权人地址原始
    private String asoado;
    //原始专利权人国别
    private List<String> asoadco;
    //原始专利权人州/省
    private List<String> asoadp;
    //原始专利权人地市/市
    private List<String> asoadc;
    //原始专利权人区县
    private List<String> asoadd;
    //原始专利权人街道
    private List<String> asoads;
    //原始专利权人园区
    private List<String> asoadz;
//    //原始专利权人地址经纬度
//    private List<GeoPoint> asoadll;
    //原始专利权人国省代码(中国）
    private List<String> asoadcc;
    //标准原始专利权人类型-缩写
    private String asost;
    //标准原始专利权人类型-中文
    private String asostcn;
}
