package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/11 9:41 上午
 */
@Data
public class InvalidinfoPojo  {

    //无效请求人
    private List<String> invap;

    //标准无效请求人
    private List<String> invaps;

    //无效全文（改为从文件中获取）
    private String invtext;

    //无效案由（取无效全文第一段）
    private String invca;

    //无效决定号
    private String invn;

    //无效委内编号
    private String invinn;

    //无效请求日
    private String invapd;

    //无效决定日
    private String invdd;

    //无效决定
    private String invden;

    //无效决定-中文
    private String invdcn;

    //无效决定-缩写
    private String invdabbr;

    //无效决定要点
    private String invdp;

    //无效法律依据
    private List<String> invlb;

    //无效主审员
    private String invme;

    //无效参审员
    private String invae;

    //无效合议组组长
    private String invle;

    //无效联系人
    private List<String> invco;

    //无效联系人地址
    private List<String> invcoadd;

    //无效联系人省
    private List<String> invcop;

    //无效联系人城市
    private List<String> invcoc;

    //无效联系人单位
    private List<String> invcof;

}
