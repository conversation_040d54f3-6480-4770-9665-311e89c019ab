package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 4:57 下午
 */
@Data
public class TransferinfoPojo {
    //专利转移原因-英文
    private String assren;
    //专利转移原因-中文
    private String assrcn;
    //专利转移原因-缩写
    private String assrabbr;
    //转让记录号
    private String assn;
    //转让前地址
    private List<String> assbad;
    //转让前国家
    private List<String> assbadco;
    //转让前省
    private List<String> assbadp;
    //转让前市
    private List<String> assbadc;
    //转让前县
    private List<String> assbadd;
    //转让前街道
    private List<String> assbads;
    //转让前园区
    private List<String> assbadz;

    //转让人
    private List<String> assor;
    //标准转让人
    private List<String> assors;
    //受让人
    private List<String> assee;
    //标准受让人
    private List<String> assees;
    //转让记录日期
    private String assd;
    //转让生效日
    private String assed;
    //转让生效年
    private String assey;
    //涉外申请途径
    private String oar;
    //受让人地址
    private List<String> assaad;
    //转让后国家
    private List<String> assaadco;
    //转让后州/省
    private List<String> assaadp;
    //转让后市
    private List<String> assaadc;
    //转让后县
    private List<String> assaadd;
    //转让后街道
    private List<String> assaads;
    //转让后园区
    private List<String> assaadz;
    //转让联系人
    private List<String> assco;
    //转让联系人地址
    private List<String> asscoadd;

}
