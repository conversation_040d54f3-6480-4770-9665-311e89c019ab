package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/9 5:03 下午
 */
@Data
public class CurrenapplicantinfoPojo {

    //当前申请人中文
    private String apccn;

    //当前申请人英文
    private String apcen;

    //当前申请人日文
    private String apcjp;

    //当前申请人原始
    private List<String> apco;

    //标准当前申请人
    private List<String> apcsdo;
    //标准当前申请人中文
    private String apcsdcn;
    //标准当前申请人英文
    private String apcsden;
    //标准当前申请人日文
    private String apcsdjp;

    //当前申请人地址中文
    private String apcadcn;

    //当前申请人地址英文
    private String apcaden;

    //当前申请人地址日文
    private String apcadjp;

    //当前申请人地址原文
    private String apcado;

    //当前申请人国家
    private List<String> apcadco;

    //当前申请人州/省
    private List<String> apcadp;

    //当前申请人地市
    private List<String> apcadc;

    //当前申请人区县
    private List<String> apcads;

    //当前申请人园区
    private List<String> apcadz;

    //当前申请人国省代码(中国）
    private List<String> apcadcc;

    //标准当前申请人类型-缩写
    private String apcst;
    //标准当前申请人类型-中文
    private String apcstcn;


}
