package cn.patent.entity;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 3:03 下午
 */
@Data
public class CitedPojo  {

    //被引证申请日
    private List<String> ctgad;
    //被引证公开日
    private List<String> ctgpd;
    //被引证专利申请人
    private List<String> ctgap;
    //被引证专利公开号
    private List<String> ctgpn;
    //被引证专利申请号
    private List<String> ctgan;
    //标准被引证专利申请人
    private List<String> ctgaps;
    //被引证当前申请人
    private List<String> ctgapc;
    //被引证国别
    private String ctgco;
    //被引证简单同族公开号
    private List<List<String>> ctgsfpn;
    //被引证扩展同族公开号
    private List<List<String>> ctgefpn;
    //被引方
    private String ctgp;
    //被引来源
    private String ctgs;
}
