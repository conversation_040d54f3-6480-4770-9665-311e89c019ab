package cn.patent.entity;

import cn.gwssi.common.common.pojo.GsonObject;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class Patent extends GsonObject{

    private String pid;

    //标题
    private String ticn;
    private String tien;
    private String tijp;
    private List<String> tio;

    //摘要
    private String abcn;
    private String aben;
    private String abjp;
    private List<String> abot;

    //权利要求
    //这些字段暂时不需要
//    private String clmcn;
//    private String clmen;
//    private String clmjp;
//    private String clmo;

    //权利要求类型-英文
    private String clmten;
    //权利要求类型-中文
    private String clmtcn;
    //权利要求类型-缩写
    private String clmtabbr;

    //首权
    private String fclmcn;
    private String fclmen;
    private String fclmjp;
    private String fclmo;

    //独立权利要求
    private List<String> iclmcn;
    private List<String> iclmen;
    private List<String> iclmjp;
    private List<String> iclmo;

    //从属权利要求
    private List<String> dclmcn;
    private List<String> dclmen;
    private List<String> dclmjp;
    private List<String> dclmo;

    //技术领域
    private String tfcn;
    private String tfen;
    private String tfjp;
    private String tfo;

    //背景技术
    private String tbcn;
    private String tben;
    private String tbjp;
    private String tbo;

    //发明内容
    private String iscn;
    private String isen;
    private String isjp;
    private String iso;

    //附图说明
    private String ddcn;
    private String dden;
    private String ddjp;
    private String ddo;

    //摘要附图
//    private String abspic;

    //具体实施方式
    private String secn;
    private String seen;
    private String sejp;
    private String seo;

    //说明书其他
    private String otherdesccn;
    private String otherdescen;
    private String otherdescjp;
    private String otherdesco;

    //技术功效句
    private List<String> tesc;

    //技术功效短语
    private List<String> tepc;

    //技术功效词
    private List<String> tew;

    //申请语言
    private String apl;

    //专利类型(DOCDB)-A（发明）、U、P（临时申请）、W（PCT）、F（外观）、T（译本）
    private String docdbpt;

    //专利类型(中国)-1,2,3,8,9
    private String ptcn;

    //专利类型(美国）-F（防卫性公告）、D（外观）、P（植物）、R（再公告）、S（法定发明登记）、U（发明）
    private String ptus;

    //专利类型-简单-A（申请）、B（授权）、U（新型）、D（外观）
    private String pts;

    //授权日
    private String gd;

    //审查时长-中国发明专利的实质审查生效日到授权公告日的时长，单位为月
    private String exp;

    //提出实审时长-中国发明专利的申请日到实质审查生效日的时长，单位为月
    private String pexp;

    //实质审查生效日
    private String seed;

    //申请日
    private String ad;

    //申请年
    private String ady;

    //申请年月
    private String adym;

    //申请号DOCDB
    private List<String> andb;

    //申请号号码DOCDB
    private List<String> anndb;

    //申请号原始
    private List<String> ano;

    //申请号码原始
    private List<String> anno;

    //申请号EPO
    private List<String> ane;

    //申请号号码EPO
    private List<String> anne;

    //申请号合并
    private String anm;

    //PCT国际申请号
    private List<String> pctan;

    //受理局
    private String anc;

    //公开（公告）号DOCDB
    private List<String> pndb;
    //公开（公告）号号码DOCDB
    private List<String> pnndb;
    //公开（公告）号原始
    private List<String> pno;
    //公开（公告）号码原始
    private List<String> pnno;
    //公开（公告）号EPO
    private List<String> pne;
    //公开（公告）号码EPO
    private List<String> pnne;
    //PCT国际申请公开号
    private List<String> pctpn;
    //公开日
    private String pd;
    //公开年
    private String pdy;
    //公开年月
    private String pdym;
    //公开类型(存的数字)
    private String ptabbr;
    //公开类型 (存中文)
    private String ptzh;
    //首次公开日
    private String pdf;
    //公开国别
    private String pnc;

    //IPC分类
    private Set<String> ipc;
    //IPC分类-部
    private Set<String> ipcs;
    //IPC分类-大类
    private Set<String> ipcc;
    //IPC分类-小类
    private Set<String> ipcsc;
    //IPC分类-大组
    private Set<String> ipcg;

    //IPC主分类
    private String ipcm;
    //IPC主分类-部
    private String ipcms;
    //IPC主分类-大类
    private String ipcmc;
    //IPC主分类-小类
    private String ipcmsc;
    //IPC分类-大组
    private String ipcmg;

    //CPC分类
    private Set<String> cpc;
    //CPC发明点分类
    private Set<String> cpci;
    //C-Sets
    private Set<String> cpccs;
    //CPC分类-部
    private Set<String> cpcs;
    //CPC分类-大类
    private Set<String> cpcc;
    //CPC分类-小类
    private Set<String> cpcsc;
    //CPC分类-大组
    private Set<String> cpcg;
    //CPC分类-小组
    private Set<String> cpcsg;

    //CPC主分类
    private String cpcm;
    //CPC主分类-部
    private String cpcms;
    //CPC主分类-大类
    private String cpcmc;
    //CPC主分类-小类
    private String cpcmsc;
    //CPC主分类-大组
    private String cpcmg;
    //CPC主分类-小组
    private String cpcmsg;

    //LOC分类
    private Set<String> loc;
    //LOC分类-大类
    private Set<String> locc;
    //LOC分类-小类
    private Set<String> locsc;

    //美国UPC分类
    private Set<String> uc;
    //美国UPC主分类
    private String ucm;

    //日本FI分类
    private Set<String> fi;
    //日本FI主分类
    private String fim;
    //日本F-term分类
    private Set<String> ft;

    //国民经济行业主分类
    private String neimc;
    //国民经济行业分类门类
    private Set<String> neic;
    //国民经济行业分类大类
    private Set<String> neicc;
    //国民经济行业分类中类
    private Set<String> neimic;
    //国民经济行业分类
    private Set<String> neisc;
    //战略性新兴产业主分类
    private String seimc;
    //战略性新兴产业门类
    private Set<String> seic;
    //战略性新兴产业大类
    private Set<String> seicc;
    //战略性新兴产业分类
    private Set<String> seisc;
    //申请人信息
    private List<ApplicantinfoPojo> applicantinfo;
    //工商别名
    private List<String> apfn;
    //工商英文名
    private List<String> apne;
    //工商注册地址
    private List<String> aprad;
    //工商注册地址省
    private List<String> apradp;
    //工商注册地址地市
    private List<String> apradc;
    //工商注册地址区县
    private List<String> apradd;
//    //工商地址经纬度
//    private List<GeoPoint> apradll;
//    //联系地址经纬度
//    private List<GeoPoint> apcadll;
    //工商公司类型
    private List<String> apct;
    //工商成立日期
    private List<String> apcet;
    //工商统一社会信用代码
    private List<String> apuscc;
    //工商注册号
    private List<String> aprn;
    //工商企业状态
    private List<String> apcs;
    //工商上市代码
    private List<String> apclc;
    //企业所属行业
    private List<String> apci;
    //企业标签
    private List<String> apcl;

    //第一原始申请人
    private List<String> apfo;
    //标准第一原始申请人
    private List<String> apfos;
    //第一原始申请人中文
    private String apfcn;
    //标准第一原始申请人中文
    private String apfscn;
    //第一原始申请人英文
    private String apfen;
    //标准第一原始申请人英文
    private String apfsen;
    //第一原始申请人日文
    private String apfjp;
    //标准第一原始申请人日文
    private String apfsjp;
    //第一原始申请人类型(中国）-缩写
    private String pafotabbr;
    //第一原始申请人类型(中国）-中文名称
    private String pafotcn;
    //第一原始申请人国家
    private List<String> apfoco;

    //专利权人信息
    private List<AssigneeinfoPojo> assigneeinfo;

    //第一原始专利权人
    private List<String> asfo;
    //标准第一原始专利权人
    private List<String> asfos;
    //第一原始专利权人中文
    private String asfcn;
    //标准第一原始专利权人中文
    private String asfscn;
    //第一原始专利权人英文
    private String asfen;
    //标准第一原始专利权人英文
    private String asfsen;
    //第一原始专利权人日文
    private String asfjp;
    //标准第一原始专利权人日文
    private String asfsjp;
    //第一原始专利权人国家
    private List<String> asfoco;

    //美国专利权人类型
    private List<String> patus;
    //美国第一专利权人类型
    private String paftus;

    //当前申请人
    private List<CurrenapplicantinfoPojo> currenapplicantinfo;

    //当前专利权人
    private List<CurrentassigneeinfoPojo> currentassigneeinfo;

    //第一当前申请人中文
    private String apfccn;
    //第一当前申请人英文
    private String apfcen;
    //第一当前申请人日文
    private String apfcjp;
    //第一当前申请人原始
    private List<String> apfco;
    //标准第一当前申请人中文
    private String apfcscn;
    //标准第一当前申请人英文
    private String apfcsen;
    //标准第一当前申请人日文
    private String apfcsjp;
    //标准第一当前申请人原始
    private List<String> apfcso;
    //第一当前专利权人中文
    private String asfccn;
    //第一当前申请(专利权)人英文
    private String asfcen;
    //第一当前专利权人日文
    private String asfcjp;
    //第一当前专利权人原始
    private List<String> asfco;
    //标准第一当前专利权人中文
    private String asfcscn;
    //标准第一当前专利权人英文
    private String asfcsen;
    //标准第一当前专利权人日文
    private String asfcsjp;
    //标准第一当前专利权人原始
    private List<String> asfcso;

    //授权时专利权人
    private List<String> pag;
    //授权时第一专利权人
    private String pafg;
    //授权时专利权人地址(中国）
    private List<String> pagad;
    //授权时专利权人国家(中国）
    private List<String> pagadco;
    //授权时专利权人省(中国）
    private List<String> pagadp;
    //授权时专利权人地市(中国）
    private List<String> pagadc;
    //授权时专利权人区县(中国）
    private List<String> pagadd;
    //授权时专利权人街道(中国）
    private List<String> pagads;
    //授权时专利权人园区(中国）
    private List<String> pagadz;
//    //授权时专利权人经纬度(中国）
//    private GeoPoint pagadll;
    //授权时专利权人国省代码(中国）
    private List<String> pagadcc;

    //异议方
    private List<String> oppo;

    //发明人信息
    private List<InventorinfoPojo> inventorinfo;
    //当前发明人信息
    private List<CurrentInventorInfoPojo> currentinventorinfo;

    //第一发明人中文
    private String infcn;
    //第一发明人英文
    private String infen;
    //第一发明人日文
    private String infjp;
    //第一发明人原始
    private List<String> info;

    //代理人中文
    private List<String> agtcn;
    //代理人英文
    private List<String> agten;
    //代理人日文
    private List<String> agtjp;
    //代理人原始
    private List<String> agto;
    //代理人地址
    private List<String> agtad;
    //代理人国别
    private List<String> agtadco;
    //代理人城市
    private List<String> agtadc;
    //代理人所在州
    private List<String> agtadp;

    //代理机构中文
    private List<String> agccn;
    //代理机构英文
    private List<String> agcen;
    //代理机构日文
    private List<String> agcjp;
    //代理机构原文
    private List<String> agco;

    //审查员中文
    private List<String> excn;
    //审查员英文
    private List<String> exen;
    //审查员日文
    private List<String> exjp;
    //审查员原始
    private List<String> exo;

    //助理审查员
    private List<ExaminerAssiantPojo> examiner_assiants;

    //优先权
    private List<PriorityPojo> priority;

    //最早的"优先权日"/"申请日"
    private String prdad;

    //最早优先权国
    private Set<String> prce;
    //最早优先权号码原始
    private Set<String> prneo;
    //最早优先权号码DOCDB
    private Set<String> prnedb;
    //最早优先权号码EPO
    private Set<String> prneep;
    //最早优先权日
    private Set<String> prde;
    //最早优先权年
    private Set<String> prdye;
    //引证信息
    private List<CitationPojo> citation;
    //X引证文献
    private List<String> ctx;
    //Y引证文献
    private List<String> cty;
    //A引证文献
    private List<String> cta;
    //家族引证申请人
    private List<String> fctap;
    //家族引证专利
    private List<String> fct;
    //引证专利数量
    private int ctc;
    //引证公开号数量
    private int ctpnc;
    //引证申请号数量
    private int ctaac;
    //家族引证次数
    private int fctc;
    //引证国别数量
    private int ctcoc;
    //引证号码
    private List<String> ctn;

    //被引证信息
    private List<CitedPojo> cited;
    //非专利引证
    private List<String> ctnp;
    //家族被引证专利
    private List<String> fctg;
    //家族被引证申请人
    private List<String> fctgap;
    //被引证专利数量
    private int ctgc;
    //被引证专利公开号数量
    private int ctgpnc;
    //被引证专利申请号数量
    private int ctganc;
    //家族被引证次数
    private int fctgc;
    //简单同族被引专利公开号数量
    private int sfctgpnc;
    //简单家族被引专利申请号数量
    private int sfctganc;
    //简单家族被引专利国别数量
    private int sfctgcoc;
    //被引专利简单同族数量
    private int ctgsfc;
    //被引专利扩展同族数量
    private int ctgefc;
    //非专利引证数量
    private int ctnpc;
    //被引证号码
    private List<String> ctgn;
    //被引证专利国别数量
    private int ctgcoc;

    //PCT进入国家阶段日
    private String pctnd;
    //PCT国际申请申请号原始
    private List<String> pctao;
    //PCT国际申请申请号WIPO
    private List<String> pctaw;
    //PCT国际申请公开号原始
    private List<String> pctpo;
    //PCT指定国
    private List<String> pctco;

    //同族信息
    private List<PatentfamilysimPojo> patentfamilysim;
    //简单同族个数
    private int sfc;
    //简单同族申请号个数
    private int sfanc;
    //简单同族国家数
    private int sfcoc;
    //简单同族代表性专利
    private List<String> sfrpn;
    //扩展同族个数
    private int efc;
    //扩展同族公开号个数
    private int efpnc;
    //扩展同族申请号个数
    private int efanc;
    //扩展同族国家数
    private int efcoc;
    //DOCDB同族个数
    private int docdbfc;
    //简单同族号
    private String sfid;
    //扩展同族号
    private String efid;
    //扩展同族首次公开日
    private List<String> efpdf;
    //专利有效性-中文
    private String slscn;
    //专利有效性-代码
    private String slsabbr;
    //当前法律状态-存的英文
    private String legalen;
    //当前法律状态-中文
    private String legalcn;
    //当前法律状态-缩写
    private String legalabbr;
    //法律状态
    private List<LawstatusPojo> lawstatus;
    //法律状态更新日
    private String lsud;
    //预估到期日
    private String eed;
    //预估到期年
    private String eey;
    //法律状态公告时间
    private List<String> lsad;
    //专利寿命
    private String pl;

    //独立权利要求数量
    private int iclmc;
    //从属权利要求数量
    private int dclmc;
    //首权字数
    private int fclmolec;
    //权利要求数量
    private int clmc;
    //申请时权利要求个数
    private int clmac;
    //授权时权利要求个数
    private int clmbc;
    //文献页数
    private int docn;
    //说明书页数
    private int desn;
    //原始申请人数量
    private String apc;
    //原始专利权人数量
    private int asc;
    //当前专利权人数量
    private int appzcc;
    //发明人数量
    private String inq;
    //CPC分类数量
    private String cpcq;
    //CPC小类数量
    private String cpcsq;
    //IPC分类数量
    private String ipcq;
    //IPC小类数量
    private String ipcsq;
    //FI分类号数量
    private String fic;
    //FT分类号数量
    private String ftc;
    //政府利益
    private String govi;
    //PCT路径进入专利
    private String pctpt;
    //专利价值度
    private String pv;
    //法律
    private String pvl;
    //经济
    private String pve;
    //技术
    private String pvt;
    //奖励等级
    private List<String> rl;
    //奖励名称
    private List<String> rn;
    //奖励届次
    private List<String> rs;
    //奖励年度
    private List<String> ry;
    //标准类型
    private List<String> seps;
    //标准项目
    private List<String> sepp;
    //标准号
    private List<String> sepn;
    //标准标题
    private List<String> septi;
    //标准持有公司
    private List<String> sephc;
    //所有标准专利
    private String sep;
    //所有无优先权专利
    private String prem;
    //海关专利
    private String cus;
    //备案号
    private List<String> recn;
    //备案时间
    private List<String> recd;
    //取消时间
    private List<String> cacd;
    //备案状态
    private List<String> recs;
    //审批意见
    private List<String> ac;
    //有无标签
    private List<String> flag;
    //FDA-CAS号
    private String casn;
    //FDA-中文名称
    private String fdac;
    //FDA-英文名称
    private String fdae;
    //FDA-申请号
    private String fdaan;
    //FDA-申请类型
    private String fdaat;
    //FDA-公司名称
    private String fdacn;
    //FDA-公司简称
    private String fdacns;
    //FDA-产品号
    private String fdapid;
    //FDA-商品名
    private String fdapn;
    //FDA-活性成分
    private String fdaai;
    //FDA-靶点
    private String fdat;
    //FDA-适应症
    private String fdai;
    //FDA-专利到期日
    private String fdaped;
    //FDA-PED药物专利到期日
    private String fdapeded;
    //FDA-剂型
    private String fdadf;
    //FDA-是否为物质专利
    private String fdacp;
    //FDA-是否为产品专利
    private String fdapp;
    //FDA-专利用途代码
    private String fdauc;

    //是否诉讼
    private String li;
    //诉讼次数
    private int lic;
    //当事人委托律所
    private String liplf;
    //诉讼信息
    private List<LitigationsPojo> litigations;

    //许可信息
    private List<PermissioninfoPojo> permissioninfo;

    //许可专利
    private String lice;

    //当前许可备案阶段
    private String licesc;

    //当前被许可人
    private List<String> liceeec;

    //许可次数
    private int licec;

    //权利转移
    private List<TransferinfoPojo> transferinfo;
    //转让后当前地址
    private List<String> assaadrc;
    //是否转让
    private String ass;
    //转让次数
    private int assc;
    //申请权转让次数
    private int aassc;
    //专利权转让次数
    private int passc;
    //口审信息
    private List<OralPojo> oral;
    //复审信息
    private List<ReviewPojo> review;
    //所有发生复审/无效的专利
    private String reeinv;
    //复审次数
    private int reec;

    //无效次数
    private int invc;
    //无效信息
    private List<InvalidinfoPojo> invalidinfo;

    //虚拟字段reeinvt决定类型,该字段只支持检索，不做统计
    private List<String> reeinvten; //取值是从mongo复审无效库中reviews/reeinvt和invalids/reeinvt获取

    private List<String> reeinvtcn;

    private List<String> reeinvtabbr;

    //质押信息
    private List<PledgePojo> pledge;

    //保全生效日
    private List<String> preed;
    //保全解除日
    private List<String> prerd;

    //是否质押
    private String ple;
    //专利保全
    private String pre;
    //当前质权人
    private List<String> peec;
    //标准当前质权人
    private List<String> peecs;
    //质押期限
    private String plet;
    //质押备案阶段-中文
    private String plescn;
    //质押备案阶段-缩写
    private String plesabbr;
    //保全阶段-中文
    private String prescn;
    //保全阶段-缩写
    private String presabbr;
    //保全次数
    private int prec;
    //质押次数
    private int plec;

    //关联案件类型(缩写)
    private List<String> rpt;
    //关联案件类型(英文)
    private List<String> rpten;
    //关联案件类型(中文)
    private List<String> rptcn;
    //关联案件申请号
    private List<String> rpap;
    //WO国家阶段
    private List<String> wocos;

    //海关备案最近更新时间
    private String crut;
    //变更入库时间
    private String cst;
    //文献代码
    private String pnk;
    //母案
    private List<String> man;
    //分案
    private List<String> dan;
    //一案双申单值
    private String da;
    //一案双申多值(存多值，当前专利pno和一案双申里的所有类型的codeStr和codeNum)
    private List<String> das;
    //一案发明申请号
    private String dafm;
    //一案新型申请号
    private String daxx;
    //专利权人历史（历史专利权人（pah） + 当前专利权人（pacs））
    private List<String> par;
    //相关发明人（历史发明人（inh）+当前发明人（inv））
    private List<String> inr;
    //全文
    private String tacd;
//    //全文语义
//    private TextFeature tacd_vec;
//    //图片检索
//    private List<Feature> image;
    //主要著录项信息
    //根据最新设计，暂时不需要了设置MBI字段了
    // private List<String> mbi;

    //专利最新命中
    private List<String> patentchgdates;
    //法律状态变更
    private List<String> legaltimestamp;
    //专利进入时审
    private List<String> reetimestamp;
    //专利授权
    private List<String> legatimestamp;
    //专利失效
    private List<String> legtimestamp;
    //专利权转让
    private List<String> passctimestamp;
    //专利权人变更
    private List<String> changetimestamp;
    //复审无效信息更新
    private List<String> invctimestamp;
    //质押信息更新
    private List<String> plectimestamp;
    //诉讼信息更新
    private List<String> lictimestamp;
    //海关备案信息更新
    private List<String> customstimestamp;
    //许可信息更新
    private List<String> licectimestamp;
    //说明书变化
    private List<String> desntimestamp;
    //入库时间
    private String instime;
    //化学式
    private List<ChemicalInfoPojo> chemicalinfo;
    //标题关键字中文
    private Set<String> titlewordzh;
    //摘要关键字中文
    private Set<String> abstractwordzh;
    //说明书关键字中文
    private Set<String> descriptionwordzh;
    //权利要求关键字中文
    private Set<String> claimswordzh;
    //标题关键字英文
    private Set<String> titleworden;
    //摘要关键字英文
    private Set<String> abstractworden;
    //说明书关键字英文
    private Set<String> descriptionworden;
    //权利要求关键字英文
    private Set<String> claimsworden;
    //ipph格式的申请号
    private String ipphan;
    //ipph格式的公开号
    private String ipphpn;

    public Patent() {
        this.tfcn = "";
        this.tbcn = "";
        this.iscn = "";
        this.ddcn = "";
        this.secn = "";
        this.otherdesccn = "";
    }

    public String getTicn() {
        return Objects.nonNull(this.ticn) ? this.ticn : "";
    }
    public String getAbcn() {
        return Objects.nonNull(this.abcn) ? this.abcn : "";
    }
    public String getFclmcn() {
        return Objects.nonNull(this.fclmcn) ? this.fclmcn : "";
    }
    public List<String> getIclmcn() {
        return Objects.nonNull(this.iclmcn) ? this.iclmcn : Collections.emptyList();
    }
    public List<String> getDclmcn() {
        return Objects.nonNull(this.dclmcn) ? this.dclmcn : Collections.emptyList();
    }
}
