package cn.patent.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/11 9:46 上午
 */
@Data
public class PledgePojo  {

    //质押保全类型-中文
    private String plepretcn;
    //质押保全类型-代码
    private String plepretabbr;
    //出质人
    private List<String> por;
    //标准出质人
    private List<String> pors;
    //质权人
    private List<String> pee;
    //标准质权人
    private List<String> pees;
    //质押登记号
    private String plen;
    //质押生效日
    private String pleed;
    //质押变更日
    private String plrcd;
    //质押年
    private String pley;
    //质押解除日
    private String plerd;
}
